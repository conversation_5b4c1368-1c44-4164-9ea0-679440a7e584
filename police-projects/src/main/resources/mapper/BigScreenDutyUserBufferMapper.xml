<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.bigscreen.mapper.BigScreenDutyUserBufferMapper">

    <select id="selectByDto" resultType="com.trs.police.bigscreen.domain.vo.BufferUserInfoVO">
        select
            t.id,
            t.cr_time as crTime,
            t.cr_user_id as crUserId,
            t.cr_dept_id as crDeptId,
            t.user_id as userId,
            d.name as deptName,
            t.dept_id  as deptId,
            t.xm,
            t.zjhm,
            t.sjh,
            t.jh,
            t.duty,
            t.post_code as postCode,
            t.post_name as postName,
            t.police_kind as policeKind,
            t.profile_pic as profilePic,
            t.district_code as districtCode
        from tb_bigscreen_duty_user_buffer t
        left join t_dept d on t.dept_id = d.id
        <where>
            t.is_del = 0
            and t.district_code = #{dto.districtCode}
            <if test="dto.postCode != null and dto.postCode != ''">
                and t.post_code = #{dto.postCode}
            </if>
            <if test="dto.searchField != null and dto.searchField != ''">
                and t.${dto.searchField} like concat('%',#{dto.searchValue},'%')
            </if>
        </where>
    </select>
</mapper>