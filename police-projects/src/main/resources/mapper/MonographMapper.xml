<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.nc.monograph.mapper.MonographMapper">

    <select id="monographList" resultType="com.trs.police.nc.monograph.vo.MonographListVO">
        <include refid="monographJoinSearch"></include>
        <where>
            <if test="dto.portType != null and dto.portType != '' ">
                <!--收专刊-->
                <if test="dto.portType == 0">
                    and mdr.relation_type in (0, 2, 3)
                </if>
                <!--写专刊-->
                <if test="dto.portType == 1">
                    and (mdr.relation_type = #{dto.portType}
                    or mdr.relation_type = '2')
                </if>
                <!--忽略改字段-->
                <if test="dto.portType == 2">
                    <!--不过滤-->
                </if>
            </if>
            <!-- 默认查询场景是收专刊 -->
            <if test="dto.portType == null or dto.portType == ''">
                and m.monograph_status = 0
            </if>
            <if test="dto.monographStatus != null">
                and m.monograph_status = #{dto.monographStatus}
            </if>
            <if test="dto.monographType != null">
                and m.monograph_type = #{dto.monographType}
            </if>
            <if test="dto.publishDeptId != null">
                and m.create_dept_id = #{dto.publishDeptId}
            </if>
            <if test="dto.deptId != null">
                and mdr.dept_id = #{dto.deptId}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and m.update_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and m.update_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                <if test="dto.portType == null or dto.portType == ''">
                    and m.name like concat('%',#{dto.searchValue},'%')
                </if>
                <if test="dto.portType != null and dto.portType != ''">
                    and (m.name like concat('%',#{dto.searchValue},'%')
                    or m.monograph_content like concat('%',#{dto.searchValue},'%'))
                </if>
            </if>
            <if test="null != statisticQueryDTO">
                <if test="null != statisticQueryDTO.startTimeAfter and '' != statisticQueryDTO.startTimeAfter">
                    and m.start_time <![CDATA[>=]]> #{statisticQueryDTO.startTimeAfter}
                </if>
                <if test="null != statisticQueryDTO.endTimeBefore and '' != statisticQueryDTO.endTimeBefore">
                    and m.start_time <![CDATA[<=]]> #{statisticQueryDTO.endTimeBefore}
                </if>
                <if test="null != statisticQueryDTO.publishStartAfter and '' != statisticQueryDTO.publishStartAfter">
                    and m.publish_time <![CDATA[>=]]> #{statisticQueryDTO.publishStartAfter}
                </if>
                <if test="null != statisticQueryDTO.publishEndBefore and '' != statisticQueryDTO.publishEndBefore">
                    and m.publish_time <![CDATA[<=]]> #{statisticQueryDTO.publishEndBefore}
                </if>
                <if test="null != statisticQueryDTO.deptRelationStatus">
                    and mdr.relation_status = #{statisticQueryDTO.deptRelationStatus}
                </if>
                <if test="null != statisticQueryDTO.hasOverDue and true == statisticQueryDTO.hasOverDue">
                    and mdr.relation_status = 0 and NOW() > m.end_time
                </if>
                <if test="null != statisticQueryDTO.submitted and true == statisticQueryDTO.submitted">
                    and exists (
                        SELECT mdtr.id
                    from
                        t_monograph_directory_dept_relation mdtr
                    where
                        mdtr.monograph_id = m.id
                        and mdtr.dept_id = mdr.dept_id
                        and mdtr.relation_status = 1
                    )
                </if>
            </if>
        </where>
        group by m.id
        order by m.start_time desc
    </select>

    <select id="selectMonographById" resultType="com.trs.police.nc.monograph.vo.MonographListVO">
        <include refid="monographJoinSearch"></include>
        where 1 = 1
        <if test="dto.monographId != null and dto.monographId != ''">
            and m.id = #{dto.monographId}
        </if>
        group by monographId
    </select>
    <select id="monographStatistics" resultType="com.trs.police.nc.monograph.vo.MonographStatisticsOverDueVO">
        SELECT
        (SELECT td.short_name from t_dept td WHERE td.id = r.dept_id) as deptName,
        r.dept_id as deptId,
        COUNT(DISTINCT CASE WHEN m.monograph_type = 0 THEN m.id END) AS statisticsDayCount,
        COUNT(DISTINCT CASE WHEN m.monograph_type = 1 THEN m.id END) AS statisticsWeekCount,
        COUNT(DISTINCT CASE WHEN m.monograph_type = 2 THEN m.id END) AS statisticsMonthCount,
        COUNT(DISTINCT CASE WHEN m.monograph_type in (0,1,2) THEN m.id END) as totalCount
        FROM
        t_monograph m
        JOIN
        t_monograph_dept_relation r ON m.id = r.monograph_id
        <where>
            and r.relation_type in (1, 2)
            and exists (
                SELECT mdtr.id
                from
                    t_monograph_directory_dept_relation mdtr
                where
                    mdtr.monograph_id = m.id
                    and mdtr.dept_id = r.dept_id
                    and mdtr.relation_status = 1
            )
            <if test="dto.startTime != null and dto.startTime != ''">
                and m.start_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and m.start_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY
        r.dept_id
        <if test="dto.orderField != null and dto.orderField != '' and dto.orderType != null and dto.orderType != ''">
            ORDER BY ${dto.orderField} ${dto.orderType}
        </if>
    </select>
    <select id="monographStatisticsList" resultType="com.trs.police.nc.monograph.vo.MonographStatisticsListVO">
        SELECT
        DISTINCT
        tm.id as monographId,
        tm.name as monographName,
        tm.monograph_type as monographType,
        tm.start_time as startTime,
        CASE
        WHEN tm.completion = 1 OR tm.end_time >= NOW() THEN TRUE
        ELSE false
        END as overdueStatus
        from
        t_monograph tm
        left join
        t_monograph_dept_relation tr on tm.id = tr.monograph_id
        <where>
            1=1
            <if test="dto.deptId != null">
                and tr.dept_id = #{dto.deptId}
            </if>
            <if test="dto.monographType != null">
                and tm.monograph_type = #{dto.monographType}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                and tm.start_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and tm.start_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                and tm.name like concat('%',#{dto.searchValue},'%')
            </if>
        </where>
    </select>
    <select id="monographStatisticsOverDue"
            resultType="com.trs.police.nc.monograph.vo.MonographStatisticsOverDueVO">
        SELECT
        (SELECT td.short_name FROM t_dept td WHERE td.id = r.dept_id) AS deptName,
        r.dept_id as deptId,
        COUNT(DISTINCT CASE WHEN m.monograph_type = 0 AND (CASE WHEN m.completion = 1 OR m.end_time >= NOW() THEN FALSE ELSE TRUE END) THEN m.id END) AS statisticsDayCount,
        COUNT(DISTINCT CASE WHEN m.monograph_type = 1 AND (CASE WHEN m.completion = 1 OR m.end_time >= NOW() THEN FALSE ELSE TRUE END) THEN m.id END) AS statisticsWeekCount,
        COUNT(DISTINCT CASE WHEN m.monograph_type = 2 AND (CASE WHEN m.completion = 1 OR m.end_time >= NOW() THEN FALSE ELSE TRUE END) THEN m.id END) AS statisticsMonthCount,
        COUNT(DISTINCT CASE WHEN m.monograph_type in(0,1,2) AND (CASE WHEN m.completion = 1 OR m.end_time >= NOW() THEN FALSE ELSE TRUE END) THEN m.id END) AS totalCount
        FROM
        t_monograph m
        JOIN
        t_monograph_dept_relation r ON m.id = r.monograph_id
        <where>
            r.relation_status = 0
            and NOW() > m.end_time
            and r.relation_type in (1, 2)
            <if test="dto.startTime != null and dto.startTime != ''">
                and m.start_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and m.start_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY
        r.dept_id
        <if test="dto.orderField != null and dto.orderField != '' and dto.orderType != null and dto.orderType != ''">
            ORDER BY ${dto.orderField} ${dto.orderType}
        </if>
    </select>
    <select id="selectListByTj" resultType="com.trs.police.nc.monograph.entity.Monograph">
        select * from t_monograph
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                and DATE_FORMAT(publish_time, '%Y-%m-%d') >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and DATE_FORMAT(publish_time, '%Y-%m-%d') &lt;= #{dto.endTime}
            </if>
            <if test="types != null and types.size() > 0">
                 and monograph_type in
                <foreach collection="types" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
        </where>
        order by publish_time desc
    </select>

    <sql id="monographJoinSearch">
        select
        m.id as monographId,
        m.name as monographName,
        m.template_id as templateId,
        m.monograph_content as monographContent,
        m.monograph_type as monographType,
        m.monograph_status as monographStatus,
        m.cover_image as coverImage,
        m.completion as completion,
        mdr.being_urged as beingUrged,
        mdr.relation_status as relationStatus,
        m.start_time as updateTime,
        m.end_time as endTime,
        m.create_dept_id as createDeptId,
        DATE_FORMAT(m.publish_time, '%Y-%m-%d %H:%i:%s') as publishTime
        from
        t_monograph m
        left join t_monograph_dept_relation mdr on m.id = mdr.monograph_id
    </sql>
</mapper>