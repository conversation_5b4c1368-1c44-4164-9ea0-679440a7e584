package com.trs.police.lz.authCenter.domain.dto;

import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/04/01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuthCenterDto extends BaseDTO {

    /**
     * 搜索字段
     */
    private String searchField;

    /**
     * 搜索内容
     */
    private String searchValue;

    /**
     * 1：通知通报，2：技战法，3：典型案例
     */
    private Integer type;

    /**
     * 状态 0：未选用，1：选用
     */
    private Integer status;
}
