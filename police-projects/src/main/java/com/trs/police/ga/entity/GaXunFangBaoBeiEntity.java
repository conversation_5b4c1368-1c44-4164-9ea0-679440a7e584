package com.trs.police.ga.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.zg.entity.ZgBaoBeiEntity;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 广安 - 勤务管理巡防报备表
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since ：2025/3/21 16:53
 * @version 1.0
 * @since 1.0
 */
@TableName("v_tb_qwgl_xfbb")
@Data
public class GaXunFangBaoBeiEntity implements Serializable {

    @TableId
    private String id;

    @TableField
    private String bcId;

    @TableField
    private String bcMc;

    @TableField
    private Date bcKssj;

    @TableField
    private Date bcJssj;

    /**
     * 班次跨天数
     * 01：今日
     * 02：明日
     * 03：2天后
     * 04：3天后
     * 05：4天后
     * 06：5天后
     * 07：6天后
     * 08：7天后
     */
    @TableField
    private String bcKtsDict;

    @TableField
    private Integer bcKts;

    @TableField
    private String xfqyId;

    @TableField
    private String xfqyMc;

    @TableField
    private String jzId;

    @TableField
    private String jzMc;

    @TableField
    private String xfxlfs;

    @TableField
    private String xfwzlx;

    @TableField
    private String xfzzlx;

    @TableField
    private String zbpdmc;

    @TableField
    private String zbpdh;

    @TableField
    private String zbpdhh;

    @TableField
    private String zbdh;

    @TableField
    private Date bbSjBbrq;

    @TableField
    private Date bbSjKssj;

    @TableField
    private Date bbSjJssj;

    /**
     * 报备状态
     * 01：排班中
     * 02：报备中
     * 03：报备结束
     */
    @TableField
    private String bbZt;

    /**
     * 审核人姓名
     */
    @TableField
    private String shrXm;

    @TableField
    private String shrSfzh;

    @TableField
    private String shrLxfs;

    @TableField
    private String shrSsbm;

    @TableField
    private String shrSsbmdm;

    /**
     * 01：未审核
     * 02：审核中
     * 03：审核通过
     * 04：审核不通过
     */
    @TableField
    private String shZt;

    @TableField
    private String ssbm;

    @TableField
    private String ssbmdm;

    @TableField
    private String ssxgaj;

    @TableField
    private String ssxgajdm;

    @TableField
    private String sssgaj;

    @TableField
    private String sssgajdm;

    @TableField
    private String xtSjly;

    @TableField
    private String xtSjzt;

    @TableField
    private String xtScbz;

    @TableField
    private String xtCjip;

    @TableField
    private Timestamp xtCjsj;

    @TableField
    private String xtCjrId;

    @TableField
    private String xtCjr;

    @TableField
    private String xtCjbmdm;

    @TableField
    private String xtCjbmmc;

    @TableField
    private String xtZhgxip;

    @TableField
    private Timestamp xtZhgxsj;

    @TableField
    private String xtZhgxrid;

    @TableField
    private String xtZhgxr;

    @TableField
    private String xtZhgxbmdm;

    @TableField
    private String xtZhgxbm;

    @TableField
    private String bz;

    @TableField
    private String xfzt;

    @TableField
    private Double jd;

    @TableField
    private Double wd;

    @TableField
    private Integer xfsc;

    @TableField
    private Integer xflc;

    @TableField
    private String xffwlx;

    @TableField
    private String xffwid;

    @TableField
    private String dqwz;

    /**
     * 勤务类型
     */
    @TableField
    private String qwlx;

    @TableField
    private String xffaId;

    @TableField
    private String xffaMc;

    @TableField
    private String qwdjId;

    /**
     * 勤务等级
     */
    @TableField
    private String qwdj;

    /**
     * 负责人姓名
     */
    @TableField
    private String fzrXm;

    @TableField
    private String fzrSfzh;

    /**
     * convert<Br/>
     *
     * @param entity 参数
     * @return 结果
     */
    public static ZgBaoBeiEntity convert(GaXunFangBaoBeiEntity entity) {
        ZgBaoBeiEntity zgBaoBeiEntity = new ZgBaoBeiEntity();
        BeanUtil.copyPropertiesIgnoreNull(entity, zgBaoBeiEntity);
        if (entity.getJd() != null && entity.getWd() != null) {
            String zb = String.format("point(%s %s)", entity.getJd(), entity.getWd());
            zgBaoBeiEntity.setZb(zb);
        }
        // 当前位置
        zgBaoBeiEntity.setXxdz(entity.getDqwz());
        return zgBaoBeiEntity;
    }
}
