package com.trs.police.ga.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.datasource.starter.typehandler.GeometryTypeHandler;
import com.trs.police.zg.entity.ZgJingWuZhanEntity;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2025/3/20 17:06
 * @version 1.0
 * @since 1.0
 */
@TableName("v_tb_jcgl_jwz")
@Data
public class GaJingWuZhanEntity implements Serializable {

    @TableId
    private String jwzId;

    @TableField
    private String jwzLx;

    @TableField
    private String jwzMc;

    @TableField
    private String jwzDz;

    @TableField
    private String xzqhdm;

    @TableField
    private Double jd;

    @TableField
    private Double wd;

    @TableField(jdbcType = JdbcType.OTHER, typeHandler = GeometryTypeHandler.class)
    private String zb;

    @TableField
    private String zbhash;

    /**
     * 修建时间
     */
    @TableField
    private Date xjSj;

    /**
     * 负责人身份证号
     */
    @TableField
    private String fzrSfzh;

    /**
     * 负责人姓名
     */
    @TableField
    private String fzrXm;

    /**
     * 警务站图片集合，逗号分隔
     */
    @TableField
    private String jwzTp;

    @TableField
    private String ssbmid;

    @TableField
    private String ssbm;

    @TableField
    private String ssbmdm;

    @TableField
    private String ssxgajid;

    @TableField
    private String ssxgaj;

    @TableField
    private String ssxgajdm;

    @TableField
    private String sssgajid;

    @TableField
    private String sssgaj;

    @TableField
    private String sssgajdm;

    @TableField
    private String xtSjly;

    @TableField
    private String xtSjzt;

    @TableField
    private String xtScbz;

    @TableField
    private String xtCjip;

    @TableField
    private Timestamp xtCjsj;

    @TableField
    private String xtCjrId;

    @TableField
    private String xtCjr;

    @TableField
    private String xtCjbmdm;

    @TableField
    private String xtCjbmmc;

    @TableField
    private String xtZhgxip;

    @TableField
    private Timestamp xtZhgxsj;

    @TableField
    private String xtZhgxrid;

    @TableField
    private String xtZhgxr;

    @TableField
    private String xtZhgxbmdm;

    @TableField
    private String xtZhgxbm;

    @TableField
    private String bz;

    /**
     * 转换
     *
     * @param entity 参数
     * @return 结果
     */
    public static ZgJingWuZhanEntity convert(GaJingWuZhanEntity entity) {
        ZgJingWuZhanEntity zgJingWuZhanEntity = new ZgJingWuZhanEntity();
        BeanUtils.copyProperties(entity, zgJingWuZhanEntity);
        String zb = String.format("point(%s %s)", entity.getJd(), entity.getWd());
        zgJingWuZhanEntity.setZb(zb);
        return zgJingWuZhanEntity;
    }
}
