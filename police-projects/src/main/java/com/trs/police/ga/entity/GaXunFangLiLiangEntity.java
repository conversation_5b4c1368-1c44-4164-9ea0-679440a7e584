package com.trs.police.ga.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.zg.entity.ZgXunFangLiLiangEntity;
import com.trs.police.zg.vo.XunFangLiLiangVo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 巡防力量（广安）
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2025/3/20 15:41
 * @version 1.0
 * @since 1.0
 */
@Data
@TableName(value = "v_tb_jcgl_xfll")
public class GaXunFangLiLiangEntity extends ZgXunFangLiLiangEntity {

    /**
     * toVo<Br>
     *
     * @param entity 参数
     * @return 结果
     */
    public static XunFangLiLiangVo toVo(GaXunFangLiLiangEntity entity) {
        XunFangLiLiangVo vo = new XunFangLiLiangVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
