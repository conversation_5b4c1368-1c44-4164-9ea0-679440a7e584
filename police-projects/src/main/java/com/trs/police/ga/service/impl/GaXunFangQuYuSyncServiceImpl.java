package com.trs.police.ga.service.impl;

import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.zg.mapper.ZgXunFangQuYuMapper;
import com.trs.police.zg.service.impl.ZgXunFangQuYuSyncServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @version 1.0
 * @since ：2025/3/26 9:50
 * @since 1.0
 */
@Service
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "ga")
public class GaXunFangQuYuSyncServiceImpl extends ZgXunFangQuYuSyncServiceImpl {

    public GaXunFangQuYuSyncServiceImpl(SyncTaskMapper syncTaskMapper,
                                        RedisTemplate<String, Object> redisTemplate,
                                        ZgXunFangQuYuMapper mapper) {
        super(syncTaskMapper, redisTemplate, mapper);
    }

    @Override
    public String desc() {
        return "广安巡防区域信息同步";
    }
}
