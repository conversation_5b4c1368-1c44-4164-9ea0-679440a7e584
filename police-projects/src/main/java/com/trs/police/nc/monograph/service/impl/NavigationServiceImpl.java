package com.trs.police.nc.monograph.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.nc.monograph.service.NavigationService;
import com.trs.police.nc.monograph.service.ZlService;
import com.trs.police.nc.monograph.vo.NavigationInfo;
import com.trs.police.nc.monograph.vo.SystemVO;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 导航服务实现
 *
 * <AUTHOR>
 */
@Component
public class NavigationServiceImpl implements NavigationService {

    @Autowired
    private ZlService zlService;

    @Override
    public NavigationInfo getNavigation() {
        // 构造vo
        NavigationInfo info = new NavigationInfo();
        info.setBaseUrl(BeanFactoryHolder.getEnv().getProperty("com.trs.monograph.zl.baseUrl"));
        // 构造主导航
        JSONArray data = JSON.parseObject(zlService.getnavigation(AuthHelper.getNotNullSimpleUser().getIdCard()))
                .getJSONArray("data");
        List<SystemVO> navigationList = data.toJavaList(SystemVO.class);
        String property = BeanFactoryHolder.getEnv().getProperty(
                "com.trs.monograph.zl.main",
                "{\"智慧接处警\": \"SYS_CODE_ICC,SYS_CODE_VCS,SYS_CODE_AIA,SYS_CODE_SMS\",\"风险防控\": \"SYS_CODE_FPCS,SYS_CODE_GPCS,SYS_CODE_TESYP\",\"指挥行动\": \"SYS_CODE_IFS,SYS_CODE_ADS,SYS_CODE_SCS\"}"
        );
        Map<String, String> mainNavigationMap = JSON.parseObject(property, Map.class);
        List<SystemVO> main = mainNavigationMap.entrySet()
                .stream()
                .map(entry -> {
                    SystemVO systemVO = new SystemVO();
                    systemVO.setSystemName(entry.getKey());
                    systemVO.setData(navigationList.stream()
                            .filter(vo -> entry.getValue().contains(vo.getSystemCode()))
                            .collect(Collectors.toList())
                    );
                    return systemVO;
                })
                .collect(Collectors.toList());
        info.setMain(main);
        // 构造其他导航
        String mainCode = mainNavigationMap.values()
                .stream()
                .collect(Collectors.joining(","));
        List<SystemVO> other = navigationList.stream()
                .filter(vo -> !mainCode.contains(vo.getSystemCode()))
                .collect(Collectors.toList());
        info.setOther(other);
        return info;
    }
}
