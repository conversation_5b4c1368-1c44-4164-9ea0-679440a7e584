package com.trs.police.nc.monograph.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 专刊目录与部门配置
 *
 * <AUTHOR>
 */
@Data
@TableName("t_monograph_directory_dept_conf")
public class TemplateDeptRelation {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("dept_id")
    private Long deptId;

    @TableField("directory_id")
    private Long directoryId;
}
