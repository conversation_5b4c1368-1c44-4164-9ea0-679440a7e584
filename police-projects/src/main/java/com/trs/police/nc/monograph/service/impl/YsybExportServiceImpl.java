package com.trs.police.nc.monograph.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.trs.police.common.core.utils.DataUtils;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.nc.monograph.constant.FillMode;
import com.trs.police.nc.monograph.entity.Monograph;
import com.trs.police.nc.monograph.entity.MonographDirectoryDeptRelation;
import com.trs.police.nc.monograph.entity.MonographTemplate;
import com.trs.police.nc.monograph.mapper.MonographMapper;
import com.trs.police.nc.monograph.mpservice.MonographDirectoryDeptRelationMpService;
import com.trs.police.nc.monograph.mpservice.MonographTemplateMpService;
import com.trs.police.nc.monograph.service.ExportService;
import com.trs.police.nc.monograph.service.MonographDirectoryService;
import com.trs.police.nc.monograph.vo.MonographDirectoryVO;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 一所一表导出实现
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class YsybExportServiceImpl implements ExportService {

    @Autowired
    private MonographTemplateMpService monographTemplateMpService;

    @Autowired
    private MonographDirectoryService directoryService;

    @Autowired
    private MonographDirectoryDeptRelationMpService directoryDeptRelationMpService;

    @Autowired
    private JarFileServiceImpl jarFileService;

    @Autowired
    private MonographMapper monographMapper;


    @Override
    public void export(Long monographId) {
        Monograph monograph = monographMapper.selectById(monographId);
        List<MonographDirectoryVO> resultList = directoryService.flatListByTemplateId(monograph.getTemplateId());
        resultList.sort(Comparator.comparingInt(MonographDirectoryVO::getPeerSorting));
        // 填写数据数据
        List<MonographDirectoryDeptRelation> list = directoryDeptRelationMpService.lambdaQuery()
                .eq(MonographDirectoryDeptRelation::getMonographId, monograph.getId())
                .list();
        Function<MonographDirectoryVO, Optional<MonographDirectoryDeptRelation>> findByDirectoryId = DataUtils.findSingleByT(
                resultList,
                d -> list,
                (d, c) -> d.getId().equals(c.getDirectoryId()),
                MonographDirectoryVO::getId
        );
        // 生成excel
        List<String> sheetNames = resultList.stream().map(MonographDirectoryVO::getDirectoryName).collect(Collectors.toList());
        try (
                InputStream inputStream = jarFileService.getFile("monograph/ysyb.xlsx");
                InputStream allSheetInputStream = createSheetFromTemplate(inputStream, sheetNames);
                OutputStream outputStream = getRequestOutputStream(monograph.getName());
                InputStream defaultExcelData = jarFileService.getFile("monograph/ysybDefaultData.json");
                ExcelWriter excelWriter = EasyExcel.write(outputStream).withTemplate(allSheetInputStream).build()
        ) {
            String defaultContent = IOUtils.toString(defaultExcelData);
            // 构造数据
            for (MonographDirectoryVO directory : resultList) {
                Map<String, String> map = new HashMap<>();
                map.put("pcs", directory.getDirectoryName());
                MonographDirectoryDeptRelation content = findByDirectoryId.apply(directory).orElse(new MonographDirectoryDeptRelation());
                map.put("otherContent", content.getDirectoryContent());
                List<Map> listData = JSONArray.parseArray(StringUtil.isEmpty(content.getExcelListData()) ? "[]" : content.getExcelListData(), Map.class);
                if (CollectionUtils.isEmpty(listData)) {
                    listData.add(JSON.parseObject(defaultContent, Map.class));
                } else {
                    AtomicInteger order = new AtomicInteger(1);
                    listData.forEach(m -> m.put("order", order.getAndIncrement()));
                }
                WriteSheet writeSheet = EasyExcel.writerSheet(directory.getDirectoryName()).build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                excelWriter.fill(listData, fillConfig, writeSheet);
                excelWriter.fill(map, fillConfig, writeSheet);
            }
            excelWriter.finish();
        } catch (Exception e) {
            Try.run(() -> {
                HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
                response.reset();
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().println(JSON.toJSONString(RestfulResults.error(e.getMessage())));
            }).onFailure(throwable -> log.error("重置返回异常", throwable));
            log.error("导出异常", e);
        }
    }

    @Override
    public Boolean accept(Monograph monograph) {
        MonographTemplate byId = monographTemplateMpService.getById(monograph.getTemplateId());
        return FillMode.YSYB.equals(FillMode.ofCode(byId.getFillMode()).orElse(FillMode.COLLABORATION));
    }

    private OutputStream getRequestOutputStream(String fileName) throws Exception {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        response.reset();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", fileName + ".xlsx");
        ServletOutputStream outputStream = response.getOutputStream();
        return outputStream;
    }

    private InputStream createSheetFromTemplate(InputStream inputStream, List<String> addSheetName) throws Exception {
        List<String> reverse = Lists.reverse(addSheetName);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        //原模板只有一个sheet，通过poi复制出需要的sheet个数的模板
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        //设置模板的第一个sheet的名称
        for (String name : reverse) {
            workbook.cloneSheet(0, name);
        }
        workbook.removeSheetAt(0);
        //写到流里
        workbook.write(bos);
        byte[] bArray = bos.toByteArray();
        InputStream is = new ByteArrayInputStream(bArray);
        return is;
    }
}
