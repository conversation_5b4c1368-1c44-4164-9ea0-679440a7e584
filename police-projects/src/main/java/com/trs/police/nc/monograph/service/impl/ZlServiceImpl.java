package com.trs.police.nc.monograph.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.nc.monograph.dto.CommandDTO;
import com.trs.police.nc.monograph.dto.CommandWspzxx;
import com.trs.police.nc.monograph.service.ZlService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 指令服务实现类
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ys.fight.collaboration.approval.version", havingValue = "nc", matchIfMissing = true)
public class ZlServiceImpl implements ZlService {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public String getnavigation(String idCard) {
        return "";
    }

    @Override
    public String getToken() {
        return "";
    }

    @Override
    public CommandWspzxx getWspzxx() {
        // 获取服务地址
        String baseUrl = getServiceUrl(); // url 从配置中获取

        // 拼接完整 URL
        String requestUrl = baseUrl + "/commandcenter-ifs-order-new/officialNumber/message/order?xxzlxDm=0103&zlflDm=1";

        // 调用接口并接收原始响应字符串
        String response = restTemplate.getForObject(requestUrl, String.class);

        // 解析 JSON 响应
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 先解析为通用结构
            JsonNode rootNode = objectMapper.readTree(response);
            JsonNode dataNode = rootNode.get("data");

            // 反序列化为 CommandWspzxx 对象
            return objectMapper.treeToValue(dataNode, CommandWspzxx.class);
        } catch (Exception e) {
            throw new TRSException("Failed to parse response", e);
        }
    }

    @Override
    public String sendCommand(CommandDTO commandDTO, List<byte[]> files) {
        return "";
    }

    private String getServiceUrl() {
        return BeanFactoryHolder.getEnv().getProperty("com.trs.monograph.zl.url");
    }
}
