package com.trs.police.nc.monograph.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 指令信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CommandMessageOfOrderBo implements Serializable {

    /**
     * 是否需要反馈 0-不需要 1-需要
     */
    private String xyfk;

    /**
     * 如果xyfk传1，则此字段必传
     * 处置时限 格式 yyyy-MM-dd HH:mm:ss
     */
    private String czsx;

    /**
     * 如果xyfk传1，则此字段必传
     * 反馈类型，固定传 01
     */
    private String fklx;

    /**
     * 消息类别代码 固定传 01
     */
    private String dxxlbDm = "01";

    /**
     * 消息类别名称 固定传 通知类
     */
    private String dxxlbMc = "通知类";

    /**
     * 指令类型代码，固定传 0103
     */
    private String dxxzlxDm = "0103";

    /**
     * 指令类型名称，固定传 工作通知
     */
    private String dxxzlxMc = "工作通知";

    /**
     * 消息标题、指令标题
     */
    private String xxbt;

    /**
     * 签发人姓名
     */
    private String lkQfrXm;

    /**
     * 联系电话
     */
    private String lkLxrDh;

    /**
     * 联系人姓名
     */
    private String lkLxrXm;

    /**
     * 业务类型，固定传 01
     */
    private String ywlx = "01";

    /**
     * 指令分类代码，固定传 1
     */
    private String zlflDm = "1";

    /**
     * 指令分类名称，固定传 联合指挥部指令
     */
    private String zlflMc = "联合指挥部指令";

    /**
     * 指令要素信息表，固定传 zl_gl_gztzb
     */
    private String ysxxbBm = "zl_gl_gztzb";

    /**
     * 接受方信息
     */
    private List<CommandJsdw> jsdwJh;

    /**
     * 文书配置信息，从3.4接口获取，下列2.17.x全部字段均从该接口获取
     */
    private CommandWspzxx wspzxx;
}
