package com.trs.police.nc.domain.entity;

import com.trs.common.pojo.BaseVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @date 创建时间：2024/9/12 15:56
 * @version 1.0
 * @since  1.0
 */
@Data
public class RespVo extends BaseVO {

    private Integer status;

    private List<NanChongDutyUser> data;

    public RespVo() {
        this.data = new ArrayList<>(0);
    }
}
