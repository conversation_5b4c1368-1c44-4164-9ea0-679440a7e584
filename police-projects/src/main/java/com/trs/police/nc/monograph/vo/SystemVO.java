package com.trs.police.nc.monograph.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 系统信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SystemVO {

    private String guid;
    private String userGuid;
    private String systemGuid;
    private String systemCode;
    private String systemName;
    private SystemNameJson systemNameJson;
    private String systemPath;
    private String systemNo;
    private Integer sort;
    private String isShow;
    private String icon;

    /**
     * 子导航
     */
    private List<SystemVO> data;

    /**
     * 系统名称
     */
    @Data
    @NoArgsConstructor
    public static class SystemNameJson {
        private String ptBR;
        private String en;
        private String zhCN;
    }
}
