package com.trs.police.nc.monograph.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 接收信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CommandJsdw implements Serializable {

    /**
     * 接收对象类型代码 (ORG:机构; USER:个人)
     */
    private String djsdxlxDm;

    /**
     * 接收对象类型名称 机构/个人
     */
    private String djsdxlxMc;

    /**
     * 接收对象代码 机构时传单位代码，个人时传身份证
     */
    private String jsdxDm;

    /**
     * 接收对象单位代码(个人传对应的单位代码，机构就是单位代码)
     */
    private String jsdxDwDm;

    /**
     * 接收对象名称
     */
    private String jsdxMc;

    /**
     * 固定传 1
     */
    private String sfqs;
}
