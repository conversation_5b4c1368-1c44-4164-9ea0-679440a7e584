package com.trs.police.nc;

import com.trs.police.nc.monograph.service.*;
import com.trs.police.nc.monograph.vo.*;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author: tang.shuai
 * @date: 2024/08/06
 * @description: 专刊
 */
@Slf4j
@RestController
@RequestMapping("/navigation")
public class NavigationController {

    @Autowired
    private NavigationService navigationService;

    /**
     * 获取导航
     *
     * @return 导航
     */
    @RequestMapping("getNavigation")
    public RestfulResultsV2<NavigationInfo> getNavigation() {
        return RestfulResultsV2.ok(navigationService.getNavigation());
    }

}

