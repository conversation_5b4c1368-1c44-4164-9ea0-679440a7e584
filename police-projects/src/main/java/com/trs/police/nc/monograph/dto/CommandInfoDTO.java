package com.trs.police.nc.monograph.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CommandDTO info信息
 */
@Data
@NoArgsConstructor
public class CommandInfoDTO implements Serializable {

    /**
     * 通知类型名称（要情通报、情况通报、专项报送、其他通知）
     */
    private String tzlx_mc;

    /**
     * 通知类型代码（01-要情通报 02-情况通报 03-专项报送 04-其他通知）
     */
    private String tzlx_dm;

    /**
     * 工作要求，限制字符串长度在2000以内
     */
    private String gzyq;

    /**
     * 内容id，请自行生成一个uuid
     */
    private String zlNrId;
}
