package com.trs.police.nc.monograph.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MonographCommentDto
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonographCommentDto {
    /**
     * 专刊id
     */
    private Long monographId;

    /**
     * 目录id
     */
    private Long directoryId;

    /**
     * 评论内容
     */
    private String comment;

    /**
     * 当前用户id
     */
    private Long commentUserId;

    /**
     * 当前用户所属部门id
     */
    private Long commentDeptId;

    /**
     * 当前用户名称
     */
    private String userName;

    /**
     * 当前用户部门名称
     */
    private String deptName;

    /**
     * 评论id
     */
    private Long id;

}
