package com.trs.police.nc.monograph.service;

import com.trs.police.nc.monograph.dto.CommandDTO;
import com.trs.police.nc.monograph.dto.CommandWspzxx;

import java.util.List;

/**
 * 指令服务
 *
 * <AUTHOR>
 */
public interface ZlService {

    /**
     * 获取到导航
     *
     * @param idCard 身份证号码
     * @return 导航
     */
    String getnavigation(String idCard);

    /**
     * 获取到token
     *
     * @return token
     */
    String getToken();

    /**
     * 获取文书配置信息
     *
     * @return cw
     */
    CommandWspzxx getWspzxx();

    /**
     * 发送指令
     *
     * @param commandDTO 指令
     * @param files 文件
     * @return 结果
     */
    String sendCommand(CommandDTO commandDTO, List<byte[]> files);

}
