package com.trs.police.nc.monograph.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 指令接口参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CommandDTO implements Serializable {

    /**
     * 内容信息字符串，具体结构化含义信息见后续，它是一个array对象序列化后的json字符串 必填
     * {@link CommandInfoDTO}
     */
    private String infos;

    /**
     * 指令基本信息
     */
    private CommandMessageOfOrderBo messageOfOrderBo;

}
