package com.trs.police.nc.monograph.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.police.bigscreen.domain.entity.WorkInstructionEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/09/13
 * @description: nc工作指令mapper
 */
@Mapper
@DS("pgsql")
public interface GzzlMapper {

    /**
     * queryList
     *
     * @return {@link List}<{@link WorkInstructionEntity}>
     */
    List<WorkInstructionEntity> queryList();

    /**
     * selectCount
     *
     * @return {@link Long}
     */
    Long selectCount();
}
