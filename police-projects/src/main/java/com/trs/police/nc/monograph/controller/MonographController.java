package com.trs.police.nc.monograph.controller;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.nc.monograph.constant.MonographType;
import com.trs.police.nc.monograph.dto.*;
import com.trs.police.nc.monograph.service.*;
import com.trs.police.nc.monograph.service.impl.MonographStatisticsServiceImpl;
import com.trs.police.nc.monograph.vo.*;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @author: tang.shuai
 * @date: 2024/08/06
 * @description: 专刊
 */
@Slf4j
@RestController
@RequestMapping("/monograph")
public class MonographController {

    @Autowired
    private MonographService monographService;

    @Autowired
    private MonographDirectoryService directoryService;

    @Autowired
    private MonographStatisticsServiceImpl statisticsService;

    @Autowired
    private MonographTemplateService templateService;

    @Autowired
    private MonographTaskService taskService;

    @Autowired
    private ExportServiceFactory exportServiceFactory;

    @Autowired
    private ZlService zlService;

    @Autowired
    private NavigationService navigationService;

    /**
     * 专刊列表
     *
     * @param monographDTO monographDTO
     * @param statisticQueryDTO  统计dto
     * @return {@link PageResult}<{@link MonographListVO}>
     */
    @GetMapping("monographList")
    public PageResult<MonographListVO> monographList(MonographDTO monographDTO, MonographStatisticQueryDTO statisticQueryDTO) {
        return monographService.monographList(monographDTO, statisticQueryDTO);
    }

    /**
     * 获取相似专刊
     *
     * @param monographDTO monographDTO
     * @return {@link List}<{@link SimilarMonographVO}>
     */
    @GetMapping("getSimilarMonograph")
    public List<SimilarMonographVO> getSimilarMonograph(MonographDTO monographDTO) {
        return monographService.getSimilarMonograph(monographDTO);
    }
    /**
     * 获取评论列表
     *
     * @param monographInfoDTO monographInfoDTO
     * @return 评论列表
     */
    @PostMapping("/commentList")
    public RestfulResultsV2<CommentVO> getCommentList(@RequestBody MonographInfoDTO monographInfoDTO){
        return monographService.commentList(monographInfoDTO);
    }

    /**
     * 添加评论
     *
     * @param commentDto 参数
     */
    @PostMapping("/addComment")
    public void addComment(@RequestBody MonographCommentDto commentDto){
        monographService.insertComment(commentDto);
    }

    /**
     * 催办
     *
     * @param monographInfoDTO 参数
     */
    @PostMapping("/reminders")
    public void reminders(@RequestBody MonographInfoDTO monographInfoDTO){
        monographService.reminders(monographInfoDTO);
    }

    /**
     * 获取详情
     *
     * @param monographInfoDTO monographInfoDTO
     * @return 专刊内容
     */
    @PostMapping("/contentDetail")
    public RestfulResultsV2<MonographDetailVO> contentDetail(@RequestBody MonographInfoDTO monographInfoDTO){
        return RestfulResultsV2.ok(monographService.contentDetail(monographInfoDTO));
    }

    /**
     * 编辑详情
     *
     * @param dto dto
     */
    @PostMapping("/editInfo")
    public void editInfo(@RequestBody MonographInfoDTO dto){
        monographService.editMonographInfo(dto);
    }

     /**
     * 导出专刊
     *
     * @param monographId 专刊id
     */
    @GetMapping("exportMonograph")
    public void export(Long monographId) {
        ExportService s = exportServiceFactory.serviceOfId(monographId);
        if (Objects.nonNull(s)) {
            s.export(monographId);
        }
    }

    /**
     * 获取目录
     *
     * @param dto dto
     *
     * @return 结果
     */
    @PostMapping("/directoryList")
    public RestfulResultsV2<MonographDirectoryVO> directoryList(@RequestBody MonographInfoDTO dto){
        return RestfulResultsV2.ok(directoryService.directoryList(dto));
    }


    /**
     * 获取当前编辑用户id
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/editLock")
    public RestfulResultsV2<MonographDirectoryVO> editLock(@RequestBody MonographInfoDTO dto){
        return RestfulResultsV2.ok(directoryService.editLock(dto));
    }

    /**
     * 发布专刊
     *
     * @param dto 参数
     * @throws ParseException 异常
     */
    @PostMapping("push")
    public void push(@RequestBody PushDTO dto) throws ParseException {
        monographService.push(dto);
    }

    /**
     * 模板列表
     *
     * @param dto dto
     * @return 模板列表
     */
    @GetMapping("templateList")
    public RestfulResultsV2<MonographTemplateVO> templateList(TemplateListDTO dto) {
        return templateService.templateList(dto);
    }

    /**
     * 编辑模板
     *
     * @param dto dto
     */
    @PostMapping("editTemplate")
    public void editTemplate(@RequestBody TemplateEditDTO dto) {
        templateService.editTemplate(dto);
    }

    /**
     * 启用或禁用模板
     *
     * @param dto 参数
     */
    @PostMapping("/startOrForbid")
    public void startOrForbid(@RequestBody TemplateListDTO dto){
        templateService.startOrForbid(dto);
    }

    /**
     * 手动初始化
     *
     * @param time 时间
     * @param code 类型
     * @param id 模板id
     */
    @PostMapping("public/init")
    public void init(String time, Integer code,@RequestParam(value = "id",required = false) Long id) {
        Date date = TimeUtils.stringToDate(time);
        LocalDateTime t = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        taskService.initMonograph(t, MonographType.codeOf(code).get(), id);
    }

    /**
     * 初始化专刊 手动执行下个周期的专刊
     *
     * @param time 时间
     * @param code 类型
     * @param id 模板id
     */
    @PostMapping("/init")
    public void initMonograph(String time, Integer code,@RequestParam(value = "id",required = false) Long id) {
        Date date = TimeUtils.stringToDate(time);
        LocalDateTime t = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        taskService.init(t, MonographType.codeOf(code).get(), id);
    }

    /**
     * 获取当前编辑用户用户
     *
     * @param dto 参数
     * @return 当前编辑用户
     */
    @PostMapping("/getEditUserInfo")
    public RestfulResultsV2<UserDto> getEditUserInfo(@RequestBody MonographInfoDTO dto){
        return directoryService.getEditUserInfo(dto);
    }

    /**
     * 删除当前编辑用户
     *
     * @param dto 参数
     */
    @PostMapping("/releaseEditUserInfo")
    public void releaseEditUserInfo(@RequestBody MonographInfoDTO dto){
        directoryService.releaseEditUserInfo(dto);
    }

    /**
     * 获取表头可编辑信息
     *
     * @param monographId 专刊id
     * @return 可编辑信息
     * @throws Exception 异常信息
     */
    @GetMapping("/editableInfo")
    public List<EditableInfoVO> editableInfo(@RequestParam Integer monographId) throws Exception {
        return monographService.editableInfo(monographId);
    }

    /**
     * 发布专刊统计
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/publishedMonographStatistics")
    public RestfulResultsV2<PublishedMonographStatisticsVO> publishedMonographStatistics(@RequestBody MonographDTO dto){
        return RestfulResultsV2.ok(statisticsService.publishedMonographStatistics(dto));
    }

    /**
     * 专刊发布日历
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/publishMonographDate")
    public RestfulResultsV2<MonographPublishDateVO> publishMonographDate(@RequestBody MonographDTO dto){
        return RestfulResultsV2.ok(statisticsService.publishMonographDate(dto));
    }

    /**
     * 专刊统计
     *
     * @param monographDTO 参数
     * @return 专刊统计
     */
    @GetMapping("monographStatistics")
    public RestfulResultsV2<PublishedMonographStatisticsVO> monographStatistics(MonographDTO monographDTO) {
        return monographService.monographStatistics(monographDTO);
    }

    /**
     * 专刊统计列表
     *
     * @param monographDTO 参数
     * @return 专刊统计列表
     */
    @GetMapping("monographStatisticsList")
    public RestfulResultsV2<MonographStatisticsListVO> monographStatisticsList(MonographDTO monographDTO) {
        return monographService.monographStatisticsList(monographDTO);
    }

    /**
     * 发送指令
     *
     * @param monographId 专刊id
     * @return 结果
     */
    @RequestMapping("commandcenter")
    public RestfulResultsV2<Long> commandcenter(Long monographId) {
        return RestfulResultsV2.ok(1L);
    }

    /**
     * 获取导航
     *
     * @return 导航
     */
    @RequestMapping("getNavigation")
    public RestfulResultsV2<NavigationInfo> getNavigation() {
        return RestfulResultsV2.ok(navigationService.getNavigation());
    }

}

