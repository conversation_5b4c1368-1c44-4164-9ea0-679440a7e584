package com.trs.police.nc.monograph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.nc.monograph.dto.MonographInfoDTO;
import com.trs.police.nc.monograph.entity.MonographComment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonographCommentMapper extends BaseMapper<MonographComment> {
    /**
     * 查询评论列表
     *
     * @param monographInfoDTO 参数
     * @return 结果
     */
    List<MonographComment> selectByIds(@Param("dto") MonographInfoDTO monographInfoDTO);
}
