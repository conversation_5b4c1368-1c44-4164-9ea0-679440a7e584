package com.trs.police.nc.service.impl;

import com.trs.police.bigscreen.domain.entity.WorkInstructionEntity;
import com.trs.police.bigscreen.mapper.WorkInstructionMapper;
import com.trs.police.bigscreen.service.BaseWorkInstructionSyncService;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.nc.monograph.mapper.GzzlMapper;
import io.vavr.Tuple2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/11 17:47
 * @since 1.0
 */
@Service
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "nc")
public class WorkInstructionSyncService extends BaseWorkInstructionSyncService<WorkInstructionEntity> {

    private final GzzlMapper gzzlMapper;

    private final WorkInstructionMapper workInstructionMapper;

    public WorkInstructionSyncService(
            GzzlMapper gzzlMapper,
            WorkInstructionMapper workInstructionMapper,
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(workInstructionMapper, syncTaskMapper, redisTemplate);
        this.gzzlMapper = gzzlMapper;
        this.workInstructionMapper = workInstructionMapper;
    }

    /**
     * findInData<BR>
     *
     * @param startTime 参数
     * @param endTime   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:05
     */
    @Override
    public Tuple2<List<WorkInstructionEntity>, String> findInData(String startTime, String endTime) {
        return new Tuple2<>(
                gzzlMapper.queryList(),
                ""
        );
    }

    @Override
    public void beforeSave() {
        // 先去查视图里面是否有数据，如果没有则不进行删除操作
        Long count = gzzlMapper.selectCount();
        if (count > 0L) {
            workInstructionMapper.delete(null);
        }
    }

    @Override
    public WorkInstructionEntity convert(WorkInstructionEntity entity) {
        return entity;
    }

    @Override
    public String key() {
        return "NcGzzl";
    }

    @Override
    public String desc() {
        return "南充工作指令同步";
    }
}
