package com.trs.police.nc.monograph.service.impl;

import com.trs.police.nc.monograph.dto.CommandDTO;
import com.trs.police.nc.monograph.dto.CommandWspzxx;
import com.trs.police.nc.monograph.service.ZlService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 开发实现
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ys.fight.collaboration.approval.version", havingValue = "dev", matchIfMissing = false)
public class ZlDevServiceImpl implements ZlService {

    @Override
    public String getnavigation(String idCard) {
        return "{\"code\":\"200\",\"message\":\"操作成功\",\"data\":[{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"3fc38f0a-916c-43de-bb94-95d358147b14\",\"systemCode\":\"SYS_CODE_ICC\",\"systemName\":\"智慧接警\",\"systemNameJson\":{\"zh-Hant\":\"智慧接處警系統\",\"pt-BR\":\"Integrated Command & Control\",\"en\":\"Integrated Command & Control\",\"zh-CN\":\"智慧接警\"},\"systemPath\":\"/command-center/icc\",\"systemNo\":\"ICC\",\"sort\":1,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"d1add964-dfda-4245-8fc1-3fb71c4b8f8b\",\"systemCode\":\"SYS_CODE_VCS\",\"systemName\":\"可视化处警\",\"systemNameJson\":{\"zh-Hant\":\"可視化指揮調度系統\",\"pt-BR\":\"Visual Command System\",\"en\":\"Visual Command System\",\"zh-CN\":\"可视化处警\"},\"systemPath\":\"/command-center/vcs\",\"systemNo\":\"VCS\",\"sort\":2,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"a8d8b3f6-ef82-44db-b343-9d323cb314e4\",\"systemCode\":\"SYS_CODE_AIA\",\"systemName\":\"警情分析\",\"systemNameJson\":{\"zh-Hant\":\"警情分析系統\",\"pt-BR\":\"Alarm Intelligence Analysis\",\"en\":\"Alarm Intelligence Analysis\",\"zh-CN\":\"警情分析\"},\"systemPath\":\"/command-center/aia\",\"systemNo\":\"AIA\",\"sort\":3,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"d69a63b7-14ca-4e68-af91-33d72b172a77\",\"systemCode\":\"SYS_CODE_NEW_IFS\",\"systemName\":\"要情指令线索\",\"systemNameJson\":{\"zh-CN\":\"要情指令线索\"},\"systemPath\":\"/command-center/ifs-new\",\"systemNo\":\"SYS_CODE_NEW_IFS\",\"sort\":4,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"6c079708-7a7c-4cbf-9759-1b103fae8703\",\"systemCode\":\"SYS_CODE_SMS\",\"systemName\":\"警情回访\",\"systemNameJson\":{\"zh-Hant\":\"警情回訪系統\",\"zh-CN\":\"警情回访\"},\"systemPath\":\"/command-center/rvs\",\"systemNo\":\"RVS\",\"sort\":5,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"46607354-fa5c-4704-aca3-a790bee5c4ca\",\"systemCode\":\"SYS_CODE_IFDS\",\"systemName\":\"警情专题大屏\",\"systemNameJson\":{\"zh-Hant\":\"智慧大屏\",\"zh-CN\":\"警情专题大屏\"},\"systemPath\":\"/command-center/ifds\",\"systemNo\":\"IFDS\",\"sort\":6,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"cf93f8cb-e1d6-44bc-860e-8c51df744162\",\"systemCode\":\"SYS_CODE_ACS\",\"systemName\":\"联网汇聚\",\"systemNameJson\":{\"zh-CN\":\"联网汇聚\"},\"systemPath\":\"/command-center/acs\",\"systemNo\":\"ACS\",\"sort\":8,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"6726b272-e8b1-4afc-b03f-01b8b73e4745\",\"systemCode\":\"SYS_CODE_FPCS\",\"systemName\":\"人员管控\",\"systemNameJson\":{\"zh-Hant\":\"重點人員管控\",\"zh-CN\":\"人员管控\"},\"systemPath\":\"/command-center/fpcs\",\"systemNo\":\"FPCS\",\"sort\":11,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"9831ca92-65c3-4778-81a0-87dde197c1c4\",\"systemCode\":\"SYS_CODE_GPCS\",\"systemName\":\"群体管控\",\"systemNameJson\":{\"zh-CN\":\"群体管控\"},\"systemPath\":\"/command-center/gpcs\",\"systemNo\":\"GPCS\",\"sort\":12,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"ae777ffd-9d1e-4dc5-a3c6-40c77a18892d\",\"systemCode\":\"SYS_CODE_IFS\",\"systemName\":\"旧-要情指令线索\",\"systemNameJson\":{\"zh-Hant\":\"要情指令系統\",\"zh-CN\":\"旧-要情指令线索\"},\"systemPath\":\"/command-center/ifs\",\"systemNo\":\"IFS\",\"sort\":21,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"8e67709d-58f2-41b4-8ddf-3f2780f163d5\",\"systemCode\":\"SYS_CODE_ADS\",\"systemName\":\"精准勤务\",\"systemNameJson\":{\"zh-Hant\":\"精準勤務\",\"zh-CN\":\"精准勤务\"},\"systemPath\":\"/command-center/ads\",\"systemNo\":\"ADS\",\"sort\":24,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"827d9aa6-fdd4-461e-8401-b9d14ccd5bea\",\"systemCode\":\"SYS_CODE_SCS\",\"systemName\":\"合成作战\",\"systemNameJson\":{\"zh-Hant\":\"合成作戰\",\"zh-CN\":\"合成作战\"},\"systemPath\":\"/command-center/scs\",\"systemNo\":\"SCS\",\"sort\":25,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"43c9d751-1f26-42be-b854-7e9dad52c23f\",\"systemCode\":\"SYS_CODE_TEST\",\"systemName\":\"视觉计算\",\"systemNameJson\":{\"zh-CN\":\"视觉计算\"},\"systemPath\":\"https://152.9.7.93:8320/PDWeb/login\",\"systemNo\":\"SYS_CODE_TEST\",\"sort\":25,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"25087b51-f1b0-4d8b-9b9a-c6fad7d84595\",\"systemCode\":\"SYS_CODE_TESDP\",\"systemName\":\"情指行可视化\",\"systemNameJson\":{\"zh-CN\":\"情指行可视化\"},\"systemPath\":\"https://80.136.0.74/ncqbjwpt/\",\"systemNo\":\"SYS_CODE_TESDP\",\"sort\":26,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"814b2a9a-9cb6-4ea9-a788-393d603cb194\",\"systemCode\":\"SYS_CODE_TESYP\",\"systemName\":\"情报研判\",\"systemNameJson\":{\"zh-CN\":\"情报研判\"},\"systemPath\":\"https://80.136.0.74/ys-app/special-journal/list\",\"systemNo\":\"SYS_CODE_TESYP\",\"sort\":27,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"3fc38f0a-916c-43de-bb94-95d358147b13\",\"systemCode\":\"SYS_CODE_PUBLIC\",\"systemName\":\"PUBLIC\",\"systemNameJson\":{\"zh-Hant\":\"PUBLIC\",\"pt-BR\":\"PUBLIC\",\"en\":\"PUBLIC\",\"zh-CN\":\"PUBLIC\"},\"systemPath\":\"/\",\"systemNo\":\"PUBLIC\",\"sort\":90,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"cd01238c-2d35-49e5-95fa-9cb38e9085cc\",\"systemCode\":\"SYS_CODE_SMP\",\"systemName\":\"系统管理平台\",\"systemNameJson\":{\"zh-Hant\":\"系統管理平台\",\"pt-BR\":\"System Management Platform\",\"en\":\"System Management Platform\",\"zh-CN\":\"系统管理平台\"},\"systemPath\":\"/command-center/smp\",\"systemNo\":\"SMP\",\"sort\":91,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"bfba3085-53dc-4a13-b3f9-3a079a191a7a\",\"systemCode\":\"SYS_CODE_SAP\",\"systemName\":\"安全审计平台\",\"systemNameJson\":{\"zh-Hant\":\"安全審計平台\",\"pt-BR\":\"Security Audit Platform\",\"en\":\"Security Audit Platform\",\"zh-CN\":\"安全审计平台\"},\"systemPath\":\"/command-center/sap\",\"systemNo\":\"SAP\",\"sort\":92,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"0885e16a-d2d0-4c23-b4fc-3279e288e22b\",\"systemCode\":\"SYS_CODE_OKCS\",\"systemName\":\"一号布控\",\"systemNameJson\":{\"zh-CN\":\"一号布控\"},\"systemPath\":\"/command-center/okcs/main\",\"systemNo\":\"OKCS\",\"sort\":93,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"73c37ea3-efaf-44cb-bd2e-23d46da111af\",\"systemCode\":\"SYS_CODE_MPA_SERVER\",\"systemName\":\"APP运维管理\",\"systemNameJson\":{\"zh-CN\":\"APP运维管理\"},\"systemPath\":\"/command-center/mpa\",\"systemNo\":\"MPA_SERVER\",\"sort\":95,\"isShow\":\"1\",\"icon\":null},{\"guid\":null,\"userGuid\":\"4965e6d8-4ceb-4018-8fe2-519083947bad\",\"systemGuid\":\"b292aacd-a213-4484-8f11-670a23a15ac7\",\"systemCode\":\"SYS_CODE_MPA\",\"systemName\":\"移动端权限\",\"systemNameJson\":{\"zh-Hant\":\"移動端權限\",\"zh-CN\":\"移动端权限\"},\"systemPath\":\"无\",\"systemNo\":\"MPA\",\"sort\":96,\"isShow\":\"1\",\"icon\":null}]}";
    }

    @Override
    public String getToken() {
        return "";
    }

    @Override
    public CommandWspzxx getWspzxx() {
        return null;
    }

    @Override
    public String sendCommand(CommandDTO commandDTO, List<byte[]> files) {
        return "";
    }
}
