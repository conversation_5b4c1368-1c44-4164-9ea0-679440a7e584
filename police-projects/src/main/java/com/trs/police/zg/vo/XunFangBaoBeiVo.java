package com.trs.police.zg.vo;

import com.trs.police.zg.anno.ZiGongField;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：值班报备
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/12/11 17:11
 * @since 1.0
 */
@Data
public class XunFangBaoBeiVo extends BaseQingWuVo {

    /**
     * 班次ID
     * zg_tb_qwgl_qwbc视图id字段
     */
    private String bcId;
    private String bcMc;

    @ZiGongField(clazz = Date.class)
    private Date bcKssj;

    @ZiGongField(clazz = Date.class)
    private Date bcJssj;
    private String bcKtsDict;

    @ZiGongField(clazz = Integer.class)
    private Integer bcKts;


    private String xfqyId;

    private String xfqyMc;

    /**
     * 警组ID
     * zg_tb_qwgl_jz视图id字段
     */
    private String jzId;

    private String jzMc;

    private String xfxlfs;

    private String xfwzlx;

    private String xfzzlx;

    private String zbpdmc;

    private String zbpdh;

    private String zbdh;

    @ZiGongField(clazz = Date.class)
    private Date bbSjBbrq;

    @ZiGongField(clazz = Date.class)
    private Date bbSjKssj;

    @ZiGongField(clazz = Date.class)
    private Date bbSjJssj;

    private String bbZt;

    private String shrXm;

    private String shrSfzh;

    private String shrLxfs;

    private String shrSsbm;

    private String shrSsbmdm;

    private String shZt;
}
