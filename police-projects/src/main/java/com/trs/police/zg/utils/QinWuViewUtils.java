package com.trs.police.zg.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.exception.ServiceException;
import com.trs.police.ga.entity.*;
import com.trs.police.ga.mapper.*;
import com.trs.police.zg.entity.*;
import com.trs.police.zg.vo.*;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 广安数据同步 - 视图对接方式
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2025/3/19 15:40
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
public class QinWuViewUtils extends QinWuUtils {

    @Resource
    private GaXunFangLiLiangMapper gaXunFangLiLiangMapper;

    @Resource
    private GaZhiBanBaoBeiMapper gaZhiBanBaoBeiMapper;

    @Resource
    private GaZhiBanBaoBeiRenYuanMapper gaZhiBanBaoBeiRenYuanMapper;

    @Resource
    private GaJianChaZhanEntityMapper gaJianChaZhanEntityMapper;

    @Resource
    private GaKuaiFanDianEntityMapper gaKuaiFanDianEntityMapper;

    @Resource
    private GaBiXunDianEntityMapper gaBiXunDianEntityMapper;

    @Resource
    private GaBiXunXianEntityMapper gaBiXunXianEntityMapper;

    @Resource
    private GaQuanCengEntityMapper gaQuanCengEntityMapper;

    @Resource
    private GaJingWuZhanEntityMapper gaJingWuZhanEntityMapper;

    @Resource
    private GaXunFangQuYuEntityMapper gaXunFangQuYuEntityMapper;

    @Resource
    private GaXunFangBaoBeiMapper gaXunFangBaoBeiMapper;

    @Resource
    private GaJingZuMapper gaJingZuMapper;

    @Resource
    private GaXunFangBaoBeiCheLiangMapper gaXunFangBaoBeiCheLiangMapper;

    @Resource
    private GaXunFangBaoBeiRenYuanMapper gaXunFangBaoBeiRenYuanMapper;

    @Resource
    private GaXunFangBaoBeiQiXieMapper gaXunFangBaoBeiQiXieMapper;

    @Resource
    private GaXunFangBaoBeiZhongDuanMapper gaXunFangBaoBeiZhongDuanMapper;

    @Override
    public String key() {
        return "view";
    }

    /**
     * 时间条件 - 字段替换
     *
     * @param where 参数
     * @return 结果
     */
    private String dealWhere(String where) {
        return where.replaceAll("dep_action_time", "xt_cjsj");
    }

    /**
     * qwglXfbbZd<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/18 18:08
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiZhongDuanVo>> qwglXfbbZd(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaXunFangBaoBeiZhongDuanEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaXunFangBaoBeiZhongDuanMapper.selectCountByCondition(where));
        Page<GaXunFangBaoBeiZhongDuanEntity> pageInfo = gaXunFangBaoBeiZhongDuanMapper.doPageSelect(page, dealWhere(where));
        log.info("勤务管理-巡防报备-终端 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(page.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaXunFangBaoBeiZhongDuanEntity::toVo)
                .collect(Collectors.toList())
        );
    }

    /**
     * qwglZbbb<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 20:11
     */
    @Override
    public Tuple2<Long, List<ZhiBanBaoBeiVo>> qwglZbbb(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaZhiBanBaoBeiEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaZhiBanBaoBeiMapper.selectCount(where));
        Page<GaZhiBanBaoBeiEntity> pageInfo = gaZhiBanBaoBeiMapper.doPageSelect(page, where);
        log.info("勤务管理-值班报备 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageInfo.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaZhiBanBaoBeiEntity::toVo)
                .collect(Collectors.toList())
        );
    }

    /**
     * qwglXfbb<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:14
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiVo>> qwglXfbb(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        // 暂不支持类型 - 报备（不走视图）
        return new Tuple2<>(0L, List.of());
    }

    /**
     * xfbbCl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:30
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiCheLiangVo>> xfbbCl(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        // 警车（巡防报备车辆） 不走视图对接方式
        return new Tuple2<>(0L, List.of());
    }

    /**
     * xfbbCl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:30
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiRenYuanVo>> xfbbRy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        // 警员（巡防报备人员） 不走视图对接方式
        return new Tuple2<>(0L, List.of());
    }

    /**
     * zbbbRy<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 20:11
     */
    @Override
    public Tuple2<Long, List<ZhiBanBaoBeiRenYuanVo>> zbbbRy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaZhiBanBaoBeiRenYuanEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaZhiBanBaoBeiRenYuanMapper.selectCount(where));
        Page<GaZhiBanBaoBeiRenYuanEntity> pageInfo = gaZhiBanBaoBeiRenYuanMapper
                .doPageSelect(page, where);
        log.info("勤务管理-值班报备人员 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageInfo.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaZhiBanBaoBeiRenYuanEntity::toVo)
                .collect(Collectors.toList())
        );
    }

    /**
     * xfll - 巡防力量<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 18:19
     */
    @Override
    public Tuple2<Long, List<XunFangLiLiangVo>> xfll(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaXunFangLiLiangEntity> pageInfo = Page.of(pageNum, pageSize, false);
        pageInfo.setTotal(gaXunFangLiLiangMapper.selectCount(where));
        Page<GaXunFangLiLiangEntity> pageList = gaXunFangLiLiangMapper
                .doPageSelect(pageInfo, where);
        log.info("巡防力量 total: {}, pageList.getRecords().size(): {}",
                pageInfo.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageList.getTotal(), pageList.getRecords().stream()
                .map(GaXunFangLiLiangEntity::toVo)
                .collect(Collectors.toList())
        );
    }

    /**
     * wzSb<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 18:19
     */
    @Override
    public Tuple2<Long, List<SheBeiLiShiWeiZhiVo>> wzSb(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        // 暂不支持类型 - 设备历史位置
        log.info("暂未开通 - 设备历史位置");
        return Tuple.of(0L, List.of());
    }

    /**
     * xfcl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 18:19
     */
    @Override
    public Tuple2<Long, List<XunFangCheLiangVo>> xfcl(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        // 暂不支持类型 - 巡防车辆（不走视图）
        return Tuple.of(0L, List.of());
    }

    /**
     * xfcl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/13 18:19
     */
    @Override
    public Tuple2<Long, List<QinWuGuanLiXunFangQuYuVo>> qwglXfqy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        // 暂不支持类型 - 勤务管理巡防区域 （不走视图）
        return Tuple.of(0L, List.of());
    }

    /**
     * 获取警务站数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgJingWuZhanEntity>> getJwz(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaJingWuZhanEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaJingWuZhanEntityMapper.selectCount(dealWhere(where)));
        Page<GaJingWuZhanEntity> pageInfo = gaJingWuZhanEntityMapper
                .doPageSelect(page, dealWhere(where));
        log.info("警务站 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageInfo.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaJingWuZhanEntity::convert)
                .collect(Collectors.toList())
        );
    }

    /**
     * 获取快反点数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgKuaiFanDianEntity>> getKfd(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaKuaiFanDianEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaKuaiFanDianEntityMapper.selectCount(dealWhere(where)));
        Page<GaKuaiFanDianEntity> pageInfo = gaKuaiFanDianEntityMapper
                .doPageSelect(page, dealWhere(where));
        log.info("快返点 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageInfo.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaKuaiFanDianEntity::convert)
                .collect(Collectors.toList())
        );
    }

    /**
     * 获取必巡点数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgBiXunDianEntity>> getBxd(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaBiXunDianEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaBiXunDianEntityMapper.selectCount(dealWhere(where)));
        Page<GaBiXunDianEntity> pageInfo = gaBiXunDianEntityMapper
                .doPageSelect(page, dealWhere(where));
        log.info("必巡点 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageInfo.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaBiXunDianEntity::convert)
                .collect(Collectors.toList())
        );
    }

    /**
     * 获取必巡线数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgBiXunXianEntity>> getBxx(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaBiXunXianEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaBiXunXianEntityMapper.selectCount(dealWhere(where)));
        Page<GaBiXunXianEntity> pageList = gaBiXunXianEntityMapper
                .doPageSelect(page, dealWhere(where));
        log.info("必巡线 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageList.getRecords() == null
                        ? 0
                        : pageList.getRecords().size()
        );
        if (pageList.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageList.getTotal(), pageList.getRecords()
                .stream()
                .map(GaBiXunXianEntity::convert)
                .collect(Collectors.toList())
        );
    }

    /**
     * 获取巡防区域数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgXunFangQuYuEntity>> getXfqy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaXunFangQuYuEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaXunFangQuYuEntityMapper.selectCount(dealWhere(where)));
        Page<GaXunFangQuYuEntity> pageList = gaXunFangQuYuEntityMapper
                .doPageSelect(page, dealWhere(where));
        log.info("巡防区域 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageList.getRecords() == null
                        ? 0
                        : pageList.getRecords().size()
        );
        if (pageList.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageList.getTotal(), pageList.getRecords().stream()
                .map(GaXunFangQuYuEntity::convert)
                .collect(Collectors.toList())
        );
    }

    /**
     * 检查站
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgJianChaZhanEntity>> getJcz(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaJianChaZhanEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaJianChaZhanEntityMapper.selectCount(dealWhere(where)));
        Page<GaJianChaZhanEntity> pageList = gaJianChaZhanEntityMapper
                .doPageSelect(page, dealWhere(where));
        log.info("检查站 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageList.getRecords() == null
                        ? 0
                        : pageList.getRecords().size()
        );
        if (pageList.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageList.getTotal(), pageList.getRecords().stream()
                .map(GaJianChaZhanEntity::convert)
                .collect(Collectors.toList())
        );
    }

    /**
     * 获取圈层数据
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgQuanCengEntity>> getQc(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaQuanCengEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaQuanCengEntityMapper.selectCount(dealWhere(where)));
        Page<GaQuanCengEntity> pageList = gaQuanCengEntityMapper
                .doPageSelect(page, dealWhere(where));
        log.info("圈层 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageList.getRecords() == null
                        ? 0
                        : pageList.getRecords().size()
        );
        if (pageList.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageList.getTotal(), pageList.getRecords()
                .stream()
                .map(GaQuanCengEntity::convert)
                .collect(Collectors.toList())
        );
    }

    /**
     * 报备（实际为获取巡防报备数据）
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    条件
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public Tuple2<Long, List<ZgBaoBeiEntity>> getBb(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaXunFangBaoBeiEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaXunFangBaoBeiMapper.selectCount(dealWhere(where)));
        Page<GaXunFangBaoBeiEntity> pageList = gaXunFangBaoBeiMapper.doPageSelect(page, dealWhere(where));
        log.info("巡防报备 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageList.getRecords() == null ? 0 : pageList.getRecords().size()
        );
        if (pageList.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageList.getTotal(), pageList.getRecords()
                .stream()
                .map(GaXunFangBaoBeiEntity::convert)
                .collect(Collectors.toList())
        );
    }

    /**
     * qwglXfbbCl<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiCheLiangVo>> qwglXfbbCl(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaXunFangBaoBeiCheLiangEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaXunFangBaoBeiCheLiangMapper.selectCountByCondition(where));
        Page<GaXunFangBaoBeiCheLiangEntity> pageInfo = gaXunFangBaoBeiCheLiangMapper.doPageSelect(page, where);
        log.info("勤务管理-巡防报备-车辆 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageInfo.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaXunFangBaoBeiCheLiangEntity::toVo)
                .collect(Collectors.toList())
        );
    }

    /**
     * qwglXfbbRy<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiRenYuanVo>> qwglXfbbRy(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaXunFangBaoBeiRenYuanEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaXunFangBaoBeiRenYuanMapper.selectCountByCondition(where));
        Page<GaXunFangBaoBeiRenYuanEntity> pageInfo = gaXunFangBaoBeiRenYuanMapper.doPageSelect(page, where);
        log.info("勤务管理-巡防报备-人员 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageInfo.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaXunFangBaoBeiRenYuanEntity::toVo)
                .collect(Collectors.toList())
        );
    }

    /**
     * qwglXfbbQx<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public Tuple2<Long, List<XunFangBaoBeiQiXieVo>> qwglXfbbQx(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        Page<GaXunFangBaoBeiQiXieEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaXunFangBaoBeiQiXieMapper.selectCountByCondition(where));
        Page<GaXunFangBaoBeiQiXieEntity> pageInfo = gaXunFangBaoBeiQiXieMapper.doPageSelect(page, where);
        log.info("勤务管理-巡防报备-器械 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageInfo.getRecords() == null
                        ? 0
                        : pageInfo.getRecords().size()
        );
        if (pageInfo.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageInfo.getTotal(), pageInfo.getRecords()
                .stream()
                .map(GaXunFangBaoBeiQiXieEntity::toVo)
                .collect(Collectors.toList())
        );
    }

    /**
     * qwglJingZu<BR>
     *
     * @param pageNum  参数
     * @param pageSize 参数
     * @param where    参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public Tuple2<Long, List<JingZuVo>> qwglJingZu(Integer pageNum, Integer pageSize, String where) throws ServiceException {
        // 勤务管理警组
        Page<GaJingZuEntity> page = Page.of(pageNum, pageSize, false);
        page.setTotal(gaJingZuMapper.selectCountByCondition(where));
        Page<GaJingZuEntity> pageList = gaJingZuMapper.doPageSelect(page, where);
        log.info("勤务管理-警组 total: {}, pageList.getRecords().size(): {}",
                page.getTotal(),
                pageList.getRecords() == null ? 0 : pageList.getRecords().size()
        );
        if (pageList.getRecords().isEmpty()) {
            return Tuple.of(0L, List.of());
        }
        return Tuple.of(pageList.getTotal(), pageList.getRecords().stream()
                .map(GaJingZuEntity::toVo)
                .collect(Collectors.toList())
        );
    }
}
