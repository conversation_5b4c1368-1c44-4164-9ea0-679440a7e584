package com.trs.police.zg.service.impl;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.bigscreen.domain.entity.BigScreenDutyUserEntity;
import com.trs.police.bigscreen.mapper.BigScreenDutyUserMapper;
import com.trs.police.bigscreen.service.BaseDutyUserSyncService;
import com.trs.police.bigscreen.vo.DutyUserSyncVo;
import com.trs.police.common.core.constant.enums.PoliceKindEnum;
import com.trs.police.common.core.entity.task.SyncTaskEntity;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.zg.utils.QinWuUtils;
import com.trs.police.zg.vo.BaseQingWuVo;
import com.trs.police.zg.vo.XunFangLiLiangVo;
import com.trs.police.zg.vo.ZhiBanBaoBeiRenYuanVo;
import com.trs.police.zg.vo.ZhiBanBaoBeiVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.trs.common.utils.TimeUtils.dateToString;
import static com.trs.common.utils.TimeUtils.stringToDate;
import static com.trs.police.zg.utils.QinWuUtils.convertLevel;
import static com.trs.police.zg.utils.QinWuUtils.convertNature;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/11 17:47
 * @since 1.0
 */
@Service
@Slf4j
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "zg")
public class ZgDutyUserSyncServiceNew extends BaseDutyUserSyncService {

    public ZgDutyUserSyncServiceNew(
            BigScreenDutyUserMapper bigScreenDutyUserMapper,
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(bigScreenDutyUserMapper, syncTaskMapper, redisTemplate);
    }

    /**
     * isEnable<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/12 15:35
     */
    @Override
    public Boolean isSyncEnable() {
        long timeout = getRedisTimeout();
        if (timeout > 0) {
            return Boolean.FALSE.equals(getRedisTemplate().hasKey(makeRedisKey()));
        }
        return Boolean.TRUE;
    }

    /**
     * makeRedisKey<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/12 16:06
     */
    protected String makeRedisKey() {
        return String.format(
                "BaseDutyUserSyncService:%s:%s:%s",
                getClass().getSimpleName(),
                key(),
                TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD)
        );
    }

    /**
     * getRedisTimeout<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/12 17:23
     */
    protected long getRedisTimeout() {
        return BeanFactoryHolder
                .getEnv()
                .getProperty("com.trs.bigscreen.sync.duty.zg.redis.timeout", Long.class, 120L);
    }

    /**
     * doOnFinish<BR>
     *
     * @param task 任务
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/12 15:36
     */
    @Override
    public void doOnFinish(SyncTaskEntity task) {
        super.doOnFinish(task);
        long timeout = getRedisTimeout();
        if (timeout > 0) {
            getRedisTemplate().opsForValue()
                    .set(makeRedisKey(), TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS), timeout, TimeUnit.MINUTES);
        }
    }

    /**
     * findInData<BR>
     *
     * @param startTime 参数
     * @param endTime   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:05
     */
    @Override
    public Tuple2<List<DutyUserSyncVo>, String> findInData(String startTime, String endTime) {
        final Date now = new Date();
        final var dutyTime = dateToString(now, TimeUtils.YYYYMMDD);
        final var time = dateToString(now, TimeUtils.YYYYMMDD_HHMMSS);
        final var sql = String.format(" xt_scbz = '0' and bb_sj_kssj <= '%s' and bb_sj_jssj >= '%s' ", time, time);
        List<DutyUserSyncVo> list = Try.of(() -> {
            var map = findBaoBei(sql)
                    .stream()
                    .filter(it -> Objects.nonNull(it.getBcKssj())
                            && Objects.nonNull(it.getBcJssj())
                            && StringUtils.isNotEmpty(it.getId())
                    ).peek(it -> {
                        it.setBcKssj(stringToDate(String.format("%s %s", dutyTime, dateToString(it.getBcKssj(), TimeUtils.HHMMSS))));
                        it.setBcJssj(stringToDate(String.format("%s %s", dutyTime, dateToString(it.getBcJssj(), TimeUtils.HHMMSS))));
                        log.info("[{}]过滤后数据:{}", sql, it);
                    }).filter(it -> it.getBcKssj().before(now) && it.getBcJssj().after(now))
                    .collect(Collectors.groupingBy(BaseQingWuVo::getId));
            if (map.isEmpty()) {
                log.info("[{}]过滤后没有数据", sql);
                return new ArrayList<DutyUserSyncVo>();
            }
            String ids = map.keySet()
                    .stream()
                    .map(it -> String.format("'%s'", it))
                    .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA));
            String zbbbRySql = String.format(" xt_sjzt = '1' and xt_scbz = '0' and zbbb_id in (%s) ", ids);
            return findZhiBanBaoBeiRenYuan(zbbbRySql).stream()
                    .filter(it -> map.containsKey(it.getZbbbId()))
                    .map(it -> {
                        final ZhiBanBaoBeiVo zbbb = map.get(it.getZbbbId()).get(0);
                        final XunFangLiLiangVo xfllOne = qinWuUtils.xfllOne(it.getXfllId());
                        return BigScreenDutyUserEntity.builder()
                                .crTime(now)
                                .updateTime(now)
                                .dutyTime(stringToDate(dutyTime))
                                .unitName(zbbb.getSsbm())
                                .districtCode(zbbb.getSsbmdm().substring(0, 6))
                                .policeKind(Objects.equals(it.getRyJzlx(), "02") ? PoliceKindEnum.JIAOJING.getCode() : 0)
                                .name(it.getRyXm())
                                .dh(zbbb.getZbdh())
                                .jh(Objects.nonNull(xfllOne) ? xfllOne.getJh() : "")
                                .nature(convertNature(it.getRyZbbbzw()))
                                .level(convertLevel(it.getRyZbbbzw()))
                                .build();
                    }).collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDistrictCode))
                    .entrySet()
                    .stream()
                    .map(it -> {
                        DutyUserSyncVo vo = new DutyUserSyncVo();
                        vo.setDistrictCode(it.getKey());
                        vo.setDutyTime(dutyTime);
                        vo.setUsers(it.getValue());
                        return vo;
                    }).collect(Collectors.toList());
        }).onFailure(e -> log.error("[{}]同步异常", desc(), e)).getOrElse(new ArrayList<>());
        return Tuple.of(list, sql);
    }

    /**
     * findBaoBei<BR>
     *
     * @param sql 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:21
     */
    private List<ZhiBanBaoBeiVo> findBaoBei(String sql) throws ServiceException {
        int pageNum = 1;
        Tuple2<Long, List<ZhiBanBaoBeiVo>> t = qinWuUtils.qwglZbbb(pageNum, QinWuUtils.PAGE_SIZE, sql);
        final int size = t._1.intValue();
        List<ZhiBanBaoBeiVo> d = new ArrayList<>(size);
        d.addAll(t._2);
        while (size > QinWuUtils.PAGE_SIZE * pageNum) {
            pageNum += 1;
            d.addAll(qinWuUtils.qwglZbbb(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
        }
        log.info("[{}]共查询到{}条数据", sql, d.size());
        return d;
    }

    /**
     * findZhiBanBaoBeiRenYuan<BR>
     *
     * @param zbbbRySql 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:20
     */
    private List<ZhiBanBaoBeiRenYuanVo> findZhiBanBaoBeiRenYuan(String zbbbRySql) throws ServiceException {
        int pageNum = 1;
        Tuple2<Long, List<ZhiBanBaoBeiRenYuanVo>> t = qinWuUtils.zbbbRy(pageNum, QinWuUtils.PAGE_SIZE, zbbbRySql);
        final int size = t._1.intValue();
        List<ZhiBanBaoBeiRenYuanVo> ry = new ArrayList<>(size);
        ry.addAll(t._2);
        while (size > QinWuUtils.PAGE_SIZE * pageNum) {
            pageNum += 1;
            ry.addAll(qinWuUtils.zbbbRy(pageNum, QinWuUtils.PAGE_SIZE, zbbbRySql)._2);
        }
        return ry;
    }

    @Override
    public String key() {
        return "ZgDutyUserNew";
    }

    @Override
    public String desc() {
        return "自贡值班人员同步";
    }
}
