package com.trs.police.zg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.trs.police.zg.anno.ZiGongField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName BaseZgJingWuSourceEntity
 * @Description 基础对象类
 * <AUTHOR>
 * @Date 2024/12/25 15:48
 **/
@Data
public class BaseZgJingWuSourceEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long dataId;

    private Date crTime;

    private Date updateTime;

    private String ssbmid;

    private String ssbm;

    private String ssbmdm;

    private String ssxgajid;

    private String ssxgaj;

    private String ssxgajdm;

    private String sssgajid;

    private String sssgaj;

    private String sssgajdm;

    /**
     * 数据来源
     * 1：PC
     * 2：手机端
     */
    private String xtSjly;

    /**
     * 数据状态
     * 0：注销
     * 1：正常
     * -1：封存
     */
    private String xtSjzt;

    /**
     * 删除标识
     * 0：未删除
     * 1：已删除
     */
    private String xtScbz;

    private String xtCjip;

    @ZiGongField(clazz = Date.class)
    private Date xtCjsj;

    private String xtCjrId;

    private String xtCjr;

    private String xtCjbmdm;

    private String xtCjbmmc;

    private String xtZhgxip;

    @ZiGongField(clazz = Date.class)
    private Date xtZhgxsj;

    private String xtZhgxr;

    private String xtZhgxrid;

    private String xtZhgxbmdm;

    private String xtZhgxbm;

    private String bz;

    @ZiGongField(clazz = Date.class)
    private Date depActionTime;

}
