package com.trs.police.bigscreen.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.constant.TimeSearchFlag;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.OrderDTO;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.bigscreen.constants.DutyPostConstants;
import com.trs.police.bigscreen.convert.EntityConvertToVo;
import com.trs.police.bigscreen.domain.dto.*;
import com.trs.police.bigscreen.domain.entity.BigScreenAreaInfoEntity;
import com.trs.police.bigscreen.domain.entity.BigScreenDutyUserEntity;
import com.trs.police.bigscreen.domain.entity.BigScreenJingYuanEntity;
import com.trs.police.bigscreen.domain.vo.*;
import com.trs.police.bigscreen.mapper.BigScreenAreaInfoMapper;
import com.trs.police.bigscreen.mapper.BigScreenDeviceMapper;
import com.trs.police.bigscreen.mgr.BigScreenDutyUserBufferMgr;
import com.trs.police.bigscreen.mgr.BigScreenDutyUserMgr;
import com.trs.police.bigscreen.mgr.JingWuZiYuanMgr;
import com.trs.police.bigscreen.mgr.PinDataMgr;
import com.trs.police.bigscreen.service.IBigScreenService;
import com.trs.police.bigscreen.utils.WordExportUtil;
import com.trs.police.bigscreen.vo.*;
import com.trs.police.common.core.constant.enums.PostKindEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.BigScreenWorkLogEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.mapper.BigScreenWorkLogMapper;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.OkHttpUtil;
import com.trs.police.common.core.vo.*;
import com.trs.police.common.core.vo.fight.JingYuanVO;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.permission.UserVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.device.constant.DeviceTypeConstant;
import com.trs.police.device.domain.dto.DeviceSearchDTO;
import com.trs.police.device.domain.entity.BigScreenDeviceEntity;
import com.trs.police.device.domain.vo.DeviceVO;
import com.trs.police.zg.Enum.JingWuSourceEnum;
import com.trs.police.zg.service.BaseZgJingWuSourceService;
import com.trs.police.zg.vo.BaseJingwuSourceVo;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.builder.util.KeyMgrFactory;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotNull;
import static com.trs.common.utils.AreaCodeUtil.getAreaShortCode;
import static com.trs.common.utils.AreaCodeUtil.spreadingAreaCode;
import static com.trs.common.utils.TimeUtils.YYYYMMDD_HHMMSS;
import static com.trs.police.bigscreen.constants.DutyPostConstants.ZHI_BAN_LING_DAO;
import static com.trs.police.bigscreen.constants.DutyPostConstants.ZHI_HUI_ZHANG;
import static com.trs.police.common.core.utils.ListUtil.distinctByKey;

/**
 * 相关实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-07 10:50:25
 */
@Slf4j
@Service
@AllArgsConstructor
public class BigScreenServiceImpl implements IBigScreenService {

    private final PinDataMgr pinDataMgr;

    private final BigScreenDutyUserMgr bigScreenDutyUserMgr;

    private final PermissionService permissionService;

    private final BigScreenWorkLogMapper bigScreenWorkLogMapper;

    private final JingWuZiYuanMgr jingWuZiYuanMgr;

    private final BigScreenAreaInfoMapper bigScreenAreaInfoMapper;

    private final BigScreenDeviceMapper bigScreenDeviceMapper;

    private final BigScreenDutyUserBufferMgr bigScreenDutyUserBufferMgr;

    @Override
    public List<DutyUserResultVo> dutyUsers(DutyUserDTO dto) throws ServiceException {
        dto.isValid();
        return bigScreenDutyUserMgr.dutyUsers(AuthHelper.getNotNullUser(), dto);
    }

    /**
     * modifyDutyUsers<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/14 17:56
     */
    @Override
    public Report<String> modifyDutyUsers(ModifyDutyUsersDTO dto) throws ServiceException {
        dto.isValid();
        return bigScreenDutyUserMgr.modifyDutyUsers(dto);
    }

    /**
     * addDutyUser<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/14 20:17
     */
    @Override
    public Report<String> addDutyUser(ModifyDutyUserDTO dto) throws ServiceException {
        dto.isValid();
        return bigScreenDutyUserMgr.addDutyUser(dto);
    }

    /**
     * pinData<BR>
     *
     * @param pin 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:11
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Report<String> pinData(List<PinDataDTO> pin) throws ServiceException {
        checkArgument(CollectionUtils.isNotEmpty(pin), new ParamInvalidException("操作数据不能为空"));
        final CurrentUser currentUser = AuthHelper.getNotNullUser();
        for (PinDataDTO dto : pin) {
            dto.isValid();
            pinDataMgr.pinData(currentUser, dto);
        }
        return new Report<>("上大屏的内容", "成功操作");
    }

    /**
     * unPinData<BR>
     *
     * @param pin 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:48
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Report<String> unPinData(List<UnPinDataDTO> pin) throws ServiceException {
        checkArgument(CollectionUtils.isNotEmpty(pin), new ParamInvalidException("操作数据不能为空"));
        final CurrentUser currentUser = AuthHelper.getNotNullUser();
        for (UnPinDataDTO dto : pin) {
            dto.isValid();
            pinDataMgr.unPinData(currentUser, dto);
        }
        return new Report<>("上大屏的内容", "成功取消");
    }

    /**
     * finishPinData<BR>
     *
     * @param ids 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:55
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Report<String> finishPinData(String ids) throws ServiceException {
        PreConditionCheck.checkNotEmpty(ids, new ParamInvalidException("待结束的数据不能为空"));
        final CurrentUser currentUser = AuthHelper.getNotNullUser();
        var set = Arrays.stream(ids.split(StringUtils.SEPARATOR_COMMA))
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        for (Long id : set) {
            pinDataMgr.finishPinData(currentUser, id);
        }
        return new Report<>("上大屏的内容", "成功结束");
    }

    /**
     * pinDataList<BR>
     *
     * @param request 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 17:17
     */
    @Override
    public RestfulResultsV2<PinDataVo> pinDataList(ListParamsRequest request) throws ServiceException {
        final CurrentUser user = AuthHelper.getNotNullUser();
        return pinDataMgr.pinDataList(user, request);
    }

    @Override
    public Report<String> addWorkLog(List<AddWorkLogDTO> dto) throws ServiceException {
        String title = "";
        for (AddWorkLogDTO d : dto) {
            d.isValid();
            if (Objects.nonNull(d.getId()) && d.getId() > 0) {
                BigScreenWorkLogEntity entity = bigScreenWorkLogMapper.selectById(d.getId());
                if (Objects.nonNull(entity)) {
                    title = "修改日志";
                    doUpdate(d, entity);
                } else {
                    title = "新增日志";
                    doAdd(d);
                }
            } else {
                title = "新增日志";
                doAdd(d);
            }
        }
        return new Report<>(title, "成功", Report.RESULT.SUCCESS);
    }

    private void doAdd(AddWorkLogDTO dto) throws ServiceException {
        BigScreenWorkLogEntity newEntity = new BigScreenWorkLogEntity();
        CurrentUser currentUser = getCurrentUser();
        newEntity.setCreateUserId(currentUser.getId());
        newEntity.setCreateDeptId(currentUser.getDeptId());
        newEntity.setCreateTime(LocalDateTime.now());
        newEntity.setDistrictCode(getCurrentUser().getDept().getDistrictCode());
        newEntity.setObjId(dto.getObjId());
        newEntity.setObjType(dto.getObjType());
        newEntity.setContent(dto.getContent());
        bigScreenWorkLogMapper.insert(newEntity);
    }

    private CurrentUser getCurrentUser() throws ServiceException {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new ServiceException("无法获取当前登录用户");
        }
        return currentUser;
    }

    private void doUpdate(AddWorkLogDTO dto, BigScreenWorkLogEntity entity) throws ServiceException {
        CurrentUser currentUser = getCurrentUser();
        entity.setUpdateUserId(currentUser.getId());
        entity.setUpdateDeptId(currentUser.getDeptId());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setObjId(dto.getObjId());
        entity.setObjType(dto.getObjType());
        entity.setContent(dto.getContent());
        bigScreenWorkLogMapper.updateById(entity);
    }

    @Override
    public Page<WorkListVO> workLogList(ListParamsRequest paramsRequest) throws ServiceException {
        PageParams pageParams = paramsRequest.getPageParams();
        List<KeyValueTypeVO> filterParams = paramsRequest.getFilterParams();
        Map<String, Object> map = new HashMap<>();
        String startTime = "startTime";
        String endTime = "endTime";
        for (KeyValueTypeVO keyValueTypeVO : filterParams) {
            if (keyValueTypeVO.getType().equals("string")) {
                map.put(keyValueTypeVO.getKey(), keyValueTypeVO.getValue());
            }
            if (keyValueTypeVO.getType().equals("timeParams") && keyValueTypeVO.getKey().equals("cr_time")) {
                TimeParams value = (TimeParams) keyValueTypeVO.getValue();
                String range = value.getRange();
                LocalDate currentDate = LocalDate.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                switch (range) {
                    case "2":
                        LocalDate firstDayOfWeek = currentDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                        map.put(startTime, firstDayOfWeek.format(formatter) + " 00:00:00");
                        LocalDate endDayOfWeek = firstDayOfWeek.plusDays(DayOfWeek.SUNDAY.getValue() - 1);
                        map.put(endTime, endDayOfWeek.format(formatter) + " 23:59:59");
                        break;
                    case "3":
                        LocalDate firstDayOfMonth = LocalDate.of(currentDate.getYear(), currentDate.getMonthValue(), 1);
                        map.put(startTime, firstDayOfMonth.format(formatter) + " 00:00:00");
                        LocalDate lastDayOfMonth = firstDayOfMonth.plusMonths(1).minusDays(1);
                        map.put(endTime, lastDayOfMonth.format(formatter) + " 23:59:59");
                        break;
                    case "99":
                        map.put(startTime, value.getBeginTime());
                        map.put(endTime, value.getEndTime());
                        break;
                    default:
                        throw new ServiceException("参数错误");
                }
            }
        }
        SearchParams searchParams = paramsRequest.getSearchParams();
        map.put("districtCode", getCurrentUser().getDept().getDistrictCode());
        SortParams sortParams = paramsRequest.getSortParams();
        Page<BigScreenWorkLogEntity> page = new Page<>(pageParams.getPageNumber(), pageParams.getPageSize());
        Page<BigScreenWorkLogEntity> bigScreenWorkLogEntityPage = bigScreenWorkLogMapper.selectPage(page,
                new QueryWrapper<BigScreenWorkLogEntity>()
                        .ge(map.containsKey(startTime), "create_time", map.get(startTime))
                        .le(map.containsKey(endTime), "create_time", map.get(endTime))
                        .eq(map.containsKey("obj_id"), "obj_id", map.get("obj_id"))
                        .eq(map.containsKey("obj_type"), "obj_type", map.get("obj_type"))
                        .eq(map.containsKey("districtCode"), "district_code", map.get("districtCode"))
                        .eq(StringUtils.isNotEmpty(searchParams.getSearchField()) && StringUtils.isNotEmpty(searchParams.getSearchValue()), searchParams.getSearchField(), searchParams.getSearchValue())
                        .orderBy(Objects.nonNull(sortParams.getSortField()) && Objects.nonNull(sortParams.getSortDirection()), sortParams.isAsc(), sortParams.getSortField())
        );
        Page<WorkListVO> result = new Page<>();
        BeanUtils.copyProperties(bigScreenWorkLogEntityPage, result);
        result.setRecords(bigScreenWorkLogEntityPage.getRecords().stream().map(bigScreenWorkLogEntity -> {
            WorkListVO workListVO = new WorkListVO();
            BeanUtils.copyProperties(bigScreenWorkLogEntity, workListVO);
            if (Objects.nonNull(bigScreenWorkLogEntity.getCreateDeptId())) {
                DeptDto crDept = permissionService.getDeptById(bigScreenWorkLogEntity.getCreateDeptId());
                workListVO.setCrDept(crDept.toSimpleVO());
            }
            if (Objects.nonNull(bigScreenWorkLogEntity.getUpdateDeptId())) {
                DeptDto updateDept = permissionService.getDeptById(bigScreenWorkLogEntity.getUpdateDeptId());
                workListVO.setUpdateDept(updateDept.toSimpleVO());
            }
            if (Objects.nonNull(bigScreenWorkLogEntity.getCreateUserId())) {
                UserDto crUser = permissionService.getUserById(bigScreenWorkLogEntity.getCreateUserId());
                workListVO.setCrUserTrueName(crUser.getRealName());
            }
            if (Objects.nonNull(bigScreenWorkLogEntity.getUpdateUserId())) {
                UserDto updateUser = permissionService.getUserById(bigScreenWorkLogEntity.getUpdateUserId());
                workListVO.setUpdateUserTrueName(updateUser.getRealName());
            }
            return workListVO;
        }).collect(Collectors.toList()));
        return result;
    }

    @Override
    public Tuple2<String, byte[]> exportWorkLog(ExportWordDTO dto) throws ServiceException {
        List<WorkListVO> records = workLogList(dto).getRecords();
        WordTemplateInfoVO templateInfo = dto.getOtherParams();
        List<WordTemplateListVO> templateList = new ArrayList<>(records.size());
        setWordTemplateInfo(templateInfo);
        for (WorkListVO workListVO : records) {
            WordTemplateListVO vo = new WordTemplateListVO();
            vo.setActionName(workListVO.getCrUserTrueName());
            vo.setActionTime(workListVO.getCreateTime().format(DateTimeFormatter.ofPattern(YYYYMMDD_HHMMSS)));
            vo.setActionContent(workListVO.getContent());
            templateList.add(vo);
        }
        Object o = dto.getFilterParams().stream().filter(obj -> "obj_type".equals(obj.getKey())).map(map -> map.getValue()).findFirst().get();
        String value = o.toString();
        String doc;
        switch (value) {
            case "case":
            case "yaoqing":
            case "zhiling":
            case "xiansuo":
            case "event":
                doc = WordExportUtil.buildImportDoc(templateInfo, templateList);
                break;
            case "person":
                doc = WordExportUtil.buildRiskDoc(templateInfo, templateList);
                break;
            default:
                throw new ServiceException("不支持的导出格式");

        }
        byte[] bytes = WordExportUtil.file2bytes(doc);
        return new Tuple2<>(doc, bytes);
    }

    /**
     * jingYuanList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/27 16:23
     */
    @Override
    public RestfulResultsV2<JingYuanVo> jingYuanList(JingYuanSearchDTO dto) throws ServiceException {
        dto.isValid();
        if (StringUtils.isEmpty(dto.getDistrictCode())) {
            Optional.ofNullable(AuthHelper.getCurrentUser())
                    .ifPresent(u -> dto.setDistrictCode(u.getDept().getDistrictCode()));
        }
        var page = jingWuZiYuanMgr.findJingYuanList(dto);
        List<String> cphm = new ArrayList<>(0);
        List<String> zsz = new ArrayList<>(0);
        List<String> jjybh = new ArrayList<>(0);
        page.getRecords().forEach(it -> {
            if (StringUtils.isNotEmpty(it.getCzcl())) {
                cphm.add(it.getCzcl());
            }
            if (StringUtils.isNotEmpty(it.getZsz())) {
                zsz.add(it.getZsz());
            }
            if (StringUtils.isNotEmpty(it.getJh())) {
                jjybh.add(it.getJh());
            }
        });
        var tcry = jingWuZiYuanMgr.findTcry(cphm);
        var tzry = jingWuZiYuanMgr.findTzry(zsz);
        var cjzt = jingWuZiYuanMgr.findCjzt(jjybh);
        return RestfulResultsV2.ok(
                page.getRecords()
                        .stream()
                        .map(EntityConvertToVo.INSTANCE::entityToVo)
                        .peek(it -> {
                            var tc = StringUtils.getList(tcry.get(it.getCzcl()), true);
                            if (tc.contains(StringUtils.showEmpty(it.getXm()))) {
                                tc.remove(StringUtils.showEmpty(it.getXm()));
                            }
                            it.setTcry(String.join(StringUtils.SEPARATOR_COMMA, tc));
                            var tz = StringUtils.getList(tzry.get(it.getZsz()), true);
                            if (tz.contains(StringUtils.showEmpty(it.getXm()))) {
                                tz.remove(StringUtils.showEmpty(it.getXm()));
                            }
                            it.setTzry(String.join(StringUtils.SEPARATOR_COMMA, tz));
                            it.setCjzt(Integer.valueOf(Try.of(() -> cjzt.get(it.getJh())).getOrElse("0")));
                            it.setRhyxsb(JsonUtil.parseArray(it.getRhyxsbStr(), RongHeSheBeiVo.class));
                        }).collect(Collectors.toList())
        ).addPageSize(dto.getPageSize()).addPageNum(dto.getPageNum()).addTotalCount(page.getTotal());
    }

    /**
     * jingYuanCount<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/27 18:16
     */
    @Override
    public List<NameNumTreeVo> jingYuanCount(JingYuanSearchDTO dto) throws ServiceException {
        dto.isValid();
        if (StringUtils.isEmpty(dto.getDistrictCode())) {
            Optional.ofNullable(AuthHelper.getCurrentUser())
                    .ifPresent(u -> dto.setDistrictCode(u.getDept().getDistrictCode()));
        }
        return jingWuZiYuanMgr.jingYuanCount(dto);
    }

    /**
     * jingCheList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/27 16:23
     */
    @Override
    public RestfulResultsV2<JingCheVo> jingCheList(JingCheSearchDTO dto) throws ServiceException {
        dto.isValid();
        if (StringUtils.isEmpty(dto.getDistrictCode())) {
            Optional.ofNullable(AuthHelper.getCurrentUser())
                    .ifPresent(u -> dto.setDistrictCode(u.getDept().getDistrictCode()));
        }
        var page = jingWuZiYuanMgr.findJingCheList(dto);
        List<String> cphm = new ArrayList<>(0);
        List<String> zsz = new ArrayList<>(0);
        page.getRecords().forEach(it -> {
            if (StringUtils.isNotEmpty(it.getCphm())) {
                cphm.add(it.getCphm());
            }
            if (StringUtils.isNotEmpty(it.getZsz())) {
                zsz.add(it.getZsz());
            }
        });
        var tcry = jingWuZiYuanMgr.findTcry(cphm);
        var tzry = jingWuZiYuanMgr.findTzry(zsz);
        return RestfulResultsV2.ok(
                page.getRecords()
                        .stream()
                        .map(EntityConvertToVo.INSTANCE::entityToVo)
                        .peek(it -> {
                            it.setTcry(String.join(
                                    StringUtils.SEPARATOR_COMMA,
                                    StringUtils.getList(tcry.get(it.getCphm()), true)
                            ));
                            it.setTzry(String.join(
                                    StringUtils.SEPARATOR_COMMA,
                                    StringUtils.getList(tzry.get(it.getZsz()), true)
                            ));
                            it.setRhyxsb(JsonUtil.parseArray(it.getRhyxsbStr(), RongHeSheBeiVo.class));
                        }).collect(Collectors.toList())
        ).addPageSize(dto.getPageSize()).addPageNum(dto.getPageNum()).addTotalCount(page.getTotal());
    }

    /**
     * jingCheCount<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/27 18:16
     */
    @Override
    public List<NameNumTreeVo> jingCheCount(JingCheSearchDTO dto) throws ServiceException {
        dto.isValid();
        if (StringUtils.isEmpty(dto.getDistrictCode())) {
            Optional.ofNullable(AuthHelper.getCurrentUser())
                    .ifPresent(u -> dto.setDistrictCode(u.getDept().getDistrictCode()));
        }
        return jingWuZiYuanMgr.jingCheCount(dto);
    }

    @Override
    public List<JingYuanVO> jingYuanListByArea(String geometries) throws ServiceException {
        List<BigScreenJingYuanEntity> list = jingWuZiYuanMgr.jingYuanListByArea(geometries);
        return list.stream()
                .map(en -> {
                    JingYuanVO vo = new JingYuanVO();
                    vo.setBh(en.getJh());
                    vo.setCjzt(en.getCjzt());
                    vo.setBbzt(en.getBbzt());
                    if (Objects.nonNull(en.getUpdateTime())) {
                        vo.setUpdateTime(TimeUtils.dateToString(en.getUpdateTime(), YYYYMMDD_HHMMSS));
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public RestfulResultsV2<AreaInfoVO> areaList(AreaSearchDTO dto) {
        Page<BigScreenAreaInfoEntity> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        if (StringUtils.isEmpty(dto.getDistrictCode())) {
            Optional.ofNullable(AuthHelper.getCurrentUser())
                    .ifPresent(u -> dto.setDistrictCode(u.getDept().getDistrictCode()));
        }
        String districtCode = Optional.ofNullable(dto.getDistrictCode())
                .map(it -> Try.of(() -> getAreaShortCode(spreadingAreaCode(it, false))))
                .filter(Try::isSuccess)
                .map(Try::get)
                .filter(StringUtils::isNotEmpty)
                .orElse(null);
        List<String> types = null;
        if (StringUtils.isNotEmpty(dto.getTypeName())) {
            types = Stream.of(dto.getTypeName().split(",")).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        }
        IPage<BigScreenAreaInfoEntity> iPage = bigScreenAreaInfoMapper.selectPage(page,
                new QueryWrapper<BigScreenAreaInfoEntity>().lambda()
                        .like(StringUtils.isNotEmpty(districtCode), BigScreenAreaInfoEntity::getDistrictCode, districtCode)
                        .in(CollectionUtils.isNotEmpty(types), BigScreenAreaInfoEntity::getTypeValue, types)
                        .and(StringUtils.isNotEmpty(dto.getSearchValue()), it ->
                                it.like(BigScreenAreaInfoEntity::getName, dto.getSearchValue()).or()
                                        .like(BigScreenAreaInfoEntity::getDeptName, dto.getSearchValue()).or()
                                        .like(BigScreenAreaInfoEntity::getPortrayModeName, dto.getSearchValue())
                        )
        );
        return RestfulResultsV2.ok(iPage.getRecords().stream().map(obj -> {
                    AreaInfoVO vo = new AreaInfoVO();
                    vo.setName(obj.getName());
                    vo.setDeptName(obj.getDeptName());
                    vo.setTypeName(obj.getTypeValue());
                    vo.setPortrayModeName(obj.getPortrayModeName());
                    vo.setPortrayModeData(obj.getPortrayModeData());
                    return vo;
                }).collect(Collectors.toList()))
                .addPageSize(dto.getPageSize())
                .addPageNum(dto.getPageNum())
                .addTotalCount(page.getTotal());
    }

    @Override
    public List<NameNumTreeVo> areaCount(AreaSearchDTO dto) {
        if (StringUtils.isEmpty(dto.getDistrictCode())) {
            Optional.ofNullable(AuthHelper.getCurrentUser())
                    .ifPresent(u -> dto.setDistrictCode(u.getDept().getDistrictCode()));
        }
        String districtCode = Optional.ofNullable(dto.getDistrictCode())
                .map(it -> Try.of(() -> getAreaShortCode(spreadingAreaCode(it, false))))
                .filter(Try::isSuccess)
                .map(Try::get)
                .filter(StringUtils::isNotEmpty)
                .orElse(null);
        List<NameNumTreeVo> result = new ArrayList<>();
        NameNumTreeVo vo = new NameNumTreeVo();
        vo.setName("全部区域");
        result.add(vo);
        List<NameNumTreeVo> nameNumTreeVos = bigScreenAreaInfoMapper.doCount(dto, districtCode);
        if (CollectionUtils.isNotEmpty(nameNumTreeVos)) {
            vo.setNum(nameNumTreeVos.stream().mapToLong(NameNumTreeVo::getNum).sum());
            result.addAll(nameNumTreeVos);
        } else {
            vo.setNum(0L);
        }
        return result;
    }

    private void setWordTemplateInfo(WordTemplateInfoVO templateInfo) throws ServiceException {
        CurrentUser currentUser = getCurrentUser();
        templateInfo.setExportPersonName(currentUser.getRealName());
        templateInfo.setExportDeptName(currentUser.getDept().getName());
        templateInfo.setExportTime(TimeUtils.dateToString(new Date(), YYYYMMDD_HHMMSS));
    }

    @Override
    public Map<String, JSONObject> listByMonth(ListByMonthDTO dto) throws ServiceException {
        //为铺满值班表 故查自然月前后15天（实际查两月数据）
        Tuple2<String, String> tuple2 = TimeUtils.getParamStartAndEndTime(
                TimeSearchFlag.MONTH,
                TimeUtils.stringToString(dto.getTime(), "yyyy-MM-dd")
        );
        String startTime = TimeUtils.dateBefOrAft(tuple2._1, -15, YYYYMMDD_HHMMSS);
        String endTime = TimeUtils.dateBefOrAft(tuple2._2, 15, YYYYMMDD_HHMMSS);
        return bigScreenDutyUserMgr.groupByDate(dto, startTime, endTime);
    }

    @Override
    public AllDutyVO allDutyByTree(String dutyTime) throws ServiceException {
        if (StringUtils.isEmpty(dutyTime)) {
            throw new ServiceException("值班日期不能为空!");
        }
        return bigScreenDutyUserMgr.allDutyByTree(dutyTime);
    }

    @Override
    public ImportResultVO batchImport(MultipartFile file, Boolean repeatStrategy) throws ServiceException {
        checkNotNull(repeatStrategy, "参数[repeatStrategy]不能为空!");
        final CurrentUser currentUser = getCurrentUser();
        if (file == null) {
            throw new ServiceException("导入文件不能为空!");
        }
        final List<ExcelToDTO> list;
        try {
            final ImportParams importParams = new ImportParams();
            importParams.setNeedSave(false);
            list = ExcelImportUtil.importExcel(file.getInputStream(), ExcelToDTO.class, importParams);
        } catch (Exception e) {
            throw new ServiceException("文件读取失败-->" + e.getMessage(), e);
        }
        final var objects = list.stream()
                .filter(filter -> !filter.isAllFieldNull())
                .collect(Collectors.toList());

        ModifyDutyUsersDTO dto = new ModifyDutyUsersDTO();
        dto.setForce(repeatStrategy);
        String districtCode = currentUser.getDept().getDistrictCode();
        dto.setDistrictCode(districtCode);
        dto.setUnitName(currentUser.getDept().getName());
        //查单位用户
        DeptVO deptAndUserTree = permissionService.findDeptAndUserTreeV3("",
                true,
                null,
                currentUser.getDept().getDistrictCode());
        if (deptAndUserTree == null) {
            throw new ServiceException("根据单位代码[" + currentUser.getDept().getDistrictCode() + "]未能查询到相关部门及用户信息，请检查接口：findDeptAndUserTree");
        }
        List<UserVO> allUsers = new ArrayList<>(0);
        findAllUsers(deptAndUserTree, allUsers);
        if (CollectionUtils.isEmpty(allUsers)) {
            throw new ServiceException("当前单位[" + currentUser.getDept().getName() + "]及其下属单位中无用户，请检查!");
        }
        Integer index = 1;
        List<ImportFailVO> vos = new ArrayList<>(0);
        for (ExcelToDTO obj : objects) {
            try {
                //必填项校验
                obj.isValid();
                //重复策略 - 跳过执行（即不导入该条数据）
                String dutyTime = TimeUtils.stringToString(obj.getDutyTime(), TimeUtils.YYYYMMDD);
                if (!repeatStrategy) {
                    bigScreenDutyUserMgr.judgeRepeated(dutyTime, districtCode);
                }
                //转换为保存DTO
                dto.setDutyTime(dutyTime);
                dto.setDutyPhone(obj.getDutyPhone());
                buildSaveDto(obj, dto, allUsers, currentUser);
                modifyDutyUsers(dto);
            } catch (ServiceException e) {
                ImportFailVO failVO = ImportFailVO.of(1, index++, e.getMessage());
                vos.add(failVO);
            }
        }
        return new ImportResultVO(list.size(), list.size() - vos.size(), vos.size(), vos);
    }

    private void findAllUsers(DeptVO deptAndUserTree, List<UserVO> allUsers) {
        if (Objects.nonNull(deptAndUserTree.getUserList())) {
            List<UserVO> userList = deptAndUserTree.getUserList();
            userList.forEach(it -> {
                it.setDeptId(deptAndUserTree.getDeptId());
            });
            allUsers.addAll(userList);
        }
        if (CollectionUtils.isNotEmpty(deptAndUserTree.getChildren())) {
            deptAndUserTree.getChildren().forEach(it -> findAllUsers(it, allUsers));
        }
    }

    private void buildSaveDto(ExcelToDTO obj, ModifyDutyUsersDTO dto, List<UserVO> allUsers, CurrentUser currentUser) throws ServiceException {
        List<ModifyDutyUsersDTO.DutyUserDataDTO> list = new ArrayList<>(0);
        //值班领导主班
        final String provinceCode = "510000";
        PostKindEnum post = Objects.equals(provinceCode, currentUser.getDept().getDistrictCode())
                ? PostKindEnum.TLD
                : PostKindEnum.JLD;
        UserVO dutyLeaderA = matchUserVO(obj.getDutyLeaderA(), allUsers, post);
        list.add(matchDutyUserInfo(dutyLeaderA, DutyUserVo.ZHI_BAN_LING_DAO, DutyUserVo.ZHUBAN));
        //值班领导副班
        UserVO dutyLeaderB = matchUserVO(obj.getDutyLeaderB(), allUsers, post);
        list.add(matchDutyUserInfo(dutyLeaderB, DutyUserVo.ZHI_BAN_LING_DAO, DutyUserVo.FUBAN));
        //指挥长主班
        UserVO commandLeaderA = matchUserVO(obj.getCommandLeaderA(), allUsers, PostKindEnum.ZHZ);
        list.add(matchDutyUserInfo(commandLeaderA, DutyUserVo.ZHI_HUI_HANG, DutyUserVo.ZHUBAN));
        //指挥长副班
        UserVO commandLeaderB = matchUserVO(obj.getCommandLeaderB(), allUsers, PostKindEnum.ZHZ);
        list.add(matchDutyUserInfo(commandLeaderB, DutyUserVo.ZHI_HUI_HANG, DutyUserVo.FUBAN));
        dto.setData(list);
        //治安岗
        dto.setZagUserIds(matchUserIds(obj.getZagUsers(), allUsers, PostKindEnum.ZAG));
        //刑侦岗
        dto.setXzgUserIds(matchUserIds(obj.getXzgUsers(), allUsers, PostKindEnum.XING_ZHEN));
        //交警岗
        dto.setJjgUserIds(matchUserIds(obj.getJjgUsers(), allUsers, PostKindEnum.JJG));
        //网安岗
        dto.setWagUserIds(matchUserIds(obj.getWagUsers(), allUsers, PostKindEnum.WAG));
        //政保岗
        dto.setZbgUserIds(matchUserIds(obj.getZbgUsers(), allUsers, PostKindEnum.ZBG));
        //技侦岗
        dto.setJzgUserIds(matchUserIds(obj.getJzgUsers(), allUsers, PostKindEnum.JZG));
    }

    private String matchUserIds(String idCards, List<UserVO> allUsers, PostKindEnum post) {
        if (StringUtils.isEmpty(idCards)) {
            return null;
        }
        // 先按岗种过滤  再按证件号去一下重
        List<UserVO> newList = new ArrayList<>(0);
        allUsers.stream()
                .filter(it -> Objects.equals(it.getPostCode(), post.getCode()))
                .filter(distinctByKey(UserVO::getIdNumber))
                .forEach(newList::add);
        if (CollectionUtils.isEmpty(newList)) {
            log.info("当前警种[{}]，证件号码[{}]未匹配到对应人员!", post.getName(), idCards);
        }
        return newList.stream()
                .filter(it -> idCards.contains(it.getIdNumber()))
                .map(it -> it.getDeptId() + "-" + it.getUserId())
                .collect(Collectors.joining(","));

    }

    private UserVO matchUserVO(String idNumber, List<UserVO> allUsers, PostKindEnum post) throws ServiceException {
        String desc = String.format("当前警种[%s]，号码[%s]匹配失败，证件号码有误或岗位不匹配，请检查!",
                post.getName(),
                idNumber);
        return allUsers.stream()
                // XMKFB-6202 调整为 对应岗种下 身份证号匹配
                .filter(it -> Objects.equals(it.getPostCode(), post.getCode()))
                .filter(it -> Objects.equals(it.getIdNumber(), idNumber))
                .findFirst()
                .orElseThrow(() -> new ServiceException(desc));
    }

    private ModifyDutyUsersDTO.DutyUserDataDTO matchDutyUserInfo(UserVO user, String nature, String level) {
        ModifyDutyUsersDTO.DutyUserDataDTO dto = new ModifyDutyUsersDTO.DutyUserDataDTO();
        dto.setName(user.getName());
        dto.setJh(user.getPoliceCode());
        dto.setDh(user.getTel());
        dto.setNature(nature);
        dto.setLevel(level);
        dto.setProfilePic(user.getAvatar());
        dto.setUniqueSign(user.getDeptId() + "-" + user.getUserId());
        return dto;
    }

    @Override
    public void downloadImportTemplate(HttpServletResponse response) throws IOException {
        final InputStream in = this.getClass().getResourceAsStream("/template/dutyUsersBatchImport.xlsx");

        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        excelWriter.finish();
        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("值班人员台账导入模板.xlsx", StandardCharsets.UTF_8));

        OutputStream responseOut = response.getOutputStream();
        responseOut.write(modifiedBytes);
        responseOut.flush();
        responseOut.close();
    }

    @Override
    public void allDutyExport(HttpServletResponse response, String dutyTime) throws ServiceException {
        if (StringUtils.isEmpty(dutyTime)) {
            throw new ServiceException("值班日期不能为空！");
        }
        final String dateTime = TimeUtils.stringToString(dutyTime, TimeUtils.YYYYMMDD);
        List<CityDutyExcelVO> list = bigScreenDutyUserMgr.findByDutyTime(dateTime);
        final InputStream in = this.getClass().getResourceAsStream("/template/policeDutyByDayExport.xlsx");

        final String fileName = "市（州）公安局值班表（" + dateTime + "）";
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);
        excelWriter.finish();
        excelWriter.finish();
        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8));

        try {
            OutputStream responseOut = response.getOutputStream();
            responseOut.write(modifiedBytes);
            responseOut.flush();
            responseOut.close();
        } catch (IOException e) {
            log.error("全部值班情况导出发生异常：{}", e.getMessage());
            throw new ServiceException(e);
        }
    }

    @Override
    public List<DutyPlanDataVO> dutyPlanList(ListParamsRequest paramsRequest) throws ServiceException {
        List<KeyValueTypeVO> filterParams = paramsRequest.getFilterParams();
        Map<String, Object> map = new HashMap<>();
        final String paramTypeString = "string";
        final String paramTypeTime = "timeParams";
        final String paramTypeBoolean = "boolean";
        for (KeyValueTypeVO keyValueTypeVO : filterParams) {
            if (keyValueTypeVO.getType().equals(paramTypeString)) {
                map.put(keyValueTypeVO.getKey(), keyValueTypeVO.getValue());
            }
            if (paramTypeBoolean.equals(keyValueTypeVO.getType())) {
                map.put("exportMark", keyValueTypeVO.getValue());
            }
            final String orderField = "duty_time";
            if (keyValueTypeVO.getType().equals(paramTypeTime)
                    && keyValueTypeVO.getKey().equals(orderField)) {
                TimeParams value = (TimeParams) keyValueTypeVO.getValue();
                String range = value.getRange();
                if ("99".equals(range)) {
                    map.put("startTime", value.getBeginTime());
                    map.put("endTime", value.getEndTime());
                } else {
                    throw new ServiceException("参数错误，暂不支持的时间范围类型：" + range);
                }
            }
        }
        SearchParams searchParams = paramsRequest.getSearchParams();
        if (StringUtils.isNotEmpty(searchParams.getSearchField())) {
            final String searchField = "name";
            if (searchField.equals(searchParams.getSearchField())) {
                map.put(searchField, searchParams.getSearchValue());
            } else {
                throw new ServiceException("参数错误，暂不支持的检索字段：" + searchParams.getSearchField());
            }
        }
        SortParams sortParams = paramsRequest.getSortParams();
        List<DutyPlanDataVO> result = bigScreenDutyUserMgr.dutyPlanList(map, sortParams);
        return result;

    }

    @Override
    public void dutyPlanExport(HttpServletResponse response, ListParamsRequest paramsRequest) throws ServiceException {
        final InputStream in = this.getClass().getResourceAsStream("/template/dutyPlanExport.xlsx");

        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();

        List<DutyPlanDataVO> list = dutyPlanList(paramsRequest);
        list.forEach(it -> {
            if (Objects.nonNull(it.getFirstDdgUser())) {
                it.setZhddzbPost(it.getFirstDdgUser().getDuty());
                it.setZhddzbPhone(it.getFirstDdgUser().getTel());
            }
        });
        excelWriter.fill(list, fillConfig, writeSheet);
        excelWriter.finish();
        excelWriter.finish();
        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("值班计划表.xlsx", StandardCharsets.UTF_8));

        try {
            OutputStream responseOut = response.getOutputStream();
            responseOut.write(modifiedBytes);
            responseOut.flush();
            responseOut.close();
        } catch (IOException e) {
            log.error("值班计划导出发生异常：{}", e.getMessage());
            throw new ServiceException(e);
        }
    }

    @Override
    public List<BaseJingwuSourceVo> jingWuSourceList(String type, JingWuSourceDTO dto) throws ServiceException {
        if (StringUtils.isEmpty(type)) {
            throw new ServiceException("类型不能为空！");
        }
        final String serviceKey = JingWuSourceEnum.getKeyByType(type);
        if (StringUtils.isEmpty(serviceKey)) {
            throw new ServiceException("找不到对应的业务实现类！");
        }
        final BaseZgJingWuSourceService service = KeyMgrFactory.findMgrByKey(BaseZgJingWuSourceService.class, serviceKey);
        return service.queryList(dto);
    }

    @Override
    public MorePersonInfo selectMore(String dutyTime, String districtCode) throws ServiceException {
        if (StringUtils.isEmpty(dutyTime)
                || StringUtils.isEmpty(districtCode)) {
            throw new ServiceException("参数错误，单位和值班日期均不能为空！");
        }
        BigScreenDutyUserEntity entity = bigScreenDutyUserMgr.getBigScreenDutyUserMapper().selectList(new QueryWrapper<BigScreenDutyUserEntity>()
                        .eq("duty_time", dutyTime)
                        .eq("district_code", districtCode)
                ).stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(entity)) {
            return null;
        }
        MorePersonInfo result = new MorePersonInfo();
        result.setDutyTel(entity.getDutyPhone());
        // 获取所有岗位人员信息
        Map<Long, UserDto> userMap = selectUserListById(entity);
        if (Objects.nonNull(userMap)) {
            analysisPostUsers(dealUserIds(entity.getJjsUserIds(), null), DutyPostConstants.POST_JJS, result, userMap);
            analysisPostUsers(dealUserIds(entity.getDdgUserIds(), null), DutyPostConstants.POST_DDG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getQbgUserIds(), null), DutyPostConstants.POST_QBG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getXzgUserIds(), null), DutyPostConstants.POST_XZG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getFkgUserIds(), null), DutyPostConstants.POST_FKG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getDbUserIds(), null), DutyPostConstants.POST_DB, result, userMap);
            analysisPostUsers(dealUserIds(entity.getZagUserIds(), null), DutyPostConstants.POST_ZAG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getXingZhenUserIds(), null),
                    DutyPostConstants.POST_XING_ZHEN, result, userMap);
            analysisPostUsers(dealUserIds(entity.getWagUserIds(), null), DutyPostConstants.POST_WAG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getJzgUserIds(), null), DutyPostConstants.POST_JZG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getZbgUserIds(), null), DutyPostConstants.POST_ZBG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getXcgUserIds(), null), DutyPostConstants.POST_XCG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getJingZhenUserIds(), null),
                    DutyPostConstants.POST_JING_ZHEN, result, userMap);
            analysisPostUsers(dealUserIds(entity.getKxgUserIds(), null), DutyPostConstants.POST_KXG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getJbgUserIds(), null), DutyPostConstants.POST_JBG, result, userMap);
            analysisPostUsers(dealUserIds(entity.getJjgUserIds(), null), DutyPostConstants.POST_JJG, result, userMap);
        }
        return result;
    }

    private Map<Long, UserDto> selectUserListById(BigScreenDutyUserEntity entity) {
        // 去重
        Set<Long> userIds = new HashSet<>(0);
        dealUserIds(entity.getJjsUserIds(), userIds);
        dealUserIds(entity.getDdgUserIds(), userIds);
        dealUserIds(entity.getQbgUserIds(), userIds);
        dealUserIds(entity.getXzgUserIds(), userIds);
        dealUserIds(entity.getFkgUserIds(), userIds);
        dealUserIds(entity.getDbUserIds(), userIds);
        dealUserIds(entity.getZagUserIds(), userIds);
        dealUserIds(entity.getXingZhenUserIds(), userIds);
        dealUserIds(entity.getWagUserIds(), userIds);
        dealUserIds(entity.getJzgUserIds(), userIds);
        dealUserIds(entity.getZbgUserIds(), userIds);
        dealUserIds(entity.getXcgUserIds(), userIds);
        dealUserIds(entity.getJingZhenUserIds(), userIds);
        dealUserIds(entity.getKxgUserIds(), userIds);
        dealUserIds(entity.getJbgUserIds(), userIds);
        if (userIds.size() > 0) {
            return permissionService.getUserListById(Arrays.asList(userIds.toArray(new Long[0])))
                    .stream()
                    .collect(Collectors.toMap(UserDto::getId, a -> a));
        }
        return null;
    }

    private Set<Long> dealUserIds(String marks, Set<Long> userIds) {
        if (StringUtils.isEmpty(marks)) {
            return null;
        }
        Set<Long> collect = Arrays.stream(marks.split(","))
                .map(it -> {
                    String id = it.split("-")[1];
                    return Long.parseLong(id);
                }).collect(Collectors.toSet());
        if (Objects.nonNull(userIds)) {
            userIds.addAll(collect);
        }
        return collect;
    }

    private void analysisPostUsers(Set<Long> userIds, String post, MorePersonInfo result, Map<Long, UserDto> userMap) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        JSONArray persons = new JSONArray(0);
        for (Long userId : userIds) {
            UserDto u = userMap.get(userId);
            JSONObject object = new JSONObject();
            object.put("userId", u.getId());
            object.put("name", u.getRealName());
            object.put("tel", u.getTel());
            object.put("post", u.getDuty());
            persons.add(object);
        }
        JSONObject position = new JSONObject();
        position.put("type", post);
        position.put("persons", persons);
        result.getPositions().add(position);
    }

    @Override
    public RestfulResultsV2<DeviceVO> deviceList(DeviceSearchDTO dto) throws ServiceException {
        String config = BeanFactoryHolder.getEnv()
                .getProperty("com.trs.bigscreen.device.upd.port.config", String.class);
        if(StringUtils.isEmpty(config)){
            throw new ServiceException("请配置设备更新端口及地域编码信息！");
        }
        List<String> codeList = new ArrayList<>(0);
        for (JSONObject obj : Objects.requireNonNull(JSON.parseArray(config, JSONObject.class))) {
            codeList.add(obj.getString("districtCode") + "000000");
        }
        Map<String, String> collect = permissionService.getDeptByCodes(codeList)
                .stream()
                .collect(Collectors.toMap(DeptDto::getDistrictCode, DeptDto::getShortName));
        Page<BigScreenDeviceEntity> pageInfo = Page.of(dto.getPageNum(), dto.getPageSize());
        if (CollectionUtils.isNotEmpty(dto.getOrderList())) {
            List<OrderItem> orderList = new ArrayList<>(dto.getOrderList().size());
            for (OrderDTO orderDTO : dto.getOrderList()) {
                if (orderDTO.isAsc()) {
                    orderList.add(OrderItem.asc(orderDTO.getFieldName()));
                } else {
                    orderList.add(OrderItem.desc(orderDTO.getFieldName()));
                }
            }
            pageInfo.setOrders(orderList);
        }
        List<GeometryVO> vos = dto.makeGeometries();
        Page<BigScreenDeviceEntity> page = bigScreenDeviceMapper.doPageSelect(
                pageInfo,
                dto,
                Objects.isNull(vos) ? null : vos.get(0)
        );
        return RestfulResultsV2.ok(page.getRecords()
                .stream()
                .map(it -> {
                    DeviceVO vo = BigScreenDeviceEntity.toVo(it);
                    vo.setUnitName(collect.get(it.getDistrictCode()));
                    return vo;
                })
                .collect(Collectors.toList())
        ).addPageSize(dto.getPageSize()).addPageNum(dto.getPageNum()).addTotalCount(page.getTotal());
    }

    @Override
    public String updateDeviceOnline(String deviceNos, Integer online) throws ServiceException {
        if (StringUtils.isEmpty(deviceNos)
                || online == null) {
            throw new ServiceException("参数错误");
        }
        for (String deviceNo : deviceNos.split(",")) {
            // 设备编号唯一
            BigScreenDeviceEntity entity = bigScreenDeviceMapper.selectOne(new QueryWrapper<BigScreenDeviceEntity>()
                    .eq("is_del", 0)
                    .eq("device_no", deviceNo));
            entity.setOnline(online);
            bigScreenDeviceMapper.updateById(entity);
        }
        return "状态更新完成";
    }

    @Override
    public void syncDeviceStatusTask() throws ServiceException {
        log.info("*** 设备状态更新定时任务-开始同步 ***");
        long pageNum = 0L;
        // 批量更新  一次最大一百条
        long pageSize = BeanFactoryHolder.getEnv()
                .getProperty("com.trs.bigscreen.sync.deviceStatus.batchNum", Long.class, 100L);
        var url = BeanFactoryHolder.getEnv()
                .getProperty("com.trs.bigscreen.sync.rxtx.deviceStatus");
        var key = BeanFactoryHolder.getEnv()
                .getProperty("com.trs.bigscreen.sync.rxtx.deviceStatu.key", String.class);
        if (StringUtils.isEmpty(url)
                || StringUtils.isEmpty(key)) {
            throw new ServiceException("url或参数key未配置");
        }
        long total = 0L;
        do {
            pageNum += 1L;
            log.info("设备状态更新定时任务-同步第{}页数据", pageNum);
            Page<BigScreenDeviceEntity> page = bigScreenDeviceMapper.doPageSelect(
                    Page.of(pageNum, pageSize),
                    new DeviceSearchDTO(),
                    null);
            if (!page.getRecords().isEmpty()) {
                total = page.getTotal();
                List<String> collect = page.getRecords().stream()
                        .map(BigScreenDeviceEntity::getDeviceNo)
                        .collect(Collectors.toList());
                var body = JsonUtil.toJsonString(Map.of(
                        "key", key,
                        "device_ids", collect.toArray(String[]::new)
                ));
                var resp = OkHttpUtil.getInstance().postData(url, body);
                if (JsonUtils.isValidObject(resp)) {
                    JSONObject json = JSONObject.parseObject(resp);
                    String code = json.getString("code");
                    if (!Objects.equals("0", code)) {
                        throw new ServiceException(StringUtils.showEmpty(json.getString("msg"), "未知异常"));
                    } else {
                        JSONArray data = json.getJSONArray("data");
                        List<String> onlineList = new ArrayList<>(0);
                        List<String> offlineList = new ArrayList<>(0);
                        for (int i = 0; i < data.size(); i++) {
                            JSONObject it = data.getJSONObject(i);
                            String deviceNo = it.getString("device_id");
                            Integer online = it.getInteger("online");
                            switch (online) {
                                case 0:
                                    offlineList.add(deviceNo);
                                    break;
                                case 1:
                                    onlineList.add(deviceNo);
                                    break;
                                default:
                                    log.info("异常数据，设备编号：{}，在线状态：{}", deviceNo, online);
                                    break;
                            }
                        }
                        // 在线设备更新状态
                        bigScreenDeviceMapper.batchUpdateStatus(onlineList, DeviceTypeConstant.DEVICE_ONLINE);
                        // 离线设备更新状态
                        bigScreenDeviceMapper.batchUpdateStatus(offlineList, DeviceTypeConstant.DEVICE_OFFLINE);
                    }
                }
            }
        } while (pageNum * pageSize < total);
        log.info("*** 设备状态更新定时任务-同步完成 ***");
    }

    @Override
    public List<CountVO> jwzyCount(String types, String startTime) throws ServiceException {
        PreConditionCheck.checkNotEmpty(types, "参数type不能为空！");
        PreConditionCheck.checkNotEmpty(startTime, "参数startTime不能为空！");
        List<String> typeList = Arrays.asList(types.split(","));
        List<CountVO> result = new ArrayList<>(0);
        for (String typeItem : typeList) {
            final String serviceKey = JingWuSourceEnum.getKeyByType(typeItem);
            if (StringUtils.isEmpty(serviceKey)) {
                throw new ServiceException("找不到对应的业务实现类！");
            }
            final BaseZgJingWuSourceService service = KeyMgrFactory.findMgrByKey(BaseZgJingWuSourceService.class, serviceKey);
            JingWuSourceDTO dto = new JingWuSourceDTO();
            dto.setStartTime(startTime);
            List list = service.queryList(dto);
            CountVO vo = new CountVO();
            vo.setType(typeItem);
            vo.setDesc(JingWuSourceEnum.getDecByType(typeItem));
            vo.setNum(list.size());
            result.add(vo);
        }
        return result;
    }

    @Override
    public String coverDutyInfo(CoverDutyInfoDTO dto) throws ServiceException {
        PreConditionCheck.checkNotNull(dto.getCoverForward(), "覆盖方向标识不能为空！");
        dto.isValid();
        bigScreenDutyUserMgr.coverDutyInfo(dto);
        return "操作成功！";
    }

    @Override
    public String addDutyUserBuffer(List<DutyUserBufferDTO> dtos) throws ServiceException {
        final CurrentUser currentUser = getCurrentUser();
        List<String> names = new ArrayList<>(0);
        for (DutyUserBufferDTO dto : dtos) {
            // 检查是否配置了岗位信息
            if (StringUtils.isEmpty(dto.getPostName())) {
                names.add(dto.getXm());
                continue;
            }
            dto.isValid();
            bigScreenDutyUserBufferMgr.addDutyUserBuffer(dto, currentUser);
        }
        if (!names.isEmpty()) {
            String errMsg = String.format("%s未配置岗位，请配置完成以后再重试", String.join("，", names));
            throw new ServiceException(errMsg);
        }
        return "操作成功！";
    }

    @Override
    public List<BufferUserInfoVO> dutyUserList(BufferUserDTO dto) throws ServiceException {
        return bigScreenDutyUserBufferMgr.dutyUserList(dto, getCurrentUser());
    }

    @Override
    public String deleteDutyUserBuffer(String ids) {
        PreConditionCheck.checkNotEmpty(ids, "参数ids不能为空！");
        bigScreenDutyUserBufferMgr.deleteDutyUserBuffer(ids);
        return "操作成功！";
    }

    @Override
    public List<DutyUserInfoVO> dutyUserInfo(String dutyTime, String districtCode) throws ServiceException {
        PreConditionCheck.checkNotEmpty(dutyTime, "参数dutyTime不能为空！");
        PreConditionCheck.checkNotEmpty(districtCode, "参数districtCode不能为空！");
        return bigScreenDutyUserMgr.dutyUserInfo(dutyTime, districtCode);
    }

    @Override
    public String editDutyInfo(List<EditDutyUserInfoDTO> dtos) throws ServiceException {

        for (EditDutyUserInfoDTO dto : dtos) {
            dto.isValid();
            if (ZHI_HUI_ZHANG.equals(dto.getPostName())
                    || ZHI_BAN_LING_DAO.equals(dto.getPostName())) {
                PreConditionCheck.checkNotEmpty(dto.getLevel(), "编辑" + dto.getPostName() + "，参数level不能为空！");
            }
            bigScreenDutyUserMgr.editDutyInfo(dto);
        }
        return "操作成功！";
    }

    @Override
    public String deleteDutyInfo(CoverDutyInfoDTO dto) throws ServiceException {
        dto.isValid();
        bigScreenDutyUserMgr.deleteDutyInfo(dto);
        return "操作成功";
    }
}
