package com.trs.police.bigscreen.mgr;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.bigscreen.domain.dto.BufferUserDTO;
import com.trs.police.bigscreen.domain.dto.DutyUserBufferDTO;
import com.trs.police.bigscreen.domain.entity.BigScreenDutyUserBufferEntity;
import com.trs.police.bigscreen.domain.vo.BufferUserInfoVO;
import com.trs.police.bigscreen.mapper.BigScreenDutyUserBufferMapper;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.openfeign.starter.service.DictService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2025/5/15 15:23
 * @version 1.0
 * @since 1.0
 */
@Component
@AllArgsConstructor
@Slf4j
public class BigScreenDutyUserBufferMgr {

    @Getter
    private final BigScreenDutyUserBufferMapper bigScreenDutyUserBufferMapper;

    @Resource
    private DictService dictService;

    final static String POST_JLD = "局领导";
    final static String POST_TLD = "厅领导";
    final static String POST_ZBLD = "值班领导";
    final static String POST_ZHZ = "指挥长";

    /**
     * 批量新增值班人员缓存信息
     *
     * @param dto 参数
     * @param user 当前用户
     */
    public void addDutyUserBuffer(DutyUserBufferDTO dto, CurrentUser user) {
        // 反查是否已存在
        List<BigScreenDutyUserBufferEntity> list = new LambdaQueryChainWrapper<>(bigScreenDutyUserBufferMapper)
                .eq(BigScreenDutyUserBufferEntity::getDistrictCode, user.getDept().getDistrictCode())
                .eq(BigScreenDutyUserBufferEntity::getUserId, dto.getUserId())
                .eq(BigScreenDutyUserBufferEntity::getDeptId, dto.getDeptId())
                .eq(BigScreenDutyUserBufferEntity::getPostName, dto.getPostName())
                .eq(BigScreenDutyUserBufferEntity::getIsDel, 0)
                .list();
        if (list.size() > 0) {
            return;
        }
        BigScreenDutyUserBufferEntity entity = new BigScreenDutyUserBufferEntity();
        entity.setDistrictCode(user.getDept().getDistrictCode());
        entity.setCrTime(new Date());
        entity.setCrUserId(user.getId());
        entity.setCrDeptId(user.getDeptId());
        BeanUtils.copyProperties(dto, entity);
        // 岗位名称
        String post = dto.getPostName();
        // 0 未匹配上
        Map<String, Long> collect = dictService.getDictByType("post_type")
                .stream()
                .collect(Collectors.toMap(DictDto::getName, DictDto::getCode));
        entity.setPostCode(collect.getOrDefault(post, 0L).intValue());
        if (POST_JLD.equals(post)
                || POST_TLD.equals(post)) {
            entity.setPostName(POST_ZBLD);
        } else {
            entity.setPostName(post);
        }
        bigScreenDutyUserBufferMapper.insert(entity);
    }

    /**
     * 列表
     *
     * @param dto 参数
     * @param currentUser 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    public List<BufferUserInfoVO> dutyUserList(BufferUserDTO dto, CurrentUser currentUser) throws ServiceException {
        dto.isValid();
        if (StringUtils.isEmpty(dto.getDistrictCode())) {
            dto.setDistrictCode(currentUser.getDept().getDistrictCode());
        }
        if (StringUtils.isNotEmpty(dto.getSearchField())) {
            switch (dto.getSearchField()) {
                case "xm":
                case "zjhm":
                    break;
                default:
                    throw new ServiceException("暂不支持的检索字段：" + dto.getSearchField());
            }
        }
        List<BufferUserInfoVO> collect = bigScreenDutyUserBufferMapper.selectByDto(dto);
        List<BufferUserInfoVO> result = new ArrayList<>(0);
        for (BufferUserInfoVO vo : collect) {
            if (POST_ZBLD.equals(vo.getPostName())
                    || POST_ZHZ.equals(vo.getPostName())) {
                BufferUserInfoVO tmp = new BufferUserInfoVO();
                BeanUtils.copyProperties(vo, tmp);
                tmp.setLevel("主班");
                result.add(tmp);
                BufferUserInfoVO tmp1 = new BufferUserInfoVO();
                BeanUtils.copyProperties(vo, tmp1);
                tmp1.setLevel("副班");
                result.add(tmp1);
            } else {
                result.add(vo);
            }
        }
        return result;
    }

    /**
     * 批量删除
     *
     * @param ids 参数
     */
    public void deleteDutyUserBuffer(String ids) {
        List<String> list = Arrays.asList(ids.split(","));
        for (String id : list) {
            BigScreenDutyUserBufferEntity entity = bigScreenDutyUserBufferMapper.selectById(Long.parseLong(id));
            entity.setIsDel(1);
            bigScreenDutyUserBufferMapper.updateById(entity);
        }
    }
}
