package com.trs.police.bigscreen.mgr;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.AreaCodeUtil;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.bigscreen.constants.DutyPostConstants;
import com.trs.police.bigscreen.convert.EntityConvertToVo;
import com.trs.police.bigscreen.domain.dto.*;
import com.trs.police.bigscreen.domain.entity.BigScreenDutyUserEntity;
import com.trs.police.bigscreen.domain.vo.DutyUserInfoVO;
import com.trs.police.bigscreen.mapper.BigScreenDutyUserMapper;
import com.trs.police.bigscreen.vo.*;
import com.trs.police.common.core.constant.enums.PoliceKindEnum;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import io.vavr.Tuple2;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.police.bigscreen.constants.DutyPostConstants.ZHI_BAN_LING_DAO;
import static com.trs.police.bigscreen.constants.DutyPostConstants.ZHI_HUI_ZHANG;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/8/15 11:17
 * @since 1.0
 */
@Component
@AllArgsConstructor
@Slf4j
public class BigScreenDutyUserMgr {

    @Getter
    private final BigScreenDutyUserMapper bigScreenDutyUserMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private DictService dictService;

    /**
     * addDutyUser<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/14 20:20
     */
    public Report<String> addDutyUser(ModifyDutyUserDTO dto) throws ServiceException {
        BigScreenDutyUserEntity entity = EntityConvertToVo.INSTANCE.dtoToEntity(dto);
        entity.setDutyTime(TimeUtils.stringToDate(dto.getDutyTime()));
        entity.setCrTime(new Date());
        entity.setUpdateTime(new Date());
        if (Optional.ofNullable(dto.getId()).filter(it -> it > 0L).isPresent()) {
            var p = PreConditionCheck.checkNotNull(
                    getBigScreenDutyUserMapper().selectById(dto.getId()),
                    new ServiceException(String.format("不存在ID=[%s]的值班数据", dto.getId()))
            );
            entity.setId(p.getId());
            entity.setCrTime(p.getCrTime());
            getBigScreenDutyUserMapper().updateById(entity);
        } else {
            getBigScreenDutyUserMapper().insert(entity);
        }
        return new Report<>("新增/编辑值班人员", "成功操作");
    }

    /**
     * modifyDutyUsers<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/14 18:14
     */
    public Report<String> modifyDutyUsers(ModifyDutyUsersDTO dto) {
        if (dto.getForce()) {
            // 强制删除数据
            getBigScreenDutyUserMapper().delete(
                    new QueryWrapper<BigScreenDutyUserEntity>()
                            .lambda()
                            .eq(BigScreenDutyUserEntity::getDutyTime, dto.getDutyTime())
                            .eq(BigScreenDutyUserEntity::getDistrictCode, dto.getDistrictCode())
            );
        }
        if (CollectionUtils.isNotEmpty(dto.getData())) {
            getBigScreenDutyUserMapper().delete(
                    new QueryWrapper<BigScreenDutyUserEntity>()
                            .lambda()
                            .eq(BigScreenDutyUserEntity::getDutyTime, dto.getDutyTime())
                            .eq(BigScreenDutyUserEntity::getDistrictCode, dto.getDistrictCode())
                            .and(and -> {
                                for (var data : dto.getData()) {
                                    and.or(i -> i
                                            .eq(BigScreenDutyUserEntity::getNature, data.getNature())
                                            .eq(BigScreenDutyUserEntity::getPoliceKind, data.getPoliceKind())
                                            .eq(BigScreenDutyUserEntity::getLevel, data.getLevel())
                                    );
                                }
                            })
            );
            final var dutyTime = TimeUtils.stringToDate(dto.getDutyTime());
            for (ModifyDutyUsersDTO.DutyUserDataDTO user : dto.getData()) {
                BigScreenDutyUserEntity entity = EntityConvertToVo.INSTANCE.dtoToEntity(user);
                entity.setCrTime(new Date());
                entity.setUpdateTime(new Date());
                entity.setDutyTime(dutyTime);
                entity.setUnitName(dto.getUnitName());
                entity.setDistrictCode(dto.getDistrictCode());
                getBigScreenDutyUserMapper().insert(entity);
            }
        }
        return new Report<>("新增/编辑值班人员", "成功操作");
    }

    /**
     * dutyUsers<BR>
     *
     * @param currentUser 参数
     * @param dto         参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/15 11:30
     */
    public List<DutyUserResultVo> dutyUsers(CurrentUser currentUser, DutyUserDTO dto) throws ServiceException {
        final List<String> natures = StringUtils.getList(dto.getNatures(), StringUtils.SEPARATOR_COMMA);
        final List<String> levels = StringUtils.getList(dto.getLevels(), StringUtils.SEPARATOR_COMMA);
        final String districtCode;
        if (dto.getOnlyLocal()) {
            districtCode = StringUtils.showEmpty(dto.getDistrictCode(), currentUser.getDept().getDistrictCode());
        } else {
            districtCode = StringUtils.showEmpty(dto.getDistrictCode());
        }
        final Boolean isShiJi = districtCode.endsWith("00");
        final Boolean isJiaoJing = Objects.equals(
                currentUser.getDept().getPoliceKind(),
                PoliceKindEnum.JIAOJING.getCode().longValue()
        );
        final String searchCode;
        if (dto.getUseShortCode() && StringUtils.isNotEmpty(districtCode)) {
            searchCode = AreaCodeUtil.getAreaShortCode(AreaCodeUtil.spreadingAreaCode(districtCode, false));
        } else {
            searchCode = districtCode;
        }
        final String nowDate = StringUtils.showEmpty(dto.getDutyTime(), TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD));
        List<BigScreenDutyUserEntity> list;
        if (dto.getOnlyLocal()) {
            list = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                    .eq(BigScreenDutyUserEntity::getDutyTime, nowDate)
                    .eq(StringUtils.isNotEmpty(searchCode) && !dto.getUseShortCode(), BigScreenDutyUserEntity::getDistrictCode, searchCode)
                    .likeRight(StringUtils.isNotEmpty(searchCode) && dto.getUseShortCode(), BigScreenDutyUserEntity::getDistrictCode, searchCode)
                    .in(CollectionUtils.isNotEmpty(natures), BigScreenDutyUserEntity::getNature, natures)
                    .in(CollectionUtils.isNotEmpty(levels), BigScreenDutyUserEntity::getLevel, levels)
                    .eq(isJiaoJing && isShiJi, BigScreenDutyUserEntity::getPoliceKind, PoliceKindEnum.JIAOJING.getCode())
                    .ne(!isJiaoJing || !isShiJi, BigScreenDutyUserEntity::getPoliceKind, PoliceKindEnum.JIAOJING.getCode())
                    .list();
        } else {
            list = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                    .eq(BigScreenDutyUserEntity::getDutyTime, nowDate)
                    .eq(StringUtils.isNotEmpty(searchCode) && !dto.getUseShortCode(), BigScreenDutyUserEntity::getDistrictCode, searchCode)
                    .likeRight(StringUtils.isNotEmpty(searchCode) && dto.getUseShortCode(), BigScreenDutyUserEntity::getDistrictCode, searchCode)
                    .in(CollectionUtils.isNotEmpty(natures), BigScreenDutyUserEntity::getNature, natures)
                    .in(CollectionUtils.isNotEmpty(levels), BigScreenDutyUserEntity::getLevel, levels)
                    .list();
        }
        var map = list.stream()
                .collect(Collectors.groupingBy(it -> it.getDistrictCode() + ";" + it.getPoliceKind()));
        return map.values()
                .stream()
                .map(it -> {
                    DutyUserResultVo vo = new DutyUserResultVo();
                    var one = it.get(0);
                    vo.setDistrictCode(one.getDistrictCode());
                    vo.setPoliceKind(one.getPoliceKind());
                    vo.setUnitName(one.getUnitName());
                    vo.setDutyUsers(
                            it.stream().map(r -> {
                                DutyUserVo u = EntityConvertToVo.INSTANCE.entityToVo(r);
                                //解析岗位人员名称
                                dealUserName(u);
                                u.setProfilePic(StringUtils.showEmpty(r.getProfilePic()));
                                return u;
                            }).sorted().collect(Collectors.toList())
                    );
                    return vo;
                }).sorted().collect(Collectors.toList());
    }

    private void dealUserName(DutyUserVo u) {
        List<Long> userIds = new ArrayList<>(0);
        //接警室
        List<Long> jjsUserIds = getUserIds(u, DutyPostConstants.POST_JJS, userIds);
        //情报岗
        List<Long> qbgUserIds = getUserIds(u, DutyPostConstants.POST_QBG, userIds);
        //调度岗
        List<Long> ddgUserIds = getUserIds(u, DutyPostConstants.POST_DDG, userIds);
        //党办
        List<Long> dbUserIds = getUserIds(u, DutyPostConstants.POST_DB, userIds);
        //风控岗
        List<Long> fkgUserIds = getUserIds(u, DutyPostConstants.POST_FKG, userIds);
        //协助岗
        List<Long> xzgUserIds = getUserIds(u, DutyPostConstants.POST_XZG, userIds);
        //警保岗
        List<Long> jbgUserIds = getUserIds(u, DutyPostConstants.POST_JBG, userIds);
        //治安岗
        List<Long> zagUserIds = getUserIds(u, DutyPostConstants.POST_ZAG, userIds);
        //刑侦岗
        List<Long> xingZhenUserIds = getUserIds(u, DutyPostConstants.POST_XING_ZHEN, userIds);
        //网安岗
        List<Long> wagUserIds = getUserIds(u, DutyPostConstants.POST_WAG, userIds);
        //技侦岗
        List<Long> jzgUserIds = getUserIds(u, DutyPostConstants.POST_JZG, userIds);
        //政保岗
        List<Long> zbgUserIds = getUserIds(u, DutyPostConstants.POST_ZBG, userIds);
        //宣传岗
        List<Long> xcgUserIds = getUserIds(u, DutyPostConstants.POST_XCG, userIds);
        //经侦岗
        List<Long> jingZhenUserIds = getUserIds(u, DutyPostConstants.POST_JING_ZHEN, userIds);
        //科信岗
        List<Long> kxgUserIds = getUserIds(u, DutyPostConstants.POST_KXG, userIds);
        //交警岗
        List<Long> jjgUserIds = getUserIds(u, DutyPostConstants.POST_JJG, userIds);
        Long dutyUserId = null;
        if (StringUtils.isNotEmpty(u.getUniqueSign())) {
            String[] split = u.getUniqueSign().split(StringUtils.SEPARATOR_LINE);
            dutyUserId = Long.parseLong(split[1]);
            userIds.add(dutyUserId);
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<UserDto> users = permissionService.getUserListById(userIds.stream()
                    .distinct()
                    .collect(Collectors.toList()));
            // 为方便前端数据回显 需要变更返回结构
            changeUserIds(u, users);
            u.setJjsUserNames(getUserNames(jjsUserIds, users));
            u.setQbgUserNames(getUserNames(qbgUserIds, users));
            u.setDdgUserNames(getUserNames(ddgUserIds, users));
            u.setDbUserNames(getUserNames(dbUserIds, users));
            u.setFkgUserNames(getUserNames(fkgUserIds, users));
            u.setXzgUserNames(getUserNames(xzgUserIds, users));
            u.setJbgUserNames(getUserNames(jbgUserIds, users));
            u.setZagUserNames(getUserNames(zagUserIds, users));
            u.setXingZhenUserNames(getUserNames(xingZhenUserIds, users));
            u.setWagUserNames(getUserNames(wagUserIds, users));
            u.setJzgUserNames(getUserNames(jzgUserIds, users));
            u.setZbgUserNames(getUserNames(zbgUserIds, users));
            u.setXcgUserNames(getUserNames(xcgUserIds, users));
            u.setJingZhenUserNames(getUserNames(jingZhenUserIds, users));
            u.setKxgUserNames(getUserNames(kxgUserIds, users));
            u.setJjgUserNames(getUserNames(jjgUserIds, users));
            if (Objects.nonNull(dutyUserId)) {
                for (UserDto user : users) {
                    if (dutyUserId.equals(user.getId())) {
                        u.setPosition(user.getDuty());
                    }
                }
            }
        }
    }

    private void changeUserIds(DutyUserVo u, List<UserDto> users) {
        u.setJjsUserIds(getUserInfo(u.getJjsUserIds(), users));
        u.setQbgUserIds(getUserInfo(u.getQbgUserIds(), users));
        u.setDdgUserIds(getUserInfo(u.getDdgUserIds(), users));
        u.setDbUserIds(getUserInfo(u.getDbUserIds(), users));
        u.setFkgUserIds(getUserInfo(u.getFkgUserIds(), users));
        u.setXzgUserIds(getUserInfo(u.getXzgUserIds(), users));
        u.setJbgUserIds(getUserInfo(u.getJbgUserIds(), users));
        u.setZagUserIds(getUserInfo(u.getZagUserIds(), users));
        u.setXingZhenUserIds(getUserInfo(u.getXingZhenUserIds(), users));
        u.setWagUserIds(getUserInfo(u.getWagUserIds(), users));
        u.setJzgUserIds(getUserInfo(u.getJzgUserIds(), users));
        u.setZbgUserIds(getUserInfo(u.getZbgUserIds(), users));
        u.setXcgUserIds(getUserInfo(u.getXcgUserIds(), users));
        u.setJingZhenUserIds(getUserInfo(u.getJingZhenUserIds(), users));
        u.setKxgUserIds(getUserInfo(u.getKxgUserIds(), users));
        u.setJjgUserIds(getUserInfo(u.getJjgUserIds(), users));
    }

    private String getUserInfo(String userIds, List<UserDto> users) {
        JSONArray array = new JSONArray(0);
        if (StringUtils.isNotEmpty(userIds)) {
            for (String s : userIds.split(StringUtils.SEPARATOR_COMMA)) {
                for (UserDto user : users) {
                    String userId = s.split(StringUtils.SEPARATOR_LINE)[1];
                    if (Objects.equals(userId, user.getId().toString())) {
                        JSONObject object = new JSONObject();
                        object.put("label", user.getRealName());
                        object.put("value", s);
                        array.add(object);
                        break;
                    }
                }
            }
        }
        return array.toJSONString();
    }

    private String getUserNames(List<Long> userIds, List<UserDto> users) {
        return users.stream()
                .filter(it -> userIds.contains(it.getId()))
                .map(UserDto::getRealName)
                .collect(Collectors.joining("、"));
    }

    private List<Long> getUserIds(DutyUserVo u, String postType, List<Long> userIds) {
        String gwUserIds;
        switch (postType) {
            case DutyPostConstants.POST_JJS:
                gwUserIds = u.getJjsUserIds();
                break;
            case DutyPostConstants.POST_DB:
                gwUserIds = u.getDbUserIds();
                break;
            case DutyPostConstants.POST_FKG:
                gwUserIds = u.getFkgUserIds();
                break;
            case DutyPostConstants.POST_JING_ZHEN:
                gwUserIds = u.getJingZhenUserIds();
                break;
            case DutyPostConstants.POST_JZG:
                gwUserIds = u.getJzgUserIds();
                break;
            case DutyPostConstants.POST_KXG:
                gwUserIds = u.getKxgUserIds();
                break;
            case DutyPostConstants.POST_QBG:
                gwUserIds = u.getQbgUserIds();
                break;
            case DutyPostConstants.POST_XCG:
                gwUserIds = u.getXcgUserIds();
                break;
            case DutyPostConstants.POST_XZG:
                gwUserIds = u.getXzgUserIds();
                break;
            case DutyPostConstants.POST_ZAG:
                gwUserIds = u.getZagUserIds();
                break;
            case DutyPostConstants.POST_ZBG:
                gwUserIds = u.getZbgUserIds();
                break;
            case DutyPostConstants.POST_XING_ZHEN:
                gwUserIds = u.getXingZhenUserIds();
                break;
            case DutyPostConstants.POST_WAG:
                gwUserIds = u.getWagUserIds();
                break;
            case DutyPostConstants.POST_DDG:
                gwUserIds = u.getDdgUserIds();
                break;
            case DutyPostConstants.POST_JBG:
                gwUserIds = u.getJbgUserIds();
                break;
            case DutyPostConstants.POST_JJG:
                gwUserIds = u.getJjgUserIds();
                break;
            default:
                gwUserIds = "";
                break;
        }
        List<Long> result = new ArrayList<>(0);
        if (StringUtils.isNotEmpty(gwUserIds)) {
            String[] split = gwUserIds.split(StringUtils.SEPARATOR_COMMA);
            for (String s : split) {
                String[] s1 = s.split(StringUtils.SEPARATOR_LINE);
                Long id = Long.valueOf(s1[1]);
                result.add(id);
                userIds.add(id);
            }
        }
        return result;
    }

    /**
     * 按日期分组查询
     *
     * @param dto 参数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 结果
     * @throws ServiceException 异常
     */
    public Map<String, JSONObject> groupByDate(ListByMonthDTO dto, String startTime, String endTime) throws ServiceException {
        Map<String, JSONObject> result = new HashMap<>(0);
        //查数据（仅主班值班人员）
        List<BigScreenDutyUserEntity> list = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                .ge(BigScreenDutyUserEntity::getDutyTime, startTime)
                .le(BigScreenDutyUserEntity::getDutyTime, endTime)
                .eq(StringUtils.isNotEmpty(dto.getUnitName()), BigScreenDutyUserEntity::getUnitName, dto.getUnitName())
                .eq(StringUtils.isNotEmpty(dto.getDistrictCode()), BigScreenDutyUserEntity::getDistrictCode, dto.getDistrictCode())
                .list();
        Map<Date, List<BigScreenDutyUserEntity>> collect = list
                .stream()
                .collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDutyTime));
        //排序
        LinkedHashMap<Date, List<BigScreenDutyUserEntity>> linkedHashMap = collect.entrySet().stream()
                .sorted(Comparator.comparing(Map.Entry::getKey))
                .collect(
                        Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new
                        )
                );
        // 调度岗人员
        Map<Date, List<BigScreenDutyUserEntity>> mapDdg = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                .eq(BigScreenDutyUserEntity::getNature, DutyPostConstants.POST_DDG)
                .ge(BigScreenDutyUserEntity::getDutyTime, startTime)
                .le(BigScreenDutyUserEntity::getDutyTime, endTime)
                .eq(StringUtils.isNotEmpty(dto.getUnitName()), BigScreenDutyUserEntity::getUnitName, dto.getUnitName())
                .eq(StringUtils.isNotEmpty(dto.getDistrictCode()), BigScreenDutyUserEntity::getDistrictCode, dto.getDistrictCode())
                .list()
                .stream()
                .collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDutyTime));
        for (Date date : linkedHashMap.keySet()) {
            List<BigScreenDutyUserEntity> dutyUsers = collect.get(date);
            //值班领导
            StringBuilder dutyLeader = new StringBuilder();
            //指挥长
            StringBuilder commandLeader = new StringBuilder();
            for (BigScreenDutyUserEntity dutyUser : dutyUsers) {
                if ("值班领导".equals(dutyUser.getNature())) {
                    dutyLeader.append(dutyUser.getName()).append("、");
                }
                if ("指挥长".equals(dutyUser.getNature())) {
                    commandLeader.append(dutyUser.getName()).append("、");
                }
            }
            if (StringUtils.isNotEmpty(dutyLeader.toString())) {
                dutyLeader.deleteCharAt(dutyLeader.length() - 1);
            }
            if (StringUtils.isNotEmpty(commandLeader.toString())) {
                commandLeader.deleteCharAt(commandLeader.length() - 1);
            }
            //调度岗人员
            String ddgUserNames = "";
            if (CollectionUtils.isNotEmpty(mapDdg.get(date))) {
                List<String> names = mapDdg.get(date)
                        .stream()
                        .map(BigScreenDutyUserEntity::getName)
                        .collect(Collectors.toList());
                ddgUserNames = String.join("、", names);
            }
            JSONObject object = new JSONObject();
            object.put("dutyLeader", dutyLeader.toString());
            object.put("commandLeader", commandLeader.toString());
            object.put("ddgUserNames", ddgUserNames);
            result.put(TimeUtils.dateToString(date, "yyyy-MM-dd"),
                    object);
        }
        return result;
    }

    /**
     * 全部值班情况
     *
     * @param dutyTime 值班日期
     * @return 结果
     */
    public AllDutyVO allDutyByTree(String dutyTime) {
        final String dateTime = TimeUtils.stringToString(dutyTime, TimeUtils.YYYYMMDD);
        List<BigScreenDutyUserEntity> list = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                .eq(BigScreenDutyUserEntity::getDutyTime, dateTime)
                .list();
        Map<String, List<BigScreenDutyUserEntity>> map = list
                .stream()
                .collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDistrictCode));
        Map<String, AllDutyVO> collect = new HashMap<>(0);
        for (String key : map.keySet()) {
            AllDutyVO vo = new AllDutyVO();
            vo.setDistrictCode(key);
            vo.setUnitName(map.get(key).get(0).getUnitName());
            vo.setChildren(new ArrayList<>(0));
            Tuple2<String, Integer> t = computeLevel(key);
            vo.setNodeLevel(t._2());
            List<BigScreenDutyUserEntity> users = map.get(key);
            for (BigScreenDutyUserEntity user : users) {
                DutyUserVo dutyUserVo = EntityConvertToVo.INSTANCE.entityToVo(user);
                dealUserName(dutyUserVo);
                setDutyUser(vo, dutyUserVo);
            }
            collect.put(t._1(), vo);
        }
        final String provinceCode = "510000";
        final String provinceName = "四川省";
        AllDutyVO result;
        Tuple2<String, Integer> tup = computeLevel(provinceCode);
        //根节点
        if (!collect.containsKey(tup._1())) {
            result = AllDutyVO.of(provinceCode, provinceName, tup._2());
        } else {
            result = collect.get(tup._1());
            collect.remove(tup._1());
        }
        result.setChildren(findChildren(tup._1(), collect, tup._2() + 1));
        for (AllDutyVO v : result.getChildren()) {
            Tuple2<String, Integer> tmp = computeLevel(v.getDistrictCode());
            v.setChildren(findChildren(tmp._1(), collect, tmp._2() + 1));
        }
        // 缺少第二层级节点
        if (!collect.isEmpty()) {
            //不存在市级值班信息的区县值班人员信息 补充空节点（实际不可能出现）
            List<Object> districtCodes = collect.keySet()
                    .stream()
                    .map(k -> (Object) (k.substring(0, 4) + "00"))
                    .collect(Collectors.toList());
            List<DistrictDto> byDistrictCodes = dictService.getByDistrictCodes(districtCodes);
            Map<String, AllDutyVO> map2 = new HashMap<>(0);
            for (DistrictDto dto : byDistrictCodes) {
                AllDutyVO vo = AllDutyVO.of(dto.getCode(), dto.getName(), 2);
                result.getChildren().add(vo);
                map2.put(dto.getCode(), vo);
            }
            for (String k : collect.keySet()) {
                String cityCode = k.substring(0, 4) + "00";
                map2.get(cityCode).getChildren().add(collect.get(k));
            }
        }
        return result;
    }

    private void setDutyUser(AllDutyVO vo, DutyUserVo user) {
        switch (user.getNature()) {
            case "值班领导":
                if (DutyUserVo.ZHUBAN.equals(user.getLevel())) {
                    vo.setZbldzb(user);
                } else {
                    vo.setZbldfb(user);
                }
                break;
            case "指挥长":
                if (DutyUserVo.ZHUBAN.equals(user.getLevel())) {
                    vo.setZhzzb(user);
                } else {
                    vo.setZhzfb(user);
                }
                break;
            default:
                break;
        }
    }

    private List<AllDutyVO> findChildren(String key, Map<String, AllDutyVO> map, Integer level) {
        List<AllDutyVO> result = new ArrayList<>(0);
        Map<String, AllDutyVO> tmp = new HashMap<>(map);
        for (String k : tmp.keySet()) {
            if (k.startsWith(key)
                    && level.equals(tmp.get(k).getNodeLevel())) {
                result.add(tmp.get(k));
                map.remove(k);
            }
        }
        return result;
    }

    private Tuple2<String, Integer> computeLevel(String key) {
        //通过地域编码解析级别 省 市 区县
        int len = key.length();
        for (int i = key.length() - 1; i > 1; i--) {
            if (key.charAt(i) == '0'
                    && key.charAt(i - 1) == '0') {
                len = i - 1;
            } else {
                break;
            }
        }
        String code = key.substring(0, len);
        return new Tuple2<>(code, code.length() / 2);
    }

    /**
     * 检查该值班日是否已存在值班人员信息
     *
     * @param dutyTime 值班日期
     * @param districtCode 地区编码
     * @throws ServiceException 异常
     */
    public void judgeRepeated(String dutyTime, String districtCode) throws ServiceException {
        List<BigScreenDutyUserEntity> list = getBigScreenDutyUserMapper().selectList(
                new QueryWrapper<BigScreenDutyUserEntity>()
                        .lambda()
                        .eq(BigScreenDutyUserEntity::getDutyTime, dutyTime)
                        .eq(BigScreenDutyUserEntity::getDistrictCode, districtCode)
        );
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ServiceException("日期[" + dutyTime + "]已存在值班人员信息，跳过导入");
        }
    }

    /**
     * 按值班日期查询值班人员信息
     *
     * @param dutyTime 日期
     * @return 结果
     */
    public List<CityDutyExcelVO> findByDutyTime(String dutyTime) {
        List<BigScreenDutyUserEntity> list = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                .eq(BigScreenDutyUserEntity::getDutyTime, dutyTime)
                .list();
        return buildVoV2(list, false);
    }

    private List<CityDutyExcelVO> buildVoV2(List<BigScreenDutyUserEntity> list, Boolean containDdg) {
        // 优化之后无需反查人员
        // 根据单位分组
        Map<String, List<BigScreenDutyUserEntity>> map = list
                .stream()
                .collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDistrictCode));
        List<CityDutyExcelVO> results = new ArrayList<>(0);
        for (String k : map.keySet()) {
            CityDutyExcelVO vo = new CityDutyExcelVO();
            BigScreenDutyUserEntity first = map.get(k).get(0);
            vo.setCrTime(TimeUtils.dateToString(first.getCrTime(), TimeUtils.YYYYMMDD_HHMMSS));
            vo.setUnitName(first.getUnitName());
            vo.setDutyPhone(first.getDutyPhone());
            vo.setDutyTime(TimeUtils.dateToString(first.getDutyTime(), "yyyy-MM-dd"));
            List<BigScreenDutyUserEntity> listByCode = map.get(k);
            if (containDdg) {
                UserDto user = new UserDto();
                Optional<BigScreenDutyUserEntity> first1 = listByCode
                        .stream()
                        .filter(it -> DutyPostConstants.POST_DDG.equals(it.getNature()))
                        .findFirst();
                first1.ifPresent(it -> {
                    user.setRealName(it.getName());
                    user.setDuty(it.getDuty());
                    user.setTel(it.getDh());
                    vo.setFirstDdgUser(user);
                });
            }
            for (BigScreenDutyUserEntity e : map.get(k)) {
                buildDutyUser(vo, e);
            }
            //返回数据唯一ID
            List<String> ids = map.get(k).stream().map(it -> it.getId().toString()).collect(Collectors.toList());
            vo.setDataIds(String.join(",", ids));
            results.add(vo);
        }
        return results;
    }

    private List<CityDutyExcelVO> buildVo(List<BigScreenDutyUserEntity> list, Boolean containDdg) {
        //获取值班人员职位
        List<Long> userIds = list.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getUniqueSign()))
                .map(e -> {
                    String t = e.getUniqueSign().split("-")[1];
                    return Long.parseLong(t);
                })
                .collect(Collectors.toList());
        if (containDdg) {
            for (BigScreenDutyUserEntity entity : list) {
                if (StringUtils.isEmpty(entity.getDdgUserIds())) {
                    continue;
                }
                userIds.addAll(Arrays.stream(entity.getDdgUserIds().split(","))
                        .map(it -> {
                            String id = it.split("-")[1];
                            return Long.parseLong(id);
                        }).collect(Collectors.toList()));
            }
        }
        Map<Long, UserDto> userMap = permissionService.getUserListById(userIds)
                .stream()
                .collect(Collectors.toMap(UserDto::getId, a -> a));
        //根据单位分组
        Map<String, List<BigScreenDutyUserEntity>> map = list
                .stream()
                .collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDistrictCode));
        List<CityDutyExcelVO> results = new ArrayList<>(0);
        for (String k : map.keySet()) {
            CityDutyExcelVO vo = new CityDutyExcelVO();
            BigScreenDutyUserEntity first = map.get(k).get(0);
            vo.setUnitName(first.getUnitName());
            vo.setDutyPhone(first.getDutyPhone());
            vo.setDutyTime(TimeUtils.dateToString(first.getDutyTime(), "yyyy-MM-dd"));
            if (containDdg) {
                dealDdgUser(vo, first, userMap);
            }
            for (BigScreenDutyUserEntity e : map.get(k)) {
                buildDutyUser(vo, e);
            }
            //返回数据唯一ID
            List<String> ids = map.get(k).stream().map(it -> it.getId().toString()).collect(Collectors.toList());
            vo.setDataIds(String.join(",", ids));
            results.add(vo);
        }
        return results;
    }

    private void dealDdgUser(CityDutyExcelVO vo, BigScreenDutyUserEntity first, Map<Long, UserDto> userMap) {
        vo.setDistrictCode(first.getDistrictCode());
        vo.setCrTime(TimeUtils.dateToString(first.getCrTime(), TimeUtils.YYYYMMDD_HHMMSS));
        if (StringUtils.isEmpty(first.getDdgUserIds())) {
            return;
        }
        List<Long> ddgUserIds = Arrays.stream(first.getDdgUserIds().split(","))
                .map(it -> {
                    String id = it.split("-")[1];
                    return Long.parseLong(id);
                }).collect(Collectors.toList());
        vo.setFirstDdgUser(userMap.get(ddgUserIds.get(0)));
        List<String> names = userMap.keySet().stream()
                .filter(ddgUserIds::contains)
                .map(it -> userMap.get(it).getRealName())
                .collect(Collectors.toList());
        vo.setZhddzbName(String.join(",", names));
    }

    private void buildDutyUser(CityDutyExcelVO vo, BigScreenDutyUserEntity e) {
        if (!DutyUserVo.ZHI_BAN_LING_DAO.equals(e.getNature())
                && !DutyUserVo.ZHI_HUI_HANG.equals(e.getNature())) {
            return;
        }
        if (DutyUserVo.ZHI_BAN_LING_DAO.equals(e.getNature())) {
            if (DutyUserVo.ZHUBAN.equals(e.getLevel())) {
                vo.setZbldZbName(e.getName());
                vo.setZbldZbPhone(e.getDh());
                vo.setZbldZbPost(e.getDuty());
            } else {
                vo.setZbldFbName(e.getName());
                vo.setZbldFbPhone(e.getDh());
                vo.setZbldFbPost(e.getDuty());
            }
        } else if (DutyUserVo.ZHI_HUI_HANG.equals(e.getNature())) {
            if (DutyUserVo.ZHUBAN.equals(e.getLevel())) {
                vo.setZhzZbName(e.getName());
                vo.setZhzZbPhone(e.getDh());
                vo.setZhzZbPost(e.getDuty());
            } else {
                vo.setZhzFbName(e.getName());
                vo.setZhzFbPhone(e.getDh());
                vo.setZhzFbPost(e.getDuty());
            }
        }
    }

    /**
     * 值班计划列表
     *
     * @param map 参数
     * @param sortParams 参数
     * @return 结果
     */
    public List<DutyPlanDataVO> dutyPlanList(Map<String, Object> map, SortParams sortParams) {
        String dataIds = (String) map.get("dataIds");
        //地域断码 多值处理
        final String districtCode = "district_code";
        List<String> codes = new ArrayList<>(0);
        if (map.containsKey(districtCode)) {
            String value = (String) map.get(districtCode);
            codes = Arrays.asList(value.split(","));
        }
        List<BigScreenDutyUserEntity> list;
        if (StringUtils.isNotEmpty(dataIds)) {
            list = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                    .in(BigScreenDutyUserEntity::getId, Arrays.asList(dataIds.split(",")))
                    .list();
        } else {
            // XMKFB-6568 修改为只查本级的数据
            list = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                    .ge(map.containsKey("startTime"), BigScreenDutyUserEntity::getDutyTime, map.get("startTime"))
                    .le(map.containsKey("endTime"), BigScreenDutyUserEntity::getDutyTime, map.get("endTime"))
                    .in(CollectionUtils.isNotEmpty(codes), BigScreenDutyUserEntity::getDistrictCode, codes)
                    .list();
        }
        Map<Date, List<BigScreenDutyUserEntity>> dutyTimeMap = list
                .stream()
                .collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDutyTime));
        List<DutyPlanDataVO> result = new ArrayList<>(0);
        for (Date k : dutyTimeMap.keySet()) {
            List<BigScreenDutyUserEntity> listOfDutyTime = dutyTimeMap.get(k);
            List<CityDutyExcelVO> vos = buildVoV2(listOfDutyTime, true);
            result.addAll(vos.stream().map(e -> {
                DutyPlanDataVO vo = new DutyPlanDataVO();
                BeanUtils.copyProperties(e, vo);
                if (Objects.isNull(map.get("exportMark"))
                        || !(Boolean) map.get("exportMark")) {
                    vo.setFirstDdgUser(null);
                }
                return vo;
            }).collect(Collectors.toList()));
        }
        List<DutyPlanDataVO> tmp = result.stream()
                .filter(it -> filterByName(it, (String) map.get("name")))
                .filter(this::filterNullData)
                .sorted(getSortField(sortParams))
                .collect(Collectors.toList());
        if (sortParams != null && sortParams.isDesc()) {
            Collections.reverse(tmp);
        }
        return tmp;
    }

    private boolean filterNullData(DutyPlanDataVO it) {
        return !(StringUtils.isEmpty(it.getZbldZbName())
                && StringUtils.isEmpty(it.getZbldFbName())
                && StringUtils.isEmpty(it.getZhzZbName())
                && StringUtils.isEmpty(it.getZhzFbName())
                && StringUtils.isEmpty(it.getZhddzbName()));
    }

    private Comparator<? super DutyPlanDataVO> getSortField(SortParams sortParams) {
        if (sortParams != null) {
            switch (sortParams.getSortField()) {
                case "duty_time":
                    return Comparator.comparing(DutyPlanDataVO::getDutyTime);
                case "cr_time":
                    return Comparator.comparing(DutyPlanDataVO::getCrTime);
                default:
                    log.info("暂不支持的排序字段：{}", sortParams.getSortField());
                    break;
            }
        }
        return Comparator.comparing(DutyPlanDataVO::getDutyTime);
    }

    private boolean filterByName(DutyPlanDataVO it, String name) {
        if (StringUtils.isEmpty(name)) {
            return true;
        }
        if (StringUtils.isNotEmpty(it.getZbldZbName())
                && it.getZbldZbName().contains(name)) {
            return true;
        }
        if (StringUtils.isNotEmpty(it.getZbldFbName())
                && it.getZbldFbName().contains(name)) {
            return true;
        }
        if (StringUtils.isNotEmpty(it.getZhzZbName())
                && it.getZhzZbName().contains(name)) {
            return true;
        }
        if (StringUtils.isNotEmpty(it.getZhzFbName())
                && it.getZhzFbName().contains(name)) {
            return true;
        }
        if (StringUtils.isNotEmpty(it.getZhddzbName())
                && it.getZhddzbName().contains(name)) {
            return true;
        }
        return false;
    }

    /**
     * 覆盖值班信息
     *
     * @param dto 参数
     */
    public void coverDutyInfo(CoverDutyInfoDTO dto) {
        // 需要覆盖的天数
        final int numDay = TimeUtils.getNumDay(dto.getStartTime(), dto.getEndTime()) + 1;
        // 查源数据
        Map<Date, List<BigScreenDutyUserEntity>> collect = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                .eq(BigScreenDutyUserEntity::getDistrictCode, dto.getDistrictCode())
                .ge(BigScreenDutyUserEntity::getDutyTime, dto.getStartTime())
                .le(BigScreenDutyUserEntity::getDutyTime, dto.getEndTime())
                .list()
                .stream()
                .collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDutyTime));
        //
        Date newStartTime = getNewDate(numDay, TimeUtils.stringToDate(dto.getStartTime(), TimeUtils.YYYYMMDD), dto.getCoverForward());
        Date newEndTime = getNewDate(numDay, TimeUtils.stringToDate(dto.getEndTime(), TimeUtils.YYYYMMDD), dto.getCoverForward());
        List<String> dateList = TimeUtils.getDateList(
                TimeUtils.dateToString(newStartTime, TimeUtils.YYYYMMDD),
                TimeUtils.dateToString(newEndTime, TimeUtils.YYYYMMDD),
                TimeUtils.YYYYMMDD);
        for (Date k : collect.keySet()) {
            // 覆盖之前  先删除原有的数据
            Date newDate = getNewDate(numDay, k, dto.getCoverForward());
            dateList.remove(TimeUtils.dateToString(newDate, TimeUtils.YYYYMMDD));
            deletedOldData(newDate, dto.getDistrictCode(), "", "", "");
            List<BigScreenDutyUserEntity> list = collect.get(k);
            for (BigScreenDutyUserEntity entity : list) {
                BigScreenDutyUserEntity newEntity = new BigScreenDutyUserEntity();
                BeanUtils.copyProperties(entity, newEntity);
                newEntity.setId(null);
                newEntity.setDutyTime(newDate);
                bigScreenDutyUserMapper.insert(newEntity);
            }
        }
        // 清空对应日期的值班信息
        for (String date : dateList) {
            new LambdaUpdateChainWrapper<>(getBigScreenDutyUserMapper())
                    .eq(BigScreenDutyUserEntity::getDistrictCode, dto.getDistrictCode())
                    .eq(BigScreenDutyUserEntity::getDutyTime, TimeUtils.stringToDate(date, TimeUtils.YYYYMMDD))
                    .remove();
        }
    }

    private void deletedOldData(Date newDate, String districtCode, String postName, String uniqueSign, String level) {
        new LambdaUpdateChainWrapper<>(getBigScreenDutyUserMapper())
                .eq(BigScreenDutyUserEntity::getDistrictCode, districtCode)
                .eq(BigScreenDutyUserEntity::getDutyTime, newDate)
                .eq(StringUtils.isNotEmpty(postName), BigScreenDutyUserEntity::getNature, postName)
                .eq(StringUtils.isNotEmpty(uniqueSign), BigScreenDutyUserEntity::getUniqueSign, uniqueSign)
                .eq(StringUtils.isNotEmpty(level), BigScreenDutyUserEntity::getLevel, level)
                .remove();
    }

    private Date getNewDate(int numDay, Date k, Boolean coverForward) {
        String dutyTime;
        if (coverForward) {
            // 往后（明天之后）
            dutyTime = TimeUtils.dateBefOrAft(k, -numDay, TimeUtils.YYYYMMDD);
        } else {
            // 往前（昨天之前）
            dutyTime = TimeUtils.dateBefOrAft(k, numDay, TimeUtils.YYYYMMDD);
        }
        return TimeUtils.stringToDate(dutyTime, TimeUtils.YYYYMMDD);
    }

    /**
     * 指定日期值班信息
     *
     * @param dutyTime 参数
     * @param districtCode  参数
     * @return 结果
     */
    public List<DutyUserInfoVO> dutyUserInfo(String dutyTime, String districtCode) {
        List<BigScreenDutyUserEntity> list = new LambdaQueryChainWrapper<>(getBigScreenDutyUserMapper())
                .eq(BigScreenDutyUserEntity::getDistrictCode, districtCode)
                .eq(BigScreenDutyUserEntity::getDutyTime, dutyTime)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<DutyUserInfoVO> result = new ArrayList<>(0);
        for (BigScreenDutyUserEntity entity : list) {
            DutyUserInfoVO vo = new DutyUserInfoVO();
            // nature 岗位名称
            vo.setPostName(entity.getNature());
            vo.setLevel(entity.getLevel());
            if (StringUtils.isNotEmpty(entity.getUniqueSign())) {
                String[] split = entity.getUniqueSign().split("-");
                vo.setDeptId(Long.valueOf(split[0]));
                vo.setUserId(Long.valueOf(split[1]));
            }
            vo.setXm(entity.getName());
            vo.setJh(entity.getJh());
            vo.setSjh(entity.getDh());
            // 值班表加一个字段 职务
            vo.setDuty(entity.getDuty());
            result.add(vo);
        }
        return result;
    }

    /**
     * 修改值班信息
     *
     * @param dto 参数
     */
    public void editDutyInfo(EditDutyUserInfoDTO dto) {
        final String uniqueSign = String.format("%s-%s", dto.getDeptId(), dto.getUserId());
        if (dto.getDeleted()) {
            new LambdaUpdateChainWrapper<>(getBigScreenDutyUserMapper())
                    .eq(BigScreenDutyUserEntity::getDistrictCode, dto.getDistrictCode())
                    .eq(BigScreenDutyUserEntity::getDutyTime, dto.getDutyTime())
                    .eq(BigScreenDutyUserEntity::getUniqueSign, uniqueSign)
                    .eq(BigScreenDutyUserEntity::getNature, dto.getPostName())
                    .eq(StringUtils.isNotEmpty(dto.getLevel()), BigScreenDutyUserEntity::getLevel, dto.getLevel())
                    .remove();
        } else {
            PreConditionCheck.checkNotEmpty(dto.getUnitName(), "新增时，当前部门名称不能为空！");
            Date dutyDate = TimeUtils.stringToDate(dto.getDutyTime(), TimeUtils.YYYYMMDD);
            if (ZHI_HUI_ZHANG.equals(dto.getPostName())
                    || ZHI_BAN_LING_DAO.equals(dto.getPostName())) {
                // 指挥长的主班副班分别只有一个  值班领导同理
                deletedOldData(dutyDate, dto.getDistrictCode(), dto.getPostName(), "", dto.getLevel());
            } else {
                // 删除已有的
                deletedOldData(dutyDate, dto.getDistrictCode(), dto.getPostName(), uniqueSign, dto.getLevel());
            }
            BigScreenDutyUserEntity entity = new BigScreenDutyUserEntity();
            entity.setCrTime(new Date());
            entity.setUpdateTime(new Date());
            entity.setDutyTime(dutyDate);
            entity.setUnitName(dto.getUnitName());
            entity.setDistrictCode(dto.getDistrictCode());
            // 赋值
            entity.setUniqueSign(uniqueSign);
            entity.setName(dto.getXm());
            entity.setJh(dto.getJh());
            entity.setDh(dto.getSjh());
            entity.setDuty(dto.getDuty());
            entity.setNature(dto.getPostName());
            entity.setLevel(Optional.ofNullable((dto.getLevel())).orElse(""));
            bigScreenDutyUserMapper.insert(entity);
        }
    }

    /**
     * 批量删除值班信息
     *
     * @param dto 参数
     */
    public void deleteDutyInfo(CoverDutyInfoDTO dto) {
        new LambdaUpdateChainWrapper<>(getBigScreenDutyUserMapper())
                .eq(BigScreenDutyUserEntity::getDistrictCode, dto.getDistrictCode())
                .ge(BigScreenDutyUserEntity::getDutyTime, TimeUtils.stringToDate(dto.getStartTime(), TimeUtils.YYYYMMDD))
                .le(BigScreenDutyUserEntity::getDutyTime, TimeUtils.stringToDate(dto.getEndTime(), TimeUtils.YYYYMMDD))
                .remove();
    }
}
