package com.trs.police.bigscreen.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.police.bigscreen.domain.dto.*;
import com.trs.police.bigscreen.domain.vo.*;
import com.trs.police.bigscreen.vo.*;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.ImportResultVO;
import com.trs.police.common.core.vo.NameNumTreeVo;
import com.trs.police.common.core.vo.fight.JingYuanVO;
import com.trs.police.device.domain.dto.DeviceSearchDTO;
import com.trs.police.device.domain.vo.DeviceVO;
import com.trs.police.zg.vo.BaseJingwuSourceVo;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.Tuple2;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 自贡大屏相关
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-07 10:49:12
 */
public interface IBigScreenService {

    /**
     * dutyUsers<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/15 11:25
     */
    List<DutyUserResultVo> dutyUsers(DutyUserDTO dto) throws ServiceException;

    /**
     * modifyDutyUsers<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/14 17:56
     */
    Report<String> modifyDutyUsers(ModifyDutyUsersDTO dto) throws ServiceException;

    /**
     * addDutyUser<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/14 20:17
     */
    Report<String> addDutyUser(ModifyDutyUserDTO dto) throws ServiceException;

    /**
     * pinData<BR>
     *
     * @param pin 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:11
     */
    Report<String> pinData(List<PinDataDTO> pin) throws ServiceException;

    /**
     * unPinData<BR>
     *
     * @param pin 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:48
     */
    Report<String> unPinData(List<UnPinDataDTO> pin) throws ServiceException;

    /**
     * finishPinData<BR>
     *
     * @param ids 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:55
     */
    Report<String> finishPinData(String ids) throws ServiceException;

    /**
     * pinDataList<BR>
     *
     * @param request 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 17:17
     */
    RestfulResultsV2<PinDataVo> pinDataList(ListParamsRequest request) throws ServiceException;


    /**
     * 添加工作日志
     *
     * @param dto 请求参数
     * @return {@link Report }<{@link String }>
     * @throws ServiceException 相关异常
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-06-07 14:39:41
     */
    Report<String> addWorkLog(List<AddWorkLogDTO> dto) throws ServiceException;

    /**
     * 列表查询
     *
     * @param paramsRequest 请求参数
     * @return {@link List }<{@link WorkListVO }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-06-07 14:39:43
     */
    Page<WorkListVO> workLogList(ListParamsRequest paramsRequest) throws ServiceException;

    /**
     * 导出
     *
     * @param dto 请求参数
     * @return {@link Tuple2 }<{@link String },{@link byte }>
     * @throws ServiceException ServiceLogicException服务逻辑异常
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-06-07 14:39:45
     */
    Tuple2<String, byte[]> exportWorkLog(ExportWordDTO dto) throws ServiceException;

    /**
     * jingYuanList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/27 16:23
     */
    RestfulResultsV2<JingYuanVo> jingYuanList(JingYuanSearchDTO dto) throws ServiceException;

    /**
     * jingYuanCount<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/27 18:16
     */
    List<NameNumTreeVo> jingYuanCount(JingYuanSearchDTO dto) throws ServiceException;

    /**
     * jingCheList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/27 16:23
     */
    RestfulResultsV2<JingCheVo> jingCheList(JingCheSearchDTO dto) throws ServiceException;

    /**
     * jingCheCount<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/27 18:16
     */
    List<NameNumTreeVo> jingCheCount(JingCheSearchDTO dto) throws ServiceException;

    /**
     * 获取警员列表（警号）
     *
     * @param geometries 地图信息
     * @return 警号
     * @throws ServiceException 异常
     */
    List<JingYuanVO> jingYuanListByArea(String geometries) throws ServiceException;

    /**
     * @param dto AreaSearchDTO
     * @return {@link RestfulResultsV2 }<{@link AreaInfoVO }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-25 15:07:43
     */
    RestfulResultsV2<AreaInfoVO> areaList(AreaSearchDTO dto);

    /**
     * @param dto AreaSearchDTO
     * @return {@link List }<{@link NameNumTreeVo }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-25 15:07:45
     */
    List<NameNumTreeVo> areaCount(AreaSearchDTO dto);

    /**
     * 按自然月返回值班人员列表
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    Map<String, JSONObject> listByMonth(ListByMonthDTO dto) throws ServiceException;

    /**
     * 全部值班情况
     *
     * @param dutyTime 值班日期
     * @return 结果
     * @throws ServiceException 异常
     */
    AllDutyVO allDutyByTree(String dutyTime) throws ServiceException;

    /**
     * 值班人员台账批量导入
     *
     * @param file           导入的excel文件
     * @param repeatStrategy 是否覆盖已有数据
     * @return 结果
     * @throws ServiceException 异常
     */
    ImportResultVO batchImport(MultipartFile file, Boolean repeatStrategy) throws ServiceException;

    /**
     * 值班人员台账批量导入模板下载
     *
     * @param response response
     * @throws IOException IOException
     */
    void downloadImportTemplate(HttpServletResponse response) throws IOException;

    /**
     * 全部值班情况导出
     *
     * @param response 参数
     * @param dutyTime 值班日期
     * @throws ServiceException 异常
     */
    void allDutyExport(HttpServletResponse response, String dutyTime) throws ServiceException;

    /**
     * 值班计划列表
     *
     * @param paramsRequest 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    List<DutyPlanDataVO> dutyPlanList(ListParamsRequest paramsRequest) throws ServiceException;

    /**
     * 全部值班情况导出
     *
     * @param response      参数
     * @param paramsRequest 参数
     * @throws ServiceException 异常
     */
    void dutyPlanExport(HttpServletResponse response, ListParamsRequest paramsRequest) throws ServiceException;

    /**
     * 获取警务资源数据
     *
     * @param type 类型
     * @param dto  参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    List<BaseJingwuSourceVo> jingWuSourceList(String type, JingWuSourceDTO dto) throws ServiceException;

    /**
     * 更多人员信息
     *
     * @param dutyTime 日期
     * @param districtCode 短码
     * @return 结果
     * @throws ServiceException 异常
     */
    MorePersonInfo selectMore(String dutyTime, String districtCode) throws ServiceException;

    /**
     * 设备信息列表
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    RestfulResultsV2<DeviceVO> deviceList(DeviceSearchDTO dto) throws ServiceException;

    /**
     * 更新设备在线状态
     *
     * @param deviceNos 参数
     * @param online 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    String updateDeviceOnline(String deviceNos, Integer online) throws ServiceException;

    /**
     * 设备状态同步
     *
     * @throws ServiceException 异常
     */
    void syncDeviceStatusTask() throws ServiceException;

    /**
     *
     * @param types 类型
     * @param startTime 开始时间
     * @return 结果
     */
    List<CountVO> jwzyCount(String types, String startTime) throws ServiceException;

    /**
     * 批量修改值班人员信息
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    String coverDutyInfo(CoverDutyInfoDTO dto) throws ServiceException;

    /**
     * 值班人员信息列表
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    List<BufferUserInfoVO> dutyUserList(BufferUserDTO dto) throws ServiceException;

    /**
     * 新增值班人员信息缓存记录
     *
     * @param dtos 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    String addDutyUserBuffer(List<DutyUserBufferDTO> dtos) throws ServiceException;

    /**
     * 获取指定日期值班人员信息
     *
     * @param dutyTime 值班日期
     * @param districtCode  地域短码
     * @return 结果
     * @throws ServiceException 异常
     */
    List<DutyUserInfoVO> dutyUserInfo(String dutyTime, String districtCode) throws ServiceException;

    /**
     * 删除缓存信息
     *
     * @param ids 参数
     * @return 结果
     */
    String deleteDutyUserBuffer(String ids);

    /**
     * 编辑值班人员信息
     *
     * @param dtos  参数
     * @return 结果
     * @throws ServiceException 异常
     */
    String editDutyInfo(List<EditDutyUserInfoDTO> dtos) throws ServiceException;

    /**
     * 删除值班人员信息
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    String deleteDutyInfo(CoverDutyInfoDTO dto) throws ServiceException;
}
