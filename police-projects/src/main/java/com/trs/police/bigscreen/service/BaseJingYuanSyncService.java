package com.trs.police.bigscreen.service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.trs.police.bigscreen.domain.entity.BigScreenJingYuanEntity;
import com.trs.police.bigscreen.mapper.BigScreenJingYuanMapper;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.zg.utils.QinWuUtils;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：数据同步服务
 *
 * @param <IN> 输入参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/5 17:54
 * @since 1.0
 */
@Getter
public abstract class BaseJingYuanSyncService<IN> extends BaseSyncService<IN, BigScreenJingYuanEntity> {

    protected final QinWuUtils qinWuUtils;

    private final BigScreenJingYuanMapper jingYuanMapper;

    public BaseJingYuanSyncService(
            BigScreenJingYuanMapper jingYuanMapper,
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(syncTaskMapper, redisTemplate);
        this.jingYuanMapper = jingYuanMapper;
        this.qinWuUtils = QinWuUtils.getInstance();
    }

    /**
     * beforeSave<BR>
     */
    @Override
    public void beforeSave() {
        var ids = getJingYuanMapper().findZaiXianIds();
        if (CollectionUtils.isNotEmpty(ids)) {
            // 先标记离线
            new LambdaUpdateChainWrapper<>(getJingYuanMapper())
                    .set(BigScreenJingYuanEntity::getCjzt, 0)
                    .set(BigScreenJingYuanEntity::getBbzt, 0)
                    .set(BigScreenJingYuanEntity::getZxzt, 0)
                    .set(BigScreenJingYuanEntity::getStatus, 0)
                    .in(BigScreenJingYuanEntity::getId, ids)
                    .update();
        }
    }

    /**
     * save<BR>
     *
     * @param entity 参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:04
     */
    @Override
    public void save(BigScreenJingYuanEntity entity) {
        var opt = new LambdaQueryChainWrapper<>(jingYuanMapper)
                .eq(BigScreenJingYuanEntity::getJh, entity.getJh())
                .oneOpt();
        entity.setUpdateTime(new Date());
        if (opt.isPresent()) {
            entity.setId(opt.get().getId());
            entity.setCrTime(opt.get().getCrTime());
            jingYuanMapper.updateById(entity);
        } else {
            jingYuanMapper.insert(entity);
        }
        jingYuanMapper.updateDqwzPoint(entity.getId());
    }
}
