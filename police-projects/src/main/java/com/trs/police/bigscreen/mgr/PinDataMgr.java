package com.trs.police.bigscreen.mgr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.bigscreen.convert.EntityConvertToVo;
import com.trs.police.bigscreen.domain.dto.PinDataDTO;
import com.trs.police.bigscreen.domain.dto.UnPinDataDTO;
import com.trs.police.bigscreen.vo.PinDataVo;
import com.trs.police.common.core.constant.SystemConstant;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.BigScreenPinDataEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.mapper.BigScreenPinDataMapper;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.DateUtil;
import com.trs.police.common.core.utils.FieldUtils;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/7 16:17
 * @since 1.0
 */
@Component
public class PinDataMgr {

    @Resource
    private BigScreenPinDataMapper pinDataMapper;

    @Resource
    private PermissionService permissionService;

    /**
     * pinData<BR>
     *
     * @param currentUser 参数
     * @param dto         参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:18
     */
    public Report<String> pinData(CurrentUser currentUser, PinDataDTO dto) throws ServiceException {
        dto.isValid();
        BigScreenPinDataEntity entity = findEntity(currentUser, dto)
                .orElse(dto.toEntity(currentUser));
        entity.fillAuditFields(currentUser);
        entity.setPinFlag(dto.getPinFlag());
        entity.setShowTitle(dto.getShowTitle());
        JSONObject customContent;
        if (JsonUtils.isValidObject(dto.getCustomContent())) {
            customContent = JSON.parseObject(dto.getCustomContent());
        } else {
            customContent = new JSONObject();
        }
        customContent.put(SystemConstant.PIN_DATA_TIME, dto.getDataTime());
        if (!Objects.equals(SystemConstant.CN_JIN_QI, dto.getDataTime())) {
            entity.setDataTime(DateUtil.utcToLocalDateTime(dto.getDataTime(), false));
        }
        entity.setCustomContent(customContent.toJSONString());
        entity.setStartTime(DateUtil.utcToLocalDateTime(dto.getStartTime(), false));
        entity.setEndTime(DateUtil.utcToLocalDateTime(dto.getEndTime(), false));
        entity.setJd(dto.getJd());
        entity.setWd(dto.getWd());
        if (Optional.ofNullable(entity.getId()).filter(it -> it > 0L).isPresent()) {
            pinDataMapper.updateById(entity);
        } else {
            pinDataMapper.insert(entity);
        }
        return new Report<>("上大屏的内容", "成功操作");
    }

    /**
     * unPinData<BR>
     *
     * @param currentUser 参数
     * @param dto         参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:51
     */
    public Report<String> unPinData(CurrentUser currentUser, UnPinDataDTO dto) throws ServiceException {
        dto.isValid();
        findEntity(currentUser, dto).ifPresent(it -> {
            it.setPinFlag(0);
            it.fillAuditFields(currentUser);
            pinDataMapper.updateById(it);
        });
        return new Report<>("上大屏的内容", "成功操作");
    }

    /**
     * finishPinData<BR>
     *
     * @param currentUser 参数
     * @param id          参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:58
     */
    public Report<String> finishPinData(CurrentUser currentUser, Long id) throws ServiceException {
        var entity = pinDataMapper.selectById(id);
        if (entity != null && Objects.equals(entity.getPinFlag(), 1)) {
            checkArgument(
                    Objects.equals(entity.getDistrictCode(), currentUser.getDept().getDistrictCode()),
                    new ServiceException("不能跨地域处理数据")
            );
            new LambdaUpdateChainWrapper<>(pinDataMapper)
                    .set(BigScreenPinDataEntity::getPinFlag, 0)
                    .eq(BigScreenPinDataEntity::getId, id)
                    .update();
        }
        return new Report<>("上大屏的内容", "成功操作");
    }

    /**
     * pinDataList<BR>
     *
     * @param currentUser 参数
     * @param request     参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 17:31
     */
    public RestfulResultsV2<PinDataVo> pinDataList(CurrentUser currentUser, ListParamsRequest request) {
        final QueryWrapper<BigScreenPinDataEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("district_code", currentUser.getDept().getDistrictCode());
        request.getFilterParams()
                .stream()
                .map(FieldUtils::reBuildFilter)
                .forEach(param -> {
                    switch (StringUtils.showEmpty(param.getKey())) {
                        case SystemConstant.TIME_PARAMS:
                            TimeParams timeParams = (TimeParams) param.getProcessedValue();
                            queryWrapper.between(!timeParams.isAll(), param.getKey(), timeParams.getBeginTime(), timeParams.getEndTime());
                            break;
                        case SystemConstant.IN:
                        case SystemConstant.ARRAY:
                            List inData = (List) param.getProcessedValue();
                            queryWrapper.in(CollectionUtils.isNotEmpty(inData), param.getKey(), inData);
                            break;
                        case SystemConstant.ARRAY_FIND_IN_SET:
                        case SystemConstant.FIND_IN_SET:
                            List findInSet = (List) param.getProcessedValue();
                            queryWrapper.and(CollectionUtils.isNotEmpty(findInSet), or -> {
                                if (CollectionUtils.isNotEmpty(findInSet)) {
                                    String sql = String.format("FIND_IN_SET({0}, %s)", param.getKey());
                                    for (int i = 0; i < findInSet.size(); i++) {
                                        var it = findInSet.get(i);
                                        or.or(i > 0).apply(sql, it);
                                    }
                                }
                            });
                            break;
                        default:
                            queryWrapper.eq(param.getKey(), param.getValue());
                    }
                });
        final SortParams sortParams = request.getSortParams();
        if (Objects.nonNull(sortParams) && StringUtils.isNotEmpty(sortParams.getSortField())) {
            queryWrapper.orderBy(
                    StringUtils.isNotEmpty(sortParams.getSortField()),
                    sortParams.isAsc(),
                    sortParams.getSortField()
            );
        } else {
            //默认按照业务时间倒序
            queryWrapper.orderByDesc("data_time");
        }
        final Page<BigScreenPinDataEntity> page = request.getPageParams().toPage();
        var data = pinDataMapper.selectPage(page, queryWrapper);
        final List<Long> deptIds = new ArrayList<>(0);
        final List<Long> userIds = new ArrayList<>(0);
        data.getRecords().forEach(it -> {
            deptIds.add(it.getCreateDeptId());
            deptIds.add(it.getUpdateDeptId());
            userIds.add(it.getCreateUserId());
            userIds.add(it.getUpdateUserId());
        });
        final Map<Long, SimpleDeptVO> dept = new HashMap<>(deptIds.size());
        final Map<Long, String> user = new HashMap<>(userIds.size());
        if (!deptIds.isEmpty()) {
            for (DeptDto it : permissionService.getDeptByIds(deptIds)) {
                dept.put(it.getId(), it.toSimpleVO());
            }
        }
        if (!userIds.isEmpty()) {
            for (UserDto u : permissionService.getUserListById(userIds)) {
                user.put(u.getId(), StringUtils.showEmpty(u.getRealName(), u.getUsername()));
            }
        }
        return RestfulResultsV2.ok(
                        data.getRecords()
                                .stream()
                                .map(it -> {
                                    var vo = EntityConvertToVo.INSTANCE.entityToVo(it);
                                    if (Objects.nonNull(it.getDataTime())) {
                                        vo.setDataTime(DateUtil.dateTimeToString(it.getDataTime()));
                                    } else if (JsonUtils.isValidObject(it.getCustomContent())) {
                                        Optional.ofNullable(JSON.parseObject(it.getCustomContent()))
                                                .map(j -> j.getString(SystemConstant.PIN_DATA_TIME))
                                                .filter(StringUtils::isNotEmpty)
                                                .ifPresent(vo::setDataTime);
                                    }
                                    vo.setCrUserTrueName(user.get(it.getCreateUserId()));
                                    vo.setCrDept(dept.get(it.getCreateDeptId()));
                                    vo.setUpdateUserTrueName(user.get(it.getUpdateUserId()));
                                    vo.setUpdateDept(dept.get(it.getUpdateDeptId()));
                                    return vo;
                                }).collect(Collectors.toList())
                ).addTotalCount(data.getTotal())
                .addPageNum(request.getPageParams().getPageNumber())
                .addPageSize(request.getPageParams().getPageSize());
    }

    /**
     * findEntity<BR>
     *
     * @param currentUser 参数
     * @param dto         参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/7 16:22
     */
    public Optional<BigScreenPinDataEntity> findEntity(CurrentUser currentUser, UnPinDataDTO dto) {
        return new LambdaQueryChainWrapper<>(pinDataMapper)
                .eq(BigScreenPinDataEntity::getDistrictCode, currentUser.getDept().getDistrictCode())
                .eq(BigScreenPinDataEntity::getObjType, dto.getObjType())
                .eq(BigScreenPinDataEntity::getObjId, dto.getObjId())
                .eq(BigScreenPinDataEntity::getPinModule, dto.getPinModule())
                .oneOpt();
    }
}
