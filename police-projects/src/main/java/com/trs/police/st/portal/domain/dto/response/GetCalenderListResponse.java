package com.trs.police.st.portal.domain.dto.response;

import com.trs.police.st.portal.constant.enums.CalenderInfoTypeEnum;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 获取日历信息返回
 */

@Data
public class GetCalenderListResponse {

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 指挥日志id，为空则代表当天没有日志
     */
    private Long commandLogId;

    /**
     * 日历具体信息
     */
    private List<CalenderInfo> calenderInfos;

    /**
     * 是否是调班日
     */
    private Boolean isAdjustDate;

    /**
     * 日历具体信息
     */
    @Data
    public static class CalenderInfo {

        /**
         * 日历具体信息id
         */
        private Long id;

        /**
         * 日历类型
         */
        private CalenderInfoTypeEnum type;

        /**
         * 标题
         */
        private String title;
    }

}
