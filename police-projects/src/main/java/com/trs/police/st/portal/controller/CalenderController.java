package com.trs.police.st.portal.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.trs.police.common.core.vo.ImportResultVO;
import com.trs.police.st.portal.domain.dto.CalenderExportDto;
import com.trs.police.st.portal.domain.dto.request.AddCalenderRequest;
import com.trs.police.st.portal.domain.dto.request.UpdateCalenderRequest;
import com.trs.police.st.portal.domain.dto.response.GetCalenderDetailResponse;
import com.trs.police.st.portal.domain.dto.response.GetCalenderListResponse;
import com.trs.police.st.portal.excel.CustomCellWriteHeightConfig;
import com.trs.police.st.portal.service.CalenderService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;

/**
 * 省厅警务日历相关接口
 */

@RequestMapping("/calender")
@RestController
public class CalenderController {

    @Resource
    private CalenderService calenderService;

    /**
     * 添加日历信息
     *
     * @param request 请求参数
     * @return 结果
     */
    @PostMapping("")
    public RestfulResultsV2<String> createCalender(@RequestBody @Validated AddCalenderRequest request) {
        return calenderService.createCalender(request);
    }

    /**
     * 更新日历信息
     *
     * @param request 请求参数
     * @return 结果
     */
    @PutMapping("")
    public RestfulResultsV2<String> updateCalender(@RequestBody @Validated UpdateCalenderRequest request) {
        return calenderService.updateCalender(request);
    }

    /**
     * 删除日历信息
     *
     * @param id 日历id
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public RestfulResultsV2<String> deleteCalender(@PathVariable Long id) {
        return calenderService.deleteCalender(id);
    }

    /**
     * 获取日历信息列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param deptId 单位代码
     * @return 结果
     */
    @GetMapping("")
    public RestfulResultsV2<GetCalenderListResponse> getCalenderList(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam Long deptId) {
        return calenderService.getCalenderList(startDate, endDate, deptId);
    }

    /**
     * 获取日历详情
     *
     * @param id 日历id
     * @return 结果
     */
    @GetMapping("/{id}")
    public RestfulResultsV2<GetCalenderDetailResponse> getCalenderDetail(@PathVariable Long id) {
        return calenderService.getCalenderDetail(id);
    }

    /**
     * 获取指定日期的日历详情
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptId 单位代码
     * @return 结果
     */
    @GetMapping("/of")
    public RestfulResultsV2<GetCalenderDetailResponse> getCalenderDetailOfDate(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam Long deptId) {
        return calenderService.getCalenderDetailOfDate(startDate, endDate, deptId);
    }

    /**
     * 导入日历信息
     *
     * @param file 请求参数
     * @return 结果
     */
    @PostMapping("/import")
    public RestfulResultsV2<ImportResultVO> importCalender(MultipartFile file) {
        ImportResultVO resultVO = calenderService.importCalender(file);
        return RestfulResultsV2.ok(resultVO);
    }

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        calenderService.downloadTemplate(response);
    }

    /**
     * 导出日历信息
     *
     * @param response    HTTP响应
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param deptId 单位代码
     */
    @GetMapping("/export")
    public void exportCalender(HttpServletResponse response, @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate, @RequestParam Long deptId) throws Exception {
        final List<CalenderExportDto> data = calenderService.exportCalender(startDate, endDate, deptId);
        final String fileName = String.format("%tF至%tF警务日历信息", startDate, endDate);
        // 导出
//        response.setContentType("application/x-download;");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-disposition", URLEncoder.encode(fileName, StandardCharsets.UTF_8) + ".xlsx");

        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setWrapped(true);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);
        EasyExcel.write(response.getOutputStream(), CalenderExportDto.class)
                .registerWriteHandler(new CustomCellWriteHeightConfig())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .registerWriteHandler(horizontalCellStyleStrategy)
                .sheet(fileName)
                .doWrite(data);
    }
}
