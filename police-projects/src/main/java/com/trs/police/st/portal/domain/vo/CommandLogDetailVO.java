package com.trs.police.st.portal.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/11/29
 * @description:
 */
@Data
public class CommandLogDetailVO {

    /**
     * 值班日期
     */
    private String dutyTime;

    /**
     * 工作情况
     */
    private String workCondition;

    /**
     * 交接班登记表
     */
    private FormInfoVO shiftSignForm;

    /**
     * 重点工作
     */
    private FormInfoVO importantWork;

    /**
     * 指令
     */
    private String instruction;

    /**
     * 短信
     */
    private String shortMessage;

    /**
     * 值班要、舆情
     */
    private List<DutyYyqVO> dutyYyq;

    /**
     * 值班收发文
     */
    private List<DutyDispatchDocVO> dutyDispatchDoc;

    /**
     * 领导批示
     */
    private List<LeaderApprovalVO> leaderApproval;

    /**
     * 其他内容
     */
    private FormInfoVO otherContent;

    /**
     * 交班物品
     */
    private ShiftItemsVO shiftItems;

    /**
     * 线索人员核查稳控调度
     */
    private FormInfoVO cluePersonDispatch;

    /**
     * 风险防控调度
     */
    private FormInfoVO riskDispatch;
}
