package com.trs.police.st.portal.domain.vo;

import lombok.Data;

import java.util.Date;

/**
 * @author: dingkeyu
 * @date: 2024/12/03
 * @description: 领导批示VO
 */
@Data
public class LeaderApprovalVO {

    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 接收时间
     */
    private String receiveTime;

    /**
     * 来源code
     */
    private String source;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 批示内容
     */
    private String approvalContent;

    /**
     * 批示领导
     */
    private String approvalLeader;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 流出时间
     */
    private String outFlowTime;

    /**
     * 流转情况
     */
    private String flowSituation;

    /**
     * 备注
     */
    private String remark;

}
