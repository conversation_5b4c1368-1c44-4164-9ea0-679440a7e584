package com.trs.police.st.portal.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @author: dingkeyu
 * @date: 2024/12/01
 * @description:
 */
@Data
public class CommandLogVO {

    private Long id;

    /**
     * 创建时间
     */
    @ExcelProperty("日期")
    private String createTime;

    /**
     * 收发文时间
     */
    private String docTime;

    /**
     * 处理时限
     */
    private String handlingTime;

    /**
     * 来源
     */
    private String source;

    /**
     * 文号
     */
    private String docNumber;

    /**
     * 标题
     */
    private String title;

    /**
     * 类型code
     */
    @ExcelProperty("类型")
    private String type;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 责任单位\人
     */
    private String responsibleEntity;

    /**
     * 办理落实情况
     */
    private String handlingSituation;

    /**
     * 是否办结
     */
    private Boolean isBj;

    /**
     * 是否盯办
     */
    private Boolean isDb;

    /**
     * 是否推入日志
     */
    private Boolean isPushLog;

    /**
     * 内容
     */
    @ExcelProperty("内容")
    private String content;

    /**
     * 接收单位
     */
    private String receiveDept;
}
