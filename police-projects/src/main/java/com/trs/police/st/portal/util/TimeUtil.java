package com.trs.police.st.portal.util;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 时间工具类
 */
public class TimeUtil {

    /**
     * 生成两个日期之间的所有日期
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 日期列表
     */
    public static List<LocalDate> generateDateRange(LocalDate start, LocalDate end) {
        return start.datesUntil(end.plusDays(1)) // 加1天以包含结束日期
                .collect(Collectors.toList());
    }

    /**
     * 将Date转换为LocalDate
     *
     * @param date 需要转换的Date对象
     * @return 转换后的LocalDate对象
     */
    public static LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 将LocalDate转换为Date
     *
     * @param localDate 需要转换的LocalDate对象
     * @return 转换后的Date对象
     */
    public static Date localDateToDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

}
