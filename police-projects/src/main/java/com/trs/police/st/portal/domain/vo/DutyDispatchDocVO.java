package com.trs.police.st.portal.domain.vo;

import lombok.Data;

/**
 * @author: dingkeyu
 * @date: 2024/11/28
 * @description: 值班收发文VO
 */
@Data
public class DutyDispatchDocVO extends CommandLogVO {

    /**
     * 批示内容
     */
    private String approvalInfo;

    /**
     * 存处
     */
    private String savePlace;

    /**
     * 是否传阅
     */
    private Boolean isCy;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 接收单位
     */
    private String receiveDept;

    /**
     * 交办
     */
    private String assigned;

    /**
     * 拟稿
     */
    private String draft;

    /**
     * 审批
     */
    private String approval;
}