package com.trs.police.st.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.st.portal.domain.dto.WritingLogSearchDTO;
import com.trs.police.st.portal.domain.entity.CommandLogSignFormEntity;
import com.trs.police.st.portal.domain.vo.CommandLogDetailVO;
import com.trs.police.st.portal.domain.vo.FormInfoVO;
import com.trs.police.st.portal.service.CommandLogSignFormService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * @author: dingkeyu
 * @date: 2024/11/30
 * @description: 交接班登记表
 */
@Component
public class ShiftSignFormAnalysis extends BaseFormInfoAnalysis<CommandLogSignFormEntity> {

    @Resource
    private CommandLogSignFormService commandLogSignFormService;

    @Resource
    private DictService dictService;

    private Map<String, DictDto> dictMap = new HashMap<>();

    /**
     * init
     */
    @PostConstruct
    public void init() {
        List<DictDto> list = dictService.getDictTree("shift_sign_form_type");
        for (DictDto dictDto : list) {
            dictMap.put(dictDto.getDictDesc(), dictDto);
        }
    }

    @Override
    public void commandLogDetail(WritingLogSearchDTO dto, CommandLogDetailVO detailVo) throws Exception {
        detailVo.setDutyTime(dto.getDutyTime());
        // 交接班登记表
        List<FormInfoVO> datas = getWritingLog(dto).getDatas();
        if (!CollectionUtils.isEmpty(datas)) {
            detailVo.setShiftSignForm(datas.get(0));
        }
    }

    @Override
    public RestfulResultsV2<FormInfoVO> getWritingLog(WritingLogSearchDTO dto) throws Exception {
        final String dutyTime = StringUtils.isEmpty(dto.getDutyTime()) ? TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD) : dto.getDutyTime();
        QueryWrapper<CommandLogSignFormEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("create_time", dutyTime);
        CommandLogSignFormEntity signFormEntity = commandLogSignFormService.getOne(queryWrapper);
        FormInfoVO formInfoVO = new FormInfoVO();
        formInfoVO.setCreateTime(dto.getDutyTime());
        formInfoVO.setFormVO(initFormVO(dictMap));
        if (Objects.nonNull(signFormEntity)) {
            formInfoVO.setId(signFormEntity.getId());
            formInfoVO.setCreateTime(TimeUtils.dateToString(signFormEntity.getCreateTime(), TimeUtils.YYYYMMDD));
            List<FormInfoVO.FormVO> formVO = getFormVO(signFormEntity);
            formInfoVO.setFormVO(formVO);
        } else {
            // 查询历史最新的长期提醒和值班须知
            QueryWrapper<CommandLogSignFormEntity> cqtxCondition = new QueryWrapper<>();
            cqtxCondition.apply(String.format("cqtx IS NOT NULL AND JSON_LENGTH(cqtx) > 0 AND create_time < '%s'", dutyTime));
            cqtxCondition.orderByDesc("create_time");
            List<CommandLogSignFormEntity> cqtxList = commandLogSignFormService.list(cqtxCondition);
            QueryWrapper<CommandLogSignFormEntity> zbxzCondition = new QueryWrapper<>();
            zbxzCondition.apply(String.format("zbxz IS NOT NULL AND JSON_LENGTH(zbxz) > 0 AND create_time < '%s'", dutyTime));
            zbxzCondition.orderByDesc("create_time");
            List<CommandLogSignFormEntity> zbxzList = commandLogSignFormService.list(zbxzCondition);

            List<String> cqtxs = CollectionUtils.isEmpty(cqtxList) ? new ArrayList<>() : JSON.parseArray(cqtxList.get(0).getCqtx(), String.class);
            List<String> zbxzs = CollectionUtils.isEmpty(zbxzList) ? new ArrayList<>() : JSON.parseArray(cqtxList.get(0).getZbxz(), String.class);

            formInfoVO.getFormVO().forEach(form -> {
                switch (form.getCode()) {
                    case "cqtx":
                        form.setFlag(true);
                        form.setValues(cqtxs);
                        break;
                    case "zbxz":
                        form.setFlag(true);
                        form.setValues(zbxzs);
                        break;
                    default:
                        break;
                }
            });
        }
        return RestfulResultsV2.ok(formInfoVO);
    }

    @Override
    protected CommandLogSignFormEntity createEntity() {
        return new CommandLogSignFormEntity();
    }

    @Override
    protected void saveOrUpdate(CommandLogSignFormEntity entity) {
        commandLogSignFormService.saveOrUpdate(entity, new QueryWrapper<CommandLogSignFormEntity>()
                .eq("create_time", entity.getCreateTime()));
    }

    @Override
    protected DictDto getDictByCode(String code) {
        return dictMap.get(code);
    }

    @Override
    public String key() {
        return "shiftSignForm";
    }

    @Override
    public String desc() {
        return "交接班登记表";
    }
}
