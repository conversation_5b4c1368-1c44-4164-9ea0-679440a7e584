package com.trs.police.st.portal.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.ImmutableMap;
import com.trs.police.bigscreen.domain.entity.BigScreenDutyUserEntity;
import com.trs.police.bigscreen.mapper.BigScreenDutyUserMapper;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.ImportResultVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.st.portal.domain.dto.CalenderExportDto;
import com.trs.police.st.portal.domain.dto.CalenderImportVO;
import com.trs.police.st.portal.domain.dto.request.AddCalenderRequest;
import com.trs.police.st.portal.domain.dto.request.UpdateCalenderRequest;
import com.trs.police.st.portal.domain.dto.response.GetCalenderDetailResponse;
import com.trs.police.st.portal.domain.dto.response.GetCalenderListResponse;
import com.trs.police.st.portal.domain.entity.CalenderArrangeEntity;
import com.trs.police.st.portal.domain.entity.CalenderInfoEntity;
import com.trs.police.st.portal.domain.entity.CommandLogInfoEntity;
import com.trs.police.st.portal.domain.vo.ServiceCalendarLevelVO;
import com.trs.police.st.portal.excel.CalenderImportListener;
import com.trs.police.st.portal.mapper.CalenderArrangeMapper;
import com.trs.police.st.portal.mapper.CalenderInfoMapper;
import com.trs.police.st.portal.mapper.CommandLogInfoMapper;
import com.trs.police.st.portal.service.CalenderService;
import com.trs.police.st.portal.util.HierarchicalUtil;
import com.trs.police.st.portal.util.TimeUtil;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 省厅警务日历相关服务实现
 */

@Service
@Slf4j
public class CalenderServiceImpl implements CalenderService {

    @Resource
    private CalenderArrangeMapper calenderArrangeMapper;

    @Resource
    private CalenderInfoMapper calenderInfoMapper;

    @Resource
    private BigScreenDutyUserMapper bigScreenDutyUserMapper;

    @Resource
    private DictService dictService;

    @Resource
    private CommandLogInfoMapper commandLogInfoMapper;
    @Qualifier("com.trs.police.common.openfeign.starter.service.PermissionService")
    @Autowired
    private PermissionService permissionService;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public RestfulResultsV2<String> createCalender(AddCalenderRequest request) {
        RestfulResultsV2 r = calenderDateCheck(request.getStartDate(), request.getEndDate(), request.getAdjustDate());
        if (Objects.nonNull(r)) {
            return r;
        }

        CalenderInfoEntity calenderInfoEntity = new CalenderInfoEntity();
        calenderInfoEntity.setType(request.getType());
        calenderInfoEntity.setTitle(request.getTitle());
        calenderInfoEntity.setStartDate(request.getStartDate());
        calenderInfoEntity.setEndDate(request.getEndDate());
        calenderInfoEntity.setRemark(request.getRemark());
        calenderInfoEntity.setAdjustDate(request.getAdjustDate());
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        Optional.ofNullable(currentUser).ifPresent(user -> {
            calenderInfoEntity.setDistrictCode(currentUser.getDept().getDistrictCode());
        });
        calenderInfoMapper.insert(calenderInfoEntity);

        //插入日历排列数据
        setDateIntervalArrange(request.getStartDate(), request.getEndDate(), calenderInfoEntity.getId());
        setAdjustDateArrange(request.getAdjustDate(), request.getStartDate(), request.getEndDate(), calenderInfoEntity.getId());

        return RestfulResultsV2.ok("success");
    }

    private static RestfulResultsV2 calenderDateCheck(LocalDate startDate, LocalDate endDate, List<LocalDate> adjustDate) {
        //如果adjustDate列表中的任意一个日期在startDate和endDate之间，则抛出异常
        if (adjustDate != null && !adjustDate.isEmpty()) {
            for (LocalDate date : adjustDate) {
                if ((date.isAfter(startDate) && date.isBefore(endDate)) || date.equals(startDate) || date.equals(endDate)) {
                    return RestfulResultsV2.error("调班日不能在开始日期和结束日期之间");
                }
            }
            //如果adjustDate中有重复日期，抛出异常
            Set<LocalDate> uniqueDates = new HashSet<>(adjustDate);
            if (uniqueDates.size() != adjustDate.size()) {
                return RestfulResultsV2.error("调班日不能重复");
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public RestfulResultsV2<String> updateCalender(UpdateCalenderRequest request) {
        RestfulResultsV2 r = calenderDateCheck(request.getStartDate(), request.getEndDate(), request.getAdjustDate());
        if (Objects.nonNull(r)) {
            return r;
        }
        CalenderInfoEntity calenderInfoEntity = new CalenderInfoEntity();
        calenderInfoEntity.setId(request.getId());
        calenderInfoEntity.setType(request.getType());
        calenderInfoEntity.setTitle(request.getTitle());
        calenderInfoEntity.setStartDate(request.getStartDate());
        calenderInfoEntity.setEndDate(request.getEndDate());
        calenderInfoEntity.setRemark(request.getRemark());
        calenderInfoEntity.setAdjustDate(request.getAdjustDate());
        CalenderInfoEntity oldInfo = calenderInfoMapper.selectById(request.getId());
        calenderInfoMapper.updateById(calenderInfoEntity);

        //重新设置排列数据
        if (!oldInfo.getStartDate().equals(request.getStartDate())
                || !oldInfo.getEndDate().equals(request.getEndDate())) {
            calenderArrangeMapper.delete(Wrappers.<CalenderArrangeEntity>lambdaQuery()
                    .eq(CalenderArrangeEntity::getCalenderInfoId, request.getId())
                    .between(CalenderArrangeEntity::getDate, oldInfo.getStartDate(), oldInfo.getEndDate()));
            setDateIntervalArrange(request.getStartDate(), request.getEndDate(), request.getId());
        }
        if (Objects.nonNull(request.getAdjustDate()) && !request.getAdjustDate().equals(oldInfo.getAdjustDate())) {
            if (Objects.nonNull(oldInfo.getAdjustDate()) && !oldInfo.getAdjustDate().isEmpty()) {
                calenderArrangeMapper.delete(Wrappers.<CalenderArrangeEntity>lambdaQuery()
                        .eq(CalenderArrangeEntity::getCalenderInfoId, request.getId())
                        .in(CalenderArrangeEntity::getDate, oldInfo.getAdjustDate()));
            }
            setAdjustDateArrange(request.getAdjustDate(), request.getStartDate(), request.getEndDate(), calenderInfoEntity.getId());
        }

        return RestfulResultsV2.ok("success");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public RestfulResultsV2<String> deleteCalender(Long id) {
        calenderInfoMapper.deleteById(id);
        calenderArrangeMapper.delete(Wrappers.<CalenderArrangeEntity>lambdaQuery()
                .eq(CalenderArrangeEntity::getCalenderInfoId, id));
        return RestfulResultsV2.ok("success");
    }

    private static String getEffectiveCode(String districtCode) {
        if (StringUtils.isBlank(districtCode)) {
            CurrentUser user = AuthHelper.getCurrentUser();
            if (Objects.nonNull(user)) {
                districtCode = user.getDept().getDistrictCode();
            }
        }
        return HierarchicalUtil.getEffectiveCode(districtCode);
    }

    @Override
    public RestfulResultsV2<GetCalenderListResponse> getCalenderList(LocalDate startDate, LocalDate endDate, Long deptId) {
        List<CalenderArrangeEntity> arrangeEntities = calenderArrangeMapper.getArrangeOfDate(startDate, endDate, deptId);
        List<Long> calenderInfoIds = arrangeEntities.stream()
                .map(CalenderArrangeEntity::getCalenderInfoId).collect(Collectors.toList());

        if (calenderInfoIds.isEmpty()) {
            return RestfulResultsV2.ok(Lists.emptyList());
        }

        List<CalenderInfoEntity> calenderInfoEntities = calenderInfoMapper.selectList(Wrappers.<CalenderInfoEntity>lambdaQuery()
                .in(CalenderInfoEntity::getId, calenderInfoIds)
                .eq(CalenderInfoEntity::getCreateDeptId, deptId));

        // 将calenderInfoEntities转换为Map，以id为key，实体为value，提高查找效率
        Map<Long, CalenderInfoEntity> calenderInfoMap = calenderInfoEntities.stream()
                .collect(Collectors.toMap(CalenderInfoEntity::getId, entity -> entity));

        // 将arrangeEntities和calenderInfoEntities整合为List<GetCalenderListResponse>类型的对象
        // 首先按日期分组
        Map<LocalDate, List<CalenderArrangeEntity>> arrangeByDateMap = arrangeEntities.stream()
                .collect(Collectors.groupingBy(CalenderArrangeEntity::getDate));

        //查询填写了指挥日志的记录
        Map<LocalDate, List<CommandLogInfoEntity>> commandLogDateMap = commandLogInfoMapper.selectList(Wrappers.<CommandLogInfoEntity>lambdaQuery()
                        .eq(CommandLogInfoEntity::getCreateDeptId, deptId)
                        .between(CommandLogInfoEntity::getDate, startDate, endDate)
                        .orderByAsc(CommandLogInfoEntity::getDate)
                        .select(CommandLogInfoEntity::getDate, CommandLogInfoEntity::getId))
                .stream().collect(Collectors.groupingBy(CommandLogInfoEntity::getDate));


        // 创建结果列表，每个日期只出现一次
        List<GetCalenderListResponse> result = new ArrayList<>();

        // 遍历每个日期组
        arrangeByDateMap.forEach((date, arrangeList) -> {
            GetCalenderListResponse response = new GetCalenderListResponse();
            response.setDate(date);

            // 创建日历信息列表
            List<GetCalenderListResponse.CalenderInfo> calenderInfoList = new ArrayList<>();

            // 遍历该日期的所有安排
            for (CalenderArrangeEntity arrangeEntity : arrangeList) {
                // 直接从哈希表中获取对应的日历信息
                CalenderInfoEntity infoEntity = calenderInfoMap.get(arrangeEntity.getCalenderInfoId());
                if (infoEntity != null) {
                    // 创建并添加日历信息
                    GetCalenderListResponse.CalenderInfo calenderInfo = new GetCalenderListResponse.CalenderInfo();
                    calenderInfo.setId(infoEntity.getId());
                    calenderInfo.setType(infoEntity.getType());
                    calenderInfo.setTitle(infoEntity.getTitle());
                    calenderInfoList.add(calenderInfo);

                    // 设置是否为调班日
                    response.setIsAdjustDate(infoEntity.getAdjustDate() != null && infoEntity.getAdjustDate().contains(date));
                }
            }

            // 设置日历信息列表
            response.setCalenderInfos(calenderInfoList);
            response.setCommandLogId(commandLogDateMap.getOrDefault(date, Collections.emptyList()).stream()
                    .map(CommandLogInfoEntity::getId).findFirst().orElse(null));

            // 添加到结果列表
            result.add(response);
        });

        return RestfulResultsV2.ok(result.stream()
                .sorted(Comparator.comparing(GetCalenderListResponse::getDate))
                .collect(Collectors.toList()));
    }

    @Override
    public RestfulResultsV2<GetCalenderDetailResponse> getCalenderDetail(Long id) {
        CalenderInfoEntity calenderInfoEntity = calenderInfoMapper.selectById(id);

        Optional.ofNullable(calenderInfoEntity).orElseThrow(() -> new RuntimeException("日历信息不存在"));

        GetCalenderDetailResponse response = new GetCalenderDetailResponse();
        response.setId(calenderInfoEntity.getId());
        response.setTitle(calenderInfoEntity.getTitle());
        response.setStartDate(calenderInfoEntity.getStartDate());
        response.setEndDate(calenderInfoEntity.getEndDate());
        response.setType(calenderInfoEntity.getType());
        response.setRemark(calenderInfoEntity.getRemark());
        response.setAdjustDate(calenderInfoEntity.getAdjustDate());

        return RestfulResultsV2.ok(response);
    }

    @Override
    public RestfulResultsV2<GetCalenderDetailResponse> getCalenderDetailOfDate(LocalDate startDate, LocalDate endDate, Long deptId) {
        List<GetCalenderDetailResponse> response = calenderInfoMapper.getCalenderDetailOfDate(startDate, endDate, deptId);
        Map<LocalDate, List<CommandLogInfoEntity>> commandLogDateMap = commandLogInfoMapper.selectList(Wrappers.<CommandLogInfoEntity>lambdaQuery()
                        .eq(CommandLogInfoEntity::getCreateDeptId, deptId)
                        .between(CommandLogInfoEntity::getDate, startDate, endDate)
                        .orderByAsc(CommandLogInfoEntity::getDate)
                        .select(CommandLogInfoEntity::getDate, CommandLogInfoEntity::getId))
                .stream().collect(Collectors.groupingBy(CommandLogInfoEntity::getDate));
        response.forEach(resp -> {
            resp.setCommandLogId(commandLogDateMap.getOrDefault(resp.getDate(), Collections.emptyList()).stream()
                            .map(CommandLogInfoEntity::getId).findFirst().orElse(null));
        });
        return RestfulResultsV2.ok(response);
    }

    @Override
    public ImportResultVO importCalender(MultipartFile file) {
        try {
            CalenderImportListener listener = new CalenderImportListener(this);
            EasyExcel.read(file.getInputStream(), CalenderImportVO.class, listener)
                    .sheet()
                    .doRead();

            ImportResultVO resultVO = new ImportResultVO();
            resultVO.setAllCount(listener.getAllCount());
            resultVO.setSuccessCount(listener.getSuccessCount());
            resultVO.setFailCount(listener.getAllCount() - listener.getSuccessCount());
            resultVO.setImportFailVOList(listener.getImportFailInfo());
            return resultVO;
        } catch (IOException e) {
            log.error("导入日历信息失败", e);
            throw new RuntimeException("导入日历信息失败", e);
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            final InputStream in = this.getClass().getResourceAsStream("/template/calenderInfoBatchImport.xlsx");

            final ByteArrayOutputStream out = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
            excelWriter.finish();
            final byte[] modifiedBytes = out.toByteArray();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("日历信息导入模板.xlsx", StandardCharsets.UTF_8));

            OutputStream responseOut = response.getOutputStream();
            responseOut.write(modifiedBytes);
            responseOut.flush();
            responseOut.close();
        } catch (IOException e) {
            log.error("下载模板失败", e);
            throw new RuntimeException("下载模板失败", e);
        }
    }

    /**
     * 勤务等级4级默认名称
     */
    private String serviceCalenderLevel4Name = null;

    @Override
    public List<CalenderExportDto> exportCalender(LocalDate startDate, LocalDate endDate, Long deptId) {
        if (endDate.isBefore(startDate)) {
            return Lists.emptyList();
        }
        DeptDto dept = permissionService.getDeptById(deptId);

        // 生成从startDate到endDate的每一天的日期列表
        List<LocalDate> localDates = TimeUtil.generateDateRange(startDate, endDate);

        // 将日历详情按日期分组，创建一个以日期为键，对应日期的日历详情列表为值的Map
        Map<LocalDate, List<GetCalenderDetailResponse>> calenderDetailByDateMap = calenderInfoMapper
                .getCalenderDetailOfDate(startDate, endDate, deptId)
                .stream()
                .filter(detail -> detail.getDate() != null)
                .collect(Collectors.groupingBy(GetCalenderDetailResponse::getDate));

        //获取值班人员列表并转换为Map
        Map<Date, List<BigScreenDutyUserEntity>> dutyUserByDateMap = bigScreenDutyUserMapper
                .selectList(Wrappers.<BigScreenDutyUserEntity>lambdaQuery()
                        .between(BigScreenDutyUserEntity::getDutyTime, startDate, endDate)
                        .eq(BigScreenDutyUserEntity::getDistrictCode, dept.getDistrictCode()))
                .stream()
                .filter(dutyUser -> dutyUser.getDutyTime() != null)
                .collect(Collectors.groupingBy(BigScreenDutyUserEntity::getDutyTime));

        //获取勤务等级列表并转换为Map
        Map<LocalDate, List<ServiceCalendarLevelVO>> serviceCalendarLevelByDataMap = calenderInfoMapper.getServiceCalendarLevel(startDate, endDate)
                .stream()
                .filter(vo -> vo.getDate() != null)
                .collect(Collectors.groupingBy(ServiceCalendarLevelVO::getDate));

        if (StringUtils.isBlank(serviceCalenderLevel4Name)) {
            DictDto levelDict = dictService.getDictByTypeAndCode("service_calendar_level", 4L);
            serviceCalenderLevel4Name = Objects.nonNull(levelDict) ? levelDict.getName() : "";
        }

        //获取指挥日志提交情况
        List<LocalDate> logDateList = commandLogInfoMapper.selectList(Wrappers.<CommandLogInfoEntity>lambdaQuery()
                        .between(CommandLogInfoEntity::getDate, startDate, endDate)
                        .orderByAsc(CommandLogInfoEntity::getDate)
                        .select(CommandLogInfoEntity::getDate))
                .stream().map(CommandLogInfoEntity::getDate)
                .collect(Collectors.toList());

        // 为每一天创建CalenderExportDto对象
        List<CalenderExportDto> result = new ArrayList<>();
        for (LocalDate date : localDates) {
            CalenderExportDto exportDto = new CalenderExportDto();

            // 设置日期
            exportDto.setDate(date);

            // 从calenderDetailByDateMap中获取当天的日历详情数据并填充到exportDto中
            List<GetCalenderDetailResponse> detailsOfDay = calenderDetailByDateMap.getOrDefault(date, Collections.emptyList());
            //填充节假日和敏感日信息
            fillHolidayAndSensitiveDay(exportDto, detailsOfDay);

            List<BigScreenDutyUserEntity> dutyUserOfDay = dutyUserByDateMap.getOrDefault(
                    TimeUtil.localDateToDate(date), Collections.emptyList());
            //填充值班信息
            fillDutyUser(exportDto, dutyUserOfDay);

            List<ServiceCalendarLevelVO> serviceCalendarLevels = serviceCalendarLevelByDataMap.getOrDefault(date, Collections.emptyList());

            exportDto.setLevel(serviceCalendarLevels.isEmpty()
                    ? serviceCalenderLevel4Name
                    : serviceCalendarLevels.get(0).getLevelName());

            exportDto.setLog(logDateList.contains(date) ? "已提交" : "未提交");

            result.add(exportDto);
        }

        return result;
    }

    private void fillHolidayAndSensitiveDay(CalenderExportDto exportDto, List<GetCalenderDetailResponse> detailsOfDay) {
        if (!detailsOfDay.isEmpty()) {
            // 提取节假日&敏感日信息
            StringBuilder holidayBuilder = new StringBuilder();
            StringBuilder sensitiveDayBuilder = new StringBuilder();

            for (GetCalenderDetailResponse detail : detailsOfDay) {
                // 根据日历类型填充不同字段
                if (detail.getType() != null) {
                    switch (detail.getType()) {
                        case HOLIDAYS:
                            // 处理节假日信息
                            if (holidayBuilder.length() > 0) {
                                holidayBuilder.append("\n");
                            }
                            holidayBuilder.append(detail.getTitle());
                            break;
                        case SENSITIVE_DAYS:
                            // 处理敏感日信息
                            if (sensitiveDayBuilder.length() > 0) {
                                sensitiveDayBuilder.append("\n");
                            }
                            sensitiveDayBuilder.append(detail.getTitle());
                            if (detail.getStartDate() != null && detail.getEndDate() != null) {
                                sensitiveDayBuilder.append("\n日期：")
                                        .append(detail.getStartDate())
                                        .append("至")
                                        .append(detail.getEndDate());
                            }
                            if (detail.getRemark() != null && !detail.getRemark().isEmpty()) {
                                sensitiveDayBuilder.append("\n备注：").append(detail.getRemark());
                            }
                            break;
                        // 可以根据需要添加更多case处理其他类型
                        default:
                            // 默认处理
                            break;
                    }
                }
            }

            // 设置处理后的字段值
            if (holidayBuilder.length() > 0) {
                exportDto.setHoliday(holidayBuilder.toString());
            } else {
                exportDto.setHoliday("无");
            }
            if (sensitiveDayBuilder.length() > 0) {
                exportDto.setSensitiveDay(sensitiveDayBuilder.toString());
            } else {
                exportDto.setSensitiveDay("无");
            }
        }
    }

    private static final String DUTY_TEMPLATE = "${name}\n（${duty}职务）\n${phone}";

    private void fillDutyUser(CalenderExportDto exportDto, List<BigScreenDutyUserEntity> dutyUserList) {
        if (!dutyUserList.isEmpty()) {
            String zbldzb = "无";
            String zbldfb = "无";
            String zhzzb = "无";
            String zhzfb = "无";
            String ddg = "无";
            String jsg = "无";
            String zag = "无";
            for (BigScreenDutyUserEntity dutyUser : dutyUserList) {
                String dutyStr = formatDutyUserStr(dutyUser.getName(), dutyUser.getDuty(), dutyUser.getDh());
                switch (dutyUser.getNature()) {
                    case "值班领导":
                        if ("主班".equalsIgnoreCase(dutyUser.getLevel())) {
                            zbldzb = dutyStr;
                        } else if ("副班".equalsIgnoreCase(dutyUser.getLevel())) {
                            zbldfb = dutyStr;
                        }
                        break;
                    case "指挥长":
                        if ("主班".equalsIgnoreCase(dutyUser.getLevel())) {
                            zhzzb = dutyStr;
                        } else if ("副班".equalsIgnoreCase(dutyUser.getLevel())) {
                            zhzfb = dutyStr;
                        }
                        break;
                    case "调度岗":
                        ddg = dutyStr;
                        break;
                    case "技术岗":
                        jsg = dutyStr;
                        break;
                    case "治安岗":
                        zag = dutyStr;
                        break;
                    default:
                        break;
                }
                exportDto.setZbldzb(zbldzb);
                exportDto.setZbldfb(zbldfb);
                exportDto.setZhzzb(zhzzb);
                exportDto.setZhzfb(zhzfb);
                exportDto.setDdg(ddg);
                exportDto.setJsg(jsg);
                exportDto.setZag(zag);
            }
        }
    }

    private static String formatDutyUserStr(String name, String duty, String phone) {
        return new StringSubstitutor(ImmutableMap.of("name", Optional.ofNullable(name).orElse("--"),
                "duty", Optional.ofNullable(duty).orElse("--"),
                "phone", Optional.ofNullable(phone).orElse("--")))
                .replace(DUTY_TEMPLATE);
    }

    /**
     * 设置日期区间的排列数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param id       日历信息id
     */
    private void setDateIntervalArrange(LocalDate startDate, LocalDate endDate, Long id) {
        setCalenderArrange(TimeUtil.generateDateRange(startDate, endDate), id);
    }

    /**
     * 设置调休日的排列数据
     *
     * @param adjustDate 调休日列表
     * @param startDate 开始日期
     * @param endDate 结束日期，和开始日期一起用于排除重复的调休日
     * @param id         日历信息id
     */
    private void setAdjustDateArrange(List<LocalDate> adjustDate, LocalDate startDate, LocalDate endDate, Long id) {
        if (adjustDate == null || adjustDate.isEmpty()) {
            return;
        }

        // 筛选出不在startDate和endDate范围内的日期
        List<LocalDate> filteredDates = adjustDate.stream()
                .filter(date -> date.isBefore(startDate) || date.isAfter(endDate))
                .collect(Collectors.toList());

        setCalenderArrange(filteredDates, id);
    }

    /**
     * 设置调休日的排列数据（重载方法，不进行日期筛选）
     *
     * @param adjustDate 调休日列表
     * @param id         日历信息id
     */
    private void setAdjustDateArrange(List<LocalDate> adjustDate, Long id) {
        setCalenderArrange(adjustDate, id);
    }

    /**
     * 设置日历排列数据
     *
     * @param dateList 日期列表
     * @param id       日历信息id
     */
    private void setCalenderArrange(List<LocalDate> dateList, Long id) {
        Optional.ofNullable(dateList).ifPresent(list -> list.forEach(date -> {
            CalenderArrangeEntity calenderArrangeEntity = new CalenderArrangeEntity();
            calenderArrangeEntity.setDate(date);
            calenderArrangeEntity.setCalenderInfoId(id);
            calenderArrangeMapper.insert(calenderArrangeEntity);
        }));
    }
}
