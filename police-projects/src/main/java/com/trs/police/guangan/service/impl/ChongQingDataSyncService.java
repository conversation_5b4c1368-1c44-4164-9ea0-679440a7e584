package com.trs.police.guangan.service.impl;

import com.trs.common.exception.ServiceException;
import com.trs.police.bigscreen.service.BaseSyncService;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.guangan.domain.entity.ChongQingDataEntity;
import com.trs.police.guangan.vo.ChongQingKafkaVo;
import com.trs.web.builder.util.KeyMgrFactory;
import io.vavr.Tuple2;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * @param <OUT> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/18 20:21
 * @since 1.0
 */
@Getter
@Slf4j
public abstract class ChongQingDataSyncService<OUT> extends BaseSyncService<ChongQingKafkaVo, OUT> {

    private final ChongQingQueryServiceImpl chongQingQueryService;

    public ChongQingDataSyncService(ChongQingQueryServiceImpl chongQingQueryService, SyncTaskMapper syncTaskMapper, RedisTemplate<String, Object> redisTemplate) {
        super(syncTaskMapper, redisTemplate);
        this.chongQingQueryService = chongQingQueryService;
    }

    /**
     * findByDeviceClass<BR>
     *
     * @param deviceClass 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/18 21:03
     */
    public static Optional<ChongQingDataSyncService> findByDeviceClass(Integer deviceClass) throws ServiceException {
        return Optional.ofNullable(KeyMgrFactory.findMgrByKeyNew(
                ChongQingDataSyncService.class,
                it -> it.supportDeviceClass(deviceClass)
        ));
    }

    /**
     * isEnable<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/12 15:35
     */
    @Override
    public Boolean isSyncEnable() {
        // 不支持定时调度
        return false;
    }

    /**
     * supportDeviceClass<BR>
     *
     * @param deviceClass 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/18 20:22
     */
    public abstract Boolean supportDeviceClass(Integer deviceClass);

    /**
     * convert<BR>
     *
     * @param kafkaVo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:04
     */
    @Override
    public OUT convert(ChongQingKafkaVo kafkaVo) {
        try {
            var t = getChongQingQueryService().queryDetail(kafkaVo.getDeviceClass(), kafkaVo.getDeviceCode());
            if (Objects.isNull(t)) {
                log.warn("数据[{}:{}]不存在！", kafkaVo.getDeviceClass(), kafkaVo.getDeviceCode());
                return null;
            } else {
                log.info("数据[{}:{}]查询成功！", kafkaVo.getDeviceClass(), kafkaVo.getDeviceCode());
            }
            return convert(kafkaVo, t);
        } catch (ServiceException e) {
            log.error("数据[{}:{}]查询出错！", kafkaVo.getDeviceClass(), kafkaVo.getDeviceCode(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * convert<BR>
     *
     * @param qingKafkaVo         参数
     * @param chongQingDataEntity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/18 20:39
     */
    public abstract OUT convert(ChongQingKafkaVo qingKafkaVo, ChongQingDataEntity chongQingDataEntity);

    /**
     * findInData<BR>
     *
     * @param startTime 参数
     * @param endTime   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:05
     */
    @Override
    public Tuple2<List<ChongQingKafkaVo>, String> findInData(String startTime, String endTime) {
        return new Tuple2<>(Collections.emptyList(), "");
    }
}
