package com.trs.police.crj.service.impl;

import com.trs.common.utils.StringUtils;
import com.trs.police.crj.service.BaseItemCheckService;
import com.trs.police.crj.vo.ChuRuJingVo;
import com.trs.police.crj.vo.ErrorItemVo;
import com.trs.police.crj.webService.domain.QueryResult;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：英文姓名比对
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/4 20:30
 * @since 1.0
 */
@Service
@Slf4j
public class YingWenXingMingItemCheckService extends BaseItemCheckService {

    /**
     * buildXingMing<BR>
     *
     * @param data 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/17 12:51
     */
    public static String buildXingMing(ChuRuJingVo data) {
        // XMKFB-2130 英文姓名的先后关系和国家有关系，
        // 包括：朝鲜、韩国、越南、日本、蒙古、阿富汗、新加坡、匈牙利，以上国家的英文姓名是 姓在前名在后。
        // 除以上国家之外的国家英文姓名是 名在前姓在后。
        // 20240701 褚川宝 根据XMKFB-2426要求该配置的内容改为正则表达式的格式，同时默认值也改为.*
        // 港澳台直接返回中文姓名
        List<String> countryList = StringUtils.getList(
                BeanFactoryHolder.getEnv().getProperty(
                        "com.trs.crj.system.return.cnName.list",
                        String.class,
                        "香港,澳门,台湾"
                )
        );
        for(String str : countryList){
            if(data.getGuoji().contains(str)){
                return data.getZhongwenming();
            }
        }
        String configRex = BeanFactoryHolder.getEnv().getProperty(
                "com.trs.crj.system.xm.style.like.cn",
                ".*"
        );
        StringBuilder builder = new StringBuilder();
        builder.append(StringUtils.showEmpty(data.getYingwenxing()).trim());
        if (StringUtils.isNotEmpty(data.getYingwenming())) {
            // 如果包含*的话也算是
            boolean isContain = Objects.equals(configRex, StringUtils.STRING_MULTIPLY_FLAG)
                    || StringUtils.contains(data.getGuoji(), configRex);
            if (!isContain) {
                log.info("[{}]英文姓名格式跟英文一致", data.getGuoji());
                // 补充空格
                if (builder.length() > 0) {
                    builder.insert(0, StringUtils.SEPARATOR_BLANK_SPACE);
                }
                builder.insert(0, data.getYingwenming().trim());
            } else {
                log.info("[{}]英文姓名格式跟中文一致", data.getGuoji());
                // 补充空格
                if (builder.length() > 0) {
                    builder.append(StringUtils.SEPARATOR_BLANK_SPACE);
                }
                builder.append(data.getYingwenming().trim());
            }
        }
        return builder.toString();
    }

    /**
     * checkData<BR>
     *
     * @param data   参数
     * @param record 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 20:15
     */
    @Override
    public Optional<ErrorItemVo> checkData(ChuRuJingVo data, QueryResult.CrjRecord record) {
        final String ywxm = buildXingMing(data);
        if (StringUtils.isEmpty(data.getYingwenxing())
                || StringUtils.isEmpty(data.getYingwenming())
                || !Objects.equals(ywxm, record.getYwxm())) {
            return Optional.of(makeError(ywxm, record.getYwxm()));
        }
        return Optional.empty();
    }

    /**
     * 将编号转换成名称<BR>
     *
     * @param bm 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 20:15
     */
    @Override
    public String convertBmToMc(String bm) {
        return StringUtils.showEmpty(bm);
    }

    @Override
    public String key() {
        return "yingwenxingming";
    }

    @Override
    public String desc() {
        return "英文姓名";
    }

    /**
     * 顺序<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 20:32
     */
    @Override
    public int order() {
        return 2;
    }
}
