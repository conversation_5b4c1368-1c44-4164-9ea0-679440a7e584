package com.trs.police.crj.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @date 创建时间：2024/8/29 16:02
 * @version 1.0
 * @since  1.0
 */
@Data
@TableName("tb_projects_crj_ld_dy_mapping")
public class LvDianDiYuMappingEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Date crTime = new Date();

    private Integer isDel = 0;

    private String ldmc;

    private String ldbm;

    private String dybm;

}
