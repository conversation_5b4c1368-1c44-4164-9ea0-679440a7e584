package com.trs.police.crj.service;

import com.alibaba.fastjson.JSON;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.crj.vo.ErrorItemVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.builder.util.KeyMgrFactory;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/4 20:29
 * @since 1.0
 */
public abstract class BaseItemCheckService implements IItemCheckService {

    private static List<BaseItemCheckService> services;

    /**
     * 获取可以检测的服务<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 21:31
     */
    public static List<BaseItemCheckService> findServices() {
        if (CollectionUtils.isEmpty(services)) {
            synchronized (BaseItemCheckService.class) {
                if (CollectionUtils.isEmpty(services)) {
                    try {
                        services = KeyMgrFactory.getMgrs(BaseItemCheckService.class).stream()
                                .filter(BaseItemCheckService::needCheck)
                                .sorted(Comparator.comparing(BaseItemCheckService::order))
                                .collect(Collectors.toList());
                    } catch (Exception e) {
                        throw new RuntimeException("加载实现类异常", e);
                    }
                }
            }
        }
        return services;
    }

    /**
     * findService<BR>
     *
     * @param key 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 21:42
     */
    public static BaseItemCheckService findService(String key) {
        try {
            return KeyMgrFactory.findMgrByKey(BaseItemCheckService.class, key, true);
        } catch (ServiceException e) {
            throw new RuntimeException("加载实现类异常", e);
        }
    }

    /**
     * makeError<BR>
     *
     * @param leftValue  参数
     * @param rightValue 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 20:42
     */
    public ErrorItemVo makeError(String leftValue, String rightValue) {
        ErrorItemVo vo = new ErrorItemVo();
        vo.setKey(key());
        vo.setDesc(desc());
        vo.setLeftValue(leftValue);
        vo.setLeftValueDesc(convertBmToMc(leftValue));
        vo.setRightValue(rightValue);
        vo.setRightValueDesc(convertBmToMc(rightValue));
        return vo;
    }

    /**
     * 将编号转换成名称<BR>
     *
     * @param bm 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 20:15
     */
    @Override
    public String convertBmToMc(String bm) {
        String mapping = BeanFactoryHolder.getEnv()
                .getProperty(String.format("com.trs.crj.%s.mapping", key()));
        String key = StringUtils.showEmpty(bm);
        if (JsonUtils.isValidObject(mapping)) {
            var json = JSON.parseObject(mapping);
            if (json.containsKey(key)) {
                return json.getString(key);
            }
        }
        return null;
    }

    /**
     * 顺序<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 20:32
     */
    public abstract int order();

    /**
     * needCheck<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/4 21:39
     */
    public boolean needCheck() {
        return true;
    }

}
