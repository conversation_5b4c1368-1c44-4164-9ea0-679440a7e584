package com.trs.police.gx.controller;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.gx.service.ITangYouCupSecurityReportService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import static com.trs.police.common.core.utils.JsonUtil.OBJECT_MAPPER;

@Slf4j
@RestController
@RequestMapping("gx")
public class TangYouCupSecurityReportController {

    @Resource
    private ITangYouCupSecurityReportService service;

    /**
     * 生成汤尤杯期间的购票数据
     * @param response
     */
    @PostMapping("/ticketsChecking")
    public void exportControlReport(MultipartFile file, HttpServletResponse response) throws UnsupportedEncodingException {

        //获得前一天21点到今天21点的数据；
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("购票信息比对-" + TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + "."+ExcelTypeEnum.XLS.getValue().toLowerCase());
        Try.run(() -> RestfulResultsV2.ok(service.exportTicketsCheckingTask(PreConditionCheck.checkNotNull(file), response)))
                .onFailure(e -> {
                    log.error("导出购票信息比对失败", e);
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("application/json;charset=UTF-8");
                    try (final PrintWriter writer = response.getWriter()) {
                        ResponseMessage message = ResponseMessage.error(e.getMessage());
                        String text = OBJECT_MAPPER.writeValueAsString(message);
                        writer.write(text);
                        log.error("导出购票信息比对失败！", e);
                    } catch (Exception ex) {
                        log.error("导出购票信息比对失败！", ex);
                    }
                });
    }
}
