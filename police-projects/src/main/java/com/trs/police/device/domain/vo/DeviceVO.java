package com.trs.police.device.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.common.pojo.BaseVO;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since 创建时间：2025/2/24 16:46
 * @version 1.0
 * @since 1.0
 */
@Data
public class DeviceVO extends BaseVO {

    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    private String deviceNo;

    private String wz;

    private Double jd;

    private Double wd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    private Integer isDel;

    private String deviceType;

    private Integer online;

    private String districtCode;

    private String unitName;

}
