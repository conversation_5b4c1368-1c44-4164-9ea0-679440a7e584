package com.trs.police.crj.service.impl;

import com.trs.police.crj.vo.ChuRuJingVo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class YingWenXingMingItemCheckServiceTest {

    @Test
    void buildXingMing() {
        ChuRuJingVo vo = new ChuRuJingVo();
        vo.setYingwenxing("MA");
        vo.setYingwenming("JACK");
        vo.setGuoji("HKG");
        System.out.println(YingWenXingMingItemCheckService.buildXingMing(vo));
        vo.setGuoji("SGP");
        System.out.println(YingWenXingMingItemCheckService.buildXingMing(vo));
    }
}