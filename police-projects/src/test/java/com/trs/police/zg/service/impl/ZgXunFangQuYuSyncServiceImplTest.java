package com.trs.police.zg.service.impl;

import com.trs.common.exception.ServiceException;
import com.trs.police.ProjectsApplication;
import com.trs.police.bigscreen.domain.dto.JingWuSourceDTO;
import com.trs.police.zg.vo.ZgXunFangQuYuVo;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = ProjectsApplication.class)
class ZgXunFangQuYuSyncServiceImplTest {

    @Autowired
    private ZgXunFangQuYuSyncServiceImpl service;

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void save() {
    }

    @Test
    void toVO() {
    }

    @Test
    void syncData() {
        service.syncData();
    }

    @Test
    void queryList() throws ServiceException {
        JingWuSourceDTO dto = new JingWuSourceDTO();
        dto.setPageNum(1);
        dto.setPageSize(10);
        final List<ZgXunFangQuYuVo> list = service.queryList(dto);
        System.out.println(list);
    }
}