package com.trs.police.zg.utils;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class QinWuUtilsTest {

    @Autowired
    private QinWuApiUtils qinWuApiUtils;

    @Test
    void getToken() throws Exception {
        System.out.println(QinWuUtils.getToken());
    }

    @Test
    public void testConvert() throws Exception{
        qinWuApiUtils.getBb(1, 10, null);
    }
}