package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 数据库实体类超类(记录数据创建以及更新信息)
 *
 * <AUTHOR>
 */
@Data
public abstract class AbstractBaseEntity implements Serializable {

    private static final long serialVersionUID = 7169740440345442446L;

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建用户主键
     */
    @TableField(fill = FieldFill.INSERT, value = "create_user_id")
    private Long createUserId;

    /**
     * 创建单位主键
     */
    @TableField(fill = FieldFill.INSERT, value = "create_dept_id")
    private Long createDeptId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新用户主键
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_user_id")
    private Long updateUserId;

    /**
     * 更新单位主键
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_dept_id")
    private Long updateDeptId;

    /**
     * 手动填充审计字段
     *
     * @param user 用户信息
     */
    public void fillAuditFields(CurrentUser user) {
        this.setCreateUserId(user.getId());
        this.setCreateDeptId(user.getDept().getId());
        this.setCreateTime(LocalDateTime.now());
        this.setUpdateUserId(user.getId());
        this.setUpdateDeptId(user.getDept().getId());
        this.setUpdateTime(LocalDateTime.now());
    }


    /**
     * 手动填充审计字段,创建时间和更新时间都为当前时间
     */
    public void fillAuditFields() {
        this.setCreateTime(LocalDateTime.now());
        this.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 生成创建人信息
     *
     * @return UserDeptVO
     */
    @JsonIgnore
    public UserDeptVO getCreateUserDept() {
        return new UserDeptVO(this.createUserId, this.createDeptId);
    }

    /**
     * 生成mac字符串
     *
     * @return 结果
     */
    public String toMacString() {
        return "AbstractBaseEntity{" +
                "id=" + id +
                ", createTime=" + createTime +
                ", createUserId=" + createUserId +
                ", createDeptId=" + createDeptId +
                ", updateUserId=" + updateUserId +
                ", updateDeptId=" + updateDeptId +
                '}';
    }
}
