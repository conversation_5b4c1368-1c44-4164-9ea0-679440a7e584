package com.trs.police.common.core.dto;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.common.utils.StringUtils;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * UnitTreeDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/12/20 17:12
 * @since 1.0
 */
@Data
public class UnitTreeDTO extends BaseDTO {

    private String dy;

    private String level;

    /**
     * makeDyCode<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/5 16:45
     */
    public String makeDyCode() {
        if (StringUtils.isEmpty(dy)) {
            return "";
        } else {
            String tmp = dy;
            while (tmp.length() < 12) {
                tmp += "0";
            }
            return tmp;
        }
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        return true;
    }
}
