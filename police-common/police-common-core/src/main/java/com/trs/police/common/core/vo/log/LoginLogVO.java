package com.trs.police.common.core.vo.log;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.configure.PoliceCloudCoreAutoConfigure;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 登录日志vo
 *
 * <AUTHOR>
 * @since 2022/11/9 18:21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginLogVO {

    private Long id;

    private Long deptId;

    private String deptName;

    private String deptIdPath;
    /**
     * 登录方式 pwd pki
     */
    private String type;

    private String userName;

    private String detail;

    private String ipAddress;

    @JsonSerialize(using = PoliceCloudCoreAutoConfigure.LocalDateTimeSerializer.class)
    @JsonDeserialize(using = PoliceCloudCoreAutoConfigure.LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    private String idCard;

    private String trueName;
}
