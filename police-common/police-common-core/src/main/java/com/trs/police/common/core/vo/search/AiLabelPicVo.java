package com.trs.police.common.core.vo.search;

import com.trs.common.pojo.BaseVO;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import lombok.*;
import org.jetbrains.annotations.NotNull;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/26 14:26
 * @since 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiLabelPicVo extends BaseVO implements Comparable<AiLabelPicVo> {

    private String dzmc;

    private String zpsj;

    private String tpdz;

    /**
     * ok<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/26 15:04
     */
    public boolean ok() {
        if (StringUtils.isNotEmpty(getZpsj())) {
            setZpsj(TimeUtils.stringToString(getZpsj(), TimeUtils.YYYYMMDD_HHMMSS));
        }
        return StringUtils.isNotEmpty(getTpdz());
    }

    @Override
    public int compareTo(@NotNull AiLabelPicVo o) {
        return StringUtils.showEmpty(o.getZpsj()).compareTo(StringUtils.showEmpty(getZpsj()));
    }
}
