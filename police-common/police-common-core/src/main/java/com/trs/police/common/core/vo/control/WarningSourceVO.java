package com.trs.police.common.core.vo.control;

import java.io.Serializable;
import lombok.Data;

/**
 * 预警感知源vo
 *
 * <AUTHOR>
 */
@Data
public class WarningSourceVO implements Serializable {

    private static final long serialVersionUID = -3847631881907207878L;
    /**
     * id
     */
    private Long id;
    /**
     * 感知源名称
     */
    private String  sourceName;
    /**
     * 感知源代码
     */
    private String sourceCode;
    /**
     * 类型
     */
    private String type;

    /**
     * 顶级节点类型（用于前端图标回显）
     */
    private String topType;
    /**
     * 类别
     */
    private String category;
    /**
     * 地址
     */
    private String address;
    /**
     * 所属辖区
     */
    private String controlStation;

    /**
     * wkt
     */
    private String point;
}
