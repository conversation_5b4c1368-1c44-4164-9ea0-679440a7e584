package com.trs.police.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/10/14 10:34
 */
public enum VirtualIdentityTypeEnum {
    /**
     * imei
     */
    IMEI(3, "imei"),
    /**
     * imsi
     */
    IMSI(2, "imsi"),
    /**
     * mac
     */
    MAC(1, "mac");

    /**
     * 云哨码值
     */
    @JsonValue
    @Getter
    @EnumValue
    private final Integer code;
    @Getter
    private final String name;

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static VirtualIdentityTypeEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (VirtualIdentityTypeEnum typeEnum : VirtualIdentityTypeEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    VirtualIdentityTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;

    }
}
