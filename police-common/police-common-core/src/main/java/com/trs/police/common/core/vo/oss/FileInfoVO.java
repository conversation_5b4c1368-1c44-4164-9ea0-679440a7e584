package com.trs.police.common.core.vo.oss;

import com.trs.police.common.core.entity.FileInfo;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.FileUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Objects;
import java.util.TimeZone;

/**
 * 文件上传VO
 *
 * <AUTHOR>
 * @since 2022/4/11 11:14
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileInfoVO implements Serializable {

    private static final long serialVersionUID = -3185907032809549626L;
    private String name;

    private String url;

    private String size;

    private Long id;

    private String previewImage;

    /**
     * 后缀名
     */
    private String ext;

    /**
     * 文件类型，参考{@link com.trs.police.common.core.constant.enums.FileTypeEnum}
     */
    private String type;

    private String duration;

    /**
     * 文件pdf格式路径
     */
    private String pdfUrl;

    public FileInfoVO(String url) {
        this.url = url;
    }

    public FileInfoVO(Long id, String url) {
        this.url = url;
        this.id = id;
    }


    public FileInfoVO(Long id, String name, String url) {
        this.name = name;
        this.url = url;
        this.id = id;
    }

    /**
     * 实体类转vo
     *
     * @param fileInfo 实体类
     * @return vo
     */
    public static FileInfoVO idNumberToImg(FileInfo fileInfo) {
        FileInfoVO vo = new FileInfoVO();
        BeanUtil.copyPropertiesIgnoreNull(fileInfo, vo);
        vo.setType(fileInfo.getContentType().getCode());
        vo.setSize(FileUtil.byteCountToDisplaySize(fileInfo.getSize(), 1));
        vo.setPdfUrl(fileInfo.getPdfUrl());
        if (Objects.nonNull(fileInfo.getDuration())) {
            vo.setDuration(generateTimeString(fileInfo.getDuration()));
        }
        return vo;
    }

    /**
     * 通过身份证号，拼接头像url
     *
     * @param idNumber 身份证号
     * @return FileInfoVO
     */
    public static FileInfoVO idNumberToImg(String idNumber) {
        return new FileInfoVO(-1L, "/oss/photo/" + idNumber);
    }

    /**
     * 毫秒转换为时间字符串
     *
     * @param duration 毫秒数
     * @return 时长
     */
    private static String generateTimeString(Long duration) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
        return formatter.format(duration - TimeZone.getDefault().getRawOffset());
    }

    /**
     * 拼接字符串
     *
     * @return 结果
     */
    public String toSimpleString() {
        return "<" + name + ">";
    }

    /**
     * 通过身份证号，拼接头像url
     *
     * @param idNumber 身份证号
     * @return FileInfoVO
     */
    public static FileInfoVO of(String idNumber) {
        if (StringUtils.isBlank(idNumber)) {
            return new FileInfoVO();
        }
        return new FileInfoVO(-1L, "/oss/photo/" + idNumber);
    }
}
