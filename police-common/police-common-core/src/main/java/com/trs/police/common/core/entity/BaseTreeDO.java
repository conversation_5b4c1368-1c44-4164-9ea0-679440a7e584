package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.trs.police.common.core.vo.Tree;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

/**
 * @author: zhang.wenquan
 * @description: 树形结构的公共维护字段
 * @date: 2022/4/14 11:39
 * @version: 1.0
 */
@Data
@NoArgsConstructor
public class BaseTreeDO implements Tree,Serializable {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 名字
     */
    @TableField("name")
    private String name;

    /**
     * 父节点id
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 目录线索，记录了根节点到叶子节点的id枚举路径 如 -1-5- 树深度较小的建议使用这个字段
     */
    @TableField("search_key")
    private String searchKey;

    /**
     * 树的深度
     * parentLevel + 1
     */
    @TableField("tree_level")
    private String treeLevel;

    /**
     * 节点类型
     */
    @TableField("node_type")
    private Integer nodeType;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 启用标识 1 启用 0 启用
     */
    @TableField("enable")
    private Integer enable;

    @Override
    public String key() {
        return searchKey;
    }

    @Override
    public Optional<Integer> sort() {
        return Optional.empty();
    }

    @Override
    public void setChildren(List<? extends Tree> children) {

    }
}
