package com.trs.police.common.core.dto.log;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 推送省厅数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LogContext implements Serializable {

    /**
     * 日志类型
     */
    private Integer logGroup;

    /**
     * 是否有查询结果 0 无 1 有
     */
    private String operateResult;

    /**
     * 查询结果内容
     */
    private String inquireContent;

    /**
     * 响应码
     */
    private String code;

    /**
     * 应用日志主键
     */
    private Long numId;

    /**
     * 创建事件
     */
    private LocalDateTime createTime;

    /**
     * ip地址
     */
    private String terminalIp;

    /**
     * 操作名称
     */
    private String operateName;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 部门id
     */
    private Long deptId;

}
