package com.trs.police.common.core.utils;

import com.trs.police.common.core.constant.enums.TimeRangeEnum;
import org.apache.commons.lang.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class TimeUtil {

    private TimeUtil() {
    }

    /**
     * 偏移量-小时
     */
    public static final char OFFSET_UNIT_HOUR = 'H';

    /**
     * 偏移量-天
     */
    public static final char OFFSET_UNIT_DAY = 'D';

    /**
     * 偏移量-月
     */
    public static final char OFFSET_UNIT_MONTH = 'M';

    /**
     * 偏移量-年
     */
    public static final char OFFSET_UNIT_YEAR = 'Y';

    /**
     * 默认的目标时间天数偏移量
     */
    public static final int DEFAULT_TARGET_TIME_OF_DAY = 7;

    /**
     * 默认的目标时间月份偏移量
     */
    public static final int DEFAULT_TARGET_TIME_OF_MONTH = 1;

    public static final DateTimeFormatter DEFAULT_TIME_PATTERN = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter DEFAULT_DATE_PATTERN = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final DateTimeFormatter CN_TIME_PATTERN = DateTimeFormatter.ofPattern("M月d日HH:mm:ss");

    public static final DateTimeFormatter CN_DATE_PATTERN = DateTimeFormatter.ofPattern("M月d日");

    public static final DateTimeFormatter WARNING_MESSAGE_PATTERN = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    public static final DateTimeFormatter WARNING_MESSAGE_PATTERN_2 = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static final LocalDateTime DEFAULT_BEGIN_TIME = LocalDateTime.parse("2000-01-01T00:00:00");
    /**
     * mysql timestamp最多存储到2038年
     */
    public static final LocalDateTime DEFAULT_END_TIME = LocalDateTime.parse("2035-01-01T00:00:00");

    /**
     * 预警时间格式化
     *
     * @param time 时间
     * @return 格式化
     */
    public static String timeStringFormatter(String time) {
        return LocalDateTime.parse(time, WARNING_MESSAGE_PATTERN).format(CN_TIME_PATTERN);
    }

    /**
     * 预警时间格式化
     *
     * @param time      时间
     * @param formatter 格式
     * @return 格式化
     */
    public static String timeStringFormatter(String time, DateTimeFormatter formatter) {
        return LocalDateTime.parse(time, WARNING_MESSAGE_PATTERN).format(formatter);
    }

    /**
     * 预警时间格式化, 手动指定格式
     *
     * @param time      时间
     * @param formatter 格式
     * @return 格式化
     */
    public static String warningTimeFormatter(String time, String formatter) {
        return LocalDateTime.parse(time, WARNING_MESSAGE_PATTERN).format(DateTimeFormatter.ofPattern(formatter));
    }

    /**
     * 生成简洁格式时间
     *
     * @param time 时间
     * @return 字符串
     */
    public static String getSimpleTime(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = LocalDate.now();
        if (time == null) {
            return "";
        }
        if (time.isBefore(now)) {
            //本世纪前
            if (time.isBefore(getBeginTime(TimeRangeEnum.ALL, today))) {
                return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                //本世纪-今年
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.CURRENT_YEAR, today))) {
                return time.format(DateTimeFormatter.ofPattern("yy-MM-dd HH:mm"));
                //今年-昨天
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.YESTERDAY, today))) {
                return time.format(DateTimeFormatter.ofPattern("MM-dd HH:mm"));
                //昨天-今天
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.TODAY, today))) {
                return time.format(DateTimeFormatter.ofPattern("昨天 HH:mm"));
                //今天-1小时前
            } else if (time.isBefore(now.minusHours(1))) {
                return time.format(DateTimeFormatter.ofPattern("今日 HH:mm"));
                //1小时前-现在
            } else {
                Duration duration = Duration.between(time, now);
                String str = durationToString(duration);
                return "0分钟".equals(str) ? "刚刚" : "今日 " + durationToString(duration) + "前";
            }
        } else {
            return time.format(DateTimeFormatter.ofPattern("MM-dd HH:mm"));
        }
    }

    /**
     * 生成简洁格式时间
     *
     * @param time 时间
     * @return 字符串
     */
    public static String getSimpleTimeNoHmsSerializer(LocalDateTime time) {
        if (time == null) {
            return "";
        }
        return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }

    /**
     * 生成简洁格式日期
     *
     * @param time 时间
     * @return 字符串
     */
    public static String getSimpleDate(LocalDateTime time) {
        LocalDate today = LocalDate.now();
        LocalDateTime todayMax = LocalDateTime.of(today, LocalTime.MAX);
        if (time == null) {
            return "";
        }
        if (time.isBefore(todayMax)) {
            //本世纪前
            if (time.isBefore(getBeginTime(TimeRangeEnum.ALL, today))) {
                return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                //本世纪-今年
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.CURRENT_YEAR, today))) {
                return time.format(DateTimeFormatter.ofPattern("yy-MM-dd"));
                //今年-昨天
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.YESTERDAY, today))) {
                return time.format(DateTimeFormatter.ofPattern("MM-dd"));
                //昨天-今天
            } else if (time.isBefore(getBeginTime(TimeRangeEnum.TODAY, today))) {
                return "昨天";
                //今天-1小时前
            } else {
                return "今天";
            }
        } else if (time.isAfter(getBeginTime(TimeRangeEnum.CURRENT_YEAR, today.plusYears(1)).minusDays(1))) {
            //今年之后
            return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }else {
            return time.format(DateTimeFormatter.ofPattern("MM-dd"));
        }
    }

    /**
     * 按照时间范围返回开始时间
     *
     * @param rangeEnum 时间范围
     * @param today     时间
     * @return 时间
     */
    public static LocalDateTime getBeginTime(TimeRangeEnum rangeEnum, LocalDate today) {
        int season = (int) Math.ceil((double) today.getMonthValue() / 3);
        switch (rangeEnum) {
            case ALL:
                return DEFAULT_BEGIN_TIME;
            case TODAY:
                return today.atStartOfDay();
            case CURRENT_WEEK:
                return today.with(DayOfWeek.MONDAY).atStartOfDay();
            case CURRENT_MONTH:
                return today.with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            case CURRENT_SEASON:
                return today.withMonth((season - 1) * 3 + 1).withDayOfMonth(1).atStartOfDay();
            case CURRENT_YEAR:
                return today.with(ChronoField.DAY_OF_YEAR, 1).atStartOfDay();
            case RECENT_DAY:
                return LocalDateTime.now().minusHours(24);
            case RECENT_WEEK:
                return today.minusDays(6).atStartOfDay();
            case RECENT_MONTH:
                return today.minusMonths(1).atStartOfDay();
            case RECENT_SEASON:
                return today.minusMonths(3).atStartOfDay();
            case RECENT_YEAR:
                return today.minusYears(1).atStartOfDay();
            case YESTERDAY:
                return today.minusDays(1).atStartOfDay();
            case LAST_WEEK:
                return today.minusWeeks(1).with(DayOfWeek.MONDAY).atStartOfDay();
            case LAST_MONTH:
                return today.minusMonths(1).with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            case LAST_SEASON:
                return today.withMonth((season - 1) * 3 + 1).minusMonths(3).withDayOfMonth(1).atStartOfDay();
            case LAST_HALF_YEAR:
                return today.minusMonths(6).withDayOfMonth(1).atStartOfDay();
            case LAST_YEAR:
                return today.minusYears(1).withDayOfYear(1).atStartOfDay();
            case TOMORROW:
                return today.plusDays(1).atStartOfDay();
            default:
                return LocalDateTime.now();
        }
    }

    /**
     * 按照时间范围返回结束时间
     *
     * @param rangeEnum 时间范围
     * @param today     时间
     * @return 时间
     */
    public static LocalDateTime getEndTime(TimeRangeEnum rangeEnum, LocalDate today) {
        int season = (int) Math.ceil((double) today.getMonthValue() / 3);
        switch (rangeEnum) {
            case TODAY:
                return today.plusDays(1).atStartOfDay();
            case CURRENT_WEEK:
                return today.plusWeeks(1).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atStartOfDay();
            case CURRENT_MONTH:
                return today.plusMonths(1).withDayOfMonth(1).atStartOfDay();
            case CURRENT_SEASON:
                return today.withMonth((season - 1) * 3 + 1).withDayOfMonth(1).plusMonths(3).atStartOfDay();
            case RECENT_DAY:
            case RECENT_WEEK:
            case RECENT_MONTH:
            case RECENT_SEASON:
            case RECENT_YEAR:
                return LocalDateTime.now();
            case CURRENT_YEAR:
                return today.plusYears(1).withDayOfYear(1).atStartOfDay();
            case YESTERDAY:
                return today.atStartOfDay();
            case LAST_WEEK:
                return today.with(DayOfWeek.MONDAY).atStartOfDay();
            case LAST_MONTH:
                return today.with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            case LAST_SEASON:
                return today.withMonth((season - 1) * 3 + 1).withDayOfMonth(1).atStartOfDay();
            case LAST_HALF_YEAR:
                return today.withDayOfMonth(1).atStartOfDay();
            case LAST_YEAR:
                return today.withDayOfYear(1).atStartOfDay();
            case TOMORROW:
                return today.plusDays(2).atStartOfDay();
            case ALL:
            default:
                return DEFAULT_END_TIME;
        }
    }

    /**
     * 生成订阅时间
     *
     * @param time 时间
     * @return 时间字符串
     */
    public static String getSubscribeTime(LocalDateTime time) {
        return time.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    /**
     * 时间段转换字符串 > 48h:      xx天 > 1h <=48h: xx小时 <=60min:    xx分
     *
     * @param duration {@link Duration}
     * @return 字符串
     */
    public static String durationToString(Duration duration) {
        Duration twoDay = Duration.ofDays(2);
        Duration anHour = Duration.ofHours(1);
        if (duration.compareTo(twoDay) > 0) {
            return duration.toDays() + "天";
        } else if (duration.compareTo(anHour) > 0) {
            return duration.toHours() + "小时";
        } else if (!duration.isNegative()) {
            return duration.toMinutes() + "分钟";
        } else {
            return "0分钟";
        }
    }

    /**
     * 时间段转换字符串
     * e.g. 1天1小时25分钟15秒
     *
     * @param duration {@link Duration}
     * @return 字符串
     */
    public static String durationToFullString(Duration duration) {
        String durationStr = duration.toString();
        return durationStr
                .replace("PT", "")
                .replace("H", "小时")
                .replace("M", "分钟")
                .replaceAll("(?:[.,]([0-9]{0,9}))?S", "秒");
    }

    /**
     * 将时间段按照单位拆分为若干时间点
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param unit      时间单位
     * @return 时间点列表
     */
    public static List<LocalDateTime> split(LocalDateTime beginTime, LocalDateTime endTime, ChronoUnit unit) {
        List<LocalDateTime> result = new ArrayList<>();
        LocalDateTime temp = beginTime;
        while (!temp.isAfter(endTime)) {
            result.add(temp);
            temp = temp.plus(1, unit);
        }
        return result;
    }

    /**
     * 获取最近几天的时间点,每天的0点,包括今天
     *
     * @param days 天数
     * @return 时间点列表
     */
    public static List<LocalDateTime> getRecentDays(int days) {
        ArrayList<LocalDateTime> result = new ArrayList<>();
        LocalDateTime temp = LocalDateTime.now().toLocalDate().atStartOfDay();
        for (int i = 0; i < days; i++) {
            result.add(temp.minusDays(i));
        }
        Collections.reverse(result);
        return result;
    }


    /**
     * 获取时间单位中文名
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 时间单位中文名
     */
    public static String getUnitCnName(LocalDateTime beginTime, LocalDateTime endTime) {
        Period period = Period.between(beginTime.toLocalDate(), endTime.toLocalDate());
        switch (getUnit(period)) {
            case YEARS:
                return "年";
            case MONTHS:
                return "月";
            case WEEKS:
                return "周";
            case DAYS:
                return "日";
            default:
                return "";
        }
    }

    /**
     * 根据时间差获取时间字符串
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 时间字符串
     */
    public static String getTimeByUnit(LocalDateTime beginTime, LocalDateTime endTime) {
        Period period = Period.between(beginTime.toLocalDate(), endTime.toLocalDate());
        switch (getUnit(period)) {
            case YEARS:
                return beginTime.format(DateTimeFormatter.ofPattern("yyyy年"));
            case MONTHS:
                return beginTime.format(DateTimeFormatter.ofPattern("yyyy年M月"));
            case WEEKS:
                WeekFields weekFields = WeekFields.of(java.util.Locale.getDefault());
                int week = beginTime.get(weekFields.weekOfWeekBasedYear());
                return beginTime.format(DateTimeFormatter.ofPattern("yyyy年")) + "第" + week + "周";
            case DAYS:
                return beginTime.format(DateTimeFormatter.ofPattern("yyyy年M月d日"));
            default:
                return "";
        }
    }


    /**
     * 获取时间单位
     *
     * @param period 时间差
     * @return 时间单位
     */
    public static ChronoUnit getUnit(Period period) {
        // 超过一年就以年为单位
        if (period.toTotalMonths() >= 12) {
            return ChronoUnit.YEARS;
        }

        // 超过一个月就以月为单位
        if (period.toTotalMonths() >= 1) {
            return ChronoUnit.MONTHS;
        }

        if (period.getDays() >= 7) {
            return ChronoUnit.WEEKS;
        }

        // 超过一天就以天为单位
        if (period.getDays() >= 1) {
            return ChronoUnit.DAYS;
        }

        // 否则以小时为单位
        return ChronoUnit.HOURS;
    }

    /**
     * 获取目标时间
     *
     * @param cValueEnd  偏移单位
     * @param sourceTime 原时间
     * @param num        偏移量
     * @return 结果
     */
    public static LocalDateTime getTargetTime(char cValueEnd, LocalDateTime sourceTime, long num) {
        LocalDateTime targetTime = LocalDateTime.now().minusMonths(DEFAULT_TARGET_TIME_OF_MONTH);
        switch (cValueEnd) {
            case OFFSET_UNIT_HOUR:
                targetTime = sourceTime.minusHours(num);
                break;
            case OFFSET_UNIT_DAY:
                targetTime = sourceTime.minusDays(num);
                break;
            case OFFSET_UNIT_MONTH:
                targetTime = sourceTime.minusMonths(num);
                break;
            case OFFSET_UNIT_YEAR:
                targetTime = sourceTime.minusYears(num);
                break;
            default:
                break;
        }
        return targetTime;
    }

    /**
     * 计算时间相差天数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return long
     */
    public static long getDaysBetweenDates(LocalDateTime startTime, LocalDateTime endTime) {
        return ChronoUnit.DAYS.between(endTime, startTime);
    }

    /**
     * 计算时间相差天数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return long
     */
    public static long getDaysBetweenDates(LocalDate startTime, LocalDate endTime) {
        return ChronoUnit.DAYS.between(endTime, startTime);
    }

    /**
     * 时间字符串转LocalDateTime
     *
     * @param time   time
     * @param format format
     * @return {@link LocalDateTime}
     */
    public static LocalDateTime stringToLocalDateTime(String time, String format) {
        if (StringUtils.isEmpty(time)) {
            return null;
        }
        format = StringUtils.isEmpty(format) ? "yyyy-MM-dd HH:mm:ss" : format;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime;
    }


    /**
     * 事件转字符串
     *
     * @param localDateTime time
     * @param format        格式
     * @return 时间字符串
     */
    public static String localDateTimeToString(LocalDateTime localDateTime, String format) {
        if (localDateTime == null) {
            return "";
        }
        format = StringUtils.isEmpty(format) ? "yyyy-MM-dd HH:mm:ss" : format;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return localDateTime.format(formatter);
    }

    /**
     * Date转LocalDateTime
     *
     * @param date date
     * @return {@link LocalDateTime}
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDateTime();
    }
}
