package com.trs.police.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Objects;
import lombok.Getter;

/**
 * 预警类型
 *
 * <AUTHOR>
 */
public enum WarningModelTypeEnum {

    /**
     * 人员预警
     */
    PERSON(1L,1L, "人员模型"),
    /**
     * 群体预警
     */
    GROUP(2L,2L, "群体模型"),
    /**
     * 区域预警
     */
    AREA(3L, 3L, "区域模型"),
    /**
     * 人员-区域预警
     */
    PERSON_AREA(4L, 1L, "区域预警"),
    /**
     * 人员-场所预警
     */
    PERSON_PLACE(5L, 1L, "场所预警"),
    /**
     * 人员-区域预警
     */
    PERSON_IMPORTANT_AREA(6L, 1L, "重点区域预警"),
    /**
     * 群体-聚集
     */
    GROUP_AGGREGATE(7L,2L, "聚集预警"),
    /**
     * 群体-分散
     */
    GROUP_SPREAD(8L, 2L, "分散预警"),
    /**
     * 区域-区域轨迹预警
     */
    AREA_TRACK(9L, 3L, "区域轨迹预警"),

    /**
     * 自定义预警模型
     */
    CUSTOM_MODEL(10L, 1L, "自定义模型预警");

    @JsonValue
    @Getter
    @EnumValue
    private final Long code;

    @Getter
    private final Long pid;

    @Getter
    private final String name;


    WarningModelTypeEnum(Long code, Long pid, String name) {
        this.code = code;
        this.pid = pid;
        this.name = name;
    }

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static WarningModelTypeEnum codeOf(Long code) {
        if (Objects.nonNull(code)) {
            for (WarningModelTypeEnum typeEnum : WarningModelTypeEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

}
