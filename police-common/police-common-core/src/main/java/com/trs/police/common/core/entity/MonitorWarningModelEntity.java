package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 预警模型
 *
 * <AUTHOR>
 * @date 2022/6/23 16:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_control_monitor_warning_model", autoResultMap = true)
public class MonitorWarningModelEntity implements Serializable {

    private static final long serialVersionUID = -3965805104311227917L;

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 模型标题
     */
    private String title;
    /**
     * 图标
     */
    private String iconUrl;
    /**
     * 详情
     */
    private String detail;
    /**
     * 模型类型 control_model_type
     */
    private Long type;
    /**
     * 区域id
     */
    private Long importantAreaId;
    /**
     * 区域类型 control_warning_source_category
     */
    private Long placeCode;

    private Boolean enableStatus;
    /**
     * 推荐标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED,javaType=true)
    private List<Integer> recommendLabel;

    /**
     * 推荐户籍地
     */
    @TableField(typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private List<String> recommendDistrict;


    /**
     * 警种
     */
    @TableField(value = "police_category")
    private String policeCategory;

    /**
     * 是否删除
     */
    @TableLogic(value = "0", delval = "1")
    private Boolean deleteStatus;
}
