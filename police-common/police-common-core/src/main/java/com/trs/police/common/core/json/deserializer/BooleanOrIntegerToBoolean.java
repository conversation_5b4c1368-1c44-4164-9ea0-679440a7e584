package com.trs.police.common.core.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.trs.common.utils.StringUtils;

import java.io.IOException;
import java.util.Objects;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/4/8 17:16
 * @since 1.0
 */
public class BooleanOrIntegerToBoolean extends JsonDeserializer<Boolean> {

    private final Boolean defaultEmptyValue;
    private final Boolean defaultNullValue;

    public BooleanOrIntegerToBoolean() {
        this(null, null);
    }

    public BooleanOrIntegerToBoolean(Boolean defaultEmptyValue, Boolean defaultNullValue) {
        this.defaultEmptyValue = defaultEmptyValue;
        this.defaultNullValue = defaultNullValue;
    }

    @Override
    public Boolean deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String value = jsonParser.getValueAsString();
        if (StringUtils.isInt(value)) {
            return !Objects.equals(0, Integer.parseInt(value));
        } else if (Objects.equals(Boolean.TRUE.toString(), value)) {
            return Boolean.TRUE;
        } else if (Objects.equals(Boolean.FALSE.toString(), value)) {
            return Boolean.FALSE;
        } else if (Objects.isNull(value)) {
            return defaultNullValue;
        } else {
            return defaultEmptyValue;
        }
    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2025</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2025/4/8 17:52
     * @since 1.0
     */
    public static class EmptyToFalse extends BooleanOrIntegerToBoolean {

        public EmptyToFalse() {
            super(false, null);
        }
    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2025</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2025/4/8 17:52
     * @since 1.0
     */
    public static class NullOrEmptyToFalse extends BooleanOrIntegerToBoolean {

        public NullOrEmptyToFalse() {
            super(false, false);
        }
    }
}
