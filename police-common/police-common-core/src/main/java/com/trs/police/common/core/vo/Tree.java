package com.trs.police.common.core.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

/**
 * 树形结构的接口定义。
 *
 * <p>该接口定义了树形结构的基本属性和方法。</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022-05-24
 */
public interface Tree extends Serializable {

    /**
     * 获取节点的主键。
     *
     * <p>主键不可为空。</p>
     *
     * @return 节点的主键
     */
    Integer getId();

    /**
     * 获取父节点的主键。
     *
     * <p>父节点id建议不可为空。</p>
     *
     * @return 父节点的主键
     */
    Long getParentId();

    /**
     * 获取节点的检索线索。
     *
     * <p>检索线索可为空。</p>
     *
     * @return 节点的检索线索
     */
    String key();

    /**
     * 获取节点的深度。
     *
     * @return 节点的深度
     */
    String getTreeLevel();

    /**
     * 获取节点的排序值。
     *
     * <p>如果节点没有排序值，则根据id升序排序。</p>
     *
     * @return 节点的排序值，可能为空
     */
    Optional<Integer> sort();

    /**
     * 设置节点的子节点列表。
     *
     * @param children 子节点列表
     */
    void setChildren(List<? extends Tree> children);
}
