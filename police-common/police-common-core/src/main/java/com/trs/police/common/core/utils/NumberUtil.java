package com.trs.police.common.core.utils;

import java.math.BigDecimal;
import java.text.NumberFormat;

/**
 * 数字格式化工具类
 *
 * <AUTHOR>
 */
public class NumberUtil {

    private NumberUtil() {
    }

    /**
     * double转为百分比字符串(1.0 -> 100%)
     *
     * @param number 小数
     * @return 字符串
     */
    public static String doubleToHundredPercent(Double number) {
        if (number == null) {
            return "0%";
        }
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        return numberFormat.format(number);
    }

    /**
     * double转为百分比字符串(1.0 -> 1%)
     *
     * @param number 小数
     * @return 字符串
     */
    public static String doubleToPercent(Double number) {
        if (number == null) {
            return "0%";
        }
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        return numberFormat.format(number / 100);
    }


    /**
     * 将数字转为带逗号的字符串
     *
     * @param number 数字
     * @return 带逗号的字符串
     */
    public static String formatNumberWithCommas(Long number) {
        String numberString = String.valueOf(number);
        StringBuilder formattedNumber = new StringBuilder();

        int length = numberString.length();
        for (int i = 0; i < length; i++) {
            formattedNumber.append(numberString.charAt(i));
            if (i < length - 1 && (length - i - 1) % 3 == 0) {
                formattedNumber.append(",");
            }
        }

        return formattedNumber.toString();
    }

    /**
     * 计算百分比
     *
     * @param numerator   分子
     * @param denominator 分母
     * @return 百分比
     */
    public static String calculatePercentage(Long numerator, Long denominator) {
        if (denominator == 0) {
            return "0%";
        }
        long percentage = Math.round(((double) numerator / denominator) * 100);
        return percentage + "%";
    }

    /**
     * 除法运算，四舍五入
     *
     * @param numerator   分子
     * @param denominator 分母
     * @return 百分比
     */
    public static int division(Integer numerator, Integer denominator) {
        if (denominator == 0) {
            return 0;
        }
        return Math.round((float)numerator / denominator);
    }

    /**
     * 比率计算（返回*100后的小数）
     *
     * @param begin   分子
     * @param end     分母
     * @return 百分比
     */
    public static double getRateDouble(Integer begin, Integer end) {
        double rate = end == 0 ? 0 : (begin) / (double) end;
        return BigDecimal.valueOf(rate * 100).doubleValue();
    }

    /**
     * 比率计算
     *
     * @param begin   分子
     * @param end     分母
     * @return 百分比
     */
    public static double getRateDoubleV2(Integer begin, Integer end) {
        double rate = end == 0 ? 0 : (begin) / (double) end;
        return BigDecimal.valueOf(rate).doubleValue();
    }
}
