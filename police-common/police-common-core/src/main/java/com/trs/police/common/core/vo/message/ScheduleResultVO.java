package com.trs.police.common.core.vo.message;

import com.trs.police.common.core.constant.enums.DelayMessageTypeEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * schedule执行结果vo
 *
 * <AUTHOR>
 */
@Data
public class ScheduleResultVO {

    /**
     * 任务id
     */
    private Long jobId;
    /**
     * 模块
     */
    private OperateModule module;
    /**
     * 操作
     */
    private DelayMessageTypeEnum operation;
    /**
     * 执行时间
     */
    private LocalDateTime executeTime;
    /**
     * 执行结果
     */
    private Boolean success;
}
