package com.trs.police.common.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.vo.control.CategoryVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 预警配置(WarningConfig)数据访问类
 *
 * <AUTHOR>
 * @since 2022-08-11 14:05:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_warning_source_type", autoResultMap = true)
public class WarningSourceTypeEntity implements Serializable {

    private static final long serialVersionUID = -12906093680402095L;

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 元数据enName
     */
    private String enName;
    /**
     * 类型中文名
     */
    private String cnName;

    /**
     * 预警详情模板
     */
    private String contentTemplate;

    /**
     * 图片
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String[] imageColumns;

    /**
     * 模块配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private CategoryVO[] properties;

    /**
     * 轨迹信息配置
     */
    private String extend;

    /**
     * 相似度
     */
    private String similarity;

    /**
     * 感知源类型
     */
    private Long sourceType;
    /**
     * id类型
     */
    private Long identifierType;
    /**
     * 状态 0启用 1停用
     */
    private Integer status;
}