package com.trs.police.common.openfeign.starter.service;

import com.trs.police.common.core.constant.PoliceMicroserviceNameConstant;
import com.trs.police.common.core.dto.ProfileVirtualIdentityDto;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.TrackVO;
import com.trs.police.common.core.vo.message.ChangeEventData;
import com.trs.police.common.core.vo.profile.*;
import com.trs.police.common.openfeign.starter.vo.PersonPoliceVO;
import com.trs.police.common.openfeign.starter.vo.UpdatePersonRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 群体 feign接口
 *
 * <AUTHOR>
 */
@FeignClient(name = PoliceMicroserviceNameConstant.SERVICE_NAME_PROFILE)
public interface ProfileService {

    /**
     * 群体列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link GroupVO}
     */
    @PostMapping("/group/list")
    PageResult<GroupVO> selectList(@RequestBody ListParamsRequest paramsRequest);

    /**
     * 人员列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link GroupVO}
     */
    @PostMapping("/person/list")
    PageResult<PersonVO> selectPersonList(@RequestBody ListParamsRequest paramsRequest);


    /**
     * 人员身份证号查询
     *
     * @param paramsRequest 列表请求参数
     * @return 人员idNumber
     */
    @PostMapping("/person/idNumbers")
    List<String> selectPersonIdNumbers(@RequestBody ListParamsRequest paramsRequest);

    /**
     * 根据id查询群体信息
     *
     * @param groupId 群体id
     * @return {@link GroupVO}
     */
    @GetMapping("/group/public/{groupId}")
    GroupVO getById(@PathVariable("groupId") Long groupId);

    /**
     * 根据id查询群体信息
     *
     * @param batchIds 群体ids
     * @return {@link GroupVO}
     */
    @PostMapping("/group/batch-ids")
    List<GroupVO> getGroupByIds(@RequestBody List<Long> batchIds);

    /**
     * 名称模糊查询群体id
     *
     * @param groupName 群体名称
     * @return {@link Long} id
     */
    @GetMapping("/group/query/fuzzy")
    List<Long> fuzzyQueryByGroupName(@RequestParam("groupName") String groupName);

    /**
     * 根据证件类型和证件号码模糊查询人员档案列表
     *
     * @param certificateNumber 证件号码
     * @param certificateType   证件类型
     * @return {@link PersonVO}
     */
    @GetMapping("/person/fuzzy")
    List<PersonVO> getPersonListFuzzy(@RequestParam("certificateNumber") String certificateNumber,
                                      @RequestParam("certificateType") String certificateType);

    /**
     * 根据证件号码查询人员档案
     *
     * @param idNumber 证件号码
     * @return {@link PersonVO}
     */
    @GetMapping("/person/public")
    PersonVO getPersonByIdNumber(@RequestParam("idNumber") String idNumber);

    /**
     * 根据标识符及类型查询人员信息
     *
     * @param identifierNumber 标识符号码
     * @param identifierType   标识符类型
     * @return 人员信息
     */
    @GetMapping("/person/public/identifier")
    List<PersonVO> findByIdentifier(@RequestParam("identifierNumber") String identifierNumber,
                                    @RequestParam("identifierType") Integer identifierType);

    /**
     * 获取群体在控，失控等人数统计
     *
     * @param id 群体id
     * @return {@link GroupStatisticVO}
     */
    @GetMapping("group/{id}/statistics")
    GroupStatisticVO getGroupStatistic(@PathVariable("id") Long id);

    /**
     * 根据人员id，批量获取人员信息
     *
     * @param ids 人员id数组
     * @return {@link PersonVO}
     */
    @PostMapping("/person/public/batch-id")
    List<PersonVO> getPersonByIds(@RequestBody List<Long> ids);

    /**
     * 根据群体id，获取在控人信息
     *
     * @param id 群体id
     * @return {@link PersonVO}
     */
    @GetMapping("/group/{id}/in-control/person")
    List<PersonVO> getGroupInControlPerson(@PathVariable("id") Long id);

    /**
     * 根据群体id，获取在控人信息
     *
     * @param id 群体id
     * @return {@link PersonVO}
     */
    @GetMapping("/group/public/{id}/in-control/person")
    List<PersonVO> getGroupInControlPersonInPublic(@PathVariable("id") Long id);

    /**
     * 批量更新人员档案信息（只新增，不删除和修改）
     *
     * @param request 人员信息
     * @return id
     */
    @PostMapping("/person/public/update/batch")
    List<Long> updatePersonList(@RequestBody UpdatePersonRequest request);

    /**
     * 更新人员档案信息
     *
     * @param personVO 人员信息
     * @return {@link Long}
     */
    @PostMapping("/person/public/update")
    Long updatePersonInfo(@RequestBody PersonVO personVO);

    /**
     * 设置人员临控状态
     *
     * @param personIds     人员id
     * @param controlStatus 布控状态
     * @return {@link Integer}
     */
    @PostMapping("/person/public/set-monitor/{monitorStatus}")
    Integer setPersonsMonitorStatus(@RequestBody List<Long> personIds,
                                    @PathVariable("monitorStatus") Integer controlStatus);

    /**
     * 设置人员临控状态
     *
     * @param idNumbers     人员身份证
     * @param monitorStatus 布控状态
     * @return {@link Integer}
     */
    @PostMapping(value = "/person/public/set-monitor/id-number/{monitorStatus}")
    Integer setPersonsMonitorStatusByIdNumbers(@RequestBody List<String> idNumbers,
                                               @PathVariable("monitorStatus") Integer monitorStatus);

    /**
     * 设置人员常控状态
     *
     * @param personIds     人员id
     * @param controlStatus 布控状态
     * @return {@link Integer}
     */
    @PostMapping(value = {"/person/public/set-control/{controlStatus}"})
    Integer setPersonsControlStatus(@RequestBody List<Long> personIds,
                                    @PathVariable("controlStatus") Integer controlStatus);

    /**
     * 取消人员常控状态
     *
     * @param id 人员id
     */
    @PutMapping(value = "/person/public/{id}/un-control")
    void setPersonUnControl(@PathVariable("id") Long id);

    /**
     * 标签查询 http://192.168.200.192:3001/project/4974/interface/api/141735
     *
     * @param id id
     * @return 标签列表
     */
    @GetMapping("/label/{id}")
    LabelVO getLabel(@PathVariable("id") Long id);

    /**
     * 根据idList查询标签
     *
     * @param ids id
     * @return 标签列表
     */
    @PostMapping("/label/list")
    List<LabelVO> getLabelByIds(@RequestBody List<Long> ids);

    /**
     * 人员id查询
     *
     * @param paramsRequest 列表请求参数
     * @return 人员Id
     */
    @PostMapping("/person/public/id/list")
    List<Long> getPersonIds(@RequestBody ListParamsRequest paramsRequest);

    /**
     * 根据人员身份证号查询所在群体
     *
     * @param idNumber 身份证号
     * @return 群体id
     */
    @GetMapping("/person/public/group/{idNumber}")
    List<Long> selectGroupIdsByIdNumber(@PathVariable("idNumber") String idNumber);

    /**
     * 查询群体人员总数
     *
     * @param groupIds 群体id
     * @return 数量
     */
    @PostMapping("/group/public/count-all")
    Integer countGroupMembers(@RequestBody List<Long> groupIds);

    /**
     * 根据人员id，批量获取人员信息
     *
     * @param idNumbers 人员idNumbers
     * @return {@link PersonVO}
     */
    @PostMapping("/person/public/batch-idNumbers")
    List<PersonVO> getPersonByIdNumbers(@RequestBody List<String> idNumbers);

    /**
     * 查看人员是否存在
     *
     * @param idType   证件号码
     * @param idNumber 证件类型
     * @return Long
     */
    @GetMapping("/person/id/{idType}/{idNumber}")
    Long findIdByIdTypeAndIdNumber(@PathVariable("idType") Integer idType, @PathVariable("idNumber") String idNumber);


    /**
     * 获取列表过滤参数树结构
     *
     * @param module 模块
     * @return LabelVO
     */
    @GetMapping("/label/params/tree/{module}")
    List<LabelVO> getListFilterParams(@PathVariable("module") String module);


    /**
     * 获取列表过滤参数树结构
     *
     * @param module 模块
     * @return LabelVO
     */
    @GetMapping("/public/label/params/tree/{module}")
    List<LabelVO> getListFilterParamsNoPermission(@PathVariable("module") String module);

    /**
     * 根据人员id获取人员信息
     *
     * @param personId 人员id
     * @return {@link PersonVO}
     */
    @Deprecated
    @GetMapping("/person/public/{personId}")
    PersonVO getByPersonId(@PathVariable("personId") Long personId);

    /**
     * 根据人员id获取人员信息
     *
     * @param personId 人员id
     * @return {@link PersonVO}
     */
    @GetMapping(value = {"/person/public/findById"})
    PersonVO findById(@RequestParam("personId") Long personId);

    /**
     * 新增常控时人员档案列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link com.trs.police.common.core.vo.profile.PersonVO}
     */
    @PostMapping("/person/list/regular")
    PageResult<PersonVO> selectRegularList(@RequestBody ListParamsRequest paramsRequest);

    /**
     * 根据人员档案id查询公安管控信息
     *
     * @param personId 人员id
     * @return 公安管控信息
     */
    @GetMapping("/person/public/{personId}/control")
    PersonPoliceVO getControlInfoByPersonId(@PathVariable("personId") Long personId);

    /**
     * 新增常控时人员档案列表查询
     *
     * @param paramsRequest 列表请求参数
     * @return {@link Long}
     */
    @PostMapping("/person/list/regular/ids")
    List<Long> getIdsByRegularParams(@RequestBody ListParamsRequest paramsRequest);

    /**
     * 处理数据库更新事件
     *
     * @param changeEventData 数据库更新事件
     */
    @PostMapping("/public/cdc/message")
    void receiveCdcMessage(@RequestBody ChangeEventData changeEventData);


    /**
     * 获取人员管控派出所id
     *
     * @param identifierNumber 证件号码
     * @param identifierType   证件类型
     * @return 管控派出所id
     */
    @GetMapping("/person/public/control-dept")
    Long getPersonControlDeptIdByIdentifier(@RequestParam("identifierNumber") String identifierNumber,
                                            @RequestParam("identifierType") Integer identifierType);

    /**
     * 根据编号获取id
     *
     * @param bh 编号
     * @return id
     */
    @GetMapping("/public/case/id/{bh}")
    String getCaseIdByBh(@PathVariable("bh") String bh);

    /**
     * 根据id获取编号
     *
     * @param id id
     * @return bh
     */
    @GetMapping("/public/case/bh/{id}")
    String getCaseBhById(@PathVariable("id") String id);

    /**
     * 根据编号获取id
     *
     * @param bh 编号
     * @return id
     */
    @GetMapping("/public/jq/id/{bh}")
    String getJqIdByBh(@PathVariable("bh") String bh);

    /**
     * 新增新虚拟身份
     *
     * @param virtualIdentity 虚拟身份
     */
    @PostMapping("/person/virtual-identity")
    void addVirtualIdentity(@RequestBody ProfileVirtualIdentityDto virtualIdentity);

    /**
     * 根据虚拟身份查询人员
     *
     * @param tel 电话号码
     * @return 人员列表
     */
    @GetMapping("/person/public/tel")
    List<PersonVO> getPersonByTel(@RequestParam("tel") String tel);

    /**
     * 根据人员id查询人员群体信息
     *
     * @param personId 人员id
     * @return 群体信息
     */
    @GetMapping("/group/public/person-id")
    List<GroupVO> getGroupsByPersonId(@RequestParam("personId") Long personId);

    /**
     * 更新人员电话号码
     *
     * @param id  人员id
     * @param tel 电话号码
     */
    @PutMapping("/person/{id}/tel")
    void updatePersonTel(@PathVariable("id") Long id, @RequestBody List<String> tel);


    /**
     * 查询人员群体活跃程度
     *
     * @param personId 人员id
     * @param groupId  人员群体id
     * @return 活跃程度
     */
    @GetMapping("/person/activity-level")
    CodeNameVO getPersonActivityLevel(@RequestParam("id") Long personId, @RequestParam("groupId") Long groupId);

    /**
     * 根据人员id查询人员卡片
     *
     * @param id 人员id
     * @return 卡片信息
     */
    @GetMapping("/person/{id}/card/by-id")
    PersonCardVO getPersonCardById(@PathVariable("id") Long id);

    /**
     * 根据人员身份证号查询人员卡片
     *
     * @param idNumber 人员身份证号
     * @return 卡片信息
     */
    @GetMapping("/{idNumber}/card")
    PersonCardVO getCardByIdNumber(@PathVariable("idNumber") String idNumber);

    /**
     * 标签查询
     *
     * @param type 类型
     * @return 标签列表
     */
    @GetMapping("/label/{type}/allLabelList")
    List<LabelVO> allLabelList(@PathVariable("type") String type);

    /**
     * 标签查询
     *
     * @param type 类型
     * @return 标签列表
     */
    @GetMapping("/label/{type}/tree")
    List<LabelVO> getLabelList(@PathVariable("type") String type);

    /**
     * 标签查询(数据权限)
     *
     * @param type 类型
     * @return 标签列表
     */
    @GetMapping("/label/{type}/tree/permission")
    List<LabelVO> getLabelListPermission(@PathVariable("type") String type);

    /**
     * 计算档案完整度
     *
     * @param deptId          部门id
     * @param level           常控级别
     * @param regularLabelStr 常控标签json
     * @return 结果
     */
    @GetMapping("/public/archives/person/{deptId}/complete-rate")
    Double getArchiveCompleteRate(@PathVariable("deptId") Long deptId,
                                  @RequestParam(value = "level", required = false) String level,
                                  @RequestParam(value = "regularLabelStr", required = false) String regularLabelStr);


    /**
     * 导入线索
     *
     * @param code 代码
     * @param relationType 关联方式
     */
    @GetMapping("/public/import/clue/12345/{code}/{relationType}")
    void importClue(@PathVariable("code") String code, @PathVariable("relationType") String relationType);

    /**
     * 导入警情
     *
     * @param code 代码
     * @param relationType 关联方式 idCard 身份证关联 phone 电话关联 idCardAndPhone 都有
     */
    @GetMapping("/public/import/jq/sthy/{code}/{relationType}")
    void importJq(@PathVariable("code") String code, @PathVariable("relationType") String relationType);

    /**
     * 导入老警情
     *
     * @param code 代码
     * @param relationType 关联方式
     */
    @GetMapping("/public/import/oldJq/sthy/{code}/{relationType}")
    void importOldJq(@PathVariable("code") String code, @PathVariable("relationType") String relationType);

    /**
     * 根据id查询轨迹
     *
     * @param trackIds 轨迹id
     * @return 感知源
     */
    @PostMapping("/public/archives/person/detail/track/by-id")
    List<TrackVO> getPersonTrack(@RequestBody List<String> trackIds);

    /**
     * 根据案件类别获取案事件id
     *
     * @param caseType 案件类型
     * @return {@link List}<{@link Long}>
     */
    @GetMapping("/public/case/getCaseEventIdsByCaseType/{caseType}")
    List<Long> getCaseEventIdsByCaseType(@PathVariable("caseType") String caseType);

    /**
     * 查询线索列表
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/public/clue/list")
    PageResult<ClueListVO> getClueList(@RequestBody ListParamsRequest request);

    /**
     * 新增群体
     *
     * @param groupName 群体名称
     * @param userId 特定用户
     * @param deptId 部门id
     * @param params 参数
     * @return id
     */
    @GetMapping("/dynamic/public/saveGroupOrPerson")
    public Long saveGroupOrPerson(@RequestParam(value = "groupName", required = false) String groupName,
                                  @RequestParam("userId") Long userId,
                                  @RequestParam("deptId") Long deptId,
                                  @RequestParam(value = "params", required = false) String params);

    /**
     * 名称精确查询
     *
     * @param groupName 群体名称
     * @return 结果
     */
    @GetMapping("/group/public/query/byName")
    Long queryByGroupName(@RequestParam("groupName") String groupName);

    /**
     * 根据警情编号查询警情
     *
     * @param bh bh
     * @return 警情
     */
    @GetMapping("/public/jq/findByBh")
    JqCommonVO findByBh(@RequestParam("bh") String bh);

    /**
     * 根据警情编号查询警情
     *
     * @param id id
     * @return 警情
     */
    @GetMapping("/public/jq/findJqById")
    List<JqCommonVO> findJqById(@RequestParam("id") String id);

    /**
     * 根据警情编号查下警情反馈信息
     *
     * @param jjdbh 警情编号
     * @return 反馈信息
     */
    @GetMapping("/public/jq/findFkxxByBh")
    List<JqFkxxCommonVO> findFkxxByBh(@RequestParam("bh") String jjdbh);

    /**
     * 根据群体id查询所有相关人员id
     *
     * @param groupId 群体ID
     * @return 结果
     */
    @GetMapping("/group/public/query/byGroupId")
    List<Long> queryByGroupId(@RequestParam("groupId") Long groupId);

    /**
     * 新增群体-人员关联关系
     *
     * @param userId 登录用户
     * @param deptId 登录用户部门
     * @param groupId 群体ID
     * @param personIds 人员ID集合
     */
    @GetMapping("/group/public/addPersonGroupRelation")
    void addPersonGroupRelation(@RequestParam("userId") Long userId,
                                @RequestParam("deptId") Long deptId,
                                @RequestParam("groupId") Long groupId,
                                @RequestParam("personIds") List<Long> personIds);

    /**
     * 根据警情编号查询警情
     *
     * @param idCard idCard
     * @param tel tel
     * @return 警情
     */
    @GetMapping("/public/jq/findByTelAndIdCard")
    List<JqCommonVO> findByTelAndIdCard(@RequestParam("idCard") String idCard, @RequestParam("tel") String tel);
}
