package com.trs.police.common.openfeign.starter.service;

import com.trs.police.common.core.constant.PoliceMicroserviceNameConstant;
import com.trs.police.common.core.constant.enums.DataPermissionEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.SearchUserDTO;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.DeptDistrict;
import com.trs.police.common.core.params.DeptRequestParams;
import com.trs.police.common.core.vo.IdNameVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.permission.*;
import com.trs.police.common.openfeign.starter.DTO.ApprovalUserV2DTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 用户组织机构open feign服务接口
 *
 * <AUTHOR>
 */
@FeignClient(name = PoliceMicroserviceNameConstant.SERVICE_NAME_PERMISSION)
public interface PermissionService {

    /**
     * 根据用户id查询用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     */
    @GetMapping("/public/user/info/{userId}")
    UserDto getUserById(@PathVariable("userId") Long userId);

    /**
     * 根据部门编号前缀查询所有用户列表
     *
     * @param deptCodePrefix 部门编号前缀
     * @return 用户列表
     */
    @GetMapping("/public/user/dept-code-prefix")
    List<CurrentUser> getUserListByDeptCodePrefix(@RequestParam("deptCodePrefix") String deptCodePrefix);

    /**
     * 根据id查询部门
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    @GetMapping("/public/dept/{deptId}")
    DeptDto getDeptById(@PathVariable("deptId") Long deptId);

    /**
     * 根据id查询部门
     *
     * @param deptIds 部门id
     * @return 部门信息
     */
    @PostMapping("/public/dept/list")
    List<DeptDto> getDeptByIds(@RequestBody List<Long> deptIds);

    /**
     * 根据编码查询部门
     *
     * @param deptCode 部门code
     * @return 部门信息
     */
    @GetMapping("public/dept/code/{deptCode}")
    DeptDto getDeptByCode(@PathVariable("deptCode") String deptCode);

    /**
     * 获取部门
     *
     * @param deptCodes code
     * @return 部门
     */
    @PostMapping("public/dept/codes")
    List<DeptDto> getDeptByCodes(@RequestBody List<String> deptCodes);

    /**
     * 根据上级部门id查询子部门
     *
     * @param pid 上级部门id
     * @return 子部门
     */
    @GetMapping("/public/dept/pid/{pid}")
    List<DeptDto> getDeptByPid(@PathVariable("pid") Long pid);

    /**
     * 根据部门id查询所有子部门（包括本身）
     *
     * @param deptId 部门id
     * @return {@link List}<{@link DeptDto}>
     */
    @GetMapping("/public/dept/deptId/{deptId}")
    List<DeptDto> getChildrenByDept(@PathVariable("deptId") Long deptId);

    /**
     * 根据上级部门code查询子部门
     *
     * @param pCode 上级部门code
     * @return 子部门
     */
    @GetMapping("/public/dept/p-code/{pCode}")
    List<DeptDto> getDeptByPCode(@PathVariable("pCode") String pCode);


    /**
     * 根据部门编号前缀查询
     *
     * @param prefix 前缀
     * @return 部门信息
     */
    @GetMapping("public/dept/code")
    List<DeptDto> getDeptByCodePrefix(@RequestParam("prefix") String prefix);

    /**
     * 根据部门集合获取用户列表
     *
     * @param deptIds 部门id集合
     * @return 用户列表
     */
    @PostMapping("/user/getUserByDeptIds")
    List<UserVO> getUserByDeptIds(@RequestBody List<Long> deptIds);

    /**
     * 根据警号集合获取用户列表
     *
     * @param jhs 警号编号
     * @return 用户列表
     */
    @PostMapping("/user/getUserByJhs")
    List<UserVO> getUserByJhs(@RequestBody List<String> jhs);

    /**
     * 获取部门树(带权限)
     *
     * @return {@link DeptVO}
     */
    @GetMapping("/dept/tree")
    List<DeptVO> getTreeByDataPermission();

    /**
     * 获取部门树(指定根节点)
     *
     * @param rootCode 根节点编码
     * @return {@link DeptVO}
     */
    @GetMapping("/dept/tree/root/{rootCode}")
    List<DeptVO> getTreeByRootCode(@PathVariable("rootCode") String rootCode);

    /**
     * 查询部门树(不带权限)
     *
     * @return {@link DeptVO}
     * <AUTHOR>
     * @since 2022/4/6 15:43
     **/
    @GetMapping("/public/dept/tree/all")
    List<DeptVO> getDeptTreeAll();

    /**
     * 查询部门树（指定根节点）
     *
     * @param deptId 根节点部门id
     * @return 部门树
     **/
    @GetMapping("/dept/tree/{deptId}")
    DeptVO getDeptTree(@PathVariable("deptId") Long deptId);

    /**
     * 根据用户id和部门id拼装currentUser
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return {@link CurrentUser}
     */
    @GetMapping("/public/user/{userId}/{deptId}")
    CurrentUser findCurrentUser(@PathVariable("userId") Long userId, @PathVariable("deptId") Long deptId);

    /**
     * 根据用户id和部门id拼装currentUser
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return {@link CurrentUser}
     */
    @GetMapping("/public/simple-user/{userId}/{deptId}")
    SimpleUserVO findSimpleUser(@PathVariable("userId") Long userId, @PathVariable("deptId") Long deptId);

    /**
     * 启用用户
     *
     * @param userId 用户id
     */
    @RequestMapping("/user/{userId}/activate")
    void activateUser(@PathVariable("userId") String userId);

    /**
     * 禁用用户
     *
     * @param userId 用户id
     */
    @RequestMapping("/user/{userId}/deactivate")
    void deactivateUser(@PathVariable("userId") String userId);

    /**
     * 查询拥有该角色的上级部门用户列表
     *
     * @param deptId 部门id
     * @param level  上级部门类型
     * @param roleId 角色id
     * @return 用户列表
     */
    @GetMapping("public/role/superior/users")
    List<UserDeptVO> getUsersByRoleAndDept(@RequestParam("deptId") Long deptId,
        @RequestParam("level") @Nullable String level,
        @RequestParam("roleId") Long roleId);

    /**
     * 获取部门下的用hi
     *
     * @param code 地域码
     * @param roleId 角色
     * @param deptType 部门类型
     * @return 部门下的用户
     */
    @GetMapping("public/role/code/type/users")
    List<UserDeptVO> getUsersByCodeAndRoleIdAndType(
            @RequestParam("deptId") @Nullable String code,
            @RequestParam("roleId") Long roleId,
            @RequestParam("level") @Nullable Integer deptType);


    /**
     * 获取审批用户
     *
     * @param dto 参数
     * @return 审批人
     */
    @PostMapping ("public/getApprovalUser")
    List<UserDeptVO> getApprovalUser(@RequestBody ApprovalUserV2DTO dto);

    /**
     * 查找部门
     *
     * @param parenId parenId
     * @param type type
     * @param childType childType
     * @return Dept
     */
    @GetMapping("public/getChildDept")
    public UserDeptVO getChildDept(
            @RequestParam("parenId") Long parenId,
            @RequestParam("type") Long type,
            @RequestParam("childType") Long childType);

    /**
     * 通过地域加警种查找部门
     *
     * @param areaCode 地域编码
     * @param policeKind 警种
     * @return 地区下的警种
     */
    @GetMapping("public/findByAreaAndPoliceKind")
    List<DeptVO> findByAreaAndPoliceKind(@RequestParam("areaCode") String areaCode, @RequestParam("policeKind") Long policeKind);

    /**
     * 查找路径上的关键节点
     *
     * @param currentDeptId currentDeptId
     * @return 关键部门
     */
    @GetMapping("public/parenDeptInKeyPath")
    DeptDto parenDeptInKeyPath(@RequestParam("currentDeptId") Long currentDeptId);


    /**
     * 查询本部门拥有该角色的用户列表
     *
     * @param deptId  部门id
     * @param roleIds 角色id
     * @return {@link UserDeptVO} 用户列表
     */
    @GetMapping("public/roles/dept/users")
    List<SimpleUserVO> getDeptUsersByRoles(
        @RequestParam("deptId") @Nullable Long deptId,
        @RequestParam("roleIds") String roleIds);

    /**
     * 查询本部门拥有该角色的用户列表
     *
     * @param deptId 部门id
     * @param roleId 角色id
     * @return 用户列表
     */
    @GetMapping("public/role/dept/users")
    List<UserDeptVO> getDeptUsersByRole(@RequestParam("deptId") Long deptId,
        @RequestParam("roleId") Long roleId);

    /**
     * 获取当前登录用户的最大数据权限
     *
     * @return {@link DataPermissionEnum}
     */
    @GetMapping("/user/current/data-permission/max")
    DataPermissionEnum getCurrentUserMaxDataPermission();

    /**
     * 获取当前登录用户的数据权限信息
     *
     * @return {@link DataPermissionEnum}
     */
    @GetMapping("/user/current/data-permission/info")
    DataPermissionInfo getCurrentUserDataPermissionInfo();

    /**
     * 判断child部门是否是parent部门的子节点
     *
     * @param parentId 父部门id
     * @param childId  子部门id
     * @return {@link Boolean}
     */
    @GetMapping("/dept/{parentId}/child/{childId}")
    Boolean isChild(@PathVariable("parentId") Long parentId, @PathVariable("childId") Long childId);

    /**
     * 获取用户武器库
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return {@link UserDeptRelationVO}
     */
    @GetMapping("/public/user/armory/{userId}/{deptId}")
    UserDeptRelationVO getUserArmory(@PathVariable("userId") Long userId, @PathVariable("deptId") Long deptId);


    /**
     * 武器库-编辑当前用户的武器库 http://192.168.200.192:3001/project/4974/interface/api/142162
     *
     * @param userDeptRelation 武器
     */
    @PutMapping("public/user/armory")
    void updateCurrentUserArmory(@RequestBody UserDeptRelationVO userDeptRelation);

    /**
     * 根据用户id查询用户详情
     *
     * @param userIds 用户id
     * @return 用户详情
     */
    @PostMapping(value = "public/user/list", consumes = MediaType.APPLICATION_JSON_VALUE)
    List<UserDto> getUserListById(@RequestBody List<Long> userIds);

    /**
     * 查询用户详情
     *
     * @return 用户详情
     */
    @GetMapping(value = "public/user/all")
    List<UserDto> getAllUserList();

    /**
     * 获取用户在该部门最大的数据权限
     *
     * @param userId 用户Id
     * @param deptId 部门Id
     * @return {@link DataPermissionEnum} 最大数据权限
     */
    @GetMapping("/data/max-permission")
    DataPermissionEnum getMaxDataPermission(@RequestParam("userId") Long userId, @RequestParam("deptId") Long deptId);

    /**
     * 根据角色名模糊获取系统角色
     *
     * @param name 用户Id
     * @return 角色信息
     */
    @GetMapping("/data/list/customByName")
    List<PermissionListVO> getCustomDataPermsByLikeName(@RequestParam("name") String name);

    /**
     * 根据角色id获取角色信息
     *
     * @param dataIds Ids
     * @return KeyValueTypeVO
     */
    @GetMapping("/data/getProfileLabelsByIds")
    List<String> getProfileLabelsByIds(@RequestParam("dataIds") String dataIds);


    /**
     * 查询部门及子部门id
     *
     * @param rootId 根节点
     * @return id列表
     */
    @GetMapping("/dept/dept-child/{rootId}")
    List<Long> getDeptIdsWithChildren(@PathVariable("rootId") Long rootId);

    /**
     * 在请求参数中加入权限部门
     *
     * @return ListParamsRequest
     */
    @GetMapping("/data/build/params")
    List<KeyValueTypeVO> buildParamsByPermission();

    /**
     * 在请求参数中加入权限部门
     *
     * @param permissionParamsEnum 构建参数模式
     * @return ListParamsRequest
     */
    @GetMapping("/data/build/params/{type}")
    List<KeyValueTypeVO> buildParamsByPermission(@PathVariable(name = "type") Integer permissionParamsEnum);

    /**
     * 获取当前用户可以查看的部门id数组，本人时返回[-1],全部时返回[]
     *
     * @return ListParamsRequest
     */
    @GetMapping("/data/dept-ids")
    List<Long> getDeptIdsByPermission();

    /**
     * 获取当前用户可以查看的部门id数组，本人时返回[-1],全部时返回[]
     *
     * @param permissionParamsEnum 构建参数模式
     * @return ListParamsRequest
     */
    @GetMapping("/data/dept-ids/{type}")
    List<Long> getDeptIdsByPermission(@PathVariable(name = "type") Integer permissionParamsEnum);


    /**
     * 通过身份证获取
     *
     * @param idNumber 身份证
     * @return 用户信息
     */
    @GetMapping("/public/user/{idNumber}")
    UserDto getUserByIdCard(@PathVariable("idNumber") String idNumber);

    /**
     * 通过身份证获取
     *
     * @param idNumbers 身份证
     * @return 用户信息
     */
    @PostMapping("/public/user/listByIdCards")
    List<UserDto> getUserByIdCards(@RequestBody List<String> idNumbers);

    /**
     * 查询当前用户信息
     *
     * @return {@link CurrentUser} 用户信息
     */
    @GetMapping("/user/current")
    CurrentUser getCurrentUserWithRole();

    /**
     * 获取角色下所有人员-部门信息
     *
     * @param roleId 角色Id
     * @return {@link CurrentUser} 人员信息
     */
    @GetMapping("/role/{roleId}/user/all-list")
    List<SimpleUserVO> getUserListByRoleId(@PathVariable("roleId") Long roleId);

    /**
     * 获取角色下所有人员-部门信息（免登版本）
     *
     * @param roleId 角色Id
     * @return {@link CurrentUser} 人员信息
     */
    @GetMapping("/public/{roleId}/user/all-list")
    List<SimpleUserVO> getUserListByRoleIdNoLogin(@PathVariable("roleId") Long roleId);

    /**
     * 根据部门和角色获取用户信息
     *
     * @param roleId 角色id
     * @param deptId 部门id
     * @return 用户信息
     */
    @GetMapping("/public/role-dept/{roleId}/{deptId}")
    List<SimpleUserVO> getUserByRoleAndDept(@PathVariable("roleId") Long roleId, @PathVariable("deptId") Long deptId);

    /**
     * 获取经纬度在指定类型部门的id
     *
     * @param lng  经度
     * @param lat  维度
     * @param type 类型 1：地区 2：派出所
     * @return 部门id
     */
    @GetMapping("/public/dept-district")
    Long getDeptIdByLatAndLng(@RequestParam("lng") String lng, @RequestParam("lat") String lat,
        @RequestParam("type") Integer type);

    /**
     * 获取经纬度在指定类型部门的code
     *
     * @param lng  经度
     * @param lat  维度
     * @param type 类型 1：地区 2：派出所
     * @return 部门code
     */
    @GetMapping("/public/deptCode-district")
    String getDeptCodeByLatAndLng(@RequestParam("lng") String lng, @RequestParam("lat") String lat,
                              @RequestParam("type") Integer type);

    /**
     * 通过参数查询部门
     *
     * @param deptRequestParams 查询参数
     * @return {@link DeptDto}
     */
    @PostMapping("/dept/public/params")
    List<DeptDto> getDeptByParams(@RequestBody DeptRequestParams deptRequestParams);

    /**
     * 在请求参数中加入档案权限
     *
     * @return ListParamsRequest
     */
    @GetMapping("/data/build/params/profile")
    List<KeyValueTypeVO> buildParamsProfileByPermission();

    /**
     * 获取当前用户可以查看的档案标签id数组
     *
     * @return 档案标签id数组
     */
    @GetMapping("/data/profile-labels")
    KeyValueTypeVO[] getUserProfileLabels();

    /**
     * 查询所有派出所
     *
     * @param pCode 上级部门编码
     * @param pid 上级部门id
     * @param areaCode code
     * @return 部门列表
     */
    @GetMapping("/public/dept/police-station")
    List<DeptDto> getPoliceStation(@RequestParam("pid") Long pid, @RequestParam("pCode") String pCode,
                                   @RequestParam("areaCode") String areaCode);

    /**
     * 根据部门编码查询下级部门
     *
     * @param deptCode 部门编码
     * @return 部门列表
     */
    @GetMapping("/dept/{deptCode}/next-level")
    List<DeptDto> getNextLevelDept(@PathVariable("deptCode") String deptCode);

    /**
     * 根据部门组织机构代码查询用户
     *
     * @param deptCode 部门编码
     * @return 用户列表
     */
    @GetMapping("/public/dept/{deptCode}/users")
    List<SimpleUserVO> getUserOfDept(@PathVariable("deptCode") String deptCode);

    /**
     * 根据部门类型获取部门
     *
     * @param deptType 部门类型
     * @return 部门列表
     */
    @GetMapping(value = "dept/public/type/{deptType}")
    List<DeptDto> getDeptByType(@PathVariable("deptType") Integer deptType);

    /**
     * 根据部门类型获取部门
     *
     * @param deptTypes 部门类型
     * @return 部门列表
     */
    @PostMapping(value = "dept/public/type/deptTypes")
    List<DeptDto> getDeptByTypes(@RequestBody List<Integer> deptTypes);

    /**
     * 获取顶级部门
     *
     * @return 部门
     */
    @GetMapping("/dept/top-level-dept/code")
    String getCurrentUserTopLevelDept();

    /**
     * 创建用户
     *
     * @param user 用户
     */
    @PostMapping("/public/user")
    void createUser(@RequestBody UserVO user);

    /**
     * 获取当前用户可以查看的档案标签id数组
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return 档案标签id数组
     */
    @GetMapping("/public/data/search/{userId}/{deptId}")
    KeyValueTypeVO[] getUserSearch(@PathVariable("userId") Long userId, @PathVariable("deptId") Long deptId);


    /**
     * 查询所有部门
     *
     * @return 部门列表
     */
    @GetMapping("/public/dept/dept-all")
    List<DeptDto> getDeptAll();

    /**
     * 查询拥有某个模块对应操做的人
     *
     * @param moduleName 模块名
     * @param operation  操作名
     * @return 权限列表
     */
    @GetMapping("/public/permission/roles")
    List<Long> rolesWithPermission(@RequestParam("moduleName") String moduleName,
        @RequestParam("operation") @Nullable String operation);

    /**
     * 根据真实用户名查询用户详情
     *
     * @param realUserNames 用户名
     * @return {@link List}<{@link UserDto}>
     */
    @PostMapping("/public/user/infos")
    List<UserDto> getUserByRealNames(@RequestBody List<String> realUserNames);

    /**
     * 根据用户名查询用户详情
     *
     * @param userNames 用户名
     * @return {@link List}<{@link UserDto}>
     */
    @PostMapping("/public/user/username/infos")
    List<UserDto> getUserByUserNames(@RequestBody List<String> userNames);

    /**
     * 角色权限列表(获取自定义角色)
     *
     * @return {@link List}<{@link IdNameVO}>
     */
    @GetMapping("/public/role")
    public List<IdNameVO> getRoles();

    /**
     * 根据部门编码列表获取当级部门所有的用户列表
     *
     * @param searchUserDTO dto参数
     * @return {@link List}<{@link SimpleUserVO}> userList
     */
    @PostMapping("/public/user/getByDeptIdList")
    List<SimpleUserVO> getUserListByDeptIdList(@RequestBody SearchUserDTO searchUserDTO);

    /**
     * 根据部门id获取子部门
     *
     * @param pid 上级部门id
     * @param usePermission 是否使用权限过滤
     * @return 子部门
     */
    @GetMapping("/dept/pid/new/{pid}")
    List<DeptVO> getNewDeptByPid(@PathVariable("pid") Long pid, @RequestParam("usePermission") Boolean usePermission);

    /**
     * 查询该id下的所有部门和用户
     *
     * @param deptId       部门id
     * @param username     用户名
     * @param containChild 是否包含子部门
     * @param postCode     岗位代码
     * @param districtCode 地域代码
     * @return {@link List }<{@link DeptVO }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-21 11:34:34
     */
    @GetMapping("/public/user/findDeptAndUserTree/{deptId}")
    DeptVO findDeptAndUserTree(@PathVariable("deptId") Long deptId,
                               @RequestParam("username") String username,
                               @RequestParam("containChild") Boolean containChild,
                               @RequestParam("postCode") Integer postCode,
                               @RequestParam("districtCode") String districtCode);

    /**
     * 查询该id下的所有部门和用户
     *
     * @param username     用户名
     * @param containChild 是否包含子部门
     * @param postCode     岗位代码
     * @param districtCode 地域代码
     * @return {@link List }<{@link DeptVO }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-21 11:34:34
     */
    @GetMapping("/public/user/findDeptAndUserTreeV3")
    DeptVO findDeptAndUserTreeV3(@RequestParam("username") String username,
                                 @RequestParam("containChild") Boolean containChild,
                                 @RequestParam("postCode") Integer postCode,
                                 @RequestParam("districtCode") String districtCode);

    /**
     * 根据部门编码和部门类型获取部门
     *
     * @param areaCode 部门编码
     * @param deptType 部门类型
     * @return 部门
     */
    @GetMapping("/public/dept/findByAreaCodeAndDeptType")
    DeptVO findByAreaCodeAndDeptType(@RequestParam("areaCode") String areaCode, @RequestParam("deptType") Long deptType);

    /**
     * 根据区域编码获取区域下的派出所信息
     *
     * @param code code
     * @return {@link List}<{@link DeptDistrict}>
     */
    @GetMapping("/public/getDeptDistrictByCode")
    List<DeptDistrict> getDeptDistrictByCode(@RequestParam("code") String code);

    /**
     * 根据部门类型获取部门
     *
     * @param policeKind 政治警种
     * @param excludePoliceKinds 排除的警种
     * @return 部门
     */
    @GetMapping("/public/dept/findControlPoliceByPoliceKind")
    List<DeptVO> findControlPoliceByPoliceKind(@RequestParam("policeKind") Long policeKind, @RequestParam("excludePoliceKinds") String excludePoliceKinds);

    /**
     * 根据地域获取部门
     *
     * @param districtCode 地域
     * @return 部门列表
     */
    @PostMapping("/public/dept/getDeptByDistrict")
    List<DeptVO> getDeptByDistrict2(@RequestBody List<String> districtCode);
}