package com.trs.police.common.openfeign.starter.DTO;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.common.utils.StringUtils;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/12/3 18:37
 * @since 1.0
 */
@Data
public class AiSummaryDto extends BaseDTO {

    private String uuid;

    private String type;

    private String subTypes;

    private Long listSize;

    private Boolean useNewModel;

    private BaseArchivesSearchDTO search;

    public AiSummaryDto() {
        super();
        this.useNewModel = false;
        BaseArchivesSearchDTO search = new BaseArchivesSearchDTO();
        search.setExport(true);
        search.setSkipFlushOnDetail(true);
        search.setNotClearAiInfo(true);
        search.setNeedAuth(false);
        this.search = search;
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getUuid(), new ServiceException("uuid不能为空"));
        checkNotEmpty(getType(), new ServiceException("type不能为空"));
        if (getUseNewModel()) {
            checkNotEmpty(getSearch().getKeyword(), new ServiceException("search.keyword不能为空"));
            checkNotEmpty(getSearch().getArchivesType(), new ServiceException("search.archivesType不能为空"));
            if (StringUtils.isEmpty(getSearch().getUuid())) {
                getSearch().setUuid(getUuid());
            }
        }
        return true;
    }
}
