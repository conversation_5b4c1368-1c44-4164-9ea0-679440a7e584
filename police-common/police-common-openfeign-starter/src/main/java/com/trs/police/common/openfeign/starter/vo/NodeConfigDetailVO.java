package com.trs.police.common.openfeign.starter.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 几点配置vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class NodeConfigDetailVO implements Serializable {

    private Long id;

    private Long processConfigId;

    private Integer orderNumber;

    private String name;

    private String nodeFormTemplate;

    private boolean isLastNode;

    private Boolean dynamicApprover;

    private String approverStrategy;

    private String buildNodeStrategy;

    private Integer nodeType;

    /**
     * 是否需要选择执行人
     */
    private Boolean needSelectImplementer;
}
