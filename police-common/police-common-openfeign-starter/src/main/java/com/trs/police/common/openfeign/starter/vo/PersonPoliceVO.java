package com.trs.police.common.openfeign.starter.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公安管控vo
 *
 * <AUTHOR>
 * @since 2022/11/17 11:13
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonPoliceVO {

    /**
     * 责任分局id
     */
    private String bureauCode;

    /**
     * 责任分局领导id
     */
    private Long bureauLeaderId;
    /**
     * 责任警种部门id
     */
    private String policeCode;

    /**
     * 责任警种部门领导id
     */
    private Long policeLeaderId;

    /**
     * 责任派出所id
     */
    private String stationCode;
    /**
     * 责任派出所领导id
     */
    private Long stationLeaderId;

    /**
     * 责任民警id
     */
    private List<Long> controlPersonList;

}
