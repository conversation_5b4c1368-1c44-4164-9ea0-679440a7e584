package com.trs.police.common.es.starter.utils;

import java.io.IOException;
import java.util.Collections;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.cluster.health.ClusterHealthRequest;
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.GetAliasesResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;

/**
 * es工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class EsUtil {

    private EsUtil() {
    }

    /**
     * 插入数据
     *
     * @param restHighLevelClient client
     * @param index               索引
     * @param id                  id
     * @param jsonString          json数据
     * @return 插入结果
     * @throws IOException io异常
     */
    public static String saveData(RestHighLevelClient restHighLevelClient, String index, String id, String jsonString)
        throws IOException {
        testConnection(restHighLevelClient);
        IndexRequest indexRequest = new IndexRequest(index);
        indexRequest.id(id);
        indexRequest.source(jsonString, XContentType.JSON);
        IndexResponse response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
        log.info("es插入数据: {}", jsonString);
        return response.getResult().getLowercase();
    }

    /**
     * 删除数据
     *
     * @param restHighLevelClient client
     * @param index               索引
     * @param id                  id
     * @return 删除结果
     * @throws IOException io异常
     */
    public static String deleteData(RestHighLevelClient restHighLevelClient, String index, String id)
        throws IOException {
        DeleteRequest deleteRequest = new DeleteRequest();
        deleteRequest.index(index);
        deleteRequest.id(id);
        DeleteResponse deleteResponse = restHighLevelClient.delete(deleteRequest, RequestOptions.DEFAULT);
        return deleteResponse.getResult().getLowercase();
    }

    /**
     * 测试连接
     *
     * @param restHighLevelClient client
     * @return 结果
     * @throws IOException io异常
     */
    public static Set<String> test(RestHighLevelClient restHighLevelClient) throws IOException {
        GetAliasesRequest getAliasesRequest = new GetAliasesRequest();
        GetAliasesResponse response = restHighLevelClient.indices().getAlias(getAliasesRequest, RequestOptions.DEFAULT);
        return response.getAliases().keySet();
    }


    /**
     * 创建索引
     *
     * @param restHighLevelClient client
     * @param index 索引名
     * @param mappingsJson 映射（json字符串）
     * @param settingsJson 设置（json字符串）
     * @throws IOException io异常
     */
    public static void createIndex(RestHighLevelClient restHighLevelClient, String index, String mappingsJson, String settingsJson) throws IOException {
        if (!checkIndexExist(restHighLevelClient, index)) {
            CreateIndexRequest request = new CreateIndexRequest(index);
            request.mapping(mappingsJson, XContentType.JSON);
            request.settings(settingsJson, XContentType.JSON);
            CreateIndexResponse response = restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
            if (response.isAcknowledged()) {
                log.info("创建索引[{}]成功！", index);
            } else {
                log.error("创建索引[{}]失败！", index);
                throw new IOException();
            }
        } else {
            log.info("索引[{}]已经存在！", index);
        }
    }

    /**
     * 判断索引是否存在
     *
     * @param restHighLevelClient client
     * @param index 索引名
     * @return 结果
     * @throws IOException 异常
     */
    public static boolean checkIndexExist(RestHighLevelClient restHighLevelClient, String index) throws IOException {
        GetIndexRequest request = new GetIndexRequest(index);
        boolean result = restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
        log.info("索引[{}]查询结果：{}", index, result);
        return result;
    }

    /**
     * 按id更新文档的某一字段
     *
     * @param restHighLevelClient client
     * @param index 索引名
     * @param id 文档id
     * @param field 字段名
     * @param value 字段值
     * @return 结果
     * @throws IOException 异常
     */
    public static String updateFieldById(RestHighLevelClient restHighLevelClient, String index, Long id, String field, Object value) throws IOException {
        testConnection(restHighLevelClient);
        UpdateRequest updateRequest = new UpdateRequest(index, id.toString());
        String expression = "ctx._source." + field + "=" + value;
        updateRequest
            .script(new Script(ScriptType.INLINE, "painless", expression, Collections.emptyMap()))
            .retryOnConflict(3);
        return restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT).toString();
    }

    /**
     * 根据query批量更新
     *
     * @param restHighLevelClient client
     * @param index 索引
     * @param query 检索
     * @param expression 更新脚本
     * @return 结果
     * @throws IOException io异常
     */
    public static String updateFieldByQuery(RestHighLevelClient restHighLevelClient, String index, QueryBuilder query, String expression) throws IOException {
        UpdateByQueryRequest request = new UpdateByQueryRequest();
        request.indices(index);
        request.setQuery(query);
        request.setScript(new Script(ScriptType.INLINE, "painless", expression, Collections.emptyMap()));
        request.setRetryBackoffInitialTime(TimeValue.timeValueSeconds(5));
        return restHighLevelClient.updateByQuery(request, RequestOptions.DEFAULT).toString();
    }

    private static boolean testConnection(RestHighLevelClient restHighLevelClient) {
        try {
            ClusterHealthRequest request = new ClusterHealthRequest();
            restHighLevelClient.cluster().health(request, RequestOptions.DEFAULT);
            return true;
        } catch (IOException e) {
            log.error("查询es失败（测试）", e);
            return false;
        }
    }
}
