package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.profile.domain.entity.RiskScoreItemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * RiskScoreItemMapper
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/8 15:56
 * @since 1.0
 */
@Mapper
public interface RiskScoreItemMapper extends BaseMapper<RiskScoreItemEntity> {

    /**
     * deleteOldData<BR>
     *
     * @param itemKey  参数
     * @param itemType 参数
     * @param recordId 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/8 16:56
     */
    public Integer deleteOldData(
        @Param("itemKey") String itemKey,
        @Param("itemType") String itemType,
        @Param("recordId") String recordId
    );
}
