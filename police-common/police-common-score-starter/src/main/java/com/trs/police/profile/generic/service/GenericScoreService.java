package com.trs.police.profile.generic.service;

import com.trs.police.profile.generic.dto.GenericScoreResult;
import com.trs.police.profile.generic.dto.ScoreContext;
import com.trs.police.profile.generic.dto.ScoreRule;
import com.trs.police.profile.generic.engine.GenericScoreEngine;
import com.trs.police.profile.generic.factory.GenericScoreStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通用积分服务
 *
 * @author: AI Assistant
 * @date: 2025/01/27
 * @description: 提供通用积分计算的完整服务
 */
@Slf4j
@Service
public class GenericScoreService {

    @Autowired
    private GenericScoreEngine scoreEngine;

    @Autowired
    private GenericScoreStrategyFactory strategyFactory;

    /**
     * 计算单个对象的积分
     *
     * @param context 积分计算上下文
     * @param rules   积分规则列表
     * @return 积分计算结果
     */
    public GenericScoreResult calculateScore(ScoreContext context, List<ScoreRule> rules) {
        log.info("开始计算积分，主键：{}，类型：{}", context.getPrimaryKey(), context.getType());

        try {
            // 1. 验证输入参数
            if (context == null || !isValidContext(context)) {
                log.warn("积分上下文无效");
                return createEmptyResult(context, "积分上下文无效");
            }

            if (CollectionUtils.isEmpty(rules)) {
                log.warn("积分规则为空");
                return createEmptyResult(context, "积分规则为空");
            }

            // 2. 过滤适用的规则
            List<ScoreRule> applicableRules = filterApplicableRules(rules, context.getType());
            if (CollectionUtils.isEmpty(applicableRules)) {
                log.warn("没有适用的积分规则，类型：{}", context.getType());
                return createEmptyResult(context, "没有适用的积分规则");
            }

            // 3. 执行积分计算
            GenericScoreResult result = scoreEngine.calculateTotalScore(context, applicableRules);

            // 4. 计算积分等级
            result.calculateScoreLevel();

            log.info("积分计算完成，主键：{}，总分：{}", context.getPrimaryKey(), result.getTotalScore());
            return result;

        } catch (Exception e) {
            log.error("计算积分异常，主键：{}，错误：{}", context.getPrimaryKey(), e.getMessage(), e);
            return createErrorResult(context, "计算积分异常：" + e.getMessage());
        }
    }

    /**
     * 批量计算积分
     *
     * @param contexts 积分计算上下文列表
     * @param rules    积分规则列表
     * @return 积分计算结果列表
     */
    public List<GenericScoreResult> batchCalculateScore(List<ScoreContext> contexts, List<ScoreRule> rules) {
        if (CollectionUtils.isEmpty(contexts)) {
            return new ArrayList<>();
        }

        log.info("开始批量计算积分，对象数量：{}", contexts.size());

        try {
            // 过滤适用的规则（按类型分组）
            Map<String, List<ScoreRule>> rulesByType = groupRulesByType(rules);

            List<GenericScoreResult> results = contexts.parallelStream()
                    .map(context -> {
                        try {
                            List<ScoreRule> applicableRules = rulesByType.getOrDefault(context.getType(), rules);
                            return calculateScore(context, applicableRules);
                        } catch (Exception e) {
                            log.error("批量计算单个对象积分失败，主键：{}，错误：{}",
                                    context.getPrimaryKey(), e.getMessage());
                            return createErrorResult(context, "计算失败：" + e.getMessage());
                        }
                    })
                    .filter(result -> result != null)
                    .collect(Collectors.toList());

            log.info("批量积分计算完成，成功计算：{}个对象", results.size());
            return results;

        } catch (Exception e) {
            log.error("批量计算积分异常，错误：{}", e.getMessage(), e);
            return contexts.stream()
                    .map(context -> createErrorResult(context, "批量计算异常：" + e.getMessage()))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 计算指定类型对象的积分
     *
     * @param context 积分计算上下文
     * @param ruleIds 指定的规则ID列表
     * @return 积分计算结果
     */
    public GenericScoreResult calculateScoreWithSpecificRules(ScoreContext context, List<String> ruleIds) {
        // 这里需要根据ruleIds获取具体的规则，简化实现
        log.info("使用指定规则计算积分，主键：{}，规则数量：{}", context.getPrimaryKey(), ruleIds.size());

        // 实际实现中应该从数据库或配置中获取指定的规则
        List<ScoreRule> specificRules = getSpecificRules(ruleIds);

        return calculateScore(context, specificRules);
    }

    /**
     * 获取积分策略统计信息
     *
     * @return 策略统计信息
     */
    public Map<String, Object> getStrategyStatistics() {
        try {
            return strategyFactory.getStrategyStatistics();
        } catch (Exception e) {
            log.error("获取策略统计信息失败，错误：{}", e.getMessage(), e);
            return Map.of("error", "获取策略信息失败：" + e.getMessage());
        }
    }

    /**
     * 验证积分上下文是否有效
     *
     * @param context 积分上下文
     * @return 是否有效
     */
    private boolean isValidContext(ScoreContext context) {
        return context.getPrimaryKey() != null
                && !context.getPrimaryKey().trim().isEmpty()
                && context.getType() != null
                && !context.getType().trim().isEmpty();
    }

    /**
     * 过滤适用的规则
     *
     * @param rules 所有规则
     * @param type  对象类型
     * @return 适用的规则列表
     */
    private List<ScoreRule> filterApplicableRules(List<ScoreRule> rules, String type) {
        return rules.stream()
                .filter(rule -> rule.isEnabled() && rule.isApplicableToScope(type))
                .collect(Collectors.toList());
    }

    /**
     * 按类型分组规则
     *
     * @param rules 规则列表
     * @return 按类型分组的规则映射
     */
    private Map<String, List<ScoreRule>> groupRulesByType(List<ScoreRule> rules) {
        return rules.stream()
                .filter(ScoreRule::isEnabled)
                .collect(Collectors.groupingBy(
                        rule -> rule.getScope() != null ? rule.getScope() : "default"
                ));
    }

    /**
     * 获取指定的规则
     *
     * @param ruleIds 规则ID列表
     * @return 规则列表
     */
    private List<ScoreRule> getSpecificRules(List<String> ruleIds) {
        // 实际实现中应该从数据库或配置中获取
        // 这里返回空列表作为示例
        log.warn("getSpecificRules方法需要实现具体的规则获取逻辑");
        return new ArrayList<>();
    }

    /**
     * 创建空的积分结果
     *
     * @param context 积分上下文
     * @param reason  原因
     * @return 空的积分结果
     */
    private GenericScoreResult createEmptyResult(ScoreContext context, String reason) {
        return GenericScoreResult.builder()
                .primaryKey(context != null ? context.getPrimaryKey() : "")
                .name(context != null ? context.getName() : "")
                .type(context != null ? context.getType() : "")
                .totalScore(0.0)
                .weightedScore(0.0)
                .detailScores(new ArrayList<>())
                .scoreLevel("未知")
                .isHighScore(false)
                .remark(reason)
                .build();
    }

    /**
     * 创建错误的积分结果
     *
     * @param context      积分上下文
     * @param errorMessage 错误信息
     * @return 错误的积分结果
     */
    private GenericScoreResult createErrorResult(ScoreContext context, String errorMessage) {
        return GenericScoreResult.builder()
                .primaryKey(context != null ? context.getPrimaryKey() : "")
                .name(context != null ? context.getName() : "")
                .type(context != null ? context.getType() : "")
                .totalScore(0.0)
                .weightedScore(0.0)
                .detailScores(new ArrayList<>())
                .scoreLevel("错误")
                .isHighScore(false)
                .remark("计算错误：" + errorMessage)
                .build();
    }

    /**
     * 检查是否存在指定类型的策略
     *
     * @param ruleType 规则类型
     * @param scope    适用范围
     * @return 是否存在
     */
    public boolean hasStrategyForType(String ruleType, String scope) {
        try {
            return strategyFactory.hasStrategy(null, ruleType, scope);
        } catch (Exception e) {
            log.error("检查策略是否存在失败，规则类型：{}，适用范围：{}，错误：{}", ruleType, scope, e.getMessage());
            return false;
        }
    }

    /**
     * 获取支持指定范围的所有策略
     *
     * @param scope 适用范围
     * @return 策略名称列表
     */
    public List<String> getSupportedStrategies(String scope) {
        try {
            return strategyFactory.getStrategiesByScope(scope).stream()
                    .map(strategy -> strategy.getStrategyName())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取支持的策略失败，适用范围：{}，错误：{}", scope, e.getMessage());
            return new ArrayList<>();
        }
    }
}
