package com.trs.police.ds.service;

import com.trs.police.ds.domain.request.KeyValueTypeVO;
import com.trs.police.ds.domain.request.RegularRequest;
import com.trs.police.ds.domain.request.TimeParams;
import com.trs.police.ds.domain.vo.homepage.regular.RegularWarningListVO;
import java.util.List;

/**
 * 管控中心service
 *
 * <AUTHOR>
 */
public interface RegularService {

    /**
     * 布控级别占比
     *
     * @param request 参数
     * @return 结果
     */
    List<KeyValueTypeVO> countMonitorLevel(RegularRequest request);

    /**
     * 管控等级占比
     *
     * @param request 参数
     * @return 结果
     */
    List<KeyValueTypeVO> countRegularLevel(RegularRequest request);

    /**
     * 24小时有轨迹
     *
     * @param warningLevel 预警级别
     * @return 轨迹
     */
    List<RegularWarningListVO> regularWarningList(String warningLevel);

    /**
     * 市、区重点人口统计
     *
     * @param timeParams 时间参数
     * @return 数量
     */
    Integer personCount(TimeParams timeParams);

    /**
     * 重点人员类别统计
     *
     * @param timeParams 时间参数
     * @return 类别统计
     */
    List<KeyValueTypeVO> personLabelStatistics(TimeParams timeParams);

    /**
     * 地图数据
     *
     * @param timeParams 时间参数
     * @return 数量
     */
    List<KeyValueTypeVO> map(TimeParams timeParams);
}
