package com.trs.police.ds.service.impl;

import com.trs.police.ds.domain.request.KeyValueTypeVO;
import com.trs.police.ds.domain.request.TimeParams;
import com.trs.police.ds.domain.vo.CodeNameCountVO;
import com.trs.police.ds.domain.vo.homepage.warning.WarningHomePageListVO;
import com.trs.police.ds.domain.vo.homepage.warning.WarningPointVO;
import com.trs.police.ds.domain.vo.homepage.warning.WarningStatisticsVO;
import com.trs.police.ds.mapper.WarningHomepageMapper;
import com.trs.police.ds.service.WarningHomePageService;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/11/7 9:30
 */
@Service
public class WarningHomePageServiceImpl implements WarningHomePageService {

    @Resource
    private WarningHomepageMapper warningHomepageMapper;

    @Override
    public List<WarningHomePageListVO> groupWarningList(TimeParams timeParams) {
        return warningHomepageMapper.groupWarningList(timeParams);
    }

    @Override
    public List<WarningHomePageListVO> areaWarningList(TimeParams timeParams) {
        return warningHomepageMapper.areaWarningList(timeParams);
    }

    @Override
    public List<WarningHomePageListVO> personWarningList(TimeParams timeParams) {
        return warningHomepageMapper.personWarningList(timeParams);
    }

    @Override
    public WarningStatisticsVO warningStatistic(TimeParams timeParams) {
        WarningStatisticsVO vo = new WarningStatisticsVO();
        List<CodeNameCountVO> result = warningHomepageMapper.statisticsWarning(timeParams);
        vo.setPersonWarningCount(
            result.stream().filter(item -> "person".equals(item.getName())).map(CodeNameCountVO::getCount)
                .reduce(0, Integer::sum));
        vo.setGroupWarningCount(
            result.stream().filter(item -> "group".equals(item.getName())).map(CodeNameCountVO::getCount)
                .reduce(0, Integer::sum));
        vo.setAreaWarningCount(
            result.stream().filter(item -> "area".equals(item.getName())).map(CodeNameCountVO::getCount)
                .reduce(0, Integer::sum));
        vo.setAllCount(result.stream().map(CodeNameCountVO::getCount).reduce(0, Integer::sum));
        return vo;
    }

    @Override
    public List<KeyValueTypeVO> warningSourceStatistic(TimeParams timeParams) {
        return warningHomepageMapper.warningSourceStatistic(timeParams);
    }

    @Override
    public List<KeyValueTypeVO> controlStatistic(TimeParams timeParams) {
        List<KeyValueTypeVO> result = warningHomepageMapper.controlStatistic(timeParams);
        List<KeyValueTypeVO> vo = new ArrayList<>();
        vo.add(new KeyValueTypeVO("红色布控",result.stream().filter(item->"1".equals(item.getKey())).map(item->Integer.valueOf(item.getValue().toString())).reduce(0,Integer::sum)));
        vo.add(new KeyValueTypeVO("橙色布控",result.stream().filter(item->"2".equals(item.getKey())).map(item->Integer.valueOf(item.getValue().toString())).reduce(0,Integer::sum)));
        vo.add(new KeyValueTypeVO("黄色布控",result.stream().filter(item->"3".equals(item.getKey())).map(item->Integer.valueOf(item.getValue().toString())).reduce(0,Integer::sum)));
        vo.add(new KeyValueTypeVO("蓝色布控",result.stream().filter(item->"4".equals(item.getKey())).map(item->Integer.valueOf(item.getValue().toString())).reduce(0,Integer::sum)));
        return  vo;
    }

    @Override
    public List<KeyValueTypeVO> personLabelStatistic(TimeParams timeParams) {
        return warningHomepageMapper.personLabelStatistic(timeParams);
    }

    @Override
    public List<WarningPointVO> warningPoint(TimeParams timeParams) {
        return warningHomepageMapper.warningPoint(timeParams);
    }
}
