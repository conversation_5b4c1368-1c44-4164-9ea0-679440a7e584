package com.trs.police.ds.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Getter;

/**
 * 布控级别
 *
 * <AUTHOR>
 * @date 2022/6/21 15:56
 */
public enum MonitorLevelEnum {
    /**
     * 红
     */
    RED(1, "红"),
    /**
     * 橙
     */
    ORANGE(2, "橙"),
    /**
     * 黄
     */
    YELLOW(3, "黄"),
    /**
     * 蓝
     */
    BLUE(4, "蓝");


    @JsonValue
    @Getter
    @EnumValue
    private final Integer code;

    @Getter
    private final String name;

    MonitorLevelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static MonitorLevelEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (MonitorLevelEnum typeEnum : MonitorLevelEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.code.toString();
    }

    /**
     * 常控级别翻译为布控级别
     *
     * @param type 常控级别
     * @return 布控级别
     */
    public static MonitorLevelEnum regularTypeToLevel(Long type) {
        if (type == 1) {
            return RED;
        } else if (type == 2) {
            return YELLOW;
        }
        return BLUE;
    }

    /**
     * 获取所有code
     *
     * @return code列表
     */
    public static List<Integer> getCodes(){
        return Arrays.stream(values()).map(MonitorLevelEnum::getCode).collect(Collectors.toList());
    }


}
