package com.trs.police.ds.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统计指标VO 同比、环比
 *
 * <AUTHOR>
 * @date 2023/05/04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndexVO implements Serializable {

    private static final long serialVersionUID = 7876212389150434967L;

    private static final DecimalFormat DF = new DecimalFormat();

    static {
        DF.setMaximumFractionDigits(2);
        DF.setMinimumFractionDigits(1);
    }

    public IndexVO(Integer count, Integer last) {
        this.count = count;
        this.last = last;
        this.rate = last == 0 ? 0 : (count - last) / (double) last;
    }

    /**
     * 统计数量
     */
    Integer count;

    /**
     * 上期数量
     */
    Integer last;

    /**
     * 变化比例
     */
    Double rate;

    /**
     * 数量变化比率转换为中文输出，增加上升和下降前缀，并保留3位小数点
     *
     * @return 中文输出
     */
    public String getChineseRate() {
        StringBuilder stringBuilder = new StringBuilder();
        if (rate == 0) {
            stringBuilder.append("持平");
            return stringBuilder.toString();
        } else if (rate < 0) {
            stringBuilder.append("下降");
        } else {
            stringBuilder.append("上升");
        }
        BigDecimal decimal = BigDecimal.valueOf(rate * 100);
        stringBuilder.append(DF.format(decimal.abs())).append("%");
        return stringBuilder.toString();
    }

    /**
     * 数量变化比率百分号保留3位小数点
     *
     * @return 数量变化比率
     */
    public String getStringRate() {
        BigDecimal decimal = BigDecimal.valueOf(rate * 100);
        return DF.format(decimal) + "%";
    }

    /**
     * 相加
     *
     * @param indexVO1 indexVO1
     * @param indexVO2 indexVO2
     * @return 相加后的结果
     */
    public static IndexVO plus(IndexVO indexVO1, IndexVO indexVO2) {
        return new IndexVO(indexVO1.getCount() + indexVO2.getCount(), indexVO1.getLast() + indexVO2.getLast());
    }

    /**
     * 当变化率为0时，返回空字符串
     *
     * @return 变化率
     */
    public String getReportRate() {
        return getRate() == 0.0 ? "" : getStringRate();
    }
}
