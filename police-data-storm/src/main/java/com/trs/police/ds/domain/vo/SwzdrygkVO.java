package com.trs.police.ds.domain.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/10/12 18:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SwzdrygkVO {

    private List<StatisticsVO> swryfb;
    private List<StatisticsVO> swqthyd;
    private List<StatisticsVO> swsj;
    private List<ClueVO> swxs;

    /**
     * 统计vo
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatisticsVO{
           private String measure;
           private String dimension;
    }

    /**
     * 线索vo
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ClueVO{
        private String id;
        private String name;
        private String xsxq;
        private String yjsj;
    }
}
