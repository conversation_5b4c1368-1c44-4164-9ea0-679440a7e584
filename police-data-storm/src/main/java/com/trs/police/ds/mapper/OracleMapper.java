package com.trs.police.ds.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.police.ds.domain.request.DataStormRequest;
import com.trs.police.ds.domain.vo.CodeNameCountVO;
import com.trs.police.ds.domain.vo.MeasureDimension;
import com.trs.police.ds.domain.vo.SwzdrygkVO.ClueVO;
import com.trs.police.ds.domain.vo.YjLb;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2023/10/9 18:25
 */
@Mapper
@DS("oracle")
public interface OracleMapper {

    /**
     * 涉稳人员分布
     *
     * @return CodeNameCountVO
     */
    List<CodeNameCountVO> getSwryfb();


    /**
     * 涉稳群体
     *
     * @param request request
     * @return CodeNameCountVO
     */
    List<CodeNameCountVO> getSwqthyd(@Param("request") DataStormRequest request);

    /**
     * 涉稳事件
     *
     * @param request 请求参数
     * @return CodeNameCountVO
     */
    List<CodeNameCountVO> getSwsj(@Param("request") DataStormRequest request);

    /**
     * 涉稳线索
     *
     * @param request 请求参数
     * @return 返回参数
     */
    List<ClueVO> getSwxs(@Param("request") DataStormRequest request);

    /**
     * 获取云墙权限总人数
     *
     * @return 人数
     */
    Long getQxrsAll();

    /**
     * 获取权限分区及人数
     *
     * @param type 类别
     * @return 区县 人数
     */
    List<MeasureDimension> getQxxq(@Param("type") String type);

    /**
     * 获取云墙权限总人数
     *
     * @return 人数
     */
    Long getQxrsDsjAll();

    /**
     * 获取云墙权限总人数
     *
     * @return 人数
     */
    Long getQxrsGabysAll();

    /**
     * 获取警务合成
     *
     * @param request 参数
     * @return 合成
     */
    List<MeasureDimension> getJwhc(@Param("request") DataStormRequest request);

    /**
     * 获取警务协作
     *
     * @param request 参数
     * @return 警务协作
     */
    List<MeasureDimension> getJwxz(@Param("request") DataStormRequest request);

    /**
     * 获取协作单位
     *
     * @param request 参数
     * @return 协作单位
     */
    List<MeasureDimension> getXzdw(@Param("request") DataStormRequest request);

    /**
     * 获取案件合成率
     *
     * @param request 参数
     * @return 案件合成率
     */
    String getCommondConut(@Param("request") DataStormRequest request);

    /**
     * 获取预警总量
     *
     * @param request 参数
     * @return 预警总量
     */
    String getYjzl(@Param("request") DataStormRequest request);

    /**
     * 获取预警占比
     *
     * @param request 参数
     * @return 预警占比
     */
    List<MeasureDimension> getPtyjzb(@Param("request") DataStormRequest request);

    /**
     * 获取处置总量
     *
     * @param request 参数
     * @return 处置总量
     */
    String getCz(@Param("request") DataStormRequest request);

    /**
     * 获取处置占比
     *
     * @param request 参数
     * @return 处置占比
     */
    List<MeasureDimension> getYjczzb(@Param("request")DataStormRequest request);

    /**
     * 获取指令中心
     *
     * @param request 参数
     * @return 指令中心
     */
    List<MeasureDimension> getZlzx(@Param("request")DataStormRequest request);

    /**
     * 获取事件梳理
     *
     * @param request 参数
     * @return 事件梳理
     */
    List<MeasureDimension> getSjsl(@Param("request")DataStormRequest request);

    /**
     * 获取警务合成排名
     *
     * @param request 参数
     * @return 事件梳理
     */
    List<MeasureDimension> getJwhcPm(@Param("request")DataStormRequest request);

    /**
     * 获取本地数据总量
     *
     * @param request 参数
     * @return 获取大数据统计情况
     */
    String getDsjsjzl(@Param("request") DataStormRequest request);

    /**
     * 获取本地数据资源
     *
     * @param request 参数
     * @return 获取大数据统计情况
     */
    String getDsjBdsjzy(@Param("request")DataStormRequest request);

    /**
     * 数据分类统计
     *
     * @param request 参数
     * @return 数据分类统计
     */
    List<MeasureDimension> getSjfltj(@Param("request")DataStormRequest request);

    /**
     * 警种赋能
     *
     * @param request 参数
     * @param type 类型 t-ps-subject
     * @return 警种赋能
     */
    List<MeasureDimension> getJingzfnVo(@Param("request") DataStormRequest request, @Param("type") int type);

    /**
     * 预警列表
     *
     * @param request 参数
     * @return 警种赋能
     */
    List<YjLb> getyjlb(@Param("request") DataStormRequest request);

    /**
     * 预警统计
     *
     * @param request 参数
     * @return 预警统计
     */
    List<MeasureDimension> getYjtj(@Param("request")DataStormRequest request);

    /**
     * 涉稳人员统计
     *
     * @param request 参数
     * @return 预警统计
     */
    List<MeasureDimension> getSwrytj(@Param("request")DataStormRequest request);
}
