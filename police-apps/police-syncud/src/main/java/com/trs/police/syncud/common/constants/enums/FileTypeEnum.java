package com.trs.police.syncud.common.constants.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 文件类型枚举
 *
 * <AUTHOR>
 * @date 2021/8/3 11:19
 */
public enum FileTypeEnum {

    /**
     * enums
     */
    VIDEO("0", "视频"),

    OFFICE("1", "office文档"),

    IMAGE("2", "图片"),

    OTHERS("3", "其他"),

    SOUND("4", "音频");

    @EnumValue
    @JsonValue
    @Getter
    private final String code;

    @Getter
    private final String name;

    FileTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据后缀名返回文件类型
     *
     * @param extension 后缀名
     * @return {@link FileTypeEnum}
     */
    public static FileTypeEnum ofExtension(String extension) {
        if (StringUtils.isBlank(extension)) {
            return FileTypeEnum.OTHERS;
        }
        switch (extension.toLowerCase()) {
            case "jpeg":
            case "jpg":
            case "png":
            case "gif":
                return FileTypeEnum.IMAGE;
            case "mp4":
            case "avi":
            case "mts":
            case "rm":
            case "rmvb":
            case "3gp":
            case "mpeg":
            case "mpg":
            case "mkv":
            case "dat":
            case "asf":
            case "wmv":
            case "flv":
            case "mov":
            case "ogg":
            case "ogm":
                return FileTypeEnum.VIDEO;
            case "mp3":
            case "cda":
            case "wav":
            case "aif":
            case "aiff":
            case "mid":
            case "wma":
            case "vqf":
            case "ape":
            case "m4a":
                return FileTypeEnum.SOUND;
            case "doc":
            case "docx":
            case "xls":
            case "xlsx":
            case "ppt":
            case "pptx":
            case "pdf":
                return FileTypeEnum.OFFICE;
            default:
                return FileTypeEnum.OTHERS;
        }
    }

    /**
     * 根据枚举值的code解析枚举值
     *
     * @param code code
     * @return {@link FileTypeEnum}
     */
    public static FileTypeEnum codeOf(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (FileTypeEnum typeEnum : FileTypeEnum.values()) {
                if (StringUtils.equals(code, typeEnum.code)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
