package com.trs.police.syncud.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.syncud.common.constants.CommonConstants;
import com.trs.police.syncud.common.constants.StatusConstants;
import com.trs.police.syncud.common.util.FileUtil;
import com.trs.police.syncud.converter.UserMapStruct;
import com.trs.police.syncud.domain.entity.YqUser;
import com.trs.police.syncud.domain.entity.YsDept;
import com.trs.police.syncud.domain.entity.YsUser;
import com.trs.police.syncud.domain.entity.YsUserDept;
import com.trs.police.syncud.domain.vo.FileInfoVO;
import com.trs.police.syncud.mapper.*;
import com.trs.police.syncud.service.FileService;
import com.trs.police.syncud.service.ThirdUserService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年06月12日 15:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class YqUserServiceImpl implements ThirdUserService {

    @Resource
    private YqUserMapper mapper;

    @Resource
    private YsUserMapper ysUserMapper;

    @Resource
    private YsUserDeptRelationMapper ysUserDeptRelationMapper;

    @Autowired
    private DyDeptMapper thirdDeptMapper;

    @Resource
    private YsDeptMapper ysDeptMapper;

    @Resource
    private UserMapStruct userMapStruct;

    @Resource
    private SignetMapper signetMapper;

    @Resource
    private FileService fileService;

    PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public Long count(QueryWrapper queryWrapper) {
        return mapper.selectCount(queryWrapper);
    }

    /**
     * 同步第三方人员数据
     */
    @Override
    //@DSTransactional
    public void doSyncUser(Page page, QueryWrapper queryWrapper) {
        //遍历判读是：入库-云哨没有；更新-云哨有但是信息不同；不做处理-云哨有且名称一致
        log.info("开始同步云墙的用户数据,pageNum:{},pageSize:{}", page.getCurrent(), page.getSize());
        Page<YqUser> resultPage = mapper.selectPage(page, queryWrapper);

        List<YqUser> resultList = resultPage.getRecords();
        for (YqUser user : resultList) {
            try {
                if (StringUtils.isEmpty(user.getIdCard())){
                    continue;
                }
                //云哨用户部门关联信息实体类
                YsUserDept ysUserDept = new YsUserDept();
                //过滤掉身份证号长度小于18的数据
                if (user.getIdCard().length() < 18) {
                    continue;
                }
                String idCard = user.getIdCard().substring(0, 18);
                //判断云哨用户表中是否存在第三方用户信息，若查询信息为空，则将第三方用户同步到云哨数据表中
                YsUser ysUser1 = ysUserMapper.selectByIdCard(idCard);
                if (ysUser1 == null) {
                    //将spUser属性拷贝至ysUser当中
                    YsUser ysUser = userMapStruct.dyCopy(user);
                    //属性拷贝时，因为YsUser和SpUser实体类的主键id属性名称一样，同样会拷贝，所以将ysDept对象的id设置为null
                    ysUser.setId(null);
                    ysUser.setCreateDateTime(LocalDateTime.now());
                    ysUser.setIdcard(ysUser.getIdcard().substring(0, 18));
                    setBirthday(user, ysUser);

                    //设置此用户账号有效期限
                    LocalDateTime validDate = LocalDateTime.now();
                    validDate = validDate.plusYears(3);
                    ysUser.setValidDate(validDate);

                    ysUser.setPassword(passwordEncoder.encode(CommonConstants.DEFAULT_PASSWORD));
                    //云墙用户status=1表示正常
                    if (Objects.equals(user.getStatus(), 1)) {
                        ysUser.setStatus(StatusConstants.USER_STATUS_USE);
                    } else {
                        ysUser.setStatus(StatusConstants.USER_STATUS_DELETE);
                    }

                    ysUserMapper.insert(ysUser);

                    //根据第三方用户所属组织查询用户再云哨里的所属组织
                    YsDept ysDept = ysDeptMapper.selectByOrgCode(user.getUnitCode());
                    if (ysDept == null){
                        log.info("当前第三方用户所在的部门没有同步到云哨！");
                        continue;
                    }

                    ysUserDept.setUserId(ysUser.getId());
                    ysUserDept.setDeptId(ysDept.getId());
                    ysUserDept.setId(null);
                    ysUserDeptRelationMapper.insert(ysUserDept);
                } else {
                    //根据第三方用户所属组织查询用户再云哨里的所属组织
                    YsDept ysDept = ysDeptMapper.selectByOrgCode(user.getUnitCode());
                    if (ysDept == null){
                        log.info("当前第三方用户所在的部门没有同步到云哨！");
                        continue;
                    }

                    ysUserDept.setUserId(ysUser1.getId());
                    ysUserDept.setDeptId(ysDept.getId());
                    ysUserDept.setId(null);
                    QueryWrapper<YsUserDept> queryWrapper1 = new QueryWrapper<>();
                    queryWrapper1.eq("user_id", ysUser1.getId());
                    queryWrapper1.eq("dept_id", ysDept.getId());
                    Long count = ysUserDeptRelationMapper.selectCount(queryWrapper1);
                    if (count == 0L) {
                        ysUserDeptRelationMapper.insert(ysUserDept);
                    }
                }
                setOtherUserInfo(resultList);
            } catch (Exception e) {
                log.error("同步用户{}失败：", user.getIdCard(), e);
            }
        }
    }

    /**
     * 设置用户的其他信息
     *
     * @param resultList 第三方用户信息
     */
    private void setOtherUserInfo(List<YqUser> resultList) {
        for (YqUser curUser : resultList) {
            //设置个人签章
            setUserSignet(curUser);
        }
    }

    /**
     * 设置人员出生日期数据
     *
     * @param thirdUser   第三方用户实体
     * @param ysUser 云哨用户实体
     */
    public void setBirthday(YqUser thirdUser, YsUser ysUser) {
        String year = thirdUser.getIdCard().substring(6, 10);//调用substring方法返回相关字段
        String month = thirdUser.getIdCard().substring(10, 12);
        String day = thirdUser.getIdCard().substring(12, 14);
        String birthday = year + "-" + month + "-" + day;
        DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");//定义一个时间转换格式“年-月-日”
        Date date = null;
        try {
            date = fmt.parse(birthday);
        } catch (ParseException e) {
            e.printStackTrace();
            log.info("生日日期格式化异常");
        }
        ysUser.setBirthday(date);
    }

    /**
     * 设置个人签章
     *
     * @param thirdUser 第三方用户信息
     */
    private void setUserSignet(YqUser thirdUser) {
        String signetId = signetMapper.getUserSignet(thirdUser.getIdCard());
        if (StringUtils.isNotEmpty(signetId)) {
            Map<String, Object> signetInfoMap = signetMapper.getSignetInfo(signetId);
            String fileUrl = BeanFactoryHolder.getEnv().getProperty("com.trs.signetUrl") + signetId;
            String cookie = BeanFactoryHolder.getEnv().getProperty("com.trs.cookie");
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("Cookie", cookie);
            String fileName = signetInfoMap.get("NAME").toString();
            try {
                MultipartFile multipartFile = FileUtil.urlConvertFile(fileUrl, fileName, requestMap, null);
                FileInfoVO fileInfoVO = fileService.uploadByMinio(multipartFile);
                if (fileInfoVO != null) {
                    ysUserMapper.updateUserSignet(thirdUser.getIdCard(), JSON.toJSONString(Arrays.asList(fileInfoVO)));
                }
            } catch (IOException e) {
                log.error(String.format("个人签章【%s】上传oss失败：", fileName), e);
            }
        }
    }
}
