package com.trs.police.syncud.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 德阳用户实体
 * @date 2024/06/12 16:32
 */

@Data
@TableName(value = "v_trs_person")
@Slf4j
public class DyUser implements Serializable {

    private static final long serialVersionUID = 4358928222943115L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private String id;

    /**
     * 编码
     */
    @TableField(value = "code_")
    private String code;

    /**
     * 姓名
     */
    @TableField(value = "name_")
    private String name;

    /**
     * 姓名首拼
     */
    @TableField(value = "name_yt_")
    private String nameYt;

    /**
     * 性别
     */
    @TableField(value = "sex_")
    private Integer sex;


    /**
     * 移动电话
     */
    @TableField(value = "mobile_")
    private String mobile;

    /**
     * 办公室电话
     */
    @TableField(value = "office_tel_")
    private String officeTel;

    /**
     * 手机
     */
    @TableField(value = "phone_")
    private String phone;

    /**
     * 身份证
     */
    @TableField(value = "idcard_")
    private String idCard;

    /**
     * 单位id
     */
    @TableField(value = "org_id_")
    private String orgId;

    /**
     * 部门id
     */
    @TableField(value = "dept_id_")
    private String deptId;

    /**
     * 可是id
     */
    @TableField(value = "unit_id_")
    private String unitId;

    /**
     * 出生年月日
     */
    @TableField(value = "birth_")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date birth;

    /**
     * 地址
     */
    @TableField(value = "address_")
    private String address;

    /**
     * 邮箱
     */
    @TableField(value = "email_")
    private String email;

    /**
     * 政治
     */
    @TableField(value = "political_")
    private Integer political;

    /**
     * 职务
     */
    @TableField(value = "duty_")
    private String duty;

    /**
     * 职务状态
     */
    @TableField(value = "duty_status_")
    private String dutyStatus;

    /**
     * 人员类型
     */
    @TableField(value = "person_type_")
    private String personType;

    /**
     * 警员图片地址
     */
    @TableField(value = "avatar_")
    private String avatar;

    /**
     * 排序
     */
    @TableField(value = "orders_")
    private Float orders;

    /**
     * 创建时间
     */
    @TableField(value = "create_time_")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time_")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 入库时间
     */
    @TableField(value = "entry_time_")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime entryTime;

    /**
     * 有效
     */
    @TableField(value = "valid_")
    private Integer valid;

    /**
     * 是否禁用
     */
    @TableField(value = "deleted_")
    private Integer deleted;

    /**
     * 姓名
     */
    @TableField(value = "detail_")
    private String detail;
}
