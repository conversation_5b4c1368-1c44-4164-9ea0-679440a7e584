package com.trs.police.syncud.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年03月13日 18:51
 */
@Data
@TableName(value = "t_user")
@Slf4j
public class YsUser implements Serializable {

    private static final long serialVersionUID = 7917771080941491320L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 创建部门id
     */
    @TableField(value = "create_dept_id")
    private Long createDeptId;


    /**
     * 修改用户id
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     *修改部门id
     */
    @TableField(value = "update_dept_id")
    private Long updateDeptId;

    /**
     *创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createDateTime;

    /**
     *修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateDateTime;

    /**
     *用户名
     */
    @TableField(value = "username")
    private String username;

    /**
     *用户真实姓名
     */
    @TableField(value = "real_name")
    private String realName;

    /**
     *身份证
     */
    @TableField(value = "idcard")
    private String idcard;

    /**
     *出生日期
     */
    @TableField(value = "birthday")
    private Date birthday;

    /**
     *性别
     */
    @TableField(value = "gender")
    private String sex;

    /**
     *移动电话
     */
    @TableField(value = "telephone")
    private String telephone;

    /**
     *电话
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     *邮箱
     */
    @TableField(value = "email")
    private String email;



    /**
     *警号
     */
    @TableField(value = "police_code")
    private String policeCode;

    /**
     *状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     *用户密码
     */
    @TableField(value = "password")
    private String password;

    /**
     *职务
     */
    @TableField(value = "duty")
    private String duty;

    /**
     *账号有效期
     */
    @TableField(value = "valid_date")
    private LocalDateTime validDate;

    /**
     * 警员图片地址
     */
    @TableField(value = "avatar")
    private String avatar;

}
