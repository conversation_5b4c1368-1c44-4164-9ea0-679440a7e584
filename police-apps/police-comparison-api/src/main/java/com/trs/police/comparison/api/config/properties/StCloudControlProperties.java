package com.trs.police.comparison.api.config.properties;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/11/15 16:34
 */
@Data
@Component
@ConfigurationProperties(prefix = StCloudControlProperties.PREFIX)
@ConditionalOnProperty(prefix = StCloudControlProperties.PREFIX, name = "enabled", havingValue = "true")
public class StCloudControlProperties {

    public static final String PREFIX = "com.trs.subscription.st.cloud.control";

    private Boolean enabled = false;

    private String endpoint;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    private String interfaceId;

    private String senderId;

    private String serviceId;

    /**
     * 订阅天数
     * 默认5年
     */
    private Integer subscribeDays = 365 * 5;

    /**
     * 布控报送类型代码
     * 11 布控
     * 12 撤控
     * 13 续控
     */
    private String subscribeTypeCode = "11";

    /**
     * 布控级别
     */
    private String bkjb = "3";

    /**
     * 审批意见
     */
    private String spyj = "同意";

    /**
     * 布控对象范围
     */
    private String bkdxfw = "010000";

    /**
     * 申请人单位
     */
    private String applicantDept;

    /**
     * 申请单位机构代码
     */
    private String applicantDeptCode;

    /**
     * 申请人联系电话
     */
    private String applicantPhone;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请人身份号码
     */
    private String applicantIdCard;

    /**
     * 审批人单位名称
     */
    private String approvalDept;

    /**
     * 审批人单位代码
     */
    private String approvalDeptCode;

    /**
     * 审批人单位联系号码
     */
    private String approvalDeptPhone;

    /**
     * 审批人联系号码
     */
    private String approvalPhone;

    /**
     * 审批人姓名
     */
    private String approvalName;

    /**
     * 审批人身份号码
     */
    private String approvalIdCard;

    /**
     * 审批时间
     */
    private String approvalDateTime;

    /**
     * 审批结果代码
     */
    private String spjgdm = "1";

    /**
     * 布控撤控事由
     */
    private String bkcksy = "掌握轨迹";


}
