package com.trs.police.comparison.api.entity.request;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 自定义服务查询请求参数实体
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class CustomizeInquireRequest implements DataServiceAPIBaseRequest {

    private static final long serialVersionUID = 9181741520404485803L;

    private Integer serviceId;

    private List<Object> condition;

    private JSONObject inputParams;

    private Integer pageNum;

    private Integer pageSize;

    private List<String> requiredItems;

    private List<String> sortItems;

    public static CustomizeInquireRequest newInstance() {
        CustomizeInquireRequest customizeInquireRequest = new CustomizeInquireRequest();
        customizeInquireRequest.setCondition(Collections.emptyList());
        customizeInquireRequest.setRequiredItems(Collections.emptyList());
        customizeInquireRequest.setSortItems(Collections.emptyList());
        return customizeInquireRequest;
    }

}
