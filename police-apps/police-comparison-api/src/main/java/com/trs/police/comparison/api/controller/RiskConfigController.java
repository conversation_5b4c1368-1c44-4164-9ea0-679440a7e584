package com.trs.police.comparison.api.controller;

import com.trs.police.comparison.api.service.RiskConfigService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/2/27 11:30
 */

@RestController
@RequestMapping("/riskConfig")
public class RiskConfigController {

    @Resource
    private RiskConfigService riskConfigService;

    /**
     * 查询关联数据规则详情
     *
     * @return 关联数据规则详情
     */
    @GetMapping(value = "getRelationDataRuleTagDetail")
    public String getRelationDataRuleTagDetail() {
        return riskConfigService.getRelationDataRuleDetail();
    }

}
