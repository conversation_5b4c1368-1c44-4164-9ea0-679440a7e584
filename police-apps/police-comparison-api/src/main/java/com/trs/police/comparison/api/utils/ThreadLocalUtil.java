package com.trs.police.comparison.api.utils;

import java.util.HashMap;

/**
 * ThreadLocal工具类
 * <AUTHOR>
 * @since 2024/6/4 15:18
 */

public class ThreadLocalUtil {
    protected static final ThreadLocal<HashMap<String, String>> THREAD_LOCAL = new ThreadLocal<>();

    public static HashMap<String, String> getMap() {
        return THREAD_LOCAL.get();
    }

    public static void setMap(HashMap<String, String> map) {
        THREAD_LOCAL.remove();
        THREAD_LOCAL.set(map);
    }
}
