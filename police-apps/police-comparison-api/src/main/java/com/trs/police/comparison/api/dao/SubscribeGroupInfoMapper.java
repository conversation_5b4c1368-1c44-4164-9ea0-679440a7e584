package com.trs.police.comparison.api.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.comparison.api.entity.SubscribeGroupInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 群体布控信息表mapper
 *
 * <AUTHOR>
 * @version 1.0
 **/

@Mapper
public interface SubscribeGroupInfoMapper extends BaseMapper<SubscribeGroupInfo> {

  Integer checkGroupInfo(@Param("monitorId") String monitorId, @Param("userName") String userName);

  SubscribeGroupInfo selectByMonitorIdAndUserName(@Param("monitorId") String monitorId, @Param("userName") String userName);

  List<SubscribeGroupInfo> selectByUserName(@Param("userName") String userName);

  Integer updateByMonitorIdAndUserName(@Param("groupInfo") SubscribeGroupInfo groupInfo);

  Integer deleteByMonitorIdAndUserName(@Param("monitorId") String monitorId, @Param("userName") String userName);
}
