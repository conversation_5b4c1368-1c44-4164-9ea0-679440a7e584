package com.trs.police.comparison.api.service.impl;

import com.trs.police.comparison.api.utils.DataServiceHelper;
import com.trs.police.comparison.api.dao.SubscribeAreaMonitorMapper;
import com.trs.police.comparison.api.entity.SubscribeAreaMonitor;
import com.trs.police.comparison.api.exception.UnauthorizedException;
import com.trs.police.comparison.api.entity.request.AreaMonitorCompareSubscriptionRequest;
import com.trs.police.comparison.api.entity.response.AreaMonitorCompareSubscriptionResponse;
import com.trs.police.comparison.api.mapstruct.AreaCompareSubscriptionEntityMapper;
import com.trs.police.comparison.api.service.AreaMonitorCompareSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import com.trs.police.comparison.api.exception.BizException;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 区域布控数据服务实现
 *
 * <AUTHOR>
 * @version 1.0
 **/

@Service
@Slf4j
@Validated
public class AreaMonitorCompareSubscriptionServiceImpl implements AreaMonitorCompareSubscriptionService {

  @Resource
  private SubscribeAreaMonitorMapper subscribeAreaMonitorMapper;

  private static final AreaCompareSubscriptionEntityMapper ENTITY_MAPPER = AreaCompareSubscriptionEntityMapper.INSTANCE;

  @Override
  public AreaMonitorCompareSubscriptionResponse execute(AreaMonitorCompareSubscriptionRequest request) throws BizException, UnauthorizedException, InterruptedException {
    final String userAccount = DataServiceHelper.UserSafeBox.getUserAccount();

    SubscribeAreaMonitor areaMonitor = ENTITY_MAPPER.convert(request, userAccount);
    areaMonitor.setUpdateTime(LocalDateTime.now());

    //如果monitorId和userAccount重复，则更新数据，否则新增数据
    int upsertResult = subscribeAreaMonitorMapper.checkAreaMonitor(request.getMonitorId(), userAccount) >= 1 ?
            subscribeAreaMonitorMapper.updateByMonitorIdAndUserName(areaMonitor) :
            subscribeAreaMonitorMapper.insert(areaMonitor);
    if (upsertResult != 1) {
      throw new BizException("区域布控信息保存失败");
    }

    return AreaMonitorCompareSubscriptionResponse.success();
  }
}
