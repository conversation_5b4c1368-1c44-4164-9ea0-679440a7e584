package com.trs.police.comparison.api.constant;

/**
 * <AUTHOR>
 */
public class ErrorMessages {

  private ErrorMessages() {}

  public static final String INVALID_PARAMETER = "参数不合法！";

  public static final String BAD_REQUEST = "非法请求";

  public static final String SYSTEM_ERROR = "系统内部错误";

  public static final String DB_ERROR = "数据库异常";

  public static final String INVALID_ROLE = "权限不合法! ";

  public static final String FIELD_MAPPER_ERROR = "添加失败，“[%s]算子”配置中系统属性“[%s]”未与数据源“[%S]”属性建立映射关系，规则无法生效!";

  public static final String UNAUTHORIZED_ROLE = "权限不合法，或者登录超时，请重新登录！";

  public static final String AUTHORIZATION_ERROR = "权限认证失败";

  public static final String USER_LOCKED = "账户被锁定";

  public static final String UNAUTHORIZED_MODULE = "无该操作权限，请联系管理员添加！";

  public static final String TRANSFER_FILE_FAIL = "传输文件失败！";

  public static final String ILLEGAL_EN_NAME_ERROR="英文名只能由英文字母、数字、下划线组成，且不能是数字或者下划线开头";
}
