package com.trs.police.comparison.api.entity.request;

import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页查询标准请求参数实体
 *
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaginationStandardRequest {

    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    @Min(value = 1, message = "每页条数不能小于1")
    private Integer pageSize;
}
