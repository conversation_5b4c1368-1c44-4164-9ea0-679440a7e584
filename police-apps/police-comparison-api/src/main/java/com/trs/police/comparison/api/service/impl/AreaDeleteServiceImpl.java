package com.trs.police.comparison.api.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.trs.police.comparison.api.exception.BizException;
import com.trs.police.comparison.api.utils.DataServiceHelper;
import com.trs.police.comparison.api.config.properties.CompareSubscriptionProperties;
import com.trs.police.comparison.api.dao.SubscribeAreaInfoMapper;
import com.trs.police.comparison.api.entity.SubscribeAreaInfo;
import com.trs.police.comparison.api.exception.UnauthorizedException;
import com.trs.police.comparison.api.entity.request.AreaDeleteRequest;
import com.trs.police.comparison.api.entity.response.AreaDeleteResponse;
import com.trs.police.comparison.api.service.AreaDeleteService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022年09月16日 09
 */
@Service
@Slf4j
@Validated
public class AreaDeleteServiceImpl implements AreaDeleteService {

    @Resource
    private SubscribeAreaInfoMapper subscribeAreaInfoMapper;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    private static final String REDIS_KEY_SEPARATOR = ":";
    @Resource
    private CompareSubscriptionProperties compareSubscriptionProperties;

    @Override
    public AreaDeleteResponse execute(AreaDeleteRequest request)
            throws BizException, UnauthorizedException, InterruptedException, JsonProcessingException {
        final String userAccount = DataServiceHelper.UserSafeBox.getUserAccount();

        subscribeAreaInfoMapper.deleteByUserNameAndAreaId(userAccount, request.getAreaId());

        List<SubscribeAreaInfo> subscribeAreaInfoList = subscribeAreaInfoMapper.selectAreaIdsByAreaId(request.getAreaId());
        String areaKey = String.join(REDIS_KEY_SEPARATOR, compareSubscriptionProperties.getRedisKeyPrefix(), "area", "wkt");
        if (subscribeAreaInfoList.isEmpty()) {
            redisTemplate.opsForHash().delete(areaKey, request.getAreaId());
        } else {
            JSONObject userInfo = new JSONObject();
            for (SubscribeAreaInfo subscribeAreaInfo : subscribeAreaInfoList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("tags", subscribeAreaInfo.getTags());
                jsonObject.put("geometries", subscribeAreaInfo.getGeometries());
                userInfo.put(subscribeAreaInfo.getUserName(), jsonObject);
            }
            redisTemplate.opsForHash().put(areaKey, request.getAreaId(), userInfo.toJSONString());
        }

        return AreaDeleteResponse.success();
    }
}
