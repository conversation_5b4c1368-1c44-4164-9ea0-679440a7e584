<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.modelstatistics.mapper.ModelSceneMapper">

    <select id="selectTrendTotal" resultType="com.trs.police.modelstatistics.domain.vo.CountVO">
        SELECT DATE_FORMAT(statistic_date,'%Y-%m-%d') AS createTime
             ,DATE_FORMAT(statistic_date,'%m-%d') as dateTime
             ,SUM(s.achievement_count) as achievementCount
        from t_model_scene sc
         left join t_model_scene_relation r on sc.id = r.scene_id
         left join t_statistics_warning_model m on r.model_id = m.id
         left join t_model_daily_statistics s on m.id = s.model_id
        <where>
            s.achievement_count is not null
            <if test="dto.startTime != null and dto.startTime != ''">
                and statistic_date >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                and statistic_date &lt;= #{dto.endTime}
            </if>
            <if test="dto.id != null and dto.id != ''">
                and sc.id = #{dto.id}
            </if>
        </where>
        group by statistic_date order by statistic_date
    </select>
    <select id="selectMonitorWarningModelList"
            resultType="com.trs.police.modelstatistics.domain.vo.MonitorWarningModelVO">
        select sc.id,sc.scene_name as title,
        a.achievementCount as achievementCount
        from t_model_scene sc
        left join t_model_scene_relation r on sc.id = r.scene_id
        left join t_statistics_warning_model tswm on r.model_id = tswm.id
        left join
        (SELECT model_id,sum( achievement_count ) AS achievementCount FROM t_model_daily_statistics
        where 1=1
        <if test="dto.startTime != null and dto.startTime != ''">
            and statistic_date >=#{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and statistic_date &lt;=#{dto.endTime}
        </if>
        group by model_id) a on tswm.id = a.model_id
        where 1=1
        <if test="dto.modelName!=null and dto.modelName!=''">
            and sc.scene_name like concat('%',#{dto.modelName},'%')
        </if>
        GROUP by sc.id
        order by
        achievementCount
        <if test="dto.orderType != null and dto.orderType != ''">
            ${dto.orderType}
        </if>
        <if test="dto.orderType == null or dto.orderType == ''">
            desc
        </if>
    </select>
</mapper>