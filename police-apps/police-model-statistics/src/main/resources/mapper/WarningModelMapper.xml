<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.modelstatistics.mapper.WarningModelMapper">

    <select id="selectAllInfo" resultType="com.trs.police.modelstatistics.domain.vo.ModelDailyStatisticsVO">
        select id as modelId,name as modelName from t_statistics_warning_model
    </select>
    <select id="selectMonitorWarningModelList"
            resultType="com.trs.police.modelstatistics.domain.vo.MonitorWarningModelVO">
        SELECT a.id,a.third_model_id as modelId,a.name as title,b.detail,b.police_category,a.type,b.icon_url,sum(c.achievement_count) as achievementCount
        FROM `t_statistics_warning_model` a left join t_control_monitor_warning_model b
        on a.third_model_id=b.id left join t_model_daily_statistics c on a.id=c.model_id
        <where>
            <if test="dto.policeCategory != null and dto.policeCategory != ''">
                and b.police_category=#{dto.policeCategory}
            </if>
        </where>
        group by a.id
        <if test="dto.achievementCountOrder != null and dto.achievementCountOrder != ''">
            ORDER BY achievementCount ${dto.achievementCountOrder}
        </if>

    </select>
</mapper>