spring:
    application:
        name: model-statistics
    cloud:
        nacos:
            server-addr: @spring.cloud.nacos.server-addr@
            username: @spring.cloud.nacos.username@
            password: @spring.cloud.nacos.password@
            config:
                file-extension: yaml
                namespace: @spring.cloud.nacos.namespace@
                extension-configs:
                  - data-id: common-third-api.properties
                    group: DEFAULT_GROUP
                    refresh: true
                  - data-id: commons-datasource.yaml
                    group: DEFAULT_GROUP
                    refresh: true
                  - data-id: commons-arthas.yaml
                    group: DEFAULT_GROUP
                    refresh: true
                  - data-id: commons-oauth2-client.yaml
                    group: DEFAULT_GROUP
                    refresh: true
                  - data-id: commons-rabbit.yaml
                    group: DEFAULT_GROUP
                  - data-id: commons-es.yaml
                    group: DEFAULT_GROUP
                    refresh: true
                  - data-id: commons-feign.yaml
                    group: DEFAULT_GROUP
                    refresh: true
                  - data-id: commons-flyway.yaml
                    group: DEFAULT_GROUP
                    refresh: true
                  - data-id: commons-kafka.yaml
                    group: DEFAULT_GROUP
                    refresh: true
            discovery:
                namespace: @spring.cloud.nacos.namespace@