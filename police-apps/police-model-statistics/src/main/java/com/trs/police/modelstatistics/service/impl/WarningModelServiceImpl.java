package com.trs.police.modelstatistics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.grt.condify.parser.MybatisSearchParser;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.vo.control.WarningStatisticsVO;
import com.trs.police.modelstatistics.common.constants.CommonConstants;
import com.trs.police.modelstatistics.common.util.ResultHelper;
import com.trs.police.modelstatistics.domain.dto.ModelDto;
import com.trs.police.modelstatistics.domain.dto.MonitorWarningModelListDto;
import com.trs.police.modelstatistics.domain.entity.WarningModel;
import com.trs.police.modelstatistics.domain.vo.*;
import com.trs.police.modelstatistics.mapper.ModelDailyStatisticsMapper;
import com.trs.police.modelstatistics.mapper.ModelSceneMapper;
import com.trs.police.modelstatistics.mapper.WarningModelMapper;
import com.trs.police.modelstatistics.service.WarningModelService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-07-30 17:31
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WarningModelServiceImpl extends ServiceImpl<WarningModelMapper, WarningModel> implements WarningModelService {

    @Autowired
    private final WarningModelMapper warningModelMapper;

    @Autowired
    private ModelStatisticsTaskService taskService;

    @Autowired
    private ModelSceneMapper sceneMapper;

    @Autowired
    private final ModelDailyStatisticsMapper statisticsMapper;

    private List<ModelDailyStatisticsVO> modelUseInfoList;

    @Override
    public RestfulResultsV2<WarningModelVO> queryForPage(IPage<WarningModel> page, QueryWrapper<WarningModel> queryWrapper) {
        return null;
    }

    /**
     * 初始化所有模型信息
     */
    @PostConstruct
    public void init(){
        modelUseInfoList = warningModelMapper.selectAllInfo();
    }

    /**
     *  获取预警模型
     *
     * @param vo vo
     * @return 预警模型
     */
    @Override
    public List<WarningModel> getWarningModel(WarningStatisticsVO vo, int type) {
        QueryWrapper<WarningModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("third_model_id", Arrays.asList(vo.getModelIds().split(",")))
                .eq("type", type);
        return this.list(queryWrapper);
    }

    @Override
    public List<CountVO> moduleTrend(ModelDto dto) {
        //获取模型使用数量，按照天统计
        List<CountVO> countVOList = statisticsMapper.selectTrendTotal(dto);
        //按照天数分组
        Map<String, CountVO> collectMap = countVOList.stream().collect(Collectors.toMap(CountVO::getCreateTime, e -> e));
        if (StringUtils.isEmpty(dto.getStartTime()) || StringUtils.isEmpty(dto.getEndTime())){
            dto.setEndTime(LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD)));
            dto.setStartTime(Objects.isNull(countVOList) || countVOList.size()==0?LocalDateTime.now().minusYears(1L).format(DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD)):countVOList.get(0).getCreateTime());
        }
        List<CountVO> finalVoList = TimeUtils.getDateList(dto.getStartTime(), dto.getEndTime(), null, TimeUtils.YYYYMMDD, true, false)
                .stream().map(date -> {
                    CountVO vo = collectMap.get(date);
                    date = TimeUtils.stringToString(date, CommonConstants.TIME_MMDD);
                    if (vo == null) {
                        vo = new CountVO();
                        vo.setCreateTime(date);
                        vo.setDateTime(date);
                        vo.setAchievementCount(0);
                    }
                    return vo;
                }).
                collect(Collectors.toList());
        return finalVoList;
    }

    @Override
    public RestfulResultsV2<ModelUseVO> modelUsedInfo(ModelDto dto) {
        IPage<ModelDailyStatisticsVO> page = MybatisSearchParser.buildPage(dto);
        statisticsMapper.selectModelUseInfo(page, dto);
        //若模型未使用，则设置使用次数为0
        page.getRecords().forEach(element -> {
            if (element.getAchievementCount() == null || element.getAchievementCount().equals("")) {
                element.setAchievementCount(0);
            }
        });
        List<ModelUseVO> list = new ArrayList<>();
        //设置返回数据
        if (!dto.getTopKey().equals(0)) {
            //根据模型的种类分类
            Map<String, List<ModelDailyStatisticsVO>> collect = page.getRecords().stream()
                    .collect(Collectors.groupingBy(ModelDailyStatisticsVO::getModelCategory, LinkedHashMap::new, Collectors.toList()));
            Set<Map.Entry<String, List<ModelDailyStatisticsVO>>> entrySet = collect.entrySet();
            for (Map.Entry<String, List<ModelDailyStatisticsVO>> e : entrySet) {
                ModelUseVO modelUseVO = new ModelUseVO(e.getKey(), e.getValue());
                list.add(modelUseVO);
            }
        } else {
            list.add(new ModelUseVO("top10", page.getRecords().stream().limit(10).collect(Collectors.toList())));
        }
        return RestfulResultsV2.ok(list);
    }

    @Override
    public WarningModelVO modelIntroduction(Integer id) {
        WarningModel warningModel = warningModelMapper.selectById(id);
        WarningModelVO warningModelVO = new WarningModelVO();
        if (warningModel!=null){
            warningModelVO.setDesc(warningModel.getModelDesc());
            warningModelVO.setApplicationScenes(warningModel.getApplicationScene()==null?null:Arrays.asList(warningModel.getApplicationScene().split(",")));
            warningModelVO.setDataSources(warningModel.getDataSource()==null?null:Arrays.asList(warningModel.getDataSource().split(",")));
            warningModelVO.setName(warningModel.getName());
            warningModelVO.setThirdModelId(warningModel.getThirdModelId());
            warningModelVO.setType(warningModel.getType());
            warningModelVO.setId(warningModel.getId());
        }
        return warningModelVO;
    }

    @Override
    public RestfulResultsV2<MonitorWarningModelVO> getMonitorWarningModelList(MonitorWarningModelListDto dto) {
        IPage<MonitorWarningModelVO> page = MybatisSearchParser.buildPage(dto);
        warningModelMapper.selectMonitorWarningModelList(page,dto);
        return ResultHelper.getIPageConverter().convert(page);
    }

    @Override
    public void tbData() {
        log.info("线索池统计数量开始");
        taskService.modelStatistics();
        log.info("线索池统计数量结束");
    }

    @Override
    public CountVO getTotalDataAbility(ModelDto dto) {
        CountVO countVO = statisticsMapper.selectTotal(dto);
        Integer count = sceneMapper.selectAllCount();
        countVO.setSceneCount(Objects.nonNull(count) ? count : 0);
        return countVO;
    }

}
