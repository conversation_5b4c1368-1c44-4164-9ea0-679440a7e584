package com.trs.police.api.service.Impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.api.constant.ImportantPeopleConstant;
import com.trs.police.api.constant.ImportantPeopleTypeEnum;
import com.trs.police.api.domain.dto.GetPersonTypeDTO;
import com.trs.police.api.domain.vo.PeopleTypeVO;
import com.trs.police.api.mapper.ImportantPeopleMapper;
import com.trs.police.api.service.ImportantPeopleService;
import com.trs.police.common.core.excpetion.TRSException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;



/**
 * 重点人员
 */
@Slf4j
@Service
public class ImportantPeopleServiceImpl implements ImportantPeopleService {

    @Autowired
    private ImportantPeopleMapper importantPeopleMapper;

    @Override
    public JSONObject getPeopleType(GetPersonTypeDTO dto) {
        Long nowTimestamp = System.currentTimeMillis();
        RSA rsa = new RSA(ImportantPeopleConstant.RSA_PRIVATE_KEY, null);
        Long timestamp = Long.parseLong(new String(rsa.decrypt(dto.getTimestamp(), KeyType.PrivateKey)));
        log.info("传入时间戳[{}]", timestamp);
        log.info("当前时间戳[{}]", nowTimestamp);
        if(nowTimestamp - timestamp > ImportantPeopleConstant.MAX_SERVICE_TIME){
            log.info("时间相差[{}]毫秒", nowTimestamp - timestamp);
            throw new TRSException("已超时，拒绝访问！");
        }
        log.info("时间相差[{}]毫秒", nowTimestamp - timestamp);
        JSONObject resultJson = new JSONObject();
        resultJson.put("code",200);
        //解密身份证号
        String idNumber = new String(rsa.decrypt(dto.getIdNumber(), KeyType.PrivateKey));
        PeopleTypeVO peopleTypeVO = importantPeopleMapper.getPeopleType(idNumber);
        String type = " ";
        String name = " ";
        String contact = " ";
        if(peopleTypeVO == null){
            String result = "查无此人相关信息";
            log.info("返回的结果信息：[{}]",result);
            resultJson.put("data",result);
            return resultJson;
        }

        if(peopleTypeVO.getControlLevel() != null){
            type = ImportantPeopleTypeEnum.getTypeByCode(peopleTypeVO.getControlLevel());
        }
        if (peopleTypeVO.getResponsibleName() != null){
            name = peopleTypeVO.getResponsibleName();
        }
        if(peopleTypeVO.getResponsibleContact() != null){
            contact = peopleTypeVO.getResponsibleContact();
        }
        String result = String.format(ImportantPeopleConstant.RESULT_TEMPLATE,type,name,contact);
        log.info("返回的结果信息：[{}]",result);
        resultJson.put("data",result);
        return resultJson;
    }
}
