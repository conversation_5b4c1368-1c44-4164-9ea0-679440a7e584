package com.trs.police.apiforward.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import okio.Buffer;
import retrofit2.Response;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 序列化工具类
 */

@Slf4j
public class SerializeUtil {

    /**
     * 将RequestBody转换为字符串
     *
     * @param body 请求体
     * @return 请求体内容字符串
     */
    public static String bodyToString(RequestBody body) {
        if (body == null) {
            return "";
        }
        Buffer buffer = new Buffer();
        try {
            body.writeTo(buffer);
        } catch (IOException e) {
            log.error("requestBody读取错误", e);
            return "";
        }
        return buffer.readString(StandardCharsets.UTF_8);
    }

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 格式化errorBody
     *
     * @param response 响应类
     * @return JsonNode
     */
    public static JsonNode getErrorBody(Response<JsonNode> response) {
        try (ResponseBody errorBody = response.errorBody()) {
            if (errorBody != null) {
                String errorBodyString = errorBody.string();
                if (!errorBodyString.isEmpty()) {
                    return OBJECT_MAPPER.readTree(errorBodyString);
                }
            }
            return OBJECT_MAPPER.createObjectNode();
        } catch (Exception e) {
            log.error("格式化错误响应体失败", e);
            return OBJECT_MAPPER.createObjectNode();
        }
    }
}
