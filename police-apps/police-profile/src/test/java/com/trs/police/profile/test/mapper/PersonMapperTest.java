package com.trs.police.profile.test.mapper;

import com.alibaba.fastjson.JSON;
import com.trs.police.profile.ProfileApp;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.mapper.PersonMapper;
import com.trs.police.profile.mapper.ProfileVirtualIdentityMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/6 9:59
 */
@SpringBootTest(classes = ProfileApp.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PersonMapperTest {

    @Resource
    private PersonMapper personMapper;

    @Resource
    private ProfileVirtualIdentityMapper profileVirtualIdentityMapper;

    @Test
    public void testGetPersonByCertificateNumberAndTypesMore() {
        ArrayList<String> list = new ArrayList<>();
        list.add("110101199003076237");
        list.add("510502199003078470");
        List<Person> personByCertificateNumberAndTypes = personMapper.getPersonByCertificateNumberAndTypes(list, 1);
        System.out.println(JSON.toJSONString(personByCertificateNumberAndTypes));
    }

    @Test
    public void test() {
        ArrayList<String> list = new ArrayList<>();
        list.add("17398892881");
        profileVirtualIdentityMapper.getByVirtualNumberAndTypes(list, 1L);
    }

}
