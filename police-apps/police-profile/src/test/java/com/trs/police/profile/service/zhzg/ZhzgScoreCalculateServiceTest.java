package com.trs.police.profile.service.zhzg;

import com.trs.police.profile.domain.dto.zhzg.*;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgScoreResultVO;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 智慧政工积分计算服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class ZhzgScoreCalculateServiceTest {

    @Resource
    private ZhzgScoreCalculateService scoreCalculateService;

    private ZhzgPersonArchiveDTO personArchive;
    private List<ZhzgScoreRuleDTO> rules;

    @BeforeEach
    void setUp() {
        // 准备测试数据 - 人员档案
        personArchive = new ZhzgPersonArchiveDTO();
        personArchive.setId(1L);
        personArchive.setName("张三");
        personArchive.setGender(1);
        personArchive.setBirthday(LocalDate.of(1974, 1, 1));
        personArchive.setJoinWorkDate(LocalDate.of(2020, 1, 1)); // 工作4年
        personArchive.setAssessments(List.of(
                new ScoreNdkhDTO(LocalDate.of(2025, 3, 1), 1),
                new ScoreNdkhDTO(LocalDate.of(2024, 2, 27), 1),
                new ScoreNdkhDTO(LocalDate.of(2023, 3, 5), 1),
                new ScoreNdkhDTO(LocalDate.of(2022, 3, 1), 3)));
        personArchive.setViolations(List.of(
                new ScoreWgwjDTO(LocalDate.of(2023, 5, 12), 1),
                new ScoreWgwjDTO(LocalDate.of(1995, 8, 30), 2)));
        personArchive.setAwards(List.of(
                new ScoreLgsjDTO(1),
                new ScoreLgsjDTO(2)
        ));
        personArchive.setExpertises(List.of(
                new ScoreExpertisesDTO(3),
                new ScoreExpertisesDTO(5)
        ));
        personArchive.setSupports(List.of(
                new ScoreSupportTO(LocalDate.of(2024, 1, 1), LocalDate.of(2025, 1, 1), 1)
        ));

        // 准备测试数据 - 积分规则（树形结构）
        rules = ZhzgScoreUtil.buildRuleTree(Arrays.asList(
                createRule(1L, "基础分值", null, 0.0, 100.0,  false, "BASE_QUALITY"),
                createRule(11L, "工作年限", 1L, 0.1, 0.0,  true, "WORK_YEARS"),
                createRule(12L, "年龄分值", 1L, 0.041, 5.0,  true, "AGE"),
                createRule(13L, "专业技术分值", 1L, 0.0, 5.0,  false, "EXPERTISES"),
                createRule(131L, "国家级人才", 13L, 5.0, 0.0,  true, "EXPERTISES", "GJJRC"),
                createRule(132L, "国家科学技术奖", 13L, 5.0, 0.0,  true, "EXPERTISES", "GJKXJSJ"),
                createRule(133L, "全国公安机关本专业业务技能大比武", 13L, 2.0, 0.0,  true, "EXPERTISES", "QGGAJGBZYYWJNDBW"),
                createRule(134L, "新型专利权", 13L, 4.0, 0.0,  true, "EXPERTISES", "XXZLQ"),
                createRule(135L, "警务技术职务任职资格评委", 13L, 2.0, 0.0,  true, "EXPERTISES", "JWJSZWRZZGPW"),
                createRule(136L, "公安部警务技术专家库", 13L, 2.0, 0.0,  true, "EXPERTISES", "GABJWJSZJK"),
                createRule(137L, "主持省（部）级以上科研项目研发工作", 13L, 4.0, 0.0,  true, "EXPERTISES", "ZCSBJYSKYXMYFGZ"),
                createRule(138L, "主持编著本专业培训教材课件", 13L, 4.0, 0.0,  true, "EXPERTISES", "ZCBZBZYPXJCKJ"),
                createRule(139L, "中文核心期刊上发表本专业论文", 13L, 2.0, 0.0,  true, "EXPERTISES", "ZWHXQKSFBBZYLW"),
                createRule(1391L, "参与起草制修订专业及相关专业标准", 13L, 3.0, 0.0,  true, "EXPERTISES", "CYQCZXDZYJXGZYBZ"),
                createRule(14L, "职务分值", 1L, 0.0, 0.0,  false, "RANK"),
                createRule(141L, "任同级警员职务或警务技术职务", 14L, 0.11, 0.0,  false, "RANK", "A"),
                createRule(142L, "现任或曾任正科级领导职务的人员", 14L, 0.02, 0.0,  false, "RANK", "B"),
                createRule(143L, "现任或曾任副科级领导职务的人员", 14L, 0.01, 0.0,  false, "RANK", "C"),

                createRule(2L, "考核分值", null, 0.0, 5.0, false, "PERFORMANCE"),
                createRule(21L, "公务员年度考核分值", 2L, 0.4, 3.0, true, "ASSESSMENT"),
                createRule(22L, "惩戒减值", 2L, -0.5, -0.7, true, "VIOLATION"),
                createRule(23L, "奖励加分", 2L, 0.0, 3.0, false, "REWARD"),
                createRule(24L, "国家级劳模奖励", 23L, 3.0, 0.0, true, "AWARDS", "GJJLM"),
                createRule(25L, "省劳模奖励", 23L, 2.0, 0.0, true, "AWARDS", "SLM"),
                createRule(26L, "全国特级优秀人民警察奖励", 23L, 2.0, 0.0, true, "AWARDS", "QGTJYXRMJC"),
                createRule(27L, "全国优秀人民警察奖励", 23L, 1.5, 0.0, true, "AWARDS", "QGYXRMJC"),
                createRule(28L, "个人一等功奖励", 23L, 1.5, 0.0, true, "AWARDS", "GRYDG"),
                createRule(29L, "个人二等功奖励", 23L, 1.0, 0.0, true, "AWARDS", "GREDG"),
                createRule(291L, "个人三等功奖励", 23L, 0.8, 0.0, true, "AWARDS", "GRSDG"),
                createRule(292L, "先进个人奖励", 23L, 1.0, 0.0, true, "AWARDS", "XJGR"),
                createRule(293L, "优秀共产党员奖励", 23L, 0.4, 0.0, true, "AWARDS", "YXGCDY"),
                createRule(294L, "优秀党务工作者奖励", 23L, 0.4, 0.0, true, "AWARDS", "TXDWGZZ"),

                createRule(3L, "工作经历分值",  null, 0.1, 0.0, true, "WORK_EXPERIENCE")
        ));
    }

    @Test
    void testCalculateScore_Success() {
        // 执行计算
        ZhzgScoreResultVO result = scoreCalculateService.calculateScore(personArchive, rules);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertNotNull(result.getTotalScore());
        assertTrue(result.getTotalScore() > 0.0);
        assertNotNull(result.getRuleScoreDetails());
        assertFalse(result.getRuleScoreDetails().isEmpty());

        // 验证详细计算结果
        List<ZhzgRuleScoreDetailVO> details = result.getRuleScoreDetails();

//        // 应该包含所有规则的计算结果
//        assertTrue(details.size() >= 6); // 6个规则（2个父规则 + 4个子规则）
//
//        // 验证工作年限计算
//        ZhzgRuleScoreDetailVO workYearsDetail = findDetailByRuleName(details, "工作年限");
//        assertNotNull(workYearsDetail);
//        assertTrue(workYearsDetail.getSuccess());
//        assertTrue(workYearsDetail.getIsHit());
//        assertTrue(workYearsDetail.getScore() > 0);
//
//        // 验证教育经历计算
//        ZhzgRuleScoreDetailVO educationDetail = findDetailByRuleName(details, "教育经历");
//        assertNotNull(educationDetail);
//        assertTrue(educationDetail.getSuccess());
//        assertTrue(educationDetail.getIsHit());
//        assertEquals(10.0, educationDetail.getScore()); // 本科10分
//
//        // 验证立功受奖计算
//        ZhzgRuleScoreDetailVO awardsDetail = findDetailByRuleName(details, "立功受奖");
//        assertNotNull(awardsDetail);
//        assertTrue(awardsDetail.getSuccess());
//        assertTrue(awardsDetail.getIsHit());
//        assertEquals(10.0, awardsDetail.getScore()); // 2条记录 × 5分 = 10分
//
//        // 验证违纪违规计算
//        ZhzgRuleScoreDetailVO violationDetail = findDetailByRuleName(details, "违纪违规");
//        assertNotNull(violationDetail);
//        assertTrue(violationDetail.getSuccess());
//        assertTrue(violationDetail.getIsHit());
//        assertEquals(-10.0, violationDetail.getScore()); // 1条记录 × -10分 = -10分

        System.out.println("积分计算测试通过！");
        System.out.println("总分：" + result.getTotalScore());
        System.out.println("计算说明：" + result.getDescription());
        System.out.println("命中规则：" + result.getRuleScoreDetails());
        for (int i = 0; i < details.size(); i++) {
            System.out.println("  " + (i + 1) + ". " + details.get(i).toString());
        }
    }

    @Test
    void testCalculateScore_EmptyRules() {
        rules = List.of();

        ZhzgScoreResultVO result = scoreCalculateService.calculateScore(personArchive, rules);

        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getErrorMessage());
    }

    /**
     * 创建积分规则
     */
    private ZhzgScoreRuleDTO createRule(Long id, String name, Long parentId, Double score,
                                        Double fullScore, Boolean isLeaf, String ruleType) {
        return createRule(id, name, parentId, score, fullScore, isLeaf, ruleType, null);
    }

    /**
     * 创建积分规则
     */
    private ZhzgScoreRuleDTO createRule(Long id, String name, Long parentId, Double score,
                                        Double fullScore, Boolean isLeaf, String ruleType, String ruleSubType) {
        ZhzgScoreRuleDTO rule = new ZhzgScoreRuleDTO();
        rule.setId(id);
        rule.setName(name);
        rule.setParentId(parentId);
        rule.setScore(score);
        rule.setFullScore(fullScore);
        rule.setIsLeaf(isLeaf);
        rule.setRuleType(ruleType);
        rule.setRuleSubType(ruleSubType);
        rule.setDescription(name + "积分规则");
        return rule;
    }
}
