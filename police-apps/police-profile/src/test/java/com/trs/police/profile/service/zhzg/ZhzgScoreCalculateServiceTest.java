package com.trs.police.profile.service.zhzg;

import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.profile.converter.ZhzgScoreConverter;
import com.trs.police.profile.domain.dto.zhzg.*;
import com.trs.police.profile.domain.entity.zhzg.ZhzgScoreRuleEntity;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgScoreResultVO;
import com.trs.police.profile.mapper.zhzg.ZhzgScoreRuleMapper;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 智慧政工积分计算服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class ZhzgScoreCalculateServiceTest {

    @Resource
    private ZhzgScoreCalculateService scoreCalculateService;

    @Resource
    private ZhzgScoreRuleMapper ruleMapper;

    @Resource
    private ZhzgScoreConverter converter;

    private ZhzgPersonArchiveDTO personArchive;
    private List<ZhzgScoreRuleDTO> rules;

    @BeforeEach
    void setUp() {
        // 准备测试数据 - 人员档案
        personArchive = new ZhzgPersonArchiveDTO();
        personArchive.setId(1L);
        personArchive.setName("张三");
        personArchive.setGender(1);
        personArchive.setBirthday(LocalDate.of(1971, 7, 24));
        personArchive.setJoinWorkDate(LocalDate.of(1992, 7, 29));
        personArchive.setJoinPublicSecurityWorkDate(LocalDate.of(1992, 7, 29));
        personArchive.setPromotionStatus(1);
        //职级
        personArchive.setRanks(List.of(
                new ScoreRankDTO(LocalDate.of(1991, 2, 1), null, 1, 6),
                new ScoreRankDTO(LocalDate.of(1987, 1, 1), LocalDate.of(1991, 1, 31), 6, 1)));
        //职务
        personArchive.setResumes(List.of(
                new ScoreResumeDTO(LocalDate.of(1991, 2, 1), null, 1, 4, "公安部", 1),
                new ScoreResumeDTO(LocalDate.of(1987, 1, 1), LocalDate.of(1991, 1, 31), 6, 1, "公安部", 1)));
        //年度考核
        personArchive.setAssessments(List.of(
                new ScoreNdkhDTO(LocalDate.of(2024, 1, 1), 1),
                new ScoreNdkhDTO(LocalDate.of(2023, 1, 1), 1),
                new ScoreNdkhDTO(LocalDate.of(2022, 1, 1), 1)));
        personArchive.setEducations(List.of(
                new ScoreEducationDTO(LocalDate.of(2005, 6, 14), null, 7, 7, "北京大学", "计算机科学与技术")
        ));
        //惩罚减分
//        personArchive.setViolations(List.of(
//                new ScoreWgwjDTO(LocalDate.of(2023, 5, 12), 1),
//                new ScoreWgwjDTO(LocalDate.of(1995, 8, 30), 2)));
//        personArchive.setAwards(List.of(
//                new ScoreLgsjDTO(1),
//                new ScoreLgsjDTO(2)
//        ));
//        personArchive.setExpertises(List.of(
//                new ScoreExpertisesDTO(3),
//                new ScoreExpertisesDTO(5)
//        ));
//        personArchive.setSupports(List.of(
//                new ScoreSupportTO(LocalDate.of(2024, 1, 1), LocalDate.of(2025, 1, 1), 1)
//        ));
//        personArchive.setVotes(List.of(
//                new ScoreEvaluationDTO(10, 12, 3, 1)
//        ));

        // 准备测试数据 - 积分规则（树形结构）
        rules = Arrays.asList(
                createRule(1L, "任职年限得分", null, 0.0, 0.0, false, "1"),
                createRule(19L, "任职年限基础得分", 1L, 0.5, 0.0, true, "RANK", "BASIC"),
                createRule(11L, "晋升四级高级警长、警务技术四级主任且一级部门正职正科级领导职务", 1L, 1.0, 0.0,  true, "RANK", "A"),
                createRule(12L, "晋升四级高级警长、警务技术四级主任且一级部门副职正科级领导职务", 1L, 0.8, 0.0,  true, "RANK", "B"),
                createRule(13L, "晋升四级高级警长、警务技术四级主任且一级部门副职副科级领导职务、二级部门正职正科级领导职务", 1L, 0.5, 0.0,  true, "RANK", "C"),
                createRule(14L, "晋升四级高级警长、警务技术四级主任且二级部门正职副科级领导职务", 1L, 0.3, 0.0,  true, "RANK", "D"),
                createRule(15L, "晋升三级高级警长、警务技术三级主任且副县级领导职务", 1L, 1.0, 0.0,  true, "RANK", "E"),
                createRule(16L, "晋升三级高级警长、警务技术三级主任且一级部门正职正科级领导职务", 1L, 0.8, 0.0,  true, "RANK", "F"),
                createRule(17L, "晋升三级高级警长、警务技术三级主任且一级部门副职正科级领导职务", 1L, 0.5, 0.0,  true, "RANK", "G"),
                createRule(18L, "晋升三级高级警长、警务技术三级主任且一级部门副职副科级领导、二级部门正职正科级领导", 1L, 0.3, 0.0,  true, "RANK", "H"),


                createRule(2L, "奖励得分", null, 0.0, 10.0, false, "2"),
                createRule(21L, "一、二级英模", 2L, 10.0, 0.0, true, "AWARDS", "YRJLM"),
                createRule(22L, "全国劳动模范", 2L, 10.0, 0.0, true, "AWARDS", "QGLDMF"),
                createRule(23L, "省部级劳动模范", 2L, 8.0, 0.0, true, "AWARDS", "SBJLDMF"),
                createRule(24L, "全国特级优秀人民警察", 2L, 8.0, 0.0, true, "AWARDS", "QGTJYXRMJC"),
                createRule(25L, "全省特级优秀人民警察", 2L, 6.0, 0.0, true, "AWARDS", "QSTJYXRMJC"),
                createRule(26L, "全国优秀人民警察", 2L, 6.0, 0.0, true, "AWARDS", "QGYXRMJC"),
                createRule(27L, "市地级劳动模范", 2L, 6.0, 0.0, true, "AWARDS", "SDJLDMF"),
                createRule(28L, "个人一等功", 2L, 6.0, 0.0, true, "AWARDS", "GRYDG"),
                createRule(29L, "个人二等功", 2L, 3.0, 0.0, true, "AWARDS", "GREDG"),
                createRule(210L, "个人三等功", 2L, 1.5, 0.0, true, "AWARDS", "GRSDG"),
                createRule(211L, "优秀共产党员", 2L, 1.5, 0.0, true, "AWARDS", "YXGCDY"),
                createRule(212L, "优秀党务工作者", 2L, 1.5, 0.0, true, "AWARDS", "YXDWGZZ"),


                createRule(3L, "年度考核分值", null, 0.0, 0.0, false, "3"),
                createRule(31L, "年度考核分值", 3L, 0.5, 0.0, true, "ASSESSMENT"),


                createRule(4L, "学历学位得分", null, 5.0, 0.0, false, "4"),
                createRule(41L, "获得博士研究生学历及学位", 4L, 5.0, 0.0, true, "EDUCATION", "A"),
                createRule(42L, "获得博士学位", 4L, 3.0, 0.0, true, "EDUCATION", "B"),
                createRule(42L, "获得硕士研究生学历及相应学位", 4L, 2.0, 0.0, true, "EDUCATION", "C"),
                createRule(42L, "获得硕士研究生学历或者学位", 4L, 1.0, 0.0, true, "EDUCATION", "D"),


                createRule(5L, "惩戒减分", null, 0.0, 0.0, false, "5"),
                createRule(51L, "政务处分警告", 5L, -1.5, 0.0, true, "VIOLATION", "GOVERNMENT_WARNING"),
                createRule(52L, "政务处分记过", 5L, -3.0, 0.0, true, "VIOLATION", "GOVERNMENT_DEMERIT"),
                createRule(53L, "政务处分记大过", 5L, -4.5, 0.0, true, "VIOLATION", "GOVERNMENT_MAJOR_DEMERIT"),
                createRule(54L, "政务处分降级", 5L, -6.0, 0.0, true, "VIOLATION", "GOVERNMENT_DEMOTION"),
                createRule(55L, "党纪处分警告", 5L, -2.0, 0.0, true, "VIOLATION", "PARTY_WARNING"),
                createRule(56L, "党纪处分严重警告", 5L, -3.5, 0.0, true, "VIOLATION", "PARTY_SERIOUS_WARNING"),
                createRule(57L, "党纪处分撤销党内职务", 5L, -5.0, 0.0, true, "VIOLATION", "PARTY_DISMISSAL"),
                createRule(58L, "党纪处分留党察看", 5L, -6.5, 0.0, true, "VIOLATION", "PARTY_PROBATION"),


                createRule(6L, "民主测评得分", null, 0.0, 0.0, false, "6"),
                createRule(61L, "民主测评得分", 6L, 0.0, 0.0, true, "EVALUATION")
        );
    }

    @Test
    void testCalculateScore_Success() {
        final List<ZhzgScoreRuleDTO> tree = ZhzgScoreUtil.buildRuleTree(rules);
        // 执行计算
        ZhzgScoreResultVO result = scoreCalculateService.calculateScore(personArchive, tree);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertNotNull(result.getTotalScore());
//        assertTrue(result.getTotalScore() > 0.0);
        assertNotNull(result.getRuleScoreDetails());
        assertFalse(result.getRuleScoreDetails().isEmpty());

        // 验证详细计算结果
        List<ZhzgRuleScoreDetailVO> details = result.getRuleScoreDetails();

        System.out.println("积分计算测试通过！");
        System.out.println("总分：" + result.getTotalScore());
        System.out.println("计算说明：" + result.getDescription());
        List<ZhzgRuleScoreTreeNodeDTO> treeNodes = convertToTreeNodes(details);
        List<ZhzgRuleScoreTreeNodeDTO> treeNode = ZhzgScoreUtil.buildTree(treeNodes);
        System.out.println("命中规则：" + JsonUtil.toJsonString(treeNode));
        for (int i = 0; i < details.size(); i++) {
            System.out.println("  " + (i + 1) + ". " + details.get(i).toString());
        }
    }

    /**
     * 将ZhzgRuleScoreDetailVO列表转换为ZhzgRuleScoreTreeNodeDTO列表
     *
     * @param ruleScoreDetails 规则积分详情列表
     * @return 树节点列表
     */
    private List<ZhzgRuleScoreTreeNodeDTO> convertToTreeNodes(List<ZhzgRuleScoreDetailVO> ruleScoreDetails) {
        if (ruleScoreDetails == null || ruleScoreDetails.isEmpty()) {
            return new ArrayList<>();
        }

        return ruleScoreDetails.stream()
                .map(detail -> ZhzgRuleScoreTreeNodeDTO.builder()
                        .ruleId(detail.getRuleId())
                        .ruleName(detail.getRuleName())
                        .parentRuleId(detail.getParentRuleId())
                        .score(detail.getScore())
                        .children(new ArrayList<>())
                        .build())
                .collect(Collectors.toList());
    }

    @Test
    void testCalculateScore_EmptyRules() {
        rules = List.of();

        ZhzgScoreResultVO result = scoreCalculateService.calculateScore(personArchive, rules);

        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getErrorMessage());
    }

    /**
     * 将测试类中的规则导入数据库
     */
    @Test
    void insertRule() {
        List<ZhzgScoreRuleEntity> list = rules.stream().map(converter::toRuleEntity).collect(Collectors.toList());
        for (ZhzgScoreRuleEntity entity : list) {
            ruleMapper.insert(entity);
        }
        System.out.println("finish.");
    }

    /**
     * 创建积分规则
     */
    private ZhzgScoreRuleDTO createRule(Long id, String name, Long parentId, Double score,
                                        Double fullScore, Boolean isLeaf, String ruleType) {
        return createRule(id, name, parentId, score, fullScore, isLeaf, ruleType, null);
    }

    /**
     * 创建积分规则
     */
    private ZhzgScoreRuleDTO createRule(Long id, String name, Long parentId, Double score,
                                        Double fullScore, Boolean isLeaf, String ruleType, String ruleSubType) {
        ZhzgScoreRuleDTO rule = new ZhzgScoreRuleDTO();
        rule.setId(id);
        rule.setName(name);
        rule.setParentId(parentId);
        rule.setScore(score);
        rule.setFullScore(fullScore);
        rule.setIsLeaf(isLeaf);
        rule.setRuleType(ruleType);
        rule.setRuleSubType(ruleSubType);
        rule.setDescription(name + "积分规则");
        String template = "insert into t_zhzg_score_rule (id, name, parent_id, score, full_score, is_leaf, is_enabled, rule_type, rule_sub_type) values (%d, %s, %d, %.3f, %.3f, %d, %d, %s, %s);";
        System.out.printf((template) + "%n",
                id, name, parentId, score, fullScore, isLeaf ? 1 : 0, 1, ruleType, ruleSubType);
        return rule;
    }
}
