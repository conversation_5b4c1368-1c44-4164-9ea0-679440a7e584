package com.trs.police.profile.test.mapper;

import com.alibaba.fastjson.JSON;
import com.trs.police.profile.ProfileApp;
import com.trs.police.profile.domain.vo.CaseListVO;
import com.trs.police.profile.mapper.ProfileCaseMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/25 23:57
 */
@SpringBootTest(classes = ProfileApp.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ProfileCaseMapperTest {

    @Resource
    private ProfileCaseMapper profileCaseMapper;

    @Test
    public void test() {
        List<CaseListVO> caseById = profileCaseMapper.getCaseById(30L);
        System.out.println(JSON.toJSONString(caseById));
    }

}
