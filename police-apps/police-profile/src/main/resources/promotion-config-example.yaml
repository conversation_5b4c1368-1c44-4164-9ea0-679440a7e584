# 职级晋升配置
police:
  promotion:
    # 职级晋升映射关系
    rank-mapping:
      # 执法勤务序列 使用中文作为key，便于理解，这里必须是"[二级警员]" 这种形式 否则PromotionRankMappingConfig读取会报错
      "[二级警员]":
        - "一级警员"
      "[一级警员]":
        - "四级警长"
      "[四级警长]":
        - "三级警长"
      "[三级警长]":
        - "二级警长"
      "[二级警长]":
        - "一级警长"
      "[一级警长]":
        - "四级高级警长"
      "[四级高级警长]":
        - "三级高级警长"
      "[三级高级警长]":
        - "二级高级警长"
      "[二级高级警长]":
        - "一级高级警长"

      # 技术职务序列
      "[警务技术员]":
        - "四级主管"
        - "四级警长"
      "[四级主管]":
        - "三级主管"
        - "三级警长"
      "[三级主管]":
        - "二级主管"
        - "二级警长"
      "[二级主管]":
        - "一级主管"
        - "一级警长"
      "[一级主管]":
        - "四级主任"
        - "四级高级警长"
      "[四级主任]":
        - "三级主任"
        - "三级高级警长"
      "[三级主任]":
        - "二级主任"
        - "二级高级警长"
      "[二级主任]":
        - "一级主任"
        - "一级高级警长"

    # 晋升策略配置
    rank-strategies:
      "[一级高级警长]":
        - "RankTenurePromotionStrategy"
        - "SeniorRankAgePromotionStrategy"
      "[二级高级警长]":
        - "RankTenurePromotionStrategy"
        - "SeniorRankAgePromotionStrategy"
      "[三级高级警长]":
        - "RankTenurePromotionStrategy"
        - "SeniorRankAgePromotionStrategy"
      "[四级高级警长]":
        - "RankTenurePromotionStrategy"
        - "SeniorRankAgePromotionStrategy"
      "[一级警长]":
        - "RankTenurePromotionStrategy"
      "[二级警长]":
        - "RankTenurePromotionStrategy"
      "[三级警长]":
        - "RankTenurePromotionStrategy"
      "[四级警长]":
        - "RankTenurePromotionStrategy"
      "[一级警员]":
        - "RankTenurePromotionStrategy"
      "[一级主任]":
        - "RankTenurePromotionStrategy"
      "[二级主任]":
        - "RankTenurePromotionStrategy"
      "[三级主任]":
        - "RankTenurePromotionStrategy"
      "[四级主任]":
        - "RankTenurePromotionStrategy"
      "[一级主管]":
        - "RankTenurePromotionStrategy"
      "[二级主管]":
        - "RankTenurePromotionStrategy"
      "[三级主管]":
        - "RankTenurePromotionStrategy"
      "[四级主管]":
        - "RankTenurePromotionStrategy"

    # 任职年限要求
    rank-tenure-requirements:
      "[二级主任]":
        "[三级主任]": 2
      "[一级主任]":
        "[二级主任]": 3
        "[正处级领导职务]": 3
      "[三级主任]":
        "[四级主任]": 2
        "[副处级领导职务]": 2
      "[四级主任]":
        "[一级主管]": 2
      "[一级主管]":
        "[二级主管]": 2
        "[正科级领导职务]": 2
      "[二级主管]":
        "[三级主管]": 2
      "[三级主管]":
        "[四级主管]": 2
        "[副科级领导职务]": 2
      "[四级主管]":
        "[警务技术员]": 2
      "[一级高级警长]":
        "[二级高级警长]": 3
        "[正处级领导职务]": 3
      "[二级高级警长]":
        "[三级高级警长]": 2
      "[三级高级警长]":
        "[四级高级警长]": 2
        "[副处级领导职务]": 2
      "[四级高级警长]":
        "[一级警长]": 2
      "[一级警长]":
        "[二级警长]": 2
        "[正科级领导职务]": 2
      "[二级警长]":
        "[三级警长]": 2
      "[三级警长]":
        "[四级警长]": 2
        "[副科级领导职务]": 2
      "[四级警长]":
        "[一级警员]": 2
      "[一级警员]":
        "[二级警员]": 2

# 日志级别配置
logging:
  level:
    com.trs.police.profile.strategy.promotion: DEBUG
    com.trs.police.profile.service.impl.PolicePromotionServiceImpl: INFO
    com.trs.police.profile.config.PromotionRankMappingConfig: DEBUG
