<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.SthyLzMapper">


    <select id="selectNew" resultType="com.trs.police.profile.domain.entity.JQ">
        SELECT JJDBH,
               XZQHDM,
               JJDWDM,
               JJDWMC,
               JJLX,
               JQLYFS,
               LHLX,
               JJYBH,
               JJYXM,
               BJSJ,
               JJSJ,
               JJWCSJ,
               BJDH,
               BJDHYHM,
               BJDHYHDZ,
               BJRMC,
               BJRXBDM,
               LXDH,
               BJRZJHM,
               BJDZ,
               JQDZ,
               BJNR,
               GXDWDM,
               GXDWMC,
               JQLBDM,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQLBDM limit 1) AS JQLBMC,
               JQLXDM,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQLXDM limit 1) AS JQLXMC,
               JQXLDM,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQXLDM limit 1) AS JQXLMC,
               JQZLDM,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQZLDM limit 1) AS JQZLMC,
               ZARS,
               BJRXZB,
               BJRYZB,
               BCJJNR,
               JQDJDM,
               JQCLZTDM,
               JQBQ,
               WXBS,
               JJXWH,
              -- JJFS,
               FXDWJD as jd,
               FXDWWD as wd
        FROM ${tableName} t
        WHERE t.BJSJ > #{beginTime}
    </select>

    <select id="selectByBh" resultType="com.trs.police.profile.domain.entity.JQ">
        SELECT JJDBH,
               XZQHDM,
               JJDWDM,
               JJDWMC,
               JJLX,
               JQLYFS,
               LHLX,
               JJYBH,
               JJYXM,
               BJSJ,
               JJSJ,
               JJWCSJ,
               BJDH,
               BJDHYHM,
               BJDHYHDZ,
               BJRMC,
               BJRXBDM,
               LXDH,
               BJRZJHM,
               BJDZ,
               JQDZ,
               BJNR,
               GXDWDM,
               GXDWMC,
               JQLBDM,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQLBDM limit 1) AS JQLBMC,
               JQLXDM,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQLXDM limit 1) AS JQLXMC,
               JQXLDM,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQXLDM limit 1) AS JQXLMC,
               JQZLDM,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQZLDM limit 1) AS JQZLMC,
               ZARS,
               BJRXZB,
               BJRYZB,
               BCJJNR,
               JQDJDM,
               JQCLZTDM,
               JQBQ,
               WXBS,
               JJXWH,
              --JJFS,
               FXDWJD as jd,
               FXDWWD as wd
        FROM ${tableName} t
        WHERE t.JJDBH = #{bh}
    </select>

    <select id="selectFkxx" resultType="com.trs.police.profile.domain.entity.JqFkxx">
        select t.*,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQLBDM limit 1) AS JQLBMC,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQLXDM limit 1) AS JQLXMC,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQXLDM limit 1) AS JQXLMC,
               (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQZLDM limit 1) AS JQZLMC
        from ${tableName} t
        where t.jjdbh = #{bh}
    </select>

    <select id="selectFkxxNew" resultType="com.trs.police.profile.domain.entity.JqFkxx">
           select t.*,
                  (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQLBDM limit 1) AS JQLBMC,
                  (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQLXDM limit 1) AS JQLXMC,
                  (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQXLDM limit 1) AS JQXLMC,
                  (SELECT value FROM icc_dict d WHERE d.dict_key = t.JQZLDM limit 1) AS JQZLMC
           from ${tableName} t
           where t.fksj > #{beginTime}
    </select>


</mapper>