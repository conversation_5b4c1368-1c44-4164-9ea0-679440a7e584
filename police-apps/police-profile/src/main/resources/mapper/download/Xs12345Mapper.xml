<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.Xs12345Mapper">

<select id="selectNew" resultType="com.trs.police.profile.domain.entity.Clue">
    select
        t.code as code,
        t.title as name,
        2 as source,
        t.content as detail,
        t.address as target_location,
        t.dep_firstenter_time as report_time,
        t.fromtel as relatedPhone
    from shzy_12345rx_gdjcxx t
    WHERE t.dep_firstenter_time > #{beginTime}

</select>


    <select id="selectXsByCode" resultType="com.trs.police.profile.domain.entity.Clue">
        select
            t.code as code,
            t.title as name,
            2 as source,
            t.content as detail,
           -- t.address as target_location,
            t.areaname as areaCode,
            t.dep_firstenter_time as report_time,
            t.fromtel as relatedPhone,
            t.idcard as idNumber
        from shzy_12345rx_gdjcxx t
        where t.code = #{bh} limit 1
    </select>
</mapper>