<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.profile.mapper.ProfileVirtualIdentityMapper">

    <!-- 根据虚拟身份号和类型获取虚拟身份 -->
    <select id="getByVirtualNumberAndType" resultType="com.trs.police.profile.domain.entity.ProfileVirtualIdentityEntity">
        SELECT *
        FROM t_profile_virtual_identity
        WHERE virtual_number = #{virtualNumber}
          AND type = #{type}
    </select>

    <!-- 根据虚拟身份号集合和类型获取虚拟身份 -->
    <select id="getByVirtualNumberAndTypes" resultType="com.trs.police.profile.domain.entity.ProfileVirtualIdentityEntity">
        SELECT *
        FROM t_profile_virtual_identity
        WHERE type = #{type}
        AND virtual_number IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>