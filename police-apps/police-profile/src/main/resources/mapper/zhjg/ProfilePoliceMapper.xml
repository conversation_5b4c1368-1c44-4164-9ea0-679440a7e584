<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.zhzg.ProfilePoliceMapper">

    <resultMap id="PoliceProfileResultMap" type="com.trs.police.profile.domain.vo.HzPoliceVO">
        <!-- 基本字段映射 -->
        <result column="name" property="name" />
        <result column="rzxl_code" property="rzxlCode" />
        <result column="age" property="age" />
        <result column="id_number" property="idCardNumber" />
        <result column="current_rank" property="currentRank" />
        <result column="personId" property="personId" />
        <result column="currentRankCode" property="currentRankCode" />
        <result column="rank_tenure_time" property="rankTenureTime" />
        <result column="department" property="department" />
        <result column="current_position" property="currentPosition" />
        <result column="currentPositionCode" property="currentPositionCode" />
        <result column="position_tenure_time" property="positionTenureTime" />
        <result column="score" property="score" />
        <result column="promotion_qualification" property="promotionQualification" />
        <result column="promotionQualificationCode" property="promotionQualificationCode" />
        <!-- JSON 类型字段处理（deptIds） -->
        <result column="deptIds" property="deptIds" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler" />
        <!-- 关联表字段（如从 LEFT JOIN 查询中映射） -->
        <result column="rank_code" property="currentRank" />
        <result column="rzxlCode" property="rzxlCode" />
        <result column="rankTenureTime" property="rankTenureTime" />
        <result column="position" property="currentPosition" />
        <result column="positionTenureTime" property="positionTenureTime" />
    </resultMap>

    <select id="selectPageList" resultMap="PoliceProfileResultMap">
        SELECT
        p.id as personId,
        p.`name` as `name`,
        <if test="dto.startAge != null or dto.endAge != null ">
            TIMESTAMPDIFF(YEAR, p.birthday, CURDATE()) AS age,
        </if>
        p.id_number,
        p.dept_ids AS deptIds,
        latest_rank.rank_code AS currentRankCode,
        latest_rank.start_time AS rankTenureTime,
        latest_rank.rank_series AS rzxlCode,
        latest_resume.department,
        latest_resume.position AS currentPositionCode,
        latest_resume.start_time AS positionTenureTime,
        p.score,
        p.promotion_status AS promotionQualificationCode
        FROM
        t_profile_police p
        LEFT JOIN (SELECT profile_id, rank_code AS rank_code, start_time AS start_time, rank_series AS rank_series,
        ROW_NUMBER() OVER (PARTITION BY profile_id ORDER BY start_time DESC) as rn FROM t_police_rank_relation WHERE deleted = 0
        GROUP BY profile_id ORDER BY start_time) AS latest_rank ON p.id = latest_rank.profile_id
        LEFT JOIN (SELECT profile_id, department, position, start_time AS start_time, person_type AS person_type,
        ROW_NUMBER() OVER (PARTITION BY profile_id ORDER BY start_time DESC) as rn FROM t_police_resume_relation WHERE deleted = 0
        GROUP BY profile_id ORDER BY start_time) AS latest_resume ON p.id = latest_resume.profile_id
        <where>
            latest_rank.rn = 1 and latest_resume.rn = 1
            <if test="dto.zjxl != null">
                AND latest_rank.rank_series = #{dto.zjxl}
            </if>
            <if test="dto.zj != null">
                AND latest_rank.rank_code = #{dto.zj}
            </if>
            <if test="dto.fhjsZjCode != null and dto.fhjsZjCode.size() > 0">
                AND latest_rank.rank_code in
                <foreach collection="dto.fhjsZjCode" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="dto.jszgqk != null">
                AND p.promotion_status = #{dto.jszgqk}
            </if>
            <if test="dto.personType != null">
                AND latest_resume.person_type = #{dto.personType}
            </if>
            <if test="dto.deptId != null">
                AND JSON_CONTAINS(dept_ids, CAST(#{dto.deptId} AS JSON), '$')
            </if>
            <if test="dto.startAge != null and dto.endAge != null ">
                and TIMESTAMPDIFF(YEAR, p.birthday, CURDATE()) BETWEEN #{dto.startAge} and #{dto.endAge}
            </if>
            <if test="dto.startAge != null and dto.endAge == null ">
                and TIMESTAMPDIFF(YEAR, p.birthday, CURDATE())  >= #{dto.startAge}
            </if>
            <if test="dto.startAge == null and dto.endAge != null ">
                and TIMESTAMPDIFF(YEAR, p.birthday, CURDATE())  &lt;= #{dto.endAge}
            </if>
            <choose>
                <when test="dto.searchKey == 'name'">
                    AND p.name LIKE CONCAT('%', #{dto.searchValue}, '%')
                </when>
                <when test="dto.searchKey == 'idCard'">
                    AND p.id_number LIKE CONCAT('%', #{dto.searchValue}, '%')
                </when>
                <when test="dto.searchKey == 'tel'">
                    AND p.tel LIKE CONCAT('%', #{dto.searchValue}, '%')
                </when>
            </choose>
        </where>
        order by score
        <choose>
            <when test="dto.scoreOrder == 'asc'">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="statisticZzmm" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select political_status as `key`, count(1) as `count`
        from t_profile_police p
        <if test="dto.areaCode != null and dto.areaCode != ''">
            LEFT JOIN t_user u on p.id_number = u.idcard
            LEFT JOIN t_user_dept_relation ru on ru.user_id = u.id
            LEFT JOIN t_dept d on ru.dept_id = d.id
        </if>
        <where>
            political_status is not null
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and d.district_code like concat('',#{dto.areaCode},'%')
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                and p.`type` = #{dto.personType}
            </if>
        </where>
        group by political_status
    </select>


    <select id="statisticAge" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select TIMESTAMPDIFF(YEAR, p.birthday, CURDATE()) AS `key`, count(distinct p.id) as `count`
        from t_profile_police p
        <if test="dto.areaCode != null and dto.areaCode != ''">
            LEFT JOIN t_user u on p.id_number = u.idcard
            LEFT JOIN t_user_dept_relation ru on ru.user_id = u.id
            LEFT JOIN t_dept d on ru.dept_id = d.id
        </if>
        <where>
            p.birthday is not null
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and d.district_code like concat('',#{dto.areaCode},'%')
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                and p.`type` = #{dto.personType}
            </if>
        </where>
        group by `key`
    </select>

    <select id="zjqkStatistic" resultType="com.trs.police.statistic.domain.bean.CountItem">
        SELECT
        latest_ranks.rank_code AS `key`,
        COUNT(DISTINCT latest_ranks.profile_id) AS `count`
        FROM (
        SELECT
        r.profile_id,
        r.rank_code,
        r.start_time,
        ROW_NUMBER() OVER (PARTITION BY r.profile_id ORDER BY r.start_time DESC) as rn
        FROM
        t_police_rank_relation r
        ) latest_ranks
        LEFT JOIN t_profile_police p ON latest_ranks.profile_id = p.id
        <if test="dto.areaCode != null and dto.areaCode != ''">
            LEFT JOIN t_user u on p.id_number = u.idcard
            LEFT JOIN t_user_dept_relation ru on ru.user_id = u.id
            LEFT JOIN t_dept d on ru.dept_id = d.id
        </if>
        <where>
            latest_ranks.rn = 1
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and d.district_code like concat('',#{dto.areaCode},'%')
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                and p.`type` = #{dto.personType}
            </if>
        </where>
        GROUP BY
        latest_ranks.rank_code;
    </select>
    <select id="statisticXl" resultType="com.trs.police.statistic.domain.bean.CountItem">
        SELECT
        latest_ranks.degree AS `key`,
        COUNT(DISTINCT latest_ranks.profile_id) AS `count`
        FROM (
        SELECT
        r.profile_id,
        r.degree,
        r.start_time,
        ROW_NUMBER() OVER (PARTITION BY r.profile_id ORDER BY r.start_time DESC) AS rn
        FROM
        t_police_education_experience_relation r
        ) latest_ranks
        LEFT JOIN t_profile_police p ON latest_ranks.profile_id = p.id
        <if test="dto.areaCode != null and dto.areaCode != ''">
            LEFT JOIN t_user u on p.id_number = u.idcard
            LEFT JOIN t_user_dept_relation ru on ru.user_id = u.id
            LEFT JOIN t_dept d on ru.dept_id = d.id
        </if>
        <where>
            latest_ranks.rn = 1
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and d.district_code like concat('',#{dto.areaCode},'%')
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                and p.`type` = #{dto.personType}
            </if>
        </where>
        GROUP BY
        latest_ranks.degree;
    </select>

    <select id="dataOverview" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select p.`type` as `key`, count(1) as `count`
        from t_profile_police p
        <if test="dto.areaCode != null and dto.areaCode != ''">
            LEFT JOIN t_user u on p.id_number = u.idcard
            LEFT JOIN t_user_dept_relation ru on ru.user_id = u.id
            LEFT JOIN t_dept d on ru.dept_id = d.id
        </if>
        <where>
            p.`type` is not null
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and d.district_code like concat('',#{dto.areaCode},'%')
            </if>
        </where>
        group by p.`type`
    </select>
</mapper>