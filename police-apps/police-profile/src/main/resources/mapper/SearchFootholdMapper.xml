<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.SearchFootholdMapper">

    <resultMap id="listMap" type="com.trs.police.profile.domain.vo.FootholdListVO">
        <result column="name" property="name"/>
        <result column="stayCount" property="stayCount"/>
        <result column="time" property="time"
                typeHandler="com.trs.police.common.core.handler.typehandler.LocalDateTimeHandler"/>

        <association select="getLon" property="lon" column="{xxdz=name,zhcxsj=time}"/>
        <association select="getLat" property="lat" column="{xxdz=name,zhcxsj=time}"/>

    </resultMap>

    <select id="getLon" resultType="java.lang.String">
        select jd
        from tb_search_luo_jiao_dian
        where xxdz = #{xxdz}
          and zhcxsj = #{zhcxsj}
        limit 1
    </select>

    <select id="getLat" resultType="java.lang.String">
        select wd
        from tb_search_luo_jiao_dian
        where xxdz = #{xxdz}
          and zhcxsj = #{zhcxsj}
        limit 1
    </select>

    <select id="getFootholdList" resultMap="listMap">
        <bind name="searchParams" value="params.searchParams"/>
        <bind name="filterParams" value="params.filterParams"/>
        select xxdz as name,
        sum(jszqcs) as stayCount,
        max(zhcxsj) as time
        from tb_search_luo_jiao_dian
        <where>
            <foreach collection="ids" item="id" open="(" close=")" separator="or">
                ( ysbsf = #{id.name} and ysz = #{id.code})
            </foreach>

            <if test="searchParams.searchValue != null and searchParams.searchValue != ''">
                and xxdz like concat('%',#{searchParams.searchValue},'%')
            </if>

            <if test="filterParams!=null and filterParams.size() > 0">
                <foreach collection="filterParams" item="filterParam">
                    <choose>
                        <when
                                test="filterParam.key == 'time' and filterParam.getProcessedValue().isAll() == false">
                            <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                            and zhcxsj between
                            #{timeParam.beginTime,typeHandler=com.trs.police.common.core.handler.typehandler.LocalDateTimeHandler}
                            and
                            #{timeParam.endTime,typeHandler=com.trs.police.common.core.handler.typehandler.LocalDateTimeHandler}
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
        group by xxdz
        order by time desc
    </select>
</mapper>