<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.mapper.PersonMapper">

    <resultMap id="personVoMap" type="com.trs.police.common.core.vo.profile.PersonVO">
        <id column="id" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="registered_residence" jdbcType="VARCHAR" property="registeredResidence"/>
        <result column="id_number" jdbcType="VARCHAR" property="certificateNumber"/>
        <result column="id_type" property="certificateType"/>
        <result column="id" property="targetId"/>
        <result column="monitor_count" property="monitorCount"/>
        <result column="regular_count" property="regularCount"/>
        <result column="car_number" property="carNumber"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="tel" property="tel"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="person_label" property="targetType"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="imgs" property="imgs"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="virtual_identity_ids" property="virtualIdentityIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <collection property="virtualIdentity" javaType="java.util.ArrayList"
            ofType="com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO"
            select="com.trs.police.common.core.mapper.CommonMapper.getVirtualIdentity"
            column="{virtualIdentityIds=virtual_identity_ids}"/>
    </resultMap>

    <resultMap id="personVoMapV2" type="com.trs.police.common.core.vo.profile.PersonVO">
        <id column="id" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="id_number" jdbcType="VARCHAR" property="certificateNumber"/>
        <result column="id_type" property="certificateType"/>
        <result column="id" property="targetId"/>
        <result column="monitor_count" property="monitorCount"/>
        <result column="regular_count" property="regularCount"/>
        <result column="car_number" property="carNumber"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="tel" property="tel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="person_label" property="targetType"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="virtual_identity_ids" property="virtualIdentityIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="imgs" property="imgs"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="approvalDetail" property="approvalDetail"/>
    </resultMap>

    <resultMap id="personMap" type="com.trs.police.profile.domain.entity.Person">
        <id column="id" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="id_type" property="idType"/>
        <result column="id" property="id"/>
        <result column="gender" property="gender"/>
        <result column="former_name" property="formerName"/>
        <result column="nick_name" property="nickName"/>
        <result column="nation" property="nation"/>
        <result column="political_status" property="politicalStatus"/>
        <result column="religious_belief" property="religiousBelief"/>
        <result column="martial_status" property="martialStatus"/>
        <result column="current_job" property="currentJob"/>
        <result column="current_position" property="currentPosition"/>
        <result column="registered_residence" property="registeredResidence"/>
        <result column="registered_residence_detail" property="registeredResidenceDetail"/>
        <result column="current_residence" property="currentResidence"/>
        <result column="current_residence_detail" property="currentResidenceDetail"/>
        <result column="main_demand" property="mainDemand"/>
        <result column="work_measures" property="workMeasures"/>
        <result column="petition_info" property="petitionInfo"/>
        <result column="monitor_status" property="monitorStatus"/>
        <result column="control_status" property="controlStatus"/>
        <result column="complete_rate" property="completeRate"/>
        <result column="person_type" property="personType"/>
        <result column="work_target" property="workTarget"/>
        <result column="control_level" property="controlLevel"/>
        <result column="risk_score" property="riskScore"/>
        <result column="raw_score" property="rawScore"/>
        <result column="risk_level" property="riskLevel"/>
        <result column="approval_statue_code" property="approvalStatueCode"/>
        <result column="tel" property="tel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="person_label" property="personLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="photo" property="photo"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToFileInfoHandler"/>
        <result column="virtual_identity_ids" property="virtualIdentityIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="vehicle_ids" property="vehicleIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="family_relation_ids" property="familyRelationIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="social_relation_ids" property="socialRelationIds"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
    </resultMap>

    <resultMap id="personListVo" type="com.trs.police.common.openfeign.starter.vo.PersonListVO">
        <id column="id" property="id"/>
        <result column="photo" property="photo"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="name" property="name"/>
        <result column="idNumber" property="idNumber"/>
        <result column="gender" property="gender"/>
        <result column="personLabel" property="personLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="riskScore" property="riskScore"/>
        <result column="riskLevel" property="riskLevel"/>
        <result column="controlLevel" property="controlLevel"/>
        <result column="dutyPolice" property="dutyPolice"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="dutyPoliceStation" property="dutyPoliceStation"/>
        <result column="registeredResidence" property="registeredResidence"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="approvalStatueCode" property="approvalStatueCode"/>
        <result column="regularLabel" property="regularLabel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="currentResidence" property="currentResidence"/>
        <result column="profileStatus" property="profileStatus"/>
        <result column="police_kind" property="policeKind"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="technology_control" property="technologyControl"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="tel" property="tel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="approval_detail" property="approvalDetailStr"/>
        <result column="is_followed" property="isFollowed"/>
        <result column="isFollowedName" property="isFollowedName"/>
        <result column="follow_time" property="followTime"/>
        <collection property="relatedJqs" javaType="java.util.ArrayList"
                    ofType="com.trs.police.common.core.vo.profile.RelatedJqVO"
                    select="getRelatedJq"
                    column="{tel=tel, idNumber=idNumber, policeKind=police_kind}"/>
    </resultMap>

    <resultMap id="personCardMap" type="com.trs.police.common.core.vo.profile.PersonCardVO">
        <id column="id" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="dept" property="dept"/>
        <result column="monitor_count" property="monitorCount"/>
        <result column="regular_count" property="regularCount"/>

        <result column="tel" property="tel"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="imgs" property="imgs"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="police_kind" property="policeKind"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <collection property="personLabel" javaType="java.util.ArrayList"
            ofType="java.lang.String"
            select="com.trs.police.common.core.mapper.CommonMapper.getPersonLabelName"
            column="{labelIds=person_label}"/>
        <collection property="virtualIdentity" column="{virtualIdentityIds=virtual_identity_ids}"
            javaType="java.util.ArrayList"
            ofType="com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO"
            select="com.trs.police.common.core.mapper.CommonMapper.getVirtualIdentity"/>
        <collection property="carNumber" column="{vehicleIds=car_number}" javaType="java.util.ArrayList"
            ofType="java.lang.String"
            select="com.trs.police.common.core.mapper.CommonMapper.getPersonVehicle"/>
    </resultMap>

    <resultMap id="personCardMapV1" type="com.trs.police.common.core.vo.profile.PersonCardVO">
        <id column="id" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="dept" property="dept"/>
        <result column="monitor_count" property="monitorCount"/>
        <result column="regular_count" property="regularCount"/>

        <result column="tel" property="tel"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="imgs" property="imgs"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="person_label" property="personLabelStr"/>
        <result column="virtual_identity_ids" property="virtualIdentityStr"/>
        <result column="car_number" property="carNumberStr"/>
    </resultMap>

    <resultMap id="regularPersonCardMap" type="com.trs.police.common.core.vo.profile.PersonCardVO">
        <id column="id" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="dept" property="dept"/>
        <result column="monitor_count" property="monitorCount"/>
        <result column="regular_count" property="regularCount"/>

        <result column="tel" property="tel"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>

        <result column="imgs" property="imgs"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

        <collection property="personLabel" javaType="java.util.ArrayList"
            ofType="java.lang.String"
            select="com.trs.police.common.core.mapper.CommonMapper.getPersonLabelName"
            column="{labelIds=person_label}"/>
    </resultMap>

    <resultMap id="groupPersonCardMap" type="com.trs.police.common.core.vo.profile.PersonCardVO"
        extends="personCardMap">
        <association column="{groupId=group_id,personId=id}" property="activeLevel" select="getActivityLevel"/>
    </resultMap>

    <resultMap id="trackPointMap" type="com.trs.police.profile.domain.vo.TrackPointWithWarningIdVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="lng" property="lng"/>
        <result column="lat" property="lat"/>
        <result column="type" property="type"/>
        <result column="time" property="time"/>
        <result column="location" property="location"/>
        <result column="sourceName" property="sourceName"/>
        <result column="content" property="content"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="warningId" property="warningId"/>
    </resultMap>

    <resultMap id="contentMap" type="com.trs.police.profile.domain.vo.ContentVO">
        <id column="warningId" property="warningId"/>
        <result column="content" property="content"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="WarningCardMap" type="com.trs.police.profile.domain.vo.WarningCardVO">
        <id column="id" property="id"/>
        <result column="warning_level" property="warningLevel"/>
        <result column="content" property="content"/>
        <result column="status" property="status"/>
        <result column="model" property="model"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="notify_person" property="notifyPerson"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="profilePersonVoMap" type="com.trs.police.profile.domain.vo.PersonGroupVO">
        <id column="id" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="id_type" property="idType"/>
        <result column="risk_score" property="riskScore"/>
        <result column="risk_level" property="riskLevel"/>
        <result column="person_label" property="personLabelIds"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="photo" property="photo"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="create_dept_id" property="createDeptId"/>
    </resultMap>
    <insert id="insertSwPerson">
        INSERT INTO tb_fx_person (
        real_name,
        person_number,
        id_card,
        nation,
        phone,
        area_code,
        area_name,
        subject_type,
        person_type,
        create_time,
        update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.realName},
            #{item.personNumber},
            #{item.idCard},
            #{item.nation},
            #{item.phone},
            #{item.areaCode},
            #{item.areaName},
            #{item.subjectType},
            #{item.personType},
            NOW(),
            NOW()
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        real_name = VALUES(real_name),
        nation = VALUES(nation),
        phone = VALUES(phone),
        area_code = VALUES(area_code),
        area_name = VALUES(area_name),
        subject_type = VALUES(subject_type),
        person_type = VALUES(person_type),
        update_time = NOW()
    </insert>



    <select id="selectGroupPageList" resultMap="groupPersonCardMap">
        select
        a.id as id,
        a.name as name,
        a.id_number as id_number,
        ifnull(a.tel,'[]') as tel,
        ifnull(a.person_label, '[]') as person_label,
        ifnull(a.id_type,1) as id_type,
        ifnull(a.vehicle_ids,'[]') as car_number,
        ifnull(a.virtual_identity_ids, '[]') as virtual_identity_ids,
        JSON_ARRAY_INSERT(ifnull(a.photo,'[]'),'$[0]' ,JSON_OBJECT('id',-1,'url',
        CONCAT('/oss/photo/',a.id_number))) as imgs,
        #{groupId} as group_id,
        0 as monitor_count,
        0 as regular_count,
        '' as dept,
        ifnull(a.police_kind, '[]') as police_kind
        from t_profile_person a
        <include refid="groupListFilter"/>
        order by a.create_time desc
    </select>

    <update id="setPersonMonitorStatus">
        update t_profile_person p
        set p.monitor_status = #{monitorStatus}
        where id in
        <foreach collection="personIds" open="(" close=")" separator="," item="personId">
            #{personId}
        </foreach>
    </update>

    <update id="setPersonControlStatus">
        update t_profile_person p
        set p.control_status = #{controlStatus}
        where id in
        <foreach collection="personIds" open="(" close=")" separator="," item="personId">
            #{personId}
        </foreach>
    </update>

    <select id="getPersonListFuzzy" resultMap="personVoMap">
        select p.id as id,
        p.name as name,
        p.id_number as id_number,
        ifnull(p.tel, '[]') as tel,
        ifnull(p.person_label, '[]') as person_label,
        ifnull(p.id_type, 1) as id_type,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number), '[]')
        from t_profile_vehicle v
        where v.id member of (ifnull(p.vehicle_ids, '[]'))) as car_number,
        ifnull(p.virtual_identity_ids, '[]') as virtual_identity_ids,
        JSON_ARRAY_INSERT(ifnull(p.photo, '[]'), '$[0]', JSON_OBJECT('id', -1, 'url',
        CONCAT('/oss/photo/', p.id_number))) as imgs
        from t_profile_person p
        where p.ID_NUMBER like concat('%', #{certificateNumber}, '%')
        and p.ID_TYPE = #{certificateType}
        and p.deleted = 0
    </select>

    <select id="getPersonByIdNumber" resultMap="personVoMap">
        select p.id as id,
        p.name as name,
        p.id_number as id_number,
        ifnull(p.id_type, 1) as id_type,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number), '[]')
        from t_profile_vehicle v
        where v.id member of ( ifnull(p.vehicle_ids, '[]'))) as car_number,
        ifnull(p.virtual_identity_ids, '[]') as virtual_identity_ids,
        JSON_ARRAY_INSERT(ifnull(p.photo, '[]'), '$[0]', JSON_OBJECT('id', -1, 'url',
        CONCAT('/oss/photo/', p.id_number))) as imgs
        from t_profile_person p
        where p.ID_NUMBER = #{idNumber} and p.deleted=0 ORDER BY p.update_time desc limit 1
    </select>

    <select id="getPersonById" resultMap="personVoMap">
        select p.id as id,
        p.name as name,
        p.id_number as id_number,
        ifnull(p.tel, '[]') as tel,
        ifnull(p.id_type, 1) as id_type,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number), '[]')
        from t_profile_vehicle v
        where v.id member of ( ifnull(p.vehicle_ids, '[]'))) as car_number,
        ifnull(p.virtual_identity_ids, '[]') as virtual_identity_ids,
        JSON_ARRAY_INSERT(ifnull(p.photo, '[]'), '$[0]', JSON_OBJECT('id', -1, 'url',
        CONCAT('/oss/photo/', p.id_number))) as imgs
        from t_profile_person p
        where p.id = #{id}
    </select>

    <select id="getPersonMobilePhoneById" resultType="java.lang.String">
        select ifnull(tel, '[]')
        from t_profile_person
        where id = #{id}
    </select>

    <sql id="groupListFilter">
        <!--@sql select * from t_profile_person a -->
        <where>
            <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                <if test="searchParams.searchField == 'fullText'">
                    and (
                    a.name like #{pattern,jdbcType=VARCHAR}
                    or a.id_number like #{pattern,jdbcType=VARCHAR}
                    or exists (select * from t_profile_vehicle m where m.car_number like #{pattern,jdbcType=VARCHAR} and
                    m.id MEMBER OF(a.vehicle_ids ) )
                    or a.tel ->> '$[*]' like #{pattern,jdbcType=VARCHAR}
                    or exists (select * from t_profile_virtual_identity m where m.type = '1' and m.virtual_number like
                    #{pattern,jdbcType=VARCHAR} and m.id MEMBER OF(a.virtual_identity_ids) )
                    or exists (select * from t_profile_virtual_identity m where m.type = '3' and m.virtual_number like
                    #{pattern,jdbcType=VARCHAR} and m.id MEMBER OF(a.virtual_identity_ids) )
                    or exists (select * from t_profile_virtual_identity m where m.type = '2' and m.virtual_number like
                    #{pattern,jdbcType=VARCHAR} and m.id MEMBER OF(a.virtual_identity_ids) )
                    )
                </if>
                <if test="searchParams.searchField == 'name'">
                    and a.name like #{pattern,jdbcType=VARCHAR}
                </if>
                <if test="searchParams.searchField == 'certificateNumber'">
                    and a.id_number like #{pattern,jdbcType=VARCHAR}
                </if>
                <if test="searchParams.searchField == 'carNumber' ">
                    and exists (select * from t_profile_vehicle m where m.car_number like #{pattern,jdbcType=VARCHAR}
                    and m.id MEMBER OF(a.vehicle_ids ) )
                </if>
                <if test="searchParams.searchField == 'phone' ">
                    and a.tel ->> '$[*]' like #{pattern,jdbcType=VARCHAR}
                </if>
                <if test="searchParams.searchField == 'mac' ">
                    and exists (select * from t_profile_virtual_identity m where m.type = '1' and m.virtual_number like
                    #{pattern,jdbcType=VARCHAR} and m.id MEMBER OF(a.virtual_identity_ids) )
                </if>
                <if test="searchParams.searchField == 'imei' ">
                    and exists (select * from t_profile_virtual_identity m where m.type = '3' and m.virtual_number like
                    #{pattern,jdbcType=VARCHAR} and m.id MEMBER OF(a.virtual_identity_ids) )
                </if>
                <if test="searchParams.searchField == 'imsi' ">
                    and exists (select * from t_profile_virtual_identity m where m.type = '2' and m.virtual_number like
                    #{pattern,jdbcType=VARCHAR} and m.id MEMBER OF(a.virtual_identity_ids) )
                </if>
            </if>
            <foreach collection="filterParams" item="param">
                <choose>
                    <when test="param.key == 'deptCode'">
                        AND a.id in (select person_id from t_profile_person_police_control c where c.control_station
                        like
                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}%')
                    </when>
                    <when test="param.key == 'type'">
                        AND #{param.value} MEMBER OF (a.person_label)
                    </when>
                    <when test="param.key == 'groupId'">
                        AND a.id in (select person_id from t_profile_person_group_relation where
                        #{param.value,jdbcType=VARCHAR} = group_id)
                    </when>
                    <when test="param.key == 'isInControl'">
                        <if test="param.value == true">
                            AND a.monitor_status = 2
                        </if>
                        <if test="param.value == false">
                            AND (a.monitor_status != 2 or a.monitor_status is null)
                        </if>
                    </when>
                    <when test="param.key == 'activityLevel'">
                        AND exists (select * from t_profile_person_group_relation r where r.person_id = a.id and
                        r.group_id = #{groupId} and r.activity_level = #{param.value})
                    </when>
                    <when test="param.key == 'missingInfoType'">
                        <foreach collection="param.value" item="missingInfoType">
                            <choose>
                                <when test="missingInfoType == 1">
                                    <!--@ignoreSql-->
                                    and a.id = '-1'
                                </when>
                                <when test="missingInfoType == 2">
                                    and (a.vehicle_ids -> '$[0]' is null)
                                </when>
                                <when test="missingInfoType == 3">
                                    <!--@ignoreSql-->
                                    and ifnull(JSON_LENGTH(a.tel),0) = 0
                                </when>
                                <when test="missingInfoType == 4 ">
                                    <!--@ignoreSql-->
                                    and not exists (select * from t_profile_virtual_identity v where v.id MEMBER OF
                                    (a.virtual_identity_ids) and v.type=1)
                                </when>
                                <when test="missingInfoType == 5">
                                    <!--@ignoreSql-->
                                    and not exists (select * from t_profile_virtual_identity v where v.id MEMBER OF
                                    (a.virtual_identity_ids) and v.type=5)
                                </when>
                                <when test="missingInfoType == 6">
                                    <!--@ignoreSql-->
                                    and not exists (select * from t_profile_virtual_identity v where v.id MEMBER OF
                                    (a.virtual_identity_ids) and v.type=3)
                                </when>
                            </choose>
                        </foreach>
                    </when>
                </choose>
            </foreach>
        </where>

    </sql>

    <select id="selectPageList" resultMap="personVoMap">
        select
        a.id as id,
        a.name as name,
        a.id_number as id_number,
        ifnull(a.tel, '[]') as tel,
        ifnull(a.id_type,1) as id_type,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number),'[]') from t_profile_vehicle v where v.id member of
        (ifnull(a.vehicle_ids, '[]'))) as car_number,
        ifnull(a.virtual_identity_ids, '[]') as virtual_identity_ids,
        JSON_ARRAY_INSERT(ifnull(a.photo,'[]'),'$[0]' ,JSON_OBJECT('id',-1,'url',
        CONCAT('/oss/photo/',a.id_number))) as imgs
        from t_profile_person a
        <include refid="groupListFilter"/>
        order by a.create_time desc
    </select>

    <select id="selectPersonIds" resultType="java.lang.Long">
        SELECT
        a.id AS id
        FROM
        t_profile_person a
        <include refid="groupListFilter"/>
    </select>

    <select id="getPersonByGroup" resultMap="personVoMap">
        select p.id as id,
        p.name as name,
        p.id_number as id_number,
        ifnull(p.tel, '[]') as tel,
        ifnull(p.id_type, 1) as id_type,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number), '[]')
        from t_profile_vehicle v
        where v.id member of ( ifnull(p.vehicle_ids, '[]'))) as car_number,
        ifnull(p.virtual_identity_ids, '[]') as virtual_identity_ids,
        JSON_ARRAY_INSERT(ifnull(p.photo, '[]'), '$[0]', JSON_OBJECT('id', -1, 'url',
        CONCAT('/oss/photo/', p.id_number))) as imgs
        from t_profile_person p
        left join t_profile_person_group_relation pg on pg.person_id = p.id
        where pg.group_id = #{groupId}
        and p.monitor_status in ('2', '3')
    </select>

    <select id="getByIds" resultMap="personVoMap">
        select p.id as id,
        p.name as name,
        p.id_number as id_number,
        ifnull(p.tel, '[]') as tel,
        ifnull(p.id_type,1) as id_type,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number), '[]')
        from t_profile_vehicle v
        where v.id member of ( ifnull(p.vehicle_ids, '[]'))) as car_number,
        ifnull(p.virtual_identity_ids, '[]') as virtual_identity_ids,
        JSON_ARRAY_INSERT(ifnull(p.photo,'[]'),'$[0]' ,JSON_OBJECT('id',-1,'url',
        CONCAT('/oss/photo/',p.id_number))) as imgs,
        ifnull(p.person_label,'[]') as person_label
        from t_profile_person p
        where p.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getPerson" resultMap="personVoMapV2">
        select p.id as id,
        p.name as name,
        p.id_number as id_number,
        ifnull(p.tel, '[]') as tel,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number), '[]')
        from t_profile_vehicle v
        where v.id member of ( ifnull(p.vehicle_ids, '[]'))) as car_number,
        ifnull(p.virtual_identity_ids, '[]') as virtual_identity_ids,
        ifnull(p.id_type, 1) as id_type,
        JSON_ARRAY_INSERT(ifnull(p.photo, '[]'), '$[0]', JSON_OBJECT('id', -1, 'url',
        CONCAT('/oss/photo/', p.id_number))) as imgs,
        ifnull(p.person_label, '[]') as person_label,
        1 as can_edit,
        approval_detail as approvalDetail
        from t_profile_person p
        where p.ID = #{personId}
    </select>

    <select id="selectWarningListByIdCard" resultType="com.trs.police.profile.domain.vo.PersonArchiveWarningDto">
        <bind name="currentUser" value="@com.trs.police.common.core.utils.AuthHelper@getNotNullUser()"/>
        select w.*,
        w.activity_address as address,
        exists(select * from t_warning_notify
        where user_id=#{currentUser.id} and dept_id =#{currentUser.dept.id}) as isExistRelation
        from t_warning w
        where w.id in (select distinct warning_id from t_warning_track where person_id = #{personId})
        order by w.warning_time desc
    </select>

    <select id="getModelTypesByIds" resultType="java.lang.Long">
        select type
        from t_control_monitor_warning_model
        where id in
        <foreach close=")" collection="modelIds" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getIdsByType" resultType="java.lang.Long">
        select id from t_control_monitor_warning_model
        where type in
        <foreach close=")" collection="types" item="type" open="(" separator=",">
            #{type}
        </foreach>
    </select>

    <sql id="areaListFilter">
        <where>
            <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                <if test="searchParams.searchField == 'fullText'">
                    and (a.name like #{pattern,jdbcType=VARCHAR} or a.id_number like #{pattern,jdbcType=VARCHAR})
                </if>
                <if test="searchParams.searchField == 'name'">
                    and a.name like #{pattern,jdbcType=VARCHAR}
                </if>
                <if test="searchParams.searchField == 'idNumber'">
                    and a.id_number like #{pattern,jdbcType=VARCHAR}
                </if>
            </if>
            <foreach collection="filterParams" item="param">
                <choose>
                    <when test="param.key == 'permissionDeptCode'">
                        AND a.id in (select r.person_id from t_profile_person_police_control r where r.control_station
                        in
                        <foreach collection="param.value" item="dept" open="(" close=")" separator=",">
                            #{dept}
                        </foreach>
                        )
                    </when>
                    <when test="param.key == 'currentUser'">
                        AND a.id in (select r.person_id from t_profile_person_police_control r where r.control_person =
                        #{param.value})
                    </when>
                    <when test="param.key == 'district'">
                        <bind name="districtList"
                            value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(param.getProcessedValue())"/>
                        AND
                        <foreach collection="param.getProcessedValue()" item="item" separator="or" open="("
                            close=")">
                            <bind value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)"
                                name="district"/>
                            a.registered_residence like CONCAT(#{district}, '%')
                        </foreach>
                    </when>
                    <when test="param.key == 'dept'">
                        <bind name="deptList"
                            value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(param.getProcessedValue())"/>
                        AND
                        JSON_OVERLAPS(#{deptList}
                        ,(SELECT JSON_ARRAYAGG(d.id)
                        FROM t_dept d
                        WHERE ifnull((select concat(path, d.id, '-') from t_profile_person_police_control t join t_dept
                        d on t.control_station=d.code where t.person_id = a.id limi), '')
                        LIKE CONCAT('%-', d.id, '-%'))
                        ) > 0
                    </when>
                    <when test="param.key == 'personLabel'">
                        <bind name="labels" value="param.getProcessedValue()"/>
                        AND
                        <foreach collection="labels" item="item" open="(" separator="OR" close=")">
                            (JSON_OVERLAPS(a.person_label
                            ,(SELECT JSON_ARRAYAGG( l.id )
                            FROM t_profile_label l
                            WHERE CONCAT(l.path, l.id, '-')
                            LIKE CONCAT('%-', ${item}, '-%' ))
                            )
                            >0)
                        </foreach>
                    </when>
                </choose>
            </foreach>
        </where>
    </sql>

    <select id="selectIdNumber" resultType="java.lang.String">
        select a.id_number as id_number
        from t_profile_person a
        <include refid="areaListFilter"/>
    </select>

    <select id="selectPersonList" resultMap="personVoMap">
        select a.id as id,
        a.name as name,
        a.id_number as id_number,
        ifnull(a.tel, '[]') as tel,
        ifnull(a.id_type,'1') as id_type,
        ifnull(a.person_label,'[]') as person_label,
        JSON_ARRAY_INSERT(ifnull(a.photo,'[]'),'$[0]' ,JSON_OBJECT('id',-1,'url',
        CONCAT('/oss/photo/',a.id_number))) as imgs,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number),'[]') from t_profile_vehicle v where v.id member of
        (ifnull(a.vehicle_ids, '[]'))) as car_number,
        ifnull(a.virtual_identity_ids,'[]') as virtual_identity_ids
        from t_profile_person a
        <include refid="areaListFilter"/>
    </select>

    <select id="personListJoinSearchCount" resultType="java.lang.Long">
        select count(distinct(a.id))
        from
        t_profile_person a
        <if test="null != joinVO.controlRegularMonitor and true == joinVO.controlRegularMonitor">
            left join
            t_control_regular_monitor m
            on
            a.id = m.target_id
        </if>
        <if test="null != joinVO.controlMonitor and true == joinVO.controlMonitor">
            left join t_control_monitor_target_relation r on a.id = r.target_id
            left join t_control_monitor tcm on tcm.id = r.monitor_id
        </if>
        left join
        t_profile_person_police_control pppc
        on
        a.id = pppc.person_id
        <if test="null != joinVO.policeKind and true == joinVO.policeKind">
            left join t_dept d on d.code = pppc.control_police
        </if>
        <include refid="personListJoinSearchWhere"></include>
    </select>

    <sql id="personListJoinSearchWhere">
        <where>
            a.deleted = 0
            <!-- 从filterParams生成筛选条件 -->
            <foreach collection="filterParams" item="param">
                <if test="null != param.getProcessedValue()">
                    <include refid="personListSingleSearch"></include>
                </if>
                <if test="'regularLabel' == param.key and param.getProcessedValue().size > 0">
                    AND JSON_OVERLAPS(m.label_ids ,
                    <foreach collection="param.getProcessedValue()" item="item" separator=", " open="JSON_ARRAY(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="'controlPolice' == param.key and param.getProcessedValue() != null and param.getProcessedValue().size() > 0">
                    AND pppc.control_police in
                    <foreach collection="param.getProcessedValue()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="'policeKind' == param.key and param.getProcessedValue() != null and param.getProcessedValue().size() > 0">
                    AND d.police_kind in
                    <foreach collection="param.getProcessedValue()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="'controlPerson' == param.key">
                    AND JSON_CONTAINS(pppc.control_person, JSON_Array(${param.value}))
                </if>
                <if test="'monitorPerson' == param.key">
                    AND tcm.monitor_type = 1
                </if>
                <if test="'ids' == param.key and param.getProcessedValue() != null and param.getProcessedValue().size() > 0">
                    AND a.id in
                    <foreach collection="param.getProcessedValue()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="'resolveDifficulty' == param.key">
                    AND a.resolve_difficulty = #{param.value}
                </if>
            </foreach>
            <include refid="personListKeyWordSearch"></include>
            <include refid="personPermissionV2"></include>
        </where>
    </sql>

    <select id="personListJoinSearch" resultMap="personListVo">
        select
            distinct
            <include refid="personListSelect"></include>
            ,a.current_residence as currentResidence
        <if test="null != joinVO.controlRegularMonitor and true == joinVO.controlRegularMonitor">
            ,m.label_ids as regularLabel
        </if>
        <include refid="personListJoinSearchFrom"></include>
    </select>

    <select id="personIdsJoinSearch" resultType="java.lang.Long">
        select distinct a.id
        <include refid="personListJoinSearchFrom"></include>
    </select>

    <sql id="personListJoinSearchFrom">
        from
        t_profile_person a
        <if test="null != joinVO.controlRegularMonitor and true == joinVO.controlRegularMonitor">
            left join
            t_control_regular_monitor m
            on
            a.id = m.target_id
        </if>
        <if test="null != joinVO.controlMonitor and true == joinVO.controlMonitor">
            left join t_control_monitor_target_relation r on a.id = r.target_id
            left join t_control_monitor tcm on tcm.id = r.monitor_id
        </if>
        left join
        t_profile_person_police_control pppc
        on
        a.id = pppc.person_id
        <if test="null != joinVO.policeKind and true == joinVO.policeKind">
            left join t_dept d on d.code = pppc.control_police
        </if>
        <include refid="personListJoinSearchWhere"></include>
        <include refid="personListSort"></include>
    </sql>


    <select id="personListJoinSearchV2" resultMap="personListVo">
        select
        distinct
        <include refid="personListSelect"></include>
        <include refid="personListSelectPoliceKind"></include>
        ,a.current_residence as currentResidence
        <if test="null != joinVO.controlRegularMonitor and true == joinVO.controlRegularMonitor">
            ,m.label_ids as regularLabel
        </if>
        <include refid="personListJoinSearchV2From"></include>
    </select>

    <select id="personIdsJoinSearchV2" resultType="java.lang.Long">
        select distinct a.id
        <include refid="personListJoinSearchV2From"></include>
    </select>

    <sql id="personListJoinSearchV2From" >
        from
        t_profile_person a
        <if test="null != joinVO.controlRegularMonitor and true == joinVO.controlRegularMonitor">
            left join
            t_control_regular_monitor m
            on
            a.id = m.target_id
        </if>
        <if test="null != joinVO.controlMonitor and true == joinVO.controlMonitor">
            left join t_control_monitor_target_relation r on a.id = r.target_id
            left join t_control_monitor tcm on tcm.id = r.monitor_id
        </if>
        left join
        t_profile_person_police_control pppc
        on
        a.id = pppc.person_id
        left join
        t_profile_person_follow pf
        on
        a.id = pf.person_id and pf.create_dept_id = #{currentDeptId}
        <include refid="relatedPoliceKind"></include>
        <where>
            a.deleted = 0
            <!-- 从filterParams生成筛选条件 -->
            <foreach collection="filterParams" item="param">
                <if test="null != param.getProcessedValue()">
                    <include refid="personListSingleSearch"></include>
                    <include refid="policeKindFilter"></include>
                </if>
                <if test="'regularLabel' == param.key and param.getProcessedValue().size > 0">
                    AND JSON_OVERLAPS(m.label_ids ,
                    <foreach collection="param.getProcessedValue()" item="item" separator=", " open="JSON_ARRAY(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="'controlPolice' == param.key and param.getProcessedValue() != null and param.getProcessedValue().size() > 0">
                    AND pppc.control_police in
                    <foreach collection="param.getProcessedValue()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="'controlPerson' == param.key">
                    AND JSON_CONTAINS(pppc.control_person, JSON_Array(${param.value}))
                </if>
                <if test="'monitorPerson' == param.key">
                    AND tcm.monitor_type = 1
                </if>
                <if test="'ids' == param.key and param.getProcessedValue() != null and param.getProcessedValue().size() > 0">
                    AND a.id in
                    <foreach collection="param.getProcessedValue()" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
            </foreach>
            <include refid="personListKeyWordSearch"></include>
            <include refid="personPermissionV2"></include>
        </where>
        <include refid="personListSort"></include>
    </sql>

    <sql id="personListSelect">
        a.id as id,
        JSON_ARRAY_APPEND(
            IFNULL(a.photo, '[]'),
            '$',
            JSON_OBJECT('id', -1, 'url', CONCAT('/oss/photo/', a.id_number))
        ) AS photo ,
        a.name as name ,
        a.id_number as idNumber ,
        a.gender as gender,
        a.person_label as personLabel ,
        a.risk_score as riskScore ,
        <if test="policeKind==null">
            a.risk_level as riskLevel ,
            a.control_level as controlLevel ,
        </if>
        (
        SELECT
        REPLACE(GROUP_CONCAT( control_person SEPARATOR ','), '],[', ',') AS result
        FROM
        t_profile_person_police_control b
        WHERE
        person_id = a.id
        <if test="policeKind!=null">
            AND b.police_kind = #{policeKind}
        </if>
        ) as dutyPolice ,
        (
        SELECT
        REPLACE(GROUP_CONCAT(control_station SEPARATOR ','), '],[', ',') AS result
        FROM t_profile_person_police_control b
        WHERE person_id = a.id
        <if test="policeKind!=null">
            AND b.police_kind = #{policeKind}
        </if>
        ) as dutyPoliceStation,
        (
        SELECT
        REPLACE(GROUP_CONCAT(control_bureau SEPARATOR ','), '],[', ',') AS result
        FROM
        t_profile_person_police_control b
        WHERE
        person_id = a.id
        <if test="policeKind!=null">
            AND b.police_kind = #{policeKind}
        </if>
        ) as controlBureau,
        a.registered_residence as registeredResidence ,
        a.create_time as createTime ,
        a.update_time as updateTime,
        a.approval_statue_code as approvalStatueCode,
        a.profile_status as profileStatus,
        a.resolve_difficulty as resolveDifficulty,
        a.tel as tel,
        a.chinese_name as chineseName,
        a.visa_end_date as visaEndDate,
        a.inflow_time as inflowTime,
        a.is_slrylb as isSlrylb,
        a.occupation_category as occupationCategory,
        a.work_category as workCategory,
        a.work_address as workAddress,
        a.basic_investigation_address as basicInvestigationAddress,
        a.residence_reason as residenceReason,
        a.specific_analysis as specificAnalysis,
        a.other_illegal_activities as otherIllegalActivities,
        a.police_kind
    </sql>

    <sql id="relatedPoliceKind">
            <choose>
                <when test="policeKind == 3">
                    left join t_profile_person_risk_jz pk on a.id = pk.person_id
                </when>
                <when test="policeKind == 10">
                    left join t_profile_person_risk_sd pk on a.id = pk.person_id
                </when>
                <when test="policeKind == 2">
                    left join t_profile_person_risk_other pk on a.id = pk.person_id and pk.police_kind = 2
                </when>
                <when test="policeKind == 4">
                    left join t_profile_person_risk_za pk on a.id = pk.person_id
                </when>
                <when test="policeKind == 99">
                    left join t_profile_person_risk_other pk on a.id = pk.person_id and pk.police_kind = 99
                </when>
            </choose>
    </sql>

    <sql id="policeKindFilter">
            <choose>
                <when test="policeKind == -1">
                    and JSON_LENGTH(a.police_kind) > 0
                    and (
                        a.approval_detail is null
                    or (
                        JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$.*.status'), '3')
                        or JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$.*.approvalUserId'), '${currentUserId}')
                        or a.create_dept_id = #{currentDeptId}
                        )
                    )
                </when>
                <otherwise>
                    and JSON_CONTAINS(a.police_kind, '${policeKind}')
                    and (
                        a.approval_detail is null
                    or (
                        JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$."${policeKind}".status'), '3')
                        or JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$."${policeKind}".approvalUserId'), '${currentUserId}')
                        or a.create_dept_id = #{currentDeptId}
                        )
                    )
                </otherwise>
            </choose>
            <if test="'durgsKind'==param.key">
                AND pk.durgs_kind = '${param.value}'
            </if>
            <if test="'riskLevel'==param.key">
                AND pk.risk_level = '${param.value}'
            </if>
            <if test="'controlLevel'==param.key">
                AND pk.control_Level = '${param.value}'
            </if>
            <if test="'personLevel'==param.key">
                AND pk.person_level = '${param.value}'
            </if>
            <if test="'dutyType'==param.key">
                AND pk.work_duty_type = '${param.value}'
            </if>
            <if test="'sksflry'==param.key">
                AND pk.sksflry = '${param.value}'
            </if>
            <if test="'retireTime'==param.key and !param.value.isAll">
                AND (pk.retire_time >= '${param.value.beginTime}'
                AND  pk.retire_time &lt;= '${param.value.endTime}')
            </if>
            <if test="'firstSeizureTime'==param.key">
                AND (pk.first_seizure_time >= '${param.value.beginTime}'
                AND  pk.first_seizure_time &lt;= '${param.value.endTime}')
            </if>
            <if test="'lastSeizureTime'==param.key">
                AND (pk.last_seizure_time >= '${param.value.beginTime}'
                AND  pk.last_seizure_time &lt;= '${param.value.endTime}')
            </if>
            <if test="'technologyControl'==param.key">
                and exists (
                SELECT 1 FROM t_profile_person_police_control
                WHERE person_id = a.id and police_kind = 4
                and JSON_CONTAINS(technology_control, '${param.value}')
                )
            </if>
            <if test="'followTime'==param.key">
                AND (pf.update_time >= '${param.value.beginTime}'
                AND  pf.update_time &lt;= '${param.value.endTime}'
                AND  pf.is_followed = 1)
            </if>
            <if test="'havingJq'==param.key and policeKind == 12">
                <if test="1==param.value">
                    and exists (select 1 from t_profile_sthy tps where
                    a.id_number = tps.BJRZJHM or json_contains(a.tel, concat('"', tps.BJDH, '"')))
                </if>
                <if test="0==param.value">
                    and not exists (select 1 from t_profile_sthy tps where
                    a.id_number = tps.BJRZJHM or json_contains(a.tel, concat('"', tps.BJDH, '"')))
                </if>
            </if>
    </sql>

    <sql id="personListSelectPoliceKind">
            <choose>
                <when test="policeKind == 3">
                    ,pk.retire_time,
                    pk.risk_level riskLevelNum,
                    pk.work_unit,
                    pk.work_duty,
                    pk.current_location
                </when>
                <when test="policeKind == 10">
                    ,pk.secondary_control_district,
                    pk.first_seizure_time,
                    pk.last_seizure_time,
                    pk.durgs_kind,
                    pk.person_level personLevel
                </when>
                <when test="policeKind == 2">
                    ,pk.person_level personLevel
                </when>
                <when test="policeKind == 4">
                    ,pk.sksflry,
                    pk.work_unit,
                    pk.work_duty,
                    pk.work_duty_type,
                    pk.event_place,
                    pk.control_level controlLevel,
                    (
                    SELECT
                    REPLACE(GROUP_CONCAT( technology_control SEPARATOR ','), '],[', ',') AS result
                    FROM
                    t_profile_person_police_control b
                    WHERE
                    person_id = a.id and police_kind = 4
                    ) as technology_control
                </when>
                <when test="policeKind == 99">
                    ,pk.person_level personLevel
                </when>
                <when test="policeKind == 12">
                    ,a.control_level controlLevel
                </when>
            </choose>
            ,approval_detail
            ,IFNULL(pf.is_followed, 0) as is_followed
            ,CASE WHEN IFNULL(pf.is_followed, 0) = 1 THEN '已关注' ELSE '未关注' END as isFollowedName
            ,pf.update_time as follow_time
    </sql>

    <sql id="personPermission">
        <!--档案数据权限-->
        <if test="permissionInfo != null and permissionInfo.profilePermission != null and permissionInfo.profilePermission.personLabelIds != null">
            and json_overlaps(
                a.person_label,
                #{permissionInfo.profilePermission.personLabelIds,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler}
            )
        </if>
        <if test="permissionInfo != null">
            <if test="permissionInfo.permission == 'SELF'">
                AND a.create_user_id = #{permissionInfo.userId}
            </if>
            <if test="permissionInfo.permission == 'DEPT'">
                AND a.create_dept_id = #{permissionInfo.deptId}
            </if>
            <if test="permissionInfo.permission == 'DEPT_AND_CHILD'">
                AND a.create_dept_id in <foreach collection="permissionInfo.deptIds" open="(" close=")" separator="," item="it">#{it}</foreach>
            </if>
            <if test="permissionInfo.permission == 'DISTRICT'">

            </if>
            <if test="permissionInfo.permission == 'DISTRICT_AND_CHILD'">

            </if>
        </if>
    </sql>

    <sql id="personPermissionV2">
        <!--档案数据权限-->
        <if test="permissionInfo != null and permissionInfo.profilePermission != null and permissionInfo.profilePermission.personLabelIds != null and permissionInfo.profilePermission.personLabelIds.size() > 0">
            and json_overlaps(
            a.person_label,
            #{permissionInfo.profilePermission.personLabelIds,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler}
            )
        </if>
        <if test="permissionInfo != null">
            <if test="permissionInfo.permission == 'SELF'">
                AND a.create_user_id = #{permissionInfo.userId}
            </if>
            <if test="permissionInfo.permission == 'DEPT'">
                AND (
                a.create_dept_id = #{permissionInfo.deptId} OR
                pppc.control_bureau = #{permissionInfo.deptCode} OR
                pppc.control_police = #{permissionInfo.deptCode} OR
                pppc.control_station = #{permissionInfo.deptCode}
                )
            </if>
            <if test="permissionInfo.permission == 'DEPT_AND_CHILD' or permissionInfo.permission == 'CURRENT_DEPT_POLICE_KIND_AND_CHILD' or permissionInfo.permission == 'POLICE_KIND_AND_CHILD' or
            (permissionInfo.permission == 'TARGET_POLICE_KIND' and permissionInfo.policeKinds!=null and permissionInfo.policeKinds.size()>0)">
                AND (
                a.create_dept_id IN <foreach collection="permissionInfo.deptIds" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_bureau IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_police IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_station IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach>
                )
            </if>
            <if test="permissionInfo.permission == 'DISTRICT'">
                AND (
                a.create_dept_id IN <foreach collection="permissionInfo.deptIds" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_bureau IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_police IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_station IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach>
                )
            </if>
            <if test="permissionInfo.permission == 'DISTRICT_AND_CHILD'">
                AND (
                a.create_dept_id IN <foreach collection="permissionInfo.deptIds" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_bureau IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_police IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach> OR
                pppc.control_station IN <foreach collection="permissionInfo.deptCodes" open="(" close=")" separator="," item="it">#{it}</foreach>
                )
            </if>
        </if>
    </sql>

    <sql id="personListSort">
        <!-- 排序参数 -->
        <choose>
            <when test="sortParams != null and sortParams.sortField != null">
                <if test="'updateTime'==sortParams.sortField">
                    ORDER BY a.update_time ${sortParams.getProcessedValue()}, a.id desc
                </if>
                <if test="'createTime'==sortParams.sortField">
                    ORDER BY a.create_time ${sortParams.getProcessedValue()}, a.id desc
                </if>
                <if test="'riskScore'==sortParams.sortField">
                    ORDER BY a.risk_score ${sortParams.getProcessedValue()}, a.update_time desc
                </if>
                <if test="'followTime'==sortParams.sortField">
                    ORDER BY pf.is_followed desc, pf.update_time ${sortParams.getProcessedValue()}
                </if>
            </when>
            <otherwise>
                order by a.update_time desc,a.id desc
            </otherwise>
        </choose>
    </sql>

    <sql id="personListKeyWordSearch">
        <!-- 关键词检索 -->
        <if test="null != searchParams.getSearchValue()">
            <bind name="pattern" value="'%' + searchParams.getSearchValue() + '%'"/>
            <if test="null != searchParams.searchField and '%%' != pattern">
                <if test="'name' == searchParams.searchField">
                    AND a.name like #{pattern,jdbcType=VARCHAR}
                </if>
                <if test="'idNumber' == searchParams.searchField">
                    AND a.id_number like #{pattern,jdbcType=VARCHAR}
                </if>
                <if test="'fullText' == searchParams.searchField">
                    AND (a.name like #{pattern,jdbcType=VARCHAR} OR a.id_number like #{pattern,jdbcType=VARCHAR})
                </if>
            </if>
        </if>
    </sql>

    <sql id="personListSingleSearch">
        <if test="'personLabel'==param.key">
            AND
            <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                     close=")">
                JSON_OVERLAPS( a.person_label,
                (
                SELECT JSON_ARRAYAGG( l.id )
                FROM t_profile_label l
                WHERE CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', ${item}, '-%' )
                ))> 0
            </foreach>
        </if>
        <if test="'control_level'==param.key and policeKind==null">
            AND a.control_level in <foreach collection="param.getProcessedValue()" item="it" open="(" close=")" separator=",">${it}</foreach>
        </if>
        <if test="'registeredResidence'==param.key">
            AND
            <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                     close=")">
                a.registered_residence like concat(
                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                , '%')
            </foreach>
        </if>
        <if test="'currentResidence'==param.key">
            AND
            <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                     close=")">
                a.current_residence like concat(
                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                , '%')
            </foreach>
        </if>
        <if test="'createTime'==param.key">
            AND (a.create_time >= '${param.value.beginTime}'
            AND  a.create_time &lt;= '${param.value.endTime}')
        </if>
        <if test="'updateTime'==param.key">
            AND (a.update_time >= '${param.value.beginTime}'
            AND  a.update_time &lt;= '${param.value.endTime}')
        </if>
        <if test="'approvalStatueCode'==param.key">
            AND a.approval_statue_code = '${param.value}'
        </if>
        <if test="'riskLevel'==param.key and policeKind==null">
            AND a.risk_level = '${param.value}'
        </if>
        <!-- 查询部门代码列表 -->
        <if test="'dutyPoliceStation'==param.key">
            AND
            <foreach collection="param.getProcessedValue()" item="item" open="(" separator=" OR "
                     close=")">
                pppc.control_station like concat(
                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                , '%')
            </foreach>
        </if>
        <if test="'controlBureau'==param.key and '' != joinVO.controlBureauPrefix">
            AND
            <foreach collection="joinVO.controlBureauPrefix.split(',')" item="item" open="(" separator=" OR " close=")">
                pppc.control_bureau like concat('',#{item},'%')
            </foreach>
        </if>
        <if test="'createdBySelf' == param.key and 1 == param.value">
            AND a.create_user_id = #{currentUserId}
        </if>
        <if test="'profileStatus'==param.key">
            AND a.profile_status = '${param.value}'
        </if>
        <if test="'currentPosition'==param.key">
            AND a.current_position = '${param.value}'
        </if>
        <if test="param.key == 'isFollowed'">
            <choose>
                <when test="param.value == 1">
                    AND pf.is_followed = 1
                </when>
                <when test="param.value == 0">
                    AND (pf.is_followed = 0 OR pf.is_followed IS NULL)
                </when>
            </choose>
        </if>
        <if test="'isSlrylb'==param.key">
            AND a.is_slrylb = '${param.value}'
        </if>
        <if test="'inflowTime'==param.key">
            AND (a.inflow_time >= '${param.value.beginTime}'
            AND  a.inflow_time &lt;= '${param.value.endTime}')
        </if>
    </sql>
    
    <select id="getRelatedJq" resultType="com.trs.police.common.core.vo.profile.RelatedJqVO">
        <choose>
            <when test="policeKind!=null and policeKind.contains('12')">
                select jjdbh, bjnr, JJDWDM, jjdwmc, BJSJ
                from t_profile_sthy
                where #{idNumber} = BJRZJHM or json_contains(#{tel}, concat('"', BJDH, '"'))
            </when>
            <otherwise>
                select jjdbh, bjnr, JJDWDM, jjdwmc, BJSJ
                from t_profile_sthy
                where 1=-1
            </otherwise>
        </choose>
    </select>

    <select id="getPersonByIdNumbers" resultMap="personVoMap">
        select a.id as id,
        a.name as name,
        a.id_number as id_number,
        ifnull(a.tel, '[]') as tel,
        ifnull(a.id_type,'1') as id_type,
        ifnull(a.person_label,'[]') as person_label,
        JSON_ARRAY_INSERT(ifnull(a.photo,'[]'),'$[0]' ,JSON_OBJECT('id',-1,'url',
        CONCAT('/oss/photo/',a.id_number))) as photo,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number),'[]') from t_profile_vehicle v where v.id member of
        (ifnull(a.vehicle_ids, '[]'))) as car_number,
        ifnull(a.virtual_identity_ids, '[]') as virtual_identity_ids
        from t_profile_person a
        <where>
            a.id_number in
            <foreach collection="idNumbers" item="idNumber" separator="," open="(" close=")">
                #{idNumber}
            </foreach>
        </where>
    </select>

    <select id="selectRegularPageList" resultType="java.lang.Long">
        <bind name="current" value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser()"/>
        select a.id
        from t_profile_person a
        <include refid="searchAndFilter"/>
        and a.profile_status != 1
        order by a.id desc
    </select>

    <sql id="searchAndFilter">
        <if test="null != myNotMonitor and 1 == myNotMonitor">
            <bind name="userId"
                  value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id"/>
            <bind name="deptId"
                  value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().getDeptId()"/>
            left join t_control_regular_monitor m on a.id = m.target_id and
            m.create_user_id=#{userId} and m.create_dept_id=#{deptId} and m.deleted=0
        </if>
        <!--@sql select * from t_profile_person a -->
        <where>
            and 1=1
            <if test="@java.util.Objects@nonNull(searchParams) and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                <if test="searchParams.searchField == 'fullText'">
                    and (a.name like #{pattern,jdbcType=VARCHAR} or a.id_number like #{pattern,jdbcType=VARCHAR})
                </if>
                <if test="searchParams.searchField == 'name'">
                    and a.name like #{pattern,jdbcType=VARCHAR}
                </if>
                <if test="searchParams.searchField == 'idNumber'">
                    and a.id_number like #{pattern,jdbcType=VARCHAR}
                </if>
            </if>
            <if test="filterParams != null">
                <foreach collection="filterParams" item="param">
                    <choose>
                        <when test="param.key == 'currentUserId'">
                            <bind name="userId" value="param.getProcessedValue()"/>
                        </when>
                        <when test="param.key == 'currentUserDept'">
                            <bind name="deptId" value="param.getProcessedValue()"/>
                        </when>
                        <when test="param.key == 'dutyDept'">
                            <bind name="dutyDept" value="param.getProcessedValue()"/>
                            <if test="dutyDept != null and dutyDept.size() > 0">
                                AND exists (select c.control_station from t_profile_person_police_control c where
                                c.person_id=a.id and c.control_station in
                                (SELECT d.code FROM t_dept d
                                WHERE
                                <foreach collection="dutyDept" item="item" separator="OR">
                                    CONCAT( d.path, d.id, '-') LIKE CONCAT('%-', #{item}, '-%' )
                                </foreach>))
                            </if>
                        </when>
                        <when test="param.key == 'permissionDeptCode'">
                            AND exists (select 1 from t_profile_person_police_control c where
                            a.id=c.person_id and
                            c.control_station in
                            <foreach collection="param.value" open="(" close=")" separator="," item="dept">
                                #{dept}
                            </foreach>
                            )
                        </when>
                        <when test="param.key == 'currentUser'">
                            and exists
                            (select c.person_id from t_profile_person_police_control c where
                            c.control_person = #{param.value} and c.person_id=a.id)
                        </when>
                        <when test="param.key == 'myNotMonitor'">
                             AND m.id is null
                        </when>
                        <when test="param.key == 'personLabel'">
                            <bind name="labels" value="param.getProcessedValue()"/>
                            <if test="labels != null and labels.size() > 0">
                                AND JSON_OVERLAPS(a.person_label,
                                (
                                SELECT JSON_ARRAYAGG( l.id )
                                FROM t_profile_label l
                                WHERE
                                <foreach collection="labels" item="item" separator="OR">
                                    CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', #{item}, '-%' )
                                </foreach>
                                ))>0
                            </if>
                        </when>
                        <when test="param.key == 'idNumbers'">
                            and a.id_number in
                            <foreach collection="param.value" item="idNumber" open="(" separator="," close=")">
                                #{idNumber}
                            </foreach>
                        </when>
                        <when test="param.key == 'personType'">
                            and a.person_type = #{param.value}
                        </when>
                        <when test="param.key == 'controlLevel'">
                            <bind name="controlLevels" value="param.getProcessedValue()"/>
                            <if test="controlLevels != null and controlLevels.size() > 0">
                                and a.control_level in
                            <foreach collection="controlLevels" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            </if>
                        </when>
                    </choose>
                </foreach>
            </if>
            AND a.deleted=0
        </where>
    </sql>

    <select id="selectRegularMonitorList" resultMap="regularMonitorList">
        select a.id as id,
        a.level as level_code,
        (select d.name
        from t_dict d
        where d.code = a.level
        and d.type = 'control_regular_monitor_level') as level_name,
        (select count(*)
        from t_warning
        where monitor_id = a.id
        and control_type = 2) as warning_count,
        (select t.real_name
        from t_user t
        where t.id = a.create_user_id
        and t.status = 1) as create_user,
        (select short_name from t_dept where id = a.create_dept_id) as create_dept,
        a.create_time as create_time
        from t_control_regular_monitor a
        where target_id = #{personId}
        and a.deleted =0
        order by a.create_time desc
    </select>

    <select id="getIdsByRegularParams" resultType="java.lang.Long">
        select a.id
        from t_profile_person a
        <include refid="searchAndFilter"/>
    </select>

    <resultMap id="regularMonitorList" type="com.trs.police.common.core.vo.control.RegularMonitorListVO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="warning_count" property="warningCount"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="status" property="status"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="create_time" property="createTime"/>
        <association property="level" javaType="com.trs.police.common.core.vo.CodeNameVO">
            <result column="level_code" property="code"/>
            <result column="level_name" property="name"/>
        </association>
    </resultMap>

    <update id="updateWhenPersonDeleting" parameterType="java.lang.String">
        UPDATE t_control_monitor_target_filter_params
        set id_numbers= JSON_REMOVE(id_numbers, REPLACE(JSON_SEARCH(id_numbers, 'one', #{idNumber}), '"', ''))
        where #{idNumber} MEMBER OF(id_numbers)
    </update>

    <update id="updateWhenPersonCreating" parameterType="com.trs.police.profile.debezium.vo.ProfilePersonInfoVO">

        UPDATE t_control_monitor_target_filter_params
        set id_numbers= JSON_ARRAY_INSERT(ifnull(id_numbers,'[]'),'$[0]',#{vo.idNumber})
        <where>
            (JSON_EXTRACT(filter_params,'$.personLabel') is null or
            <choose>
                <when test="vo.personLabel !=null and vo.personLabel.size()!=0">
                    (
                    <foreach collection="vo.personLabel" item="item" open="(" separator="OR" close=")">
                        (JSON_OVERLAPS(JSON_EXTRACT(filter_params,'$.personLabel')
                        ,( select JSON_ARRAYAGG(t1.id)
                        from t_profile_label t1
                        where (select concat(t2.path, t2.id, '-') from t_profile_label t2 where t2.id=${item})
                        like CONCAT('%-', t1.id, '-%')
                        ))
                        >0)
                    </foreach>
                    ) )
                </when>
                <otherwise>
                    false )
                </otherwise>
            </choose>
            and (JSON_EXTRACT(filter_params,'$.district') is null or
            <choose>
                <when test="@org.apache.commons.lang3.StringUtils@isNotBlank(vo.registeredResidence)">
                    (JSON_OVERLAPS(JSON_EXTRACT(filter_params,'$.district')
                    ,( select JSON_ARRAYAGG(t1.code)
                    from t_district t1
                    where (select concat(t2.path, t2.code, '-') from t_district t2 where
                    t2.code=#{vo.registeredResidence})
                    like CONCAT('%-', t1.code, '-%')
                    ))
                    >0) )
                </when>
                <otherwise>
                    false )
                </otherwise>
            </choose>
            and (JSON_EXTRACT(filter_params,'$.dept') is null or
            <choose>
                <when test="vo.controlStation !=null">
                    (JSON_OVERLAPS(JSON_EXTRACT(filter_params,'$.dept')
                    ,( SELECT JSON_ARRAYAGG(d.id)
                    FROM t_dept d
                    WHERE CONCAT(d.path
                    , d.id
                    , '-') LIKE CONCAT('%-'
                    , ${vo.controlStation}
                    , '-%' )))
                    >0) )
                </when>
                <otherwise>
                    false )
                </otherwise>
            </choose>
        </where>
        ;

        UPDATE t_control_regular_monitor
        set warning_config_ids=(SELECT json_arrayagg(id) from t_control_regular_monitor_config where deleted =0 and
        (level MEMBER of(regular_levels)) and (JSON_OVERLAPS(person_labels,(SELECT p.person_label from t_profile_person
        p where target_id=p.id))>0))
        where target_id = #{vo.id};


        UPDATE t_control_monitor cm
        set cm.person_label=(SELECT JSON_ARRAYAGG(label_id) from (SELECT DISTINCT jt.label_id
        FROM t_profile_person t , JSON_TABLE(t.person_label, '$[*]' COLUMNS (label_id JSON PATH '$')) jt where id member
        of (cm.profile_target_id)) t)
        where monitor_type=1 and #{vo.id} member of (cm.profile_target_id);
    </update>
    <update id="setPersonMonitorStatusByIdNumbers">
        update t_profile_person p
        set p.monitor_status = #{monitorStatus}
        where id_number in
        <foreach collection="idNumbers" open="(" close=")" separator="," item="idNumber">
            #{idNumber}
        </foreach>

    </update>

    <resultMap id="ProfilePersonInfoMap" type="com.trs.police.profile.debezium.vo.ProfilePersonInfoVO">
        <result column="id" javaType="java.lang.Long" property="id"/>
        <result column="id_number" javaType="java.lang.String" property="idNumber"/>
        <result column="id_type" javaType="java.lang.Integer" property="idType"/>
        <result column="deleted" property="deleted"/>
        <result column="tel" property="tel"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="person_label" property="personLabel"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="vehicle" property="carNumber"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="registered_residence" javaType="java.lang.String" property="registeredResidence"/>
        <result column="registered_residence_detail" javaType="java.lang.String" property="registeredResidenceDetail"/>
        <result column="current_residence_detail" javaType="java.lang.String" property="currentResidenceDetail"/>
        <result column="control_station" javaType="java.lang.Long" property="controlStation"/>
        <collection property="virtualIdentity" javaType="java.util.ArrayList"
            ofType="com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO"
            select="com.trs.police.common.core.mapper.CommonMapper.getVirtualIdentity"
            column="{virtualIdentityIds=virtual_identity_ids}"/>
    </resultMap>

    <select id="getPersonInfoById" resultMap="ProfilePersonInfoMap">
        select a.id,
        a.id_number,
        a.id_type,
        ifnull(a.person_label, '[]') as person_label,
        ifnull((select JSON_ARRAYAGG(car_number) from t_profile_vehicle where id member of (a.vehicle_ids)), '[]') as
        vehicle,
        ifnull(a.tel, '[]') as tel,
        ifnull(a.virtual_identity_ids, '[]') as virtual_identity_ids,
        a.registered_residence,
        a.registered_residence_detail,
        a.current_residence_detail,
        a.deleted,
        (select (select id from t_dept where code = control_station)
        from t_profile_person_police_control
        where person_id = a.id) as control_station
        from t_profile_person a
        where a.id = #{personId}
        and a.deleted = 0
    </select>

    <select id="findByIdentifier" resultMap="personVoMap">
        SELECT *, (SELECT IFNULL(JSON_ARRAYAGG(v.car_number),'[]') from t_profile_vehicle v where v.id member of
        (ifnull(a.vehicle_ids, '[]'))) as car_number
        FROM t_profile_person a
        <where>
           1=1
            <bind name="virtualIdentifierType"
                  value="@com.trs.police.common.core.constant.enums.IdentifierTypeEnum@identityTypeToVirtualType(identifierType)"/>
            <choose>
                <when test="virtualIdentifierType != null">
                    and JSON_OVERLAPS(a.virtual_identity_ids, #{identifierNumbers,jdbcType=VARCHAR})
                </when>
                <when test="identifierType == @com.trs.police.common.core.constant.enums.IdentifierTypeEnum@CAR_NUMBER.code">
                    and JSON_OVERLAPS(a.vehicle_ids, #{identifierNumbers,jdbcType=VARCHAR})
                </when>
                <when test="identifierType == @com.trs.police.common.core.constant.enums.IdentifierTypeEnum@PHONE_NUMBER.code">
                    and JSON_OVERLAPS(a.tel, #{identifierNumbers,jdbcType=VARCHAR})
                </when>
                <otherwise>
                    and a.id_number = #{idNumber}
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="getPersonCardList" resultMap="personCardMapV1">
        <include refid="personCardVOV1"/>
        <include refid="areaListFilter"/>
        order by a.create_time desc
    </select>

    <!-- 过期 如果不需要dept和regular_count字段 请使用personCardVOV1 -->
    <sql id="personCardVO">
        select a.id as id,
        a.name as name,
        ifnull(a.tel, '[]') as tel,
        a.id_number as id_number,
        ifnull(a.person_label, '[]') as person_label,
        JSON_ARRAY_APPEND(
            IFNULL(a.photo, '[]'),
            '$',
            JSON_OBJECT('id', -1, 'url', CONCAT('/oss/photo/', a.id_number))
        ) AS imgs,
        (select GROUP_CONCAT(name SEPARATOR ',') AS name
        from t_dept
        where code in (select control_station
        from t_profile_person_police_control
        where person_id = a.id)) as dept,
        (select count(0)
        from t_control_regular_monitor c
        where c.target_id = a.id and c.deleted=0) as regular_count,
        a.virtual_identity_ids as virtual_identity_ids,
        ifnull(a.vehicle_ids, '[]') as car_number,
        ifnull(a.police_kind, '[]') as police_kind
        from t_profile_person a
    </sql>

    <sql id="personCardVOV1">
        select a.id as id,
        a.name as name,
        ifnull(a.tel, '[]') as tel,
        a.id_number as id_number,
        ifnull(a.person_label, '[]') as person_label,
        JSON_ARRAY_APPEND(
            IFNULL(a.photo, '[]'),
            '$',
            JSON_OBJECT('id', -1, 'url', CONCAT('/oss/photo/', a.id_number))
        ) AS imgs,
        a.virtual_identity_ids as virtual_identity_ids,
        ifnull(a.vehicle_ids, '[]') as car_number
        from t_profile_person a
    </sql>

    <select id="getCardByIdNumber" resultMap="personCardMap">
        <include refid="personCardVO"/>
        where a.id_number = #{idNumber} and deleted = 0 limit 0,1
    </select>

    <select id="getCardById" resultMap="personCardMap">
        <include refid="personCardVO"/>
        where a.id = #{id}
    </select>

    <select id="getAllPersonList" resultMap="ProfilePersonInfoMap">
        select a.id,
        a.id_number,
        a.id_type,
        ifnull(a.person_label, '[]') as person_label,
        ifnull((select JSON_ARRAYAGG(car_number) from t_profile_vehicle where id member of (a.vehicle_ids)), '[]') as
        vehicle,
        ifnull(a.tel, '[]') as tel,
        ifnull(a.virtual_identity_ids, '[]') as virtual_identity_ids,
        a.registered_residence,
        a.registered_residence_detail,
        a.current_residence_detail,
        a.deleted,
        (select (select id from t_dept where code = control_station)
        from t_profile_person_police_control
        where person_id = a.id) as control_station
        from t_profile_person a
        where a.deleted = 0 order by a.id desc
    </select>
    <select id="getPersonControlDeptIdByIdentifier" resultType="java.lang.Long">
        SELECT (select id from t_dept where code = (select control_station from t_profile_person_police_control where
        person_id=a.id limit 1))
        FROM t_profile_person a
        <where>
            <bind name="virtualIdentifierType"
                value="@com.trs.police.common.core.constant.enums.IdentifierTypeEnum@identityTypeToVirtualType(identifierType)"/>
            <choose>
                <when test="virtualIdentifierType != null">
                    JSON_OVERLAPS(
                    a.virtual_identity_ids, (SELECT JSON_ARRAYAGG(v.id)
                    FROM t_profile_virtual_identity v
                    WHERE v.type = #{virtualIdentifierType,jdbcType=INTEGER}
                    AND v.virtual_number = #{identifierNumber,jdbcType=VARCHAR})) > 0
                </when>
                <when
                    test="identifierType == @com.trs.police.common.core.constant.enums.IdentifierTypeEnum@CAR_NUMBER.code">
                    JSON_OVERLAPS(
                    a.vehicle_ids, (SELECT JSON_ARRAYAGG(v.id)
                    FROM t_profile_vehicle v
                    WHERE v.car_number = #{identifierNumber,jdbcType=VARCHAR})) > 0
                </when>
                <otherwise>
                    a.id_number = #{identifierNumber,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </where>
        limit 1
    </select>
    <select id="getPersonByTel" resultType="com.trs.police.profile.domain.entity.Person">
        select p.*
        from t_profile_person p
        where #{tel} member of (p.tel)
    </select>

    <select id="getPersonVoByTel" resultMap="personVoMap">
        select a.id as id,
        a.name as name,
        ifnull(a.tel, '[]') as tel,
        a.id_number as id_number,
        ifnull(a.id_type, '1') as id_type,
        ifnull(a.person_label, '[]') as person_label,
        JSON_ARRAY_INSERT(ifnull(a.photo, '[]'), '$[0]', JSON_OBJECT('id', -1, 'url',
        CONCAT('/oss/photo/', a.id_number))) as imgs,
        (SELECT IFNULL(JSON_ARRAYAGG(v.car_number), '[]')
        from t_profile_vehicle v
        where v.id member of ( ifnull(a.vehicle_ids, '[]'))) as car_number,
        ifnull(a.virtual_identity_ids, '[]') as virtual_identity_ids
        from t_profile_person a
        where #{tel} member of (a.tel)
    </select>

    <select id="countTrackByWarningType" resultType="com.trs.police.common.core.vo.IdNameCountVO">
        select tcmwm.id as id,
        tcmwm.title as name,
        count(tcmwm.id) as count
        from (select twt.id as track_id, tw.id as warning_id, model_id
        from t_warning_track twt
        left join t_warning tw on twt.warning_id = tw.id
        WHERE twt.person_id = #{personId}
        and twt.activity_time >= #{beginTime}
        and twt.activity_time <![CDATA[<=]]> #{endTime}) a
        inner join t_control_monitor_warning_model tcmwm on json_contains(a.model_id, CONVERT(tcmwm.id, char))
        where tcmwm.id != 7
        group by tcmwm.id
    </select>

    <select id="getActivityLevel" resultType="com.trs.police.common.core.vo.CodeNameVO">
        select r.activity_level as code,
        (select name from t_dict where code = r.activity_level and type = 'profile_activity_level') as name
        from t_profile_person_group_relation r
        where r.group_id = #{groupId}
        and r.person_id = #{personId}
    </select>

    <sql id="selectTrackSql">
        select t.id as id,
        p.name as name,
        t.longitude as lng,
        t.latitude as lat,
        s.type as type,
        t.activity_time as time,
        t.place as location,
        s.name as sourceName,
        #         (select JSON_ARRAYAGG(title) from t_control_monitor_warning_model m
        #          JOIN t_warning w ON JSON_CONTAINS(w.model_id, CONVERT(m.id, CHAR))
        #         WHERE w.id = t.warning_id) as content,
        t.warning_id as warningId
        from t_warning_track t left join t_control_warning_source s on t.source_id = s.unique_key
        left join t_profile_person p on p.id = t.person_id
        where
        t.person_id = #{personId}
        <if test="filterParams!=null and filterParams.size() > 0">
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key == 'activeTime'">
                        <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                        and t.activity_time <![CDATA[>=]]> #{timeParam.beginTime}
                        and t.activity_time <![CDATA[<=]]> #{timeParam.endTime}
                    </when>
                    <when test="filterParam.key == 'behaviorType'">
                        <bind name="sourceType" value="filterParam.getProcessedValue()"/>
                        and s.type = #{sourceType}
                    </when>
                </choose>
            </foreach>
        </if>
        <if test="searchParams.searchValue != null and searchParams.searchValue != ''">
            and (t.place like concat('%',#{searchParams.searchValue},'%') or s.name like
            concat('%',#{searchParams.searchValue},'%'))
        </if>
        <choose>
            <when test="sortParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(sortParams.sortField)">
                order by
                <if test="sortParams.sortField == 'time'">
                    time
                </if>
                ${sortParams.getProcessedValue()}
            </when>
            <otherwise>
                order by time desc,create_time desc
            </otherwise>
        </choose>
    </sql>

    <select id="selectTrackPage" resultMap="trackPointMap">
        select t.id as id,
        p.name as name,
        t.longitude as lng,
        t.latitude as lat,
        s.type as type,
        t.activity_time as time,
        t.place as location,
        s.name as sourceName,
        #         (select JSON_ARRAYAGG(title) from t_control_monitor_warning_model m
        #          JOIN t_warning w ON JSON_CONTAINS(w.model_id, CONVERT(m.id, CHAR))
        #         WHERE w.id = t.warning_id) as content,
        t.warning_id as warningId
        from t_warning_track t left join t_control_warning_source s on t.source_id = s.unique_key
        left join t_profile_person p on p.id = t.person_id
        where
        t.person_id = #{personId}
        <if test="filterParams!=null and filterParams.size() > 0">
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key == 'activeTime'">
                        <bind name="timeParam" value="filterParam.getProcessedValue()"/>
                        and t.activity_time <![CDATA[>=]]> #{timeParam.beginTime}
                        and t.activity_time <![CDATA[<=]]> #{timeParam.endTime}
                    </when>
                    <when test="filterParam.key == 'behaviorType'">
                        <bind name="sourceType" value="filterParam.getProcessedValue()"/>
                        and s.type = #{sourceType}
                    </when>
                </choose>
            </foreach>
        </if>
        <if test="searchParams.searchValue != null and searchParams.searchValue != ''">
            and (t.place like concat('%',#{searchParams.searchValue},'%') or s.name like
            concat('%',#{searchParams.searchValue},'%'))
        </if>
        <choose>
            <when test="sortParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(sortParams.sortField)">
                order by
                <if test="sortParams.sortField == 'time'">
                    time
                </if>
                ${sortParams.getProcessedValue()}
            </when>
            <otherwise>
                order by time desc,t.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="selectContent" resultMap = "contentMap">
        select
        w.id as warningId,
        JSON_ARRAYAGG(title) as content
        from
        t_control_monitor_warning_model m
        join t_warning w on json_contains(w.model_id, convert(m.id, CHAR))
        <if test="warningIdList.size() > 0">
            where w.id in
            <foreach collection="warningIdList" open="(" close=")" separator="," item="warningId">
                #{warningId}
            </foreach>
        </if>
        group by w.id
    </select>

    <select id="selectWarningByTrack" resultMap="WarningCardMap">
        select w.id,
        w.warning_level,
        w.content,
        (select json_arrayagg(m.title) from t_control_monitor_warning_model m where json_contains(w.model_id,
        convert(m.id, char))) as model,
        (select json_extract(m.notify_person, '$[*].userName') from t_control_monitor m where m.id = w.monitor_id) as
        notify_person,
        IF((select count(*) from t_warning_process p where p.warning_id = w.id and p.status = 4 limit 1) = 1, '已完结',
        '处置中') as status
        from t_warning w
        left join t_warning_track t on w.id = t.warning_id
        where t.id = #{trackId}
    </select>

    <select id="selectPersonIdsByDeptId" resultType="java.lang.Double">
        select avg(tpp.complete_rate)
        from t_control_regular_monitor c left join t_dept temp_d on c.create_dept_id = temp_d.id
        left join t_profile_person tpp on c.target_id = tpp.id
        where concat(temp_d.path, temp_d.id, '-') like concat('%-', #{deptId}, '-%')
        and c.status = 5
        <if test="level != null">
            and c.level = #{level}
        </if>
        <if test="regularLabel != null">
            and JSON_OVERLAPS(c.label_ids, #{regularLabel})
        </if>
    </select>

    <select id="selectExistIdNumbers" resultType="java.lang.String">
        select id_number from t_profile_person where id_number in
        <foreach collection="idNumbers" item="idNumber" open="(" separator="," close=")">
            #{idNumber}
        </foreach>
    </select>
    <select id="selectRegularPageListCount" resultType="java.lang.Long">
        <bind name="current" value="@com.trs.police.common.core.utils.AuthHelper@getCurrentUser()"/>
        select count(0)
        from t_profile_person a
        <include refid="searchAndFilter"/>
    </select>
    <select id="getPersonExport" resultType="com.trs.police.profile.domain.vo.PersonExportVO">
        select p.id,
        (select t.name from t_dept d left join t_district t on d.district_code = t.code where d.code=c.control_bureau) as controlBureau,
        (select name from t_dept d where d.code=c.control_station) as controlStation,
        p.person_label as personLabels,
        p.current_residence_detail as currentResidenceDetail,
        (select group_concat(g.name) from t_profile_group g where g.id in (select gr.group_id from
        t_profile_person_group_relation gr where gr.person_id=p.id)) as lbmc,
        p.name as name,
        p.id_number as idNumber,
        p.registered_residence_detail as registeredResidenceDetail,
        REPLACE(REPLACE(REPLACE(IFNULL(p.tel, '[]'), '[', ''), ']', ''),'"','') as tel,
        (select d.name from t_dict d where d.type='profile_work_target' and d.code = p.work_target) as workTarget,
        p.main_demand as mainDemand,
        p.petition_info as petitionInfo,
        p.work_measures as workMeasures,
        /*(select REPLACE(REPLACE(control_person,'[',''),']','') from t_profile_person_police_control where id = c.id) as gkzrr,*/
        REPLACE(REPLACE(c.control_person,'[',''),']','') as gkzrr,
        g.control_government as controlGovernment,
        g.control_government as dzzrbm,
        g.control_government_person as dzzrr,
       (select u.real_name from t_user u where u.id = c.control_bureau_leader) as ldxm,
       (select u.telephone from t_user u where u.id = c.control_bureau_leader) as gabalddh,
       (select u.duty from t_user u where u.id = c.control_bureau_leader) as gabaldyw,
        g.control_government_person_duty as dzzrrzw,
        g.control_government_contact as dzzrrdh,
        (select concat(r.create_time,':',r.work_detail) from t_service_work_record r where r.person_id=p.id order by
        create_time desc limit 1) as record,
        (select name from t_dept d where d.code=c.control_police) as controlPolice,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='person' and
        i.profile_target_id=p.id and i.inspector_type=1 ORDER BY create_time DESC LIMIT 1) as zbyxqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='person' and
        i.profile_target_id=p.id and i.inspector_type=2 ORDER BY create_time DESC LIMIT 1) as zrrlsqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='person' and
        i.profile_target_id=p.id and i.inspector_type=3 ORDER BY create_time DESC LIMIT 1) as rywkqk,
        (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='person' and
        i.profile_target_id=p.id and i.inspector_type=4 ORDER BY create_time DESC LIMIT 1) as hjcxqk,
        (CONCAT_WS('、',g.control_government,g.control_government_person,g.control_government_contact)) as
        governmentControl
        from t_profile_person p
        left join t_profile_person_police_control c on p.id=c.person_id
        left join t_profile_person_government_control g on p.id=g.person_id
        where p.id = #{id}
        group by p.id
    </select>
    <select id="getPersonCardListTotal" resultType="java.lang.Long">
        select count(0)
        from t_profile_person a
        <include refid="areaListFilter"/>
    </select>

    <select id="getTrack" resultType="com.trs.police.profile.domain.vo.TrackPointVO">
        <bind name="searchParams" value="request.searchParams"/>
        SELECT distinct
        p.name,
        p.id,
        longitude AS lng,
        latitude AS lat,
        source_type AS type,
        place AS location,
        t.activity_time AS time,
        s.name as sourceName,
        source_id as source_id_str,(
        SELECT NAME
        FROM
        t_dict
        WHERE
        type = 'control_warning_source_type'
        AND CODE = t.source_type
        ) AS gzylx,
        FALSE AS isStayPoint,
        t.id as trackId
        FROM
        t_warning_track t
        join t_profile_person p on p.id = t.person_id
        left join t_control_warning_source s on t.source_id = s.unique_key
        where person_id = #{personId}
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
            <choose>
                <when test=" 'key'==searchParams.searchField">
                    and (place like #{pattern} or p.name like #{pattern} )
                </when>
            </choose>
        </if>
    </select>

    <select id="getProfilePersonList" resultMap="profilePersonVoMap">
        select id,name,id_number,id_type,risk_score,risk_level,create_dept_id,
        ifnull(person_label,'[]') as person_label,
        JSON_ARRAY_INSERT(ifnull(photo,'[]'),'$[0]' ,JSON_OBJECT('id',-1,'url',
        CONCAT('/oss/photo/',id_number))) as photo
        from t_profile_person where 1 = 1
        <if test="dto.controlLevel != null">
            and control_level = #{dto.controlLevel}
        </if>
        <if test="dto.registeredResidence != null">
            and registered_residence = #{dto.registeredResidence}
        </if>
        <if test="dto.idCardList != null and dto.idCardList.size > 0">
            and id_number in
            <foreach collection="dto.idCardList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.personLabel != null">
            and JSON_OVERLAPS(person_label,#{dto.personLabel})
        </if>
    </select>
    <select id="getPersonAberration" resultType="com.trs.police.profile.domain.vo.PersonArchiveWarningDto">
        SELECT
        w.*,
        w.activity_address AS address
        FROM
        t_warning w
        WHERE
            w.id IN (
                SELECT DISTINCT warning_id
                FROM
                    t_warning_track
                WHERE
                    person_id = #{personAberrationDTO.personId}
                    and warning_level in(1,2,3) )
        ORDER BY w.warning_time DESC
    </select>

    <select id="getPersonCaseList" resultMap="personCaseMap">
        SELECT
            a.id_number,
            a.name,
            p.id as personId,
            group_concat(c.asjbh) asjbhs
        from
        <foreach collection="filterParams" item="filterParam">
            <if test="filterParam.key=='personKind'">
                <choose>
                    <when test="filterParam.value==1">
                        t_profile_case_related_person
                    </when>
                    <otherwise>
                        t_profile_case_related_suspect
                    </otherwise>
                </choose>
                a
            </if>
        </foreach>
                left join t_profile_case c on a.asjbh = c.asjbh
                left join t_profile_person p on p.id_number = a.id_number
        where c.asjbh is not null and p.id is not null
        <if test="searchParams.searchValue != null and searchParams.searchValue != ''">
            <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
            <choose>
                <when test="'idNumber'==searchParams.searchField">
                    and (a.id_number like #{pattern} )
                </when>
                <when test="'name'==searchParams.searchField">
                    and (a.name like #{pattern} )
                </when>
                <when test="'asjbh'==searchParams.searchField">
                    and (c.asjbh like #{pattern})
                </when>
                <when test="'ajmc'==searchParams.searchField">
                    and (c.ajmc like #{pattern} )
                </when>
                <otherwise>
                    and (a.id_number like #{pattern}
                             or a.name like #{pattern}
                             or c.ajmc like #{pattern}
                             or c.asjbh like #{pattern})
                </otherwise>
            </choose>
        </if>
        group by a.id_number, a.name, p.id
    </select>

    <select id="getPersonByPersonLabel" resultMap="personMap">
        select * from t_profile_person where JSON_CONTAINS(person_label,#{personLabel}, '$')
    </select>


    <select id="temporaryControlCount" resultType="com.trs.police.common.core.vo.IdNameCountVO">
        SELECT
            person.id as id, count(1) as count, person.id_number as name
        FROM
            t_control_monitor_target_relation relation
        inner join
            t_profile_person person on relation.target_id = person.id
        inner join
            t_control_monitor mo on mo.id = relation.monitor_id
        where
            mo.monitor_type = 1
            and mo.monitor_status = 5
            and mo.deleted != 1
            and person.id in
            <foreach collection="personId" separator="," open="(" close=")" item="it">
                #{it}
            </foreach>
            and relation.target_id in
            <foreach collection="personId" separator="," open="(" close=")" item="it">
                #{it}
            </foreach>
        group by person.id;
    </select>

    <select id="regularControlCount" resultType="com.trs.police.common.core.vo.IdNameCountVO">
        SELECT
            target_id as id, count(1) as count
        FROM
            t_control_regular_monitor
        WHERE
            deleted=0
            AND target_id in
        <foreach collection="personId" separator="," open="(" close=")" item="it">
            #{it}
        </foreach>
        GROUP BY target_id
    </select>
    <select id="selectAll" resultType="com.trs.police.profile.domain.vo.PersonExportVO">
        select p.id,
       (select t.name from t_dept d left join t_district t on d.district_code = t.code where d.code=c.control_bureau) as controlBureau,
       (select name from t_dept d where d.code=c.control_bureau) as controlStation,
       p.person_label as personLabels,
       p.current_residence_detail as currentResidenceDetail,
       (select group_concat(g.name) from t_profile_group g where g.id in (select gr.group_id from
           t_profile_person_group_relation gr where gr.person_id=p.id)) as lbmc,
       p.name as name,
       p.id_number as idNumber,
       p.registered_residence_detail as registeredResidenceDetail,
       REPLACE(REPLACE(REPLACE(IFNULL(p.tel, '[]'), '[', ''), ']', ''),'"','') as tel,
       (select d.name from t_dict d where d.type='profile_work_target' and d.code = p.work_target) as workTarget,
       p.main_demand as mainDemand,
       p.petition_info as petitionInfo,
       p.work_measures as workMeasures,
       g.control_government as controlGovernment,
       g.control_government_person as dzzrr,
       g.control_government_contact as dzzrrdh,
       (select concat(r.create_time,':',r.work_detail) from t_service_work_record r where r.person_id=p.id order by
           create_time desc limit 1) as record,
       (select name from t_dept d where d.code=c.control_police) as controlPolice,
       (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='person' and
           i.profile_target_id=p.id and i.inspector_type=1 ORDER BY create_time DESC LIMIT 1) as zbyxqk,
       (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='person' and
           i.profile_target_id=p.id and i.inspector_type=2 ORDER BY create_time DESC LIMIT 1) as zrrlsqk,
       (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='person' and
           i.profile_target_id=p.id and i.inspector_type=3 ORDER BY create_time DESC LIMIT 1) as rywkqk,
       (select i.inspector_detail from t_profile_inspector i where i.profile_target_type='person' and
           i.profile_target_id=p.id and i.inspector_type=4 ORDER BY create_time DESC LIMIT 1) as hjcxqk,
       (CONCAT_WS('、',g.control_government,g.control_government_person,g.control_government_contact)) as
           governmentControl
        from t_profile_person p
                 left join t_profile_person_police_control c on p.id=c.person_id
                 left join t_profile_person_government_control g on p.id=g.person_id
        where p.deleted = 0
          AND p.approval_statue_code = '3'
    </select>
    <select id="selectPersonResultList" resultType="com.trs.police.profile.domain.vo.RelatedPersonVO">
        select p.id,p.id_number,p.risk_level,p.id_type,p.current_position,p.name,p.person_type,registered_residence
        ,p.risk_score,p.complete_rate,p.control_status,p.current_job,p.registered_residence_detail,p.tel,p.work_measures,p.work_target,
         p.family_relation_ids,p.social_relation_ids,p.current_residence_detail,p.current_residence
        <if test="policeKind != null and policeKind != ''">
            ,z.level as level
        </if>
        from t_profile_person p
        <if test="policeKind != null and policeKind == 3">
            left join (select jz.person_id,jz.risk_level as level
               from t_profile_person_risk_jz jz where deleted = 0)
                z on p.id = z.person_id
        </if>
        <if test="policeKind != null and policeKind == 4">
            left join (select za.person_id,za.control_level as level
               from t_profile_person_risk_za za where deleted = 0)
                z on p.id = z.person_id
        </if>
        <if test="policeKind != null and policeKind == 99">
            left join (select o.person_id,o.person_level as level
               from t_profile_person_risk_other o where deleted = 0 and police_kind = 99)
                z on p.id = z.person_id
        </if>
        <where>
            <if test="personLevel != null and personLevel != ''">
                and risk_level = #{personLevel}
            </if>
            <if test="personIds != null and personIds.size > 0">
                and p.id in
                <foreach collection="personIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="countTrackByTime" resultType="com.trs.police.profile.domain.vo.TrackActivityTime">
        select ${functionName}(${column})      as node,
        count(g.id) as num
        from t_warning_track g
        WHERE g.identifier in
        <foreach collection="identifiers" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        and g.activity_time <![CDATA[>=]]> #{beginTime}
        and g.activity_time <![CDATA[<=]]> #{endTime}
        group by node order by node
    </select>

    <select id="countTrackByActivityTime" resultType="com.trs.police.profile.domain.vo.TrackActivityTime">
        select ${functionColumn} as nodeName,
        count(g.id) as num
        from t_warning_track g
        WHERE g.identifier in
        <foreach collection="identifiers" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
        and g.activity_time <![CDATA[>=]]> #{beginTime}
        and g.activity_time <![CDATA[<=]]> #{endTime}
        group by nodeName order by nodeName
    </select>
    <select id="selectAllByLabels" resultMap="personListVo">
        select
        distinct
        a.id as id,
        JSON_ARRAY_APPEND(
        IFNULL(a.photo, '[]'),
        '$',
        JSON_OBJECT('id', -1, 'url', CONCAT('/oss/photo/', a.id_number))
        ) AS photo ,
        a.name as name ,
        a.id_number as idNumber ,
        a.person_label as personLabel ,
        a.risk_score as riskScore ,
        (
        SELECT
        REPLACE(GROUP_CONCAT( control_person SEPARATOR ','), '],[', ',') AS result
        FROM
        t_profile_person_police_control b
        WHERE
        person_id = a.id
        ) as dutyPolice ,
        (
        SELECT
        REPLACE(GROUP_CONCAT(control_station SEPARATOR ','), '],[', ',') AS result
        FROM t_profile_person_police_control b
        WHERE person_id = a.id
        ) as dutyPoliceStation,
        (
        SELECT
        REPLACE(GROUP_CONCAT(control_bureau SEPARATOR ','), '],[', ',') AS result
        FROM
        t_profile_person_police_control b
        WHERE
        person_id = a.id
        ) as controlBureau,
        a.registered_residence as registeredResidence ,
        a.create_time as createTime ,
        a.update_time as updateTime,
        a.approval_statue_code as approvalStatueCode,
        a.profile_status as profileStatus,
        a.resolve_difficulty as resolveDifficulty,
        a.tel as tel,
        a.chinese_name as chineseName,
        a.visa_end_date as visaEndDate,
        a.current_residence as currentResidence
        from
        t_profile_person a
        left join t_profile_person_police_control pppc on a.id = pppc.person_id
        <where>
            1=1
            <if test="labelIds != null and labelIds.size() > 0">
                AND JSON_OVERLAPS(a.person_label,
                <foreach collection="labelIds" item="labelId" open="JSON_ARRAY(" close=")" separator=",">
                    #{labelId}
                </foreach>
                )
            </if>
        </where>
    </select>

    <resultMap id="personCaseMap" type="com.trs.police.common.core.vo.profile.PersonCaseVO">
        <id property="idNumber" column="id_number"/>
        <result property="name" column="name"/>
        <result property="asjbhs" column="asjbhs"/>
        <result property="personId" column="personId"/>
    </resultMap>

    <!-- 已移除，使用 ProfilePersonFollowMapper.batchUpdateFollowStatus 替代 -->

    <select id="getRelatedWarningPersonGt"
            resultType="com.trs.police.profile.domain.vo.RelatedWarningPersonGtVO">
        SELECT
            p.id,
            w.create_time warningTime,
            w.device_code deviceCode,
            s.name,
            s.code
        from t_profile_person p
            right join t_warning_fkrxyj w on  w.id_card = p.id_number
            left join t_control_warning_source s on s.unique_key = w.device_code
        where
            p.id in
        <foreach collection="personIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getRelatedGtWarningGroup"
            resultType="com.trs.police.profile.domain.vo.RelatedGtWarningGroupVO">
        SELECT distinct
               g.id,g.name
        from t_profile_group g
            left join t_warning_fkrxyj w on JSON_CONTAINS(g.related_gt, CAST(concat('"',w.device_code,'"') AS JSON), '$')
            left join t_profile_person p on p.id_number = w.id_card
        where
            g.id is not null
            and p.id = #{personId}
    </select>

    <select id="getRelatedFamilyWithWarnInfo" resultType="com.trs.police.profile.domain.vo.RelatedPersonVO">
        SELECT f.id_number idNumber, f.name, f.relation,
            (select case when count(*)>0 then 1 else 0 end from t_warning_fkrxyj w where f.id_number = w.id_card) as havingWarning,
             (select create_time from t_warning_fkrxyj w where f.id_number = w.id_card order by create_time desc limit 1) as lastWarningTime
        from t_profile_person p
            left join t_profile_family_relation f on JSON_CONTAINS(p.family_relation_ids, JSON_ARRAY(f.id))
        where f.id is not null
            and p.id = #{personId}
    </select>

    <select id="getPersonByCertificateNumberAndTypes" resultType="com.trs.police.profile.domain.entity.Person">
        select * from t_profile_person where ID_TYPE = #{certificateType} and ID_NUMBER in(
            <foreach collection="list" item="certificateNumber" separator=",">
                #{certificateNumber}
            </foreach>
        )
    </select>
</mapper>