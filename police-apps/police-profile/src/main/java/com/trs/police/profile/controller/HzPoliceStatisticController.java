package com.trs.police.profile.controller;

import com.trs.police.profile.domain.dto.zhzg.HzBigScreenDto;
import com.trs.police.profile.factory.ProfileSceneSearchFactory;
import com.trs.police.profile.service.secne.ProfileStatisticScene;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 档案-群体工作记录
 *
 * <AUTHOR>
 * @date 2024/05/24
 */
@RestController
@RequestMapping("/hzPoliceStatistic")
public class HzPoliceStatisticController {

    /**
     * 数据总览 https://trsyapi.trscd.com.cn/project/432/interface/api/11624
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/dataOverview")
    public RestfulResultsV2<CountItem> dataOverview(@RequestBody HzBigScreenDto dto){
        ProfileStatisticScene subjectSceneSearchImpl = ProfileSceneSearchFactory.getSubjectSceneSearchImpl("dataOverview");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(dto));
    }

    /**
     * 政治面貌 https://trsyapi.trscd.com.cn/project/432/interface/api/11630
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/zzmmStatistic")
    public RestfulResultsV2<CountItem> zzmmStatistic(@RequestBody HzBigScreenDto dto){
        ProfileStatisticScene subjectSceneSearchImpl = ProfileSceneSearchFactory.getSubjectSceneSearchImpl("zzmmStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(dto));
    }

    /**
     * 资格认证统计 https://trsyapi.trscd.com.cn/project/432/interface/api/11642
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/zgrzStatistic")
    public RestfulResultsV2<CountItem> zgrzStatistic(@RequestBody HzBigScreenDto dto){
        ProfileStatisticScene subjectSceneSearchImpl = ProfileSceneSearchFactory.getSubjectSceneSearchImpl("zgrzStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(dto));
    }


    /**
     * 职级情况统计 https://trsyapi.trscd.com.cn/project/432/interface/api/11648
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/zjqkStatistic")
    public RestfulResultsV2<CountItem> zjqkStatistic(@RequestBody HzBigScreenDto dto){
        ProfileStatisticScene subjectSceneSearchImpl = ProfileSceneSearchFactory.getSubjectSceneSearchImpl("zjqkStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(dto));
    }

    /**
     * 人员结构统计 https://trsyapi.trscd.com.cn/project/432/interface/api/11654
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/yrjgStatistic")
    public RestfulResultsV2<CountItem> yrjgStatistic(@RequestBody HzBigScreenDto dto){
        ProfileStatisticScene subjectSceneSearchImpl = ProfileSceneSearchFactory.getSubjectSceneSearchImpl("yrjgStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(dto));
    }

    /**
     * 年龄结构 https://trsyapi.trscd.com.cn/project/432/interface/api/11636
     *
     * @param dto dto
     * @return 结果
     */
    @PostMapping("/ageStatistic")
    public RestfulResultsV2<CountItem> ageStatistic(@RequestBody HzBigScreenDto dto){
        ProfileStatisticScene subjectSceneSearchImpl = ProfileSceneSearchFactory.getSubjectSceneSearchImpl("ageStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(dto));
    }
}
