package com.trs.police.profile.schema.field;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.ApprovalStatusEnum;
import com.trs.police.common.core.constant.enums.ListSourceTypeEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.Dept;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.entity.MonitorWarningModelEntity;
import com.trs.police.common.core.mapper.DeptMapper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.DateUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.JwzhDictVO;
import com.trs.police.common.core.vo.control.MonitorInfoVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.UserCardVO;
import com.trs.police.common.core.vo.profile.ListSourceVO;
import com.trs.police.common.openfeign.starter.service.ControlService;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.RiskService;
import com.trs.police.profile.constant.enums.ProfileStatus;
import com.trs.police.profile.domain.entity.ProfileCaseLabelEntity;
import com.trs.police.profile.domain.entity.RiskLabelEntity;
import com.trs.police.profile.domain.vo.MapLocationVO;
import com.trs.police.profile.domain.vo.MapLocationVO.PositionInfo;
import com.trs.police.profile.domain.vo.MapLocationVO.Region;
import com.trs.police.profile.domain.vo.ProfileCaseLabelVO;
import com.trs.police.profile.mapper.LabelMapper;
import com.trs.police.profile.mapper.MonitorWarningModelMapper;
import com.trs.police.profile.mapper.RiskLabelMapper;
import com.trs.police.profile.service.ProfileDictService;
import com.trs.police.profile.util.ConverterHelper;
import com.trs.police.profile.util.DictUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据库字段映射器工厂类
 *
 * <AUTHOR>
 */
@Slf4j
public class MapperFactory {

    private MapperFactory() {
    }

    private static final Map<String, Mapper> MAPPERS = new HashMap<>();

    static {
        MAPPERS.put("json_to_image_array", new JsonToImageArrayMapper());
        MAPPERS.put("model_id_to_name", new ModuleIdArrayToNameMapper());
        MAPPERS.put("label_id_array_to_name", new LabelIdArrayToNameMapper());
        MAPPERS.put("risk_label_id_array_to_name", new RiskLabelIdArrayToNameMapper());
        MAPPERS.put("risk_label_ids_sum_score", new RiskLabelIdsSumScoreMapper());
        MAPPERS.put("risk_label_ids_sum_score_0", new RiskLabelIdsSumScoreMapper(0));
        MAPPERS.put("number_half_adjust_to_string", new NumberHalfAdjustToStringMapper());
        MAPPERS.put("case_label_array_to_name", new CaseLabelArrayToName());

        MAPPERS.put("dept_code_to_dept_name", new DeptCodeToDeptNameMapper());
        MAPPERS.put("dept_id_to_dept_name", new DeptIdToDeptNameMapper());
        MAPPERS.put("dept_id_array_to_dept_name", new DeptIdArrayToDeptName());
        MAPPERS.put("dept_path_id_array_to_dept_name", new dept_path_id_array_to_dept_name());
        MAPPERS.put("simple_dept_to_dept_name", new SimpleDeptToDeptNameMapper());

        MAPPERS.put("district_code_to_name", new DistrictToNameMapper());

        MAPPERS.put("dict_code_to_name", new DictCodeToNameMapper());
        MAPPERS.put("dict_code_array_to_name", new DictCodeArrayToNameMapper());
        MAPPERS.put("dict_code_array_to_tree_name", new DictCodeArrayToTreeNameMapper());
        MAPPERS.put("dict_code_to_dict", new DictCodeToDictMapper());
        MAPPERS.put("jwzh_dict_to_name", new JwzhDictToName());

        MAPPERS.put("user_id_array_to_user_name_array", new UserIdArrayToUserNameMapper());
        MAPPERS.put("user_id_array_to_user_card", new UserIdToUserCardMapper());
        MAPPERS.put("user_id_array_to_single_user_card", new UserIdToSingleUserCardMapper());
        MAPPERS.put("user_id_card_to_user_name", new UserIdCardToUserName());

        MAPPERS.put("source_to_name", new SourceToDescriptionMapper());

        MAPPERS.put("date_time_to_text", new TimeToSimpleStringMapper());
        MAPPERS.put("date_time_to_general_string", new DateTimeToGeneralStringMapper());
        MAPPERS.put("date_time_to_timestamp", new DateTimeToTimestampMapper());
        MAPPERS.put("date_to_text", new DateToSimpleStringMapper());
        MAPPERS.put("string_to_date_time", new StringToDateTimeMapper());
        MAPPERS.put("date_to_month", new DateToMonthStringMapper());
        MAPPERS.put("date_to_year", new DateToYearStringMapper());
        MAPPERS.put("date_to_general_string", new DateToGeneralStringMapper());

        MAPPERS.put("map_location", new MapLocationMapper());
        MAPPERS.put("map_location_string", new MapLocationStringMapper());

        MAPPERS.put("json_array_to_length", new JsonArrayLengthMapper());
        MAPPERS.put("json_array_to_string", new JsonToArrayMapper());
        MAPPERS.put("array_to_string", new ArrayToStringMapper());

        MAPPERS.put("approval_statue_code_to_name", new ApprovalCodeToName());
        MAPPERS.put("archive_code_to_name", new ProfileStatusCodeToName());
    }

    /**
     * 工厂方法
     *
     * @param mapping mapping方式
     * @return {@link Mapper}
     */
    public static Mapper getMapper(String mapping) {
        return MAPPERS.getOrDefault(mapping, new ToStringMapper());
    }

    /**
     * 列表字段数据库与展示值之间的映射
     */
    public interface Mapper {

        /**
         * 映射数据库字段为显示值
         *
         * @param recordValue 数据库字段值
         * @param dict        字典属性
         * @return 前端显示值
         */
        Object mappingDisplayValue(Object recordValue, DictProperty dict);

        /**
         * 映射数据库字段为编辑值
         *
         * @param recordValue 数据库字段值
         * @param dict        字典树形
         * @return 前端编辑值
         */
        Object mappingEditValue(Object recordValue, DictProperty dict);
    }

    /**
     * json对象类型
     */
    public static class JsonToImageArrayMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return Collections.emptyList();
            }
            return JsonUtil.parseArray(recordValue.toString(), FileInfoVO.class);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return mappingDisplayValue(recordValue, dict);
        }
    }

    /**
     * 标签数组类型
     */
    public static class LabelIdArrayToNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Long> labelIds = JsonUtil.objectToArray(recordValue, Long.class);
            if (labelIds == null || labelIds.isEmpty()) {
                return Collections.emptyList();
            }
            List<Label> labels = BeanUtil.getBean(LabelMapper.class).selectBatchIds(labelIds);
            return labels.stream().map(Label::getName).collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 模型数组类型
     */
    public static class ModuleIdArrayToNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Long> modelIds = JsonUtil.objectToArray(recordValue, Long.class);
            if (modelIds == null || modelIds.isEmpty()) {
                return Collections.emptyList();
            }
            List<MonitorWarningModelEntity> models = BeanUtil.getBean(MonitorWarningModelMapper.class).selectBatchIds(modelIds);
            return models.stream()
                    .map(MonitorWarningModelEntity::getTitle)
                    .filter(Objects::nonNull).collect(Collectors.joining(","));
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 标签数组类型
     */
    public static class DeptIdArrayToDeptName implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Long> deptIds = JsonUtil.objectToArray(recordValue, Long.class);
            if (deptIds == null || deptIds.isEmpty()) {
                return Collections.emptyList();
            }
            List<Dept> depts = BeanUtil.getBean(DeptMapper.class).selectBatchIds(deptIds);
            return depts.stream().map(Dept::getName).collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 群体类别数组类型
     */
    public static class dept_path_id_array_to_dept_name implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Object> deptObjects = Objects.nonNull(recordValue)
                    ? JsonUtil.objectToArray(recordValue, Object.class) : new ArrayList<>();
            if (CollectionUtils.isEmpty(deptObjects)){
                deptObjects = new ArrayList<>();
            }
            List<Long> deptIds = deptObjects.stream().map(e -> {
                List<Long> result = JsonUtil.objectToArray(e, Long.class);
                return com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(result) ? null : result.get(result.size() - 1);
            }).filter(e -> Objects.nonNull(e)).collect(Collectors.toList());
            if (deptIds == null || deptIds.isEmpty()) {
                return Collections.emptyList();
            }
            List<Dept> depts = BeanUtil.getBean(DeptMapper.class).selectBatchIds(deptIds);
            return depts.stream().map(Dept::getName).collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 风险标签数组类型
     */
    public static class RiskLabelIdArrayToNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Long> labelIds = Optional.ofNullable(recordValue)
                    .map(Object::toString)
                    .filter(StringUtils::isNotEmpty)
                    .map(it -> StringUtils.getLongList(it, StringUtils.SEPARATOR_COMMA, true))
                    .orElse(Collections.emptyList());
            if (labelIds.isEmpty()) {
                return Collections.emptyList();
            }
            List<RiskLabelEntity> labels = BeanUtil.getBean(RiskLabelMapper.class).selectBatchIds(labelIds);
            return labels.stream()
                    .map(it -> String.format("%s(%s)", it.getLabelName(), it.getLabelScore()))
                    .collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2024</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述： RiskLabelIdsSumScoreMapper
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2024/10/9 11:36
     * @since 1.0
     */
    public static class RiskLabelIdsSumScoreMapper implements Mapper {

        private Integer onlyInteger;

        public RiskLabelIdsSumScoreMapper() {
            this(1);
        }

        public RiskLabelIdsSumScoreMapper(Integer onlyInteger) {
            this.onlyInteger = onlyInteger;
        }

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<RiskLabelEntity> labels = Optional.ofNullable(recordValue)
                    .map(Object::toString)
                    .filter(StringUtils::isNotEmpty)
                    .map(it -> StringUtils.getLongList(it, StringUtils.SEPARATOR_COMMA, true))
                    .filter(CollectionUtils::isNotEmpty)
                    .map(it -> BeanUtil.getBean(RiskLabelMapper.class).selectBatchIds(it))
                    .orElse(Collections.emptyList());
            double score = labels.stream().mapToDouble(RiskLabelEntity::getLabelScore).sum();
            return ConverterHelper.convertScoreToString(score, onlyInteger);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 用户id数组
     */
    public static class UserIdArrayToUserNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Long> userIds = JsonUtil.objectToArray(recordValue, Long.class);
            if (userIds == null || userIds.isEmpty()) {
                return Collections.emptyList();
            }
            List<UserDto> users = BeanUtil.getBean(PermissionService.class).getUserListById(userIds);
            return users.stream().map(UserDto::getRealName).collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 组织机构id类型
     */
    private static class DeptIdToDeptNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            long id = (long) recordValue;
            DeptDto dept = BeanUtil.getBean(PermissionService.class).getDeptById(id);
            return dept.getShortName();
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 组织机构id类型
     */
    private static class SimpleDeptToDeptNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            JsonNode node = JsonUtil.parseJsonNode(recordValue.toString());
            if (node == null) {
                return null;
            }
            if (node.isArray()) {
                List<String> deptNames = new ArrayList<>();
                node.forEach(deptNode -> deptNames.add(deptNode.findValue("shortName").asText()));
                return deptNames.stream().filter(Objects::nonNull).collect(Collectors.joining("、"));
            } else {
                return node.findValue("shortName").asText();
            }

        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 组织机构代码类型
     */
    public static class DeptCodeToDeptNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null || StringUtils.isEmpty(recordValue.toString())) {
                return null;
            }
            String code = recordValue.toString();
            if (StringUtils.isEmpty(code)) {
                return null;
            }
            //兼容有多个部门的情况
            String[] arrCode = code.split(",");
            StringBuilder sDeptNames = new StringBuilder();
            for (String curCode : arrCode) {
                if (StringUtils.isEmpty(curCode) || StringUtils.isEmpty(curCode.replace(" ",""))){
                    continue;
                }
                DeptDto dept = BeanUtil.getBean(PermissionService.class).getDeptByCode(curCode);
                if (dept != null) {
                    sDeptNames.append(dept.getShortName()).append(",");
                }
            }
            if (sDeptNames.length() > 1) {
                sDeptNames.setLength(sDeptNames.length() - 1);
            }

            return sDeptNames;
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 数据库时间整理为LocalDateTime
     *
     * @param o 对象
     * @return 结果
     */
    private static LocalDateTime getLocalDateTime(Object o) {
        if (o instanceof LocalDateTime) {
            return (LocalDateTime) o;
        } else if (o instanceof Timestamp) {
            Timestamp ts = (Timestamp) o;
            return ts.toLocalDateTime();
        } else if (o instanceof Date) {
            Date date = (Date) o;
            return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        } else {
            return null;
        }
    }

    /**
     * 时间日期转换为简单时间字符串
     */
    public static class TimeToSimpleStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            LocalDateTime l = getLocalDateTime(recordValue);
            return TimeUtil.getSimpleTime(l);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            LocalDateTime l = getLocalDateTime(recordValue);
            return DateUtil.dateTimeToUtc(l);
        }
    }

    /**
     * 日期转换为普通日期字符串
     */
    public static class DateToSimpleStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            LocalDateTime l = getLocalDateTime(recordValue);
            return TimeUtil.getSimpleDate(l);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            LocalDateTime l = getLocalDateTime(recordValue);
            return l == null ? "--" :DateUtil.dateTimeToUtc(l);
        }
    }

    /**
     * 日期转换为月份字符串
     */
    public static class DateToMonthStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            LocalDateTime time = getLocalDateTime(recordValue);
            if (time == null) {
                return "--";
            }
            return time.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            LocalDateTime l = getLocalDateTime(recordValue);
            return l == null ? "--" :DateUtil.dateTimeToUtc(l);
        }
    }

    /**
     * 日期转换为年份字符串
     */
    public static class DateToYearStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            LocalDateTime time = getLocalDateTime(recordValue);
            if (time == null) {
                return "--";
            }
            return time.format(DateTimeFormatter.ofPattern("yyyy"));
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            LocalDateTime l = getLocalDateTime(recordValue);
            return l == null ? "--" :DateUtil.dateTimeToUtc(l);
        }
    }

    /**
     * 时间日期转换为标准字符串
     */
    public static class DateToGeneralStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            LocalDateTime l = getLocalDateTime(recordValue);
            return l == null ? "--" : l.format(TimeUtil.DEFAULT_DATE_PATTERN);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            LocalDateTime l = getLocalDateTime(recordValue);
            return l == null ? "--" : l.format(TimeUtil.DEFAULT_DATE_PATTERN);
        }
    }


    /**
     * 时间日期转换为标准字符串
     */
    public static class DateTimeToGeneralStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            LocalDateTime l = getLocalDateTime(recordValue);
            return l == null ? "--" : l.format(TimeUtil.DEFAULT_TIME_PATTERN);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            LocalDateTime l = getLocalDateTime(recordValue);
            return l == null ? "--" : l.format(TimeUtil.DEFAULT_TIME_PATTERN);
        }
    }

    /**
     * 时间日期转换为时间戳
     */
    public static class DateTimeToTimestampMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            LocalDateTime l = getLocalDateTime(recordValue);
            return DateUtil.dateTimeToUtc(l);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            LocalDateTime l = getLocalDateTime(recordValue);
            return DateUtil.dateTimeToUtc(l);
        }
    }

    /**
     * 码表映射类
     */
    public static class DictCodeToNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null || StringUtils.isEmpty(recordValue.toString())) {
                return null;
            }
            // 兼容多值
            String[] recordValueArr = recordValue.toString()
                    .replace("[", "")
                    .replace("]","")
                    .split(",");
            return Arrays.stream(recordValueArr).map(value -> {
                value = value.trim();
                if(StringUtils.isEmpty(value)){
                    return null;
                }
                Long code = Long.parseLong(value);
                if (Objects.isNull(dict)) {
                    return null;
                }
                String type = dict.getType();
                DictDto dictDto = BeanUtil.getBean(ProfileDictService.class).getSimpleDictByCodeAndType(code, type);
                if (Objects.isNull(dictDto)) {
                    return null;
                }
                return dictDto.getName();
            }).filter(Objects::nonNull).distinct().collect(Collectors.joining(","));
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 码表映射类
     */
    public static class DictCodeArrayToNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Long> dictCodeList = JsonUtil.objectToArray(recordValue, Long.class);
            if (dictCodeList == null || dictCodeList.isEmpty()) {
                return Collections.emptyList();
            }
            String type = dict.getType();
            List<DictDto> dictDtoList = BeanUtil.getBean(ProfileDictService.class).getDictListByTypeOnly(type);
            if (dictDtoList == null || dictDtoList.isEmpty()) {
                return Collections.emptyList();
            }
            return dictDtoList.stream()
                    .filter(dictDto -> dictCodeList.contains(dictDto.getCode()))
                    .map(DictDto::getName)
                    .collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 码表映射类
     */
    public static class DictCodeArrayToTreeNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Long> dictCodeList = JsonUtil.objectToArray(recordValue, Long.class);
            if (dictCodeList == null || dictCodeList.isEmpty()) {
                return Collections.emptyList();
            }
            String type = dict.getType();
            List<DictDto> dictDtoList = BeanUtil.getBean(ProfileDictService.class).getDictListByTypeOnly(type);
            if (dictDtoList == null || dictDtoList.isEmpty()) {
                return Collections.emptyList();
            }
            return dictCodeList.stream()
                    .map(code -> DictUtils.findParentNames(dictDtoList, code, "-"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 字符串类
     */
    public static class ToStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            // 数值型数据如果为0时，会导致前端判断出错从而呈现为“--”
            // 所以数值型强制转换成string对象
            if (recordValue instanceof Number) {
                return Optional.ofNullable(recordValue)
                        .map(Object::toString)
                        .orElse(null);
            }
            return recordValue;
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 地区代码转换名称
     */
    public static class DistrictToNameMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return null;
            }
            String code = recordValue.toString();
            if (StringUtils.isEmpty(code)) {
                return null;
            }
            DistrictListDto districtDto = BeanUtil.getBean(DictService.class).getDistrictByCode(code);
            return districtDto == null ? null : districtDto.getName();
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * userId转换为用户卡片数组
     */
    public static class UserIdToUserCardMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            List<Long> userIds = JsonUtil.parseArray((String) recordValue, Long.class);
            if (userIds == null || userIds.isEmpty()) {
                return Collections.emptyList();
            }
            List<UserDto> users = BeanUtil.getBean(PermissionService.class).getUserListById(userIds);
            return users.stream().map(UserCardVO::of).collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * userId转换为单个用户卡片
     */
    public static class UserIdToSingleUserCardMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null || "0".equals(recordValue.toString())) {
                return null;
            }
            UserDto user = BeanUtil.getBean(PermissionService.class)
                    .getUserById(Long.parseLong(recordValue.toString()));
            return UserCardVO.of(user);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 档案详情页列表来源(手动录入等)
     */
    public static class SourceToDescriptionMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return ListSourceTypeEnum.MANUAL.getDescription();
            } else {
                ListSourceVO sourceVO = JsonUtil.parseObject(recordValue.toString(), ListSourceVO.class);
                assert sourceVO != null;
                if (sourceVO.getType().equals(ListSourceTypeEnum.MONITOR)) {
                    MonitorInfoVO monitorInfoVO = BeanUtil.getBean(ControlService.class)
                            .getMonitorInfo(sourceVO.getRelatedId());
                    if (StringUtils.isEmpty(monitorInfoVO.getMonitorTitle())) {
                        return sourceVO.getType().getDescription().substring(0, sourceVO.getType().getDescription().indexOf("："));
                    }
                    return sourceVO.getType().getDescription() + monitorInfoVO.getMonitorTitle();
                } else if (sourceVO.getType() == ListSourceTypeEnum.RISK) {
                    RiskService riskService = BeanUtil.getBean(RiskService.class);
                    String riskTitle = riskService.getRiskTitle(sourceVO.getRelatedId());
                    if (StringUtils.isEmpty(riskTitle)) {
                        return sourceVO.getType().getDescription().substring(0, sourceVO.getType().getDescription().indexOf("："));
                    }
                    return sourceVO.getType().getDescription() + riskTitle;
                } else {
                    return sourceVO.getType().getDescription();
                }
            }
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 地理信息类型
     */
    public static class MapLocationMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            Map<String, Object> result = new HashMap<>(3);
            MapLocationVO mapLocationVO = JsonUtil.parseSpecificObject(recordValue, MapLocationVO.class);
            if (mapLocationVO == null) {
                return null;
            }
            PositionInfo positionInfo = mapLocationVO.getPositionInfo();
            Region region = mapLocationVO.getRegion();
            if (positionInfo != null) {
                result.put("name", positionInfo.getName());
                result.put("point", positionInfo.getPoint());
            }
            if (region != null) {
                result.put("id", region.getId());
                result.put("name", region.getName());
            }

            return result;
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 地理信息类型
     */
    public static class MapLocationStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            MapLocationVO mapLocationVO = JsonUtil.parseSpecificObject(recordValue, MapLocationVO.class);
            if (mapLocationVO == null) {
                return null;
            }
            PositionInfo positionInfo = mapLocationVO.getPositionInfo();
            Region region = mapLocationVO.getRegion();
            if (positionInfo != null) {
                return positionInfo.getName();
            } else if (region != null) {
                return region.getName();
            }

            return null;
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    private static class JwzhDictToName implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (Objects.isNull(recordValue)) {
                return null;
            }
            String code = recordValue.toString();
            if (StringUtils.isEmpty(code)) {
                return null;
            }
            DictService dictService = BeanUtil.getBean(DictService.class);
            JwzhDictVO jwzhDictEntity = dictService.getJwzhDictVO(dict.getType(), code);
            return Objects.nonNull(jwzhDictEntity) ? jwzhDictEntity.getCt() : null;
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    private static class UserIdCardToUserName implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (Objects.isNull(recordValue)) {
                return null;
            }
            String idNumber = recordValue.toString();
            if (StringUtils.isEmpty(idNumber)) {
                return null;
            }
            return BeanUtil.getBean(PermissionService.class).getUserByIdCard(idNumber).getRealName();
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 时间字符串生成简略时间
     */
    private static class StringToDateTimeMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (Objects.isNull(recordValue)) {
                return null;
            }
            return TimeUtil.getSimpleTime(LocalDateTime.parse(recordValue.toString(), TimeUtil.DEFAULT_TIME_PATTERN));
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 码表代码转换DictDto
     */
    private static class DictCodeToDictMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (Objects.isNull(recordValue)) {
                return null;
            }
            return BeanUtil.getBean(ProfileDictService.class)
                    .getSimpleDictByCodeAndType(Long.valueOf(recordValue.toString()), dict.getType());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    private static class CaseLabelArrayToName implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (Objects.isNull(recordValue)) {
                return Collections.emptyList();
            }
            List<String> caseLabel = JsonUtil.objectToArray(recordValue, String.class);
            return caseLabel.stream()
                    .map(code -> ProfileCaseLabelVO.analysisPath(code).stream().map(ProfileCaseLabelEntity::getName)
                            .collect(Collectors.toList()))
                    .collect(Collectors.toList());
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return null;
        }
    }

    /**
     * json数组长度
     */
    private static class JsonArrayLengthMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {

            if (Objects.isNull(recordValue)) {
                return 0;
            }
            List<Object> objects = JsonUtil.objectToArray(recordValue, Object.class);
            if (Objects.isNull(objects)) {
                return 0;
            }
            return objects.size();
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * json对象类型
     */
    public static class JsonToArrayMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (recordValue == null) {
                return Collections.emptyList();
            }
            return JsonUtil.parseArray(recordValue.toString(), Object.class);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return mappingDisplayValue(recordValue, dict);
        }
    }

    /**
     * 数组转字符串
     */
    private static class ArrayToStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (Objects.isNull(recordValue)) {
                return null;
            }
            return JsonUtil.objectToArray(recordValue, Object.class).stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }



    /**
     * 数字四舍五入转string
     */
    public static class NumberHalfAdjustToStringMapper implements Mapper {

        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            // 数值型数据如果为0时，会导致前端判断出错从而呈现为“--”
            // 所以数值型强制转换成string对象
            return Optional.ofNullable(recordValue)
                    .map(Object::toString)
                    .map(it -> new BigDecimal(it).setScale(0, RoundingMode.HALF_UP))
                    .map(it -> Long.toString(it.longValue()))
                    .orElse(null);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return recordValue;
        }
    }

    /**
     * 审批状态转换
     *
     * <AUTHOR>
     */
    public static class ApprovalCodeToName implements Mapper {
        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (Objects.isNull(recordValue)) {
                return null;
            }
            if ("NULL".equalsIgnoreCase(String.valueOf(recordValue))) {
                return null;
            }
            ApprovalStatusEnum statusEnum = ApprovalStatusEnum.codeOf(Integer.valueOf(String.valueOf(recordValue)));
            return Objects.isNull(statusEnum) ? null : statusEnum.getName();
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return mappingDisplayValue(recordValue, dict);
        }
    }

    /**
     * 归档转换
     *
     * <AUTHOR>
     */
    public static class ProfileStatusCodeToName implements Mapper {
        @Override
        public Object mappingDisplayValue(Object recordValue, DictProperty dict) {
            if (Objects.isNull(recordValue)) {
                return null;
            }
            if ("NULL".equalsIgnoreCase(String.valueOf(recordValue))) {
                return null;
            }
            Optional<ProfileStatus> statusEnum = ProfileStatus.ofCode(Integer.valueOf(String.valueOf(recordValue)));
            return statusEnum.map(ProfileStatus::getName).orElse(null);
        }

        @Override
        public Object mappingEditValue(Object recordValue, DictProperty dict) {
            return mappingDisplayValue(recordValue, dict);
        }
    }
}
