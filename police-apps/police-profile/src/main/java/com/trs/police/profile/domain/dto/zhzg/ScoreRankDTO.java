package com.trs.police.profile.domain.dto.zhzg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 积分计算职级信息DTO
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreRankDTO {

    /**
     * 任职开始时间
     */
    private LocalDate startTime;
    /**
     * 任职结束时间
     */
    private LocalDate endTime;
    /**
     * 任职序列，码表，type = police_rz_xl
     */
    private Integer rankSeries;
    /**
     * 职级，码表，type = police_zj
     */
    private Integer rank;
}
