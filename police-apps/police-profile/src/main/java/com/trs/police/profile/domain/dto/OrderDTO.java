package com.trs.police.profile.domain.dto;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.police.common.core.constant.SystemDefaultRoleConstant;
import com.trs.police.profile.constant.CommonConstants;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * OrderDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/10/26 18:02
 * @since 1.0
 */
@Data
public class OrderDTO extends BaseDTO {

    private String fieldName;

    private String orderType;

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getFieldName(), new ParamInvalidException("排序字段不能为空"));
        checkNotEmpty(getOrderType(), new ParamInvalidException("排序方式不能为空"));
        checkArgument(
            CommonConstants.ASC.equalsIgnoreCase(getOrderType())
                || CommonConstants.DESC.equalsIgnoreCase(getOrderType()),
            new ParamInvalidException("非法排序方式[" + getOrderType() + "]")
        );
        checkArgument(
            getFieldName().matches(SystemDefaultRoleConstant.ORDER_FIELD_REX),
            new ParamInvalidException("非法排序字段[" + getFieldName() + "]")
        );
        return true;
    }

    /**
     * 是否是升序
     *
     * @return 判断结果
     */
    public boolean isAsc() {
        return CommonConstants.ASC.equalsIgnoreCase(getOrderType());
    }

    /**
     * 是否是降序
     *
     * @return 判断结果
     */
    public boolean isDesc() {
        return !isAsc();
    }

    /**
     * 指定字段降序
     *
     * @param fieldName 字段名称
     * @return 排序对象
     */
    public static OrderDTO desc(String fieldName) {
        return of(fieldName, CommonConstants.DESC);
    }

    /**
     * 指定字段升序
     *
     * @param fieldName 字段名称
     * @return 排序对象
     */
    public static OrderDTO asc(String fieldName) {
        return of(fieldName, CommonConstants.ASC);
    }

    /**
     * 生成排序对象
     *
     * @param fieldName 字段
     * @param orderType 排序方式
     * @return 排序对象
     */
    public static OrderDTO of(String fieldName, String orderType) {
        OrderDTO dto = new OrderDTO();
        dto.setFieldName(fieldName);
        dto.setOrderType(orderType);
        return dto;
    }
}
