package com.trs.police.profile.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.json.serializer.SimpleTimeSerializer;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/8 09:57
 */
@Data
public class FootholdListVO {

    /**
     * 落脚点名称
     */
    private String name;

    /**
     * 逗留次数
     */
    private Long stayCount;

    /**
     * 时间
     */
    @JsonSerialize(using= SimpleTimeSerializer.class,nullsUsing = SimpleTimeSerializer.class)
    private LocalDateTime time;

    private String lon;

    private String lat;
}
