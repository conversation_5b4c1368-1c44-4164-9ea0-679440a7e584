package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.*;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

/**
 * 专业技术积分计算策略
 */
@Slf4j
@Component
public class RankScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "职务分值";
    private static final String RULE_TYPE = "RANK";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算{}积分，人员：{}，规则：{}", STRATEGY_NAME, personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取记录
            List<ScoreExpertisesDTO> expertises = personArchive.getExpertises();
            if (CollectionUtils.isEmpty(expertises)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无" + STRATEGY_NAME + "记录")
                        .build();
            }

            // 计算积分
            List<ScoreRankDTO> ranks = personArchive.getRanks();
            ScoreRankDTO firstRank = ranks.get(0);
            List<ScoreResumeDTO> resumes = personArchive.getResumes();
            Integer promotionStatus = personArchive.getPromotionStatus();
            RankType rankType = RankType.valueOf(rule.getRuleSubType());
            long months = 0;

            switch (rankType) {
                case A:
                    //任同级警员职务或警务技术职务（含未参加套改的同职级领导职务）每满1个月计0. 11分。
                    LocalDate startTime = firstRank.getStartTime();
                    LocalDate endTime = Objects.isNull(firstRank.getEndTime()) ? LocalDate.now() : firstRank.getEndTime();
                    months = ChronoUnit.MONTHS.between(startTime, endTime);
                    break;
                case B:
                case C:
                    //对现任或曾任科级领导职务的人员，晋升四级高级警长或警务技术四级主任给予加分：任副科级领导职务每满1个月加0.01分，任正科级领导职务每满1个月加0.02分。
                    int level = rankType == RankType.B ? 4 : 5;
                    if (promotionStatus == 1 && (firstRank.getRank() == 6 || firstRank.getRank() == 17)) {
                        months = resumes.stream().filter(dto -> dto.getPositionLevel() == level)
                                .map(dto -> {
                                    LocalDate start = dto.getStartTime();
                                    LocalDate end = Objects.isNull(dto.getEndTime()) ? LocalDate.now() : dto.getEndTime();
                                    return ChronoUnit.MONTHS.between(start, end);
                                }).reduce(0L, Long::sum);
                    }
                    break;
                default:
            }
            double calculatedScore = months * rule.getScore();

            // 应用规则配置的最大分值限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("满足%s条件：%d月", rankType.getName(), months);
            String calculateDescription = String.format("%d个月份满足%s条件 × %.3f分/条 = %.3f分",
                    months, rankType.getName(), rule.getScore(), calculatedScore);
            
            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.3f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算{}积分失败，人员：{}，规则：{}，错误：{}",
                    STRATEGY_NAME, personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Getter
    @AllArgsConstructor
    enum RankType {
        A("任同级警员职务或警务技术职务"),
        B("现任或曾任正科级领导职务的人员"),
        C("现任或曾任副科级领导职务的人员"),
        ;
        private final String name;
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return true;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
