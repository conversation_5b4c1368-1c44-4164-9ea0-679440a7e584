package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.*;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

/**
 * 专业技术积分计算策略
 */
@Slf4j
@Component
public class RankScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "任职年限得分";
    private static final String RULE_TYPE = "RANK";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算{}积分，人员：{}，规则：{}", STRATEGY_NAME, personArchive.getName(), rule.getName());
        RankType rankType = RankType.valueOf(rule.getRuleSubType());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage(rankType.getName() + "数据验证失败")
                        .build();
            }

            // 计算积分

            List<ScoreRankDTO> ranks = personArchive.getRanks();
            if (CollectionUtils.isEmpty(ranks)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription(rankType.getName() + "无职级信息记录")
                        .build();
            }

            //职务信息列表
            List<ScoreResumeDTO> resumes = personArchive.getResumes();
            //最新职务信息
            ScoreResumeDTO firstResume = null;
            if (CollectionUtils.isEmpty(resumes) && !rankType.equals(RankType.BASIC)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription(rankType.getName() + "无职务信息记录")
                        .build();
            }
            if (!rankType.equals(RankType.BASIC)) {
                firstResume = resumes.get(0);
            }
            //晋升状态，符合晋升：1
            Integer promotionStatus = personArchive.getPromotionStatus();
            if (Objects.isNull(promotionStatus)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription(rankType.getName() + "无晋升状态")
                        .build();
            }
            //考核信息
            List<ScoreNdkhDTO> assessments = personArchive.getAssessments();
            if (Objects.isNull(assessments)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription(rankType.getName() + "无考核信息记录")
                        .build();
            }

            //最新职级信息
            ScoreRankDTO firstRank = ranks.get(0);
            long rankYears = calculateRankYear(firstRank);
            long resumeYears = calculateResumeYear(firstResume);
            long years = RankType.BASIC.equals(rankType) ? rankYears : resumeYears;

            double calculatedScore = 0.0;
            switch (rankType) {
                case BASIC:
                    //晋升状态=符合晋升 && 按照最新的一条职级的开始工作年份到今天的所有年度考核记录中有称职及以上
                    if (checkBasic(promotionStatus, ranks, assessments)) {
                        calculatedScore += rankYears * rule.getScore();
                    } else {
                        rankYears = 0;
                    }
                    break;
                case A:
                    //当前是一级警长或一级主管 且 一级部门正职正科级领导职务
                    if (checkBasic(promotionStatus, ranks, assessments)
                            && checkFirstRank(firstRank, 6, 17)
                            && checkFirstResume(firstResume, 4, 1)) {
                        calculatedScore += resumeYears * rule.getScore();
                    } else {
                        resumeYears = 0;
                    }
                    break;
                case B:
                    //当前是一级警长或一级主管 且 一级部门副职正科级领导职务
                    if (checkBasic(promotionStatus, ranks, assessments)
                            && checkFirstRank(firstRank, 6, 17)
                            && checkFirstResume(firstResume, 4, 2)) {
                        calculatedScore += resumeYears * rule.getScore();
                    } else {
                        resumeYears = 0;
                    }
                    break;
                case C:
                    //当前是一级警长或一级主管 且 一级部门副职副科级领导职务、二级部门正职正科级领导职务
                    if (checkBasic(promotionStatus, ranks, assessments)
                            && checkFirstRank(firstRank, 6, 17)
                            && (checkFirstResume(firstResume, 5, 2)
                            || checkFirstResume(firstResume, 4, 3))) {
                        calculatedScore += resumeYears * rule.getScore();
                    } else {
                        resumeYears = 0;
                    }
                    break;
                case D:
                    //当前是一级警长或一级主管 且 二级部门正职副科级领导职务
                    if (checkBasic(promotionStatus, ranks, assessments)
                            && checkFirstRank(firstRank, 6, 17)
                            && checkFirstResume(firstResume, 5, 3)) {
                        calculatedScore += resumeYears * rule.getScore();
                    } else {
                        resumeYears = 0;
                    }
                    break;
                case E:
                    //当前是四级高级警长或四级主任且副县级领导职务
                    if (checkBasic(promotionStatus, ranks, assessments)
                            && checkFirstRank(firstRank, 5, 16)
                            && Objects.nonNull(firstResume) && firstResume.getPositionLevel() == 2) {
                        calculatedScore += resumeYears * rule.getScore();
                    } else {
                        resumeYears = 0;
                    }
                    break;
                case F:
                    //当前是四级高级警长或四级主任且一级部门正职正科级领导职务
                    if (checkBasic(promotionStatus, ranks, assessments)
                            && checkFirstRank(firstRank, 5, 16)
                            && checkFirstResume(firstResume, 4, 1)) {
                        calculatedScore += resumeYears * rule.getScore();
                    } else {
                        resumeYears = 0;
                    }
                    break;
                case G:
                    //当前是四级高级警长或四级主任且一级部门副职正科级领导职务
                    if (checkBasic(promotionStatus, ranks, assessments)
                            && checkFirstRank(firstRank, 5, 16)
                            && checkFirstResume(firstResume, 4, 2)) {
                        calculatedScore += resumeYears * rule.getScore();
                    } else {
                        resumeYears = 0;
                    }
                    break;
                case H:
                    //当前是四级高级警长或四级主任且一级部门副职副科级领导、二级部门正职正科级领导职务
                    if (checkBasic(promotionStatus, ranks, assessments)
                            && checkFirstRank(firstRank, 5, 16)
                            && (checkFirstResume(firstResume, 5, 2)
                            || checkFirstResume(firstResume, 4, 3))) {
                        calculatedScore += resumeYears * rule.getScore();
                    } else {
                        resumeYears = 0;
                    }
                    break;
                default:
                    break;
            }

            // 应用规则配置的最大分值限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("满足%s条件：%d年", rankType.getName(), years);
            String calculateDescription = String.format("%d个年份满足%s条件 × %.3f分/条 = %.3f分",
                    years, rankType.getName(), rule.getScore(), calculatedScore);

            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.3f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算{}积分失败，人员：{}，规则：{}，错误：{}",
                    STRATEGY_NAME, personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    /**
     * 检查基本分值条件是否符合
     *
     * @param promotionStatus 晋升状态
     * @param ranks 职级信息
     * @param assessments 考核信息
     * @return 是否符合
     */
    private boolean checkBasic(Integer promotionStatus, List<ScoreRankDTO> ranks, List<ScoreNdkhDTO> assessments) {
        //晋升状态=符合晋升 && 按照最新的一条职级的开始工作年份到今天的所有年度考核记录中有称职及以上
        if (CollectionUtils.isEmpty(ranks) || CollectionUtils.isEmpty(assessments)) {
            return false;
        }
        ScoreRankDTO scoreRankDTO = ranks.get(0);
        if (Objects.isNull(scoreRankDTO) || Objects.isNull(scoreRankDTO.getStartTime())) {
            return false;
        }
        LocalDate startTime = scoreRankDTO.getStartTime();
        return promotionStatus == 1 &&  assessments.stream().filter(dto -> dto.getAssessmentTime().isAfter(startTime))
                .allMatch(dto -> dto.getAssessmentResult() <= 2);
    }

    private static boolean checkFirstRank(ScoreRankDTO firstRank, int rank1, int rank2) {
        return Objects.nonNull(firstRank) && (firstRank.getRank() == rank1 || firstRank.getRank() == rank2);
    }

    private static boolean checkFirstResume(ScoreResumeDTO firstResume, int positionLevel, int personType) {
        return Objects.nonNull(firstResume) && firstResume.getPositionLevel() == positionLevel && firstResume.getPersonType() == personType;
    }

    private static long calculateMonth(ScoreRankDTO firstRank) {
        if (Objects.isNull(firstRank) || Objects.isNull(firstRank.getStartTime())) {
            return 0;
        }
        LocalDate startTime = firstRank.getStartTime();
        LocalDate endTime = Objects.isNull(firstRank.getEndTime()) ? LocalDate.now() : firstRank.getEndTime();
        return ChronoUnit.MONTHS.between(startTime, endTime);
    }

    private static long calculateRankYear(ScoreRankDTO firstRank) {
        if (Objects.isNull(firstRank) || Objects.isNull(firstRank.getStartTime())) {
            return 0;
        }
        LocalDate startTime = firstRank.getStartTime();
        LocalDate endTime = Objects.isNull(firstRank.getEndTime()) ? LocalDate.now() : firstRank.getEndTime();
        return ChronoUnit.YEARS.between(startTime, endTime);
    }

    private static long calculateResumeYear(ScoreResumeDTO firstResume) {
        if (Objects.isNull(firstResume) || Objects.isNull(firstResume.getStartTime())) {
            return 0;
        }
        LocalDate startTime = firstResume.getStartTime();
        LocalDate endTime = Objects.isNull(firstResume.getEndTime()) ? LocalDate.now() : firstResume.getEndTime();
        return ChronoUnit.YEARS.between(startTime, endTime);
    }

    @Getter
    @AllArgsConstructor
    enum RankType {
        BASIC("基础分值"),
        A("晋升四级高级警长、警务技术四级主任且一级部门正职正科级领导职务"),
        B("晋升四级高级警长、警务技术四级主任且一级部门副职正科级领导职务"),
        C("晋升四级高级警长、警务技术四级主任且一级部门副职副科级领导职务、二级部门正职正科级领导职务"),
        D("晋升四级高级警长、警务技术四级主任且二级部门正职副科级领导职务"),

        E("晋升三级高级警长、警务技术三级主任且副县级领导职务"),
        F("晋升三级高级警长、警务技术三级主任且一级部门正职正科级领导职务"),
        G("晋升三级高级警长、警务技术三级主任且一级部门副职正科级领导职务"),
        H("晋升三级高级警长、警务技术三级主任且一级部门副职副科级领导、二级部门正职正科级领导职务"),
        ;
        private final String name;
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return true;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
