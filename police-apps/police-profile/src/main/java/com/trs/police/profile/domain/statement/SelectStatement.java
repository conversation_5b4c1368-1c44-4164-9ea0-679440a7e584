package com.trs.police.profile.domain.statement;

import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SelectStatement extends BaseStatement {

    String keyName;

    Object keyValue;
    /**
     * 运算符
     */
    String operators = "=";

    @Override
    public String toSql() {
        return String.format("select %s from %s where %s %s %s", getColumnNames(), table, keyName, operators, keyValue);
    }

    private String getColumnNames() {
        return fields.stream().map(FieldStatement::getColumn).collect(Collectors.joining(","));
    }
}
