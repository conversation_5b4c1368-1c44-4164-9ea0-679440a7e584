package com.trs.police.profile.controller;

import com.trs.police.profile.domain.dto.ProfileGroupPoliceControlDto;
import com.trs.police.profile.domain.vo.ProfileGroupPoliceControlVo;
import com.trs.police.profile.service.GroupPoliceControlService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 档案-群体GA管控信息
 *
 * <AUTHOR>
 * @date 2024/02/27
 */
@RestController
@RequestMapping("/groupPoliceControl")
public class GroupPoliceControlController {

    @Resource
    private GroupPoliceControlService groupPoliceControlService;

    /**
     * 新增/编辑GA管控信息
     *
     * @param controlDto 管控信息
     */
    @PostMapping("/save")
    public void save(@RequestBody ProfileGroupPoliceControlDto controlDto) {
        groupPoliceControlService.saveControlInfo(controlDto);
    }

    /**
     * 新增/编辑GA管控信息
     *
     * @param controlDtos 管控信息集合
     */
    @PostMapping("/batchSave")
    public void batchSave(@RequestBody List<ProfileGroupPoliceControlDto> controlDtos) {
        groupPoliceControlService.batchSaveControlInfo(controlDtos);
    }

    /**
     * 列表查询
     *
     * @param groupId 群体档案id
     * @param policeKind 管控类型
     * @return 列表
     */
    @GetMapping("queryList")
    public List<ProfileGroupPoliceControlVo> queryList(Long groupId,Long policeKind) {
        return groupPoliceControlService.getControlInfoByGroupId(groupId, policeKind);
    }

    /**
     * 获取管控信息详情
     *
     * @param id id
     * @return 列表
     */
    @GetMapping("detail")
    public ProfileGroupPoliceControlVo detail(Long id) {
        return groupPoliceControlService.getControlInfoById(id);
    }

    /**
     * 删除公安管控信息
     *
     * @param id 公安管控信息id
     * @param moduleId 模块id
     */
    @GetMapping("del")
    public void del(Long id, Long moduleId) {
        groupPoliceControlService.delControlInfo(id, moduleId);
    }

}
