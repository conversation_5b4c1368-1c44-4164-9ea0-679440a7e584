package com.trs.police.profile.mapper.person;

import com.trs.police.profile.domain.entity.person.PersonDzControl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 人员-党政管控信息mapper
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Mapper
public interface PersonDzControlMapper extends PersonRelatedBaseMapper<PersonDzControl> {

    /**
     * 根据人员id查询
     *
     * @param personId   人员id
     * @return 结果
     */
    @Override
    @Select("select * from t_profile_person_dz_control where person_id = #{personId}")
    PersonDzControl selectByPersonId(@Param("personId") Long personId);
}
