package com.trs.police.profile.service;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.fight.CompositeListVO;
import com.trs.police.common.core.vo.profile.SeriesCaseListVO;
import com.trs.police.profile.domain.entity.ProfileCaseLabelEntity;
import com.trs.police.profile.domain.vo.*;
import org.springframework.web.bind.annotation.PathVariable;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/31 11:20
 */
public interface CaseService {

    /**
     * 更新案件标签表path
     */
    void updateCaseLabelPath();

    /**
     * 获取案件标签
     *
     * @param id 案件档案id
     * @return {@link ProfileCaseLabelVO}
     */
    ProfileCaseLabelVO getProfileCaseLabelBasicValue(Long id);

    /**
     * 查询所有案件标签
     *
     * @param type 标签类型
     * @return {@link StringCodeNameVO}
     */
    List<StringCodeNameVO> getCaseLabelByType(String type);

    /**
     * 获取案件标签编辑回显数据
     *
     * @param id 案件档案id
     * @return {@link ProfileCaseLabelVO}
     */
    ProfileCaseLabelVO getProfileCaseLabelEditValue(Long id);

    /**
     * 编辑案件标签
     *
     * @param vo {@link ProfileCaseLabelVO}
     */
    void updateCaseLabel(ProfileCaseLabelVO vo);

    /**
     * 查询案件警综详情
     *
     * @param id 案件id
     * @return 警综详情
     */
    CaseJzDetailVO getJzDetail(Long id);

    /**
     * 获取案件涉案人员列表
     *
     * @param id 案件档案列表
     * @return {ProfileCaseRelatedPersonListVO}
     */
    List<ProfileCaseRelatedPersonListVO> getProfileCaseRelatedPerson(Long id);

    /**
     * 新增涉案人员信息
     *
     * @param caseId 案件档案id
     * @param vo     涉案人员信息
     */
    void addProfileCaseRelatedPerson(Long caseId, ProfileCaseRelatedPersonVO vo);

    /**
     * 更新涉案人员信息
     *
     * @param vo 涉案人员信息
     */
    void updateProfileCaseRelatedPerson(ProfileCaseRelatedPersonVO vo);

    /**
     * 涉案人信息回显
     *
     * @param personId 涉案人id
     * @return {@link ProfileCaseRelatedPersonVO}
     */
    ProfileCaseRelatedPersonVO getProfileCaseRelatedPersonEditValue(Long personId);

    /**
     * 删除涉案人员与案件关系
     *
     * @param caseId   案件id
     * @param personId 人员id
     */
    void deleteProfileCaseRelatedPerson(Long caseId, Long personId);

    /**
     * 案件关联嫌疑人
     *
     * @param caseId 案件id
     * @return {@link CaseSuspectListVO}
     */
    List<CaseSuspectListVO> getCaseSuspectList(Long caseId);

    /**
     * 关联嫌疑人
     *
     * @param caseId 案件id
     * @param vo     嫌疑人
     */
    void relatedCaseSuspect(Long caseId, List<ProfileCaseRelatedSuspectVO> vo);

    /**
     * @param caseId 案件id
     * @return {@link SeriesCaseListVO}
     */
    List<SeriesCaseListVO> getSeriesCaseList(Long caseId);

    /**
     * 查询 案件档案列表
     *
     * @param request 请求参数
     * @return {@link CaseListVO}
     */
    PageResult<CaseListVO> getCaseList(ListParamsRequest request);

    /**
     * 新版查询案件列表
     *
     * @param request 请求参数
     * @return 案件列表信息
     */
    PageResult<NewCaseListVO> caseListV1(ListParamsRequest request);

    /**
     * 增加串案
     *
     * @param caseId        案件id
     * @param seriesCaseIds 串案id
     */
    void addSeriesCase(Long caseId, List<Long> seriesCaseIds);

    /**
     * 串案取消关联
     *
     * @param caseId       案件id
     * @param seriesCaseId 串案Id
     */
    void deleteSeriesCase(Long caseId, Long seriesCaseId);


    /**
     * 通过type查询
     *
     * @param code code
     * @return 实体
     */
    ProfileCaseLabelEntity findByCode(String code);

    /**
     * 获取案件卡片
     *
     * @param asjbh 案件编号
     * @return 案件卡片
     */
    CaseCardVO getCard(String asjbh);

    /**
     * 从jwzh表导入案件数据
     */
    void importCase();

    /**
     * 案件关联合成
     *
     * @param caseId 案件id
     * @return {@link CompositeListVO}
     */
    List<CompositeListVO> getRelatedCompositeList(Long caseId);

    /**
     * 根据编号获取id
     *
     * @param bh 编号
     * @return id
     */
    String getIdByBh(@PathVariable("bh") String bh);

    /**
     * 根据id获取编号
     *
     * @param id id
     * @return bh
     */
    String getBhById(@PathVariable("id") String id);

    /**
     * 涉案线索新增
     *
     * @param vo 涉案线索vo
     */
    void addClue(ProfileCaseClueVO vo);

    /**
     * 涉案线索编辑
     *
     * @param vo 涉案线索vo
     */
    void updateClue(ProfileCaseClueVO vo);

    /**
     * 涉案线索删除
     *
     * @param clueId 涉案线索id
     */
    void deleteClue(Long clueId);

    /**
     * 涉案线索list
     *
     * @param caseId 案件id
     * @return {@link ProfileCaseClueVO}
     */
    List<ProfileCaseClueVO> getClueList(Long caseId);

    /**
     * 案件详情
     *
     * @param caseId 案件id
     * @return {@link CaseListVO}
     */
    CaseListVO getCaseDetail(Long caseId);

    /**
     * 根据案件类别获取案事件id
     *
     * @param caseType 案件类型
     * @return {@link List}<{@link Long}>
     */
    List<Long> getCaseEventIdsByCaseType(String caseType);

    /**
     *  下载案件档案
     *
     * @param response response
     * @param caseId 案件id
     */
    void downloadCaseRecord(HttpServletResponse response, Long caseId);

    /**
     * 根据案件编号是否存在同步数据
     *
     */
    void synCaseEvent();
}
