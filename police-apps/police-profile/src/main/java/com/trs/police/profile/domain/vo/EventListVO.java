package com.trs.police.profile.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 事件列表vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class EventListVO implements Serializable {

    /**
     * 事件基本信息
     */
    private EventVO baseInfo;

    /**
     * 大屏的信息
     */
    private CaseScreenInfo screenInfo;

    /**
     * 详细信息
     */
    private EventDetailVO detail;

    public EventListVO(EventVO baseInfo) {
        this.baseInfo = baseInfo;
        if (Objects.nonNull(baseInfo.getRelatedTime())) {
            this.baseInfo.setRelatedTimeNew(baseInfo.getRelatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
    }
}
