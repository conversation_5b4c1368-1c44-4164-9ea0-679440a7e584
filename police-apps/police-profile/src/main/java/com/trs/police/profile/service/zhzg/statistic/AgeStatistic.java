package com.trs.police.profile.service.zhzg.statistic;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.profile.constant.enums.PoliceAgeRangeEnum;
import com.trs.police.profile.domain.dto.zhzg.HzBigScreenDto;
import com.trs.police.profile.mapper.zhzg.ProfilePoliceMapper;
import com.trs.police.profile.service.secne.ProfileStatisticScene;
import com.trs.police.statistic.domain.bean.CountItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * xx
 */
@Service
public class AgeStatistic implements ProfileStatisticScene<CountItem, HzBigScreenDto> {

    @Autowired
    private DictService dictService;

    @Autowired
    private ProfilePoliceMapper profilePoliceMapper;

    @Override
    public List<CountItem> search(HzBigScreenDto hzBigScreenDto) {
        if (StringUtils.isNotEmpty(hzBigScreenDto.getAreaCode())){
            hzBigScreenDto.setAreaCode(AreaUtils.areaPrefix(hzBigScreenDto.getAreaCode()));
        }
        List<DictDto> dictList = dictService.getDictByType("police_age_range");
        if (CollectionUtils.isEmpty(dictList)){
            return new ArrayList<>();
        }
        Map<Long, String> dictMap = CollectionUtils.isEmpty(dictList) ? new HashMap<>()
                : dictList.stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName));
        List<CountItem> resultList = profilePoliceMapper.statisticAge(hzBigScreenDto);
        List<CountItem> list = new ArrayList<>();
        for (DictDto dictDto : dictList) {
            CountItem countItem = new CountItem();
            countItem.setName(dictDto.getName());
            countItem.setShowName(dictDto.getName());
            countItem.setKey(String.valueOf(dictDto.getCode()));
            PoliceAgeRangeEnum policeAgeRangeEnum = PoliceAgeRangeEnum.codeOf(Integer.valueOf(dictDto.getCode().intValue()));
            if (policeAgeRangeEnum.getStart() == null){
                long sum = resultList.stream().filter(e -> Integer.valueOf(e.getKey()).compareTo(policeAgeRangeEnum.getEnd()) <= 0)
                        .mapToLong(CountItem::getCount).sum();
                countItem.setCount(sum);
            }else if (policeAgeRangeEnum.getEnd() == null){
                long sum = resultList.stream().filter(e -> Integer.valueOf(e.getKey()).compareTo(policeAgeRangeEnum.getStart()) > 0)
                        .mapToLong(CountItem::getCount).sum();
                countItem.setCount(sum);
            }else {
                long sum = resultList.stream().filter(e -> Integer.valueOf(e.getKey()).compareTo(policeAgeRangeEnum.getStart()) > 0
                        && Integer.valueOf(e.getKey()).compareTo(policeAgeRangeEnum.getEnd()) <= 0)
                        .mapToLong(CountItem::getCount).sum();
                countItem.setCount(sum);
            }
            list.add(countItem);
        }
        return list;
    }

    @Override
    public String key() {
        return "ageStatistic";
    }

    @Override
    public String desc() {
        return "年龄结构";
    }
}
