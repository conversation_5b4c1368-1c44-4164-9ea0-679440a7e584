package com.trs.police.profile.schema.field;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.common.core.vo.JwzhDictVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.profile.domain.vo.CaseSuspectListVO;
import com.trs.police.profile.mapper.LabelMapper;
import com.trs.police.profile.mapper.NewJqMapper;
import com.trs.police.profile.mapper.ProfileCaseRelatedSuspectMapper;
import com.trs.police.profile.service.ProfileDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 字典赋值
 *
 * <AUTHOR>
 * @date 2024/7/31
 */
@Slf4j
public class ListMapperFactory {
    public static final String DICT_CODE_TO_NAME = "dictCodeToName";
    public static final String DICT_DESC_TO_NAME = "dictDescToName";
    public static final String JWZH_DICT_TO_NAME = "jwzhDictToName";
    public static final String DISTRICT_TO_NAME = "districtToName";
    public static final String ID_NUMBER_TO_NAME = "idNumberToName";
    public static final String DEPT_CODE_TO_NAME = "deptCodeToName";
    public static final String CASE_SUSPECT_COUNT = "CaseSuspectCount";
    public static final String CASE_SUSPECT_PERSON = "caseSuspectPerson";
    public static final String RELATED_RISK_COUNT = "relatedRiskCount";
    public static final String PERSON_LABEL_NAME_TO_ID = "personLabelNameToId";
    public static final String DISTRICT_NAME_TO_CODE = "districtNameToCode";
    public static final String LABEL_ID_TO_NAME = "labelIdToName";
    public static final String USER_ID_TO_NAME = "userIdToName";

    public ListMapperFactory() {
    }

    private static final Map<String, ListMapperFactory.Mapper> MAPPERS = new HashMap<>();

    static {
        MAPPERS.put(DICT_CODE_TO_NAME, new DictCodeToNameMapper());
        MAPPERS.put(DICT_DESC_TO_NAME, new DictDescToNameMapper());
        MAPPERS.put(JWZH_DICT_TO_NAME, new JwzhDictToNameMapper());
        MAPPERS.put(DISTRICT_TO_NAME, new DistrictToNameMapper());
        MAPPERS.put(ID_NUMBER_TO_NAME, new IdNumberToNameMapper());
        MAPPERS.put(DEPT_CODE_TO_NAME, new DeptCodeToNameMapper());
        MAPPERS.put(CASE_SUSPECT_COUNT, new CaseSuspectCountMapper());
        MAPPERS.put(CASE_SUSPECT_PERSON, new CaseSuspectPersonMapper());
        MAPPERS.put(RELATED_RISK_COUNT, new RelatedRiskCountMapper());
        MAPPERS.put(PERSON_LABEL_NAME_TO_ID, new PersonLabelNameToIdMapper());
        MAPPERS.put(DISTRICT_NAME_TO_CODE, new DistrictNameToCodeMapper());
        MAPPERS.put(LABEL_ID_TO_NAME, new LabelIdToNameMapper());
        MAPPERS.put(USER_ID_TO_NAME, new UserIdToNameMapper());
    }


    /**
     * 工厂方法
     *
     * @param mapping mapping方式
     * @return {@link MapperFactory.Mapper}
     */
    public static Mapper getMapper(String mapping) {
        return MAPPERS.getOrDefault(mapping, new ToStringMapper());
    }

    /**
     * 列表字段数据库与展示值之间的映射
     */
    public interface Mapper {

        /**
         * 映射字典（支持key为List）
         *
         * @param records 记录
         * @param dict 字典
         * @param getKey 获取key
         * @param setValue 设置value
         * @param <T> 类型
         * @param <K> key类型
         */
        default <T, K> void mappingDisplayValueWithListKey(List<T> records, DictProperty dict, Function<T, List<K>> getKey, BiConsumer<T, String> setValue) {
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            List<Object> keys = records.stream()
                    .map(record->getKey.apply(record))
                    .filter(o -> Objects.nonNull(o) && !o.toString().isEmpty())
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(keys)) {
                return;
            }
            Map<Object, Object> keyValueMap = mappingDisplayValue(keys, dict);
            records.forEach(record -> {
                List<K> keyList = getKey.apply(record);
                if (!CollectionUtils.isEmpty(keyList)) {
                    setValue.accept(record, keyList.stream()
                            .map(keyValueMap::get)
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .collect(Collectors.joining(",")));
                }
            });
        }

        /**
         * 映射字典
         *
         * @param records 记录
         * @param dict 字典
         * @param getKey 获取key
         * @param setValue 设置value
         * @param <T> 类型
         * @param <K> key类型
         * @param <V> value类型
         */
        default <T, K, V> void mappingDisplayValue(List<T> records, DictProperty dict, Function<T, K> getKey, BiConsumer<T, V> setValue) {
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            List<Object> keys = records.stream()
                    .map(getKey)
                    .filter(o -> Objects.nonNull(o) && !o.toString().isEmpty())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(keys)) {
                return;
            }
            Map<Object, Object> keyValueMap = mappingDisplayValue(keys, dict);
            records.forEach(record -> {
                Object key = getKey.apply(record);
                if (key != null) {
                    setValue.accept(record, (V) keyValueMap.get(key));
                }
            });
        }

        /**
         * 映射字典
         *
         * @param keys key列表
         * @param dict 字典
         * @return 字典map
         */
        Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict);
    }

    /**
     * 默认的映射器
     */
    public static class ToStringMapper implements Mapper {
        @Override
        public <T,K,V> void mappingDisplayValue(List<T> records, DictProperty dict, Function<T, K> getKey, BiConsumer<T, V> setValue) {
            records.forEach(record -> {
                Object key = getKey.apply(record);
                if (key != null) {
                    setValue.accept(record, (V) String.valueOf(key));
                } else {
                    setValue.accept(record, null);
                }
            });
        }

        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            return null;
        }
    }

   /**
     * 字典转换为map
     *
     * @param dicts 字典列表
     * @param getKey 获取key
     * @param getValue 获取value
     * @param <T> 类型
     * @param <K> key类型
     * @param <V> value类型
     * @return map
     */
    public static <T,K,V> Map<Object, Object> dictToMap(List<T> dicts, Function<T, K> getKey, Function<T, V> getValue) {
        if(CollectionUtils.isEmpty(dicts)){
            return new HashMap<>();
        }
        return dicts.stream().collect(Collectors.toMap(getKey, getValue, (v1, v2) -> v1));
    }

    /**
     * 字典code转换为字典名称
     */
    public static class DictCodeToNameMapper implements Mapper{
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<DictDto> dictDtoList = BeanUtil.getBean(ProfileDictService.class).getDictListByTypeAndCodes(dict.getType(), keys);
            return dictToMap(dictDtoList, DictDto::getCode, DictDto::getName);
        }
    }

    /**
     * 字典描述转换为字典名称
     */
    public static class DictDescToNameMapper implements Mapper{
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<DictDto> dictDtoList = BeanUtil.getBean(ProfileDictService.class).getDictListByTypeAndDictDescs(dict.getType(), keys);
            return dictToMap(dictDtoList, DictDto::getDictDesc, DictDto::getName);
        }
    }

    /**
     * jz字典code转换为字典名称
     */
    public static class JwzhDictToNameMapper implements Mapper{
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<JwzhDictVO> dictDtoList = BeanUtil.getBean(DictService.class).getJwzhDictVOs(dict.getType(), keys);
            return dictToMap(dictDtoList, JwzhDictVO::getDm, JwzhDictVO::getCt);
        }
    }

    /**
     * 区域名称
     */
    public static class DistrictToNameMapper implements Mapper {
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<DistrictDto> dictDtoList = BeanUtil.getBean(DictService.class).getByDistrictCodes(keys);
            return dictToMap(dictDtoList, DistrictDto::getCode, DistrictDto::getName);
        }
    }

    /**
     * 身份证号码转换为用户姓名
     */
    public static class IdNumberToNameMapper implements Mapper {
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<UserDto> dictDtoList = BeanUtil.getBean(PermissionService.class).getUserByIdCards(keys);
            return dictToMap(dictDtoList, UserDto::getIdNumber, UserDto::getRealName);
        }
    }

    /**
     * 部门code转换为部门名称
     */
    public static class DeptCodeToNameMapper implements Mapper {
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<DeptDto> dictDtoList = BeanUtil.getBean(PermissionService.class).getDeptByCodes(keys);
            return dictToMap(dictDtoList, DeptDto::getCode, DeptDto::getShortName);
        }
    }

    /**
     * 用户id转换为用户名称
     */
    public static class UserIdToNameMapper implements Mapper {
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<UserDto> dictDtoList = BeanUtil.getBean(PermissionService.class).getUserListById(keys);
            return dictToMap(dictDtoList, UserDto::getId, UserDto::getRealName);
        }
    }

    /**
     * 获取案件嫌疑人数量
     */
    public static class CaseSuspectCountMapper implements Mapper {
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<IdNameCountVO> dictDtoList = BeanUtil.getBean(ProfileCaseRelatedSuspectMapper.class).caseSuspectCount(keys);
            return dictToMap(dictDtoList, IdNameCountVO::getId, IdNameCountVO::getCount);
        }
    }

    /**
     * 获取案件嫌疑人
     */
    public static class CaseSuspectPersonMapper implements Mapper {

        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<CaseSuspectListVO> caseSuspectList = BeanUtil.getBean(ProfileCaseRelatedSuspectMapper.class).getCaseSuspectList(keys);
            return (Map) caseSuspectList.stream().collect(Collectors.groupingBy(CaseSuspectListVO::getAsjbh));
        }
    }

    /**
     * 关联风险数量
     *
     */
    public static class RelatedRiskCountMapper implements Mapper {
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<IdNameCountVO> list = BeanUtil.getBean(NewJqMapper.class).relatedRiskCount(keys);
            return dictToMap(list, IdNameCountVO::getName, IdNameCountVO::getCount);
        }
    }

    /**
     * 人员标签名称换为id
     */
    public static class PersonLabelNameToIdMapper implements Mapper{
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<Label> labelList = BeanUtil.getBean(LabelMapper.class).selectLabelByNames(keys, dict.getType());
            return dictToMap(labelList, Label::getName, Label::getId);
        }
    }

    /**
     * 人员标签名称换为id
     */
    public static class LabelIdToNameMapper implements Mapper{
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<Label> labelList = BeanUtil.getBean(LabelMapper.class).selectBatchIds(keys);
            return dictToMap(labelList, Label::getId, Label::getName);
        }
    }

    /**
     * 区域名称换为编码
     */
    public static class DistrictNameToCodeMapper implements Mapper{
        @Override
        public Map<Object, Object> mappingDisplayValue(List keys, DictProperty dict) {
            List<DistrictDto> list = BeanUtil.getBean(DictService.class).getByDistrictNames(keys);
            return dictToMap(list, DistrictDto::getName, DistrictDto::getCode);
        }
    }
}
