package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 警员履历表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_resume_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceResumeRelation extends AbstractBaseEntity {

    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;
    /**
     * 任职开始时间
     */
    private Date startTime;
    /**
     * 任职结束时间，若勾选“至今”，则可为空
     */
    private Date endTime;
    /**
     * 任职职务，码表，type = police_ll_rzzw
     */
    private Integer position;
    /**
     * 任职职务级别，码表，type = police_ll_rzzwjb
     */
    private Integer positionLevel;
    /**
     * 任职部门（如xxx支队xxx大队）
     */
    private String department;
    /**
     * 人员类别 type=police_ll_person_type
     */
    private Integer personType;
    /**
     * 是否删除
     */
    private Boolean deleted;
}

