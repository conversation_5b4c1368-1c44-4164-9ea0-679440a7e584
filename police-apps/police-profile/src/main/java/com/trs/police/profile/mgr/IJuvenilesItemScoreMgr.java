package com.trs.police.profile.mgr;

import com.trs.police.profile.domain.entity.es.DwsWcnPoints;
import com.trs.police.profile.domain.vo.JuvenilesScoreVo;
import com.trs.web.builder.base.IKey;

import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/24 17:59
 * @since 1.0
 */
public interface IJuvenilesItemScoreMgr extends IKey {

    /**
     * firstCategory<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 17:59
     */
    String firstCategory();


    /**
     * secondCategory<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 17:59
     */
    String secondCategory();

    /**
     * classFilterShowName<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/31 09:28
     */
    default String classFilterShowName() {
        return secondCategory();
    }

    /**
     * makeDetailItem<BR>
     *
     * @param item 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:01
     */
    JuvenilesScoreVo.ScoreDetailVo makeDetailItem(Optional<DwsWcnPoints> item);

    /**
     * makeDesc<BR>
     *
     * @param item 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:01
     */
    String makeDesc(Optional<DwsWcnPoints> item);

    /**
     * makeCount<BR>
     *
     * @param item 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:01
     */
    Long makeCount(Optional<DwsWcnPoints> item);

    /**
     * makeOneScore<BR>
     *
     * @param item 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:05
     */
    Long makeOneScore(Optional<DwsWcnPoints> item);

    /**
     * makeScore<BR>
     *
     * @param item 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:06
     */
    Long makeScore(Optional<DwsWcnPoints> item);

    /**
     * order<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/31 09:16
     */
    Integer order();
}
