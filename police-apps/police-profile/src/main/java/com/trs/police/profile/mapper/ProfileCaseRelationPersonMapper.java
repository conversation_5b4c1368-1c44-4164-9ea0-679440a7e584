package com.trs.police.profile.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.profile.domain.entity.ProfileCaseEntity;
import com.trs.police.profile.domain.entity.ProfileCaseRelationPersonEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/1 11:14
 */
@Mapper
public interface ProfileCaseRelationPersonMapper extends BaseMapper<ProfileCaseRelationPersonEntity> {

    /**
     * 根据人员id查询案件关联人员信息
     *
     * @param personId 人员id
     * @param policeKind 警种id
     * @return 结果
     */
    List<ProfileCaseEntity> selectByPersonIdAndPoliceKind(@Param("personId") Long personId, @Param("policeKind") Long policeKind);
}
