package com.trs.police.profile.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.trs.common.http2.HttpRequest;
import com.trs.police.common.core.mapper.DistrictMapper;
import com.trs.police.common.core.utils.OkHttpUtil;
import com.trs.police.profile.config.StInterfaceConfig;
import com.trs.police.profile.domain.vo.XsXzAjVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trs.police.common.core.utils.StringUtil.underlineToCamel;

/**
 * 南充警综接口工具类
 * *@author:tang.shuai
 * *@create 2024-06-025 15:35
 **/
@Slf4j
public class StRequestHelper {

    @Autowired
    private DistrictMapper districtMapper;

    private final StInterfaceConfig config;

    public StRequestHelper(StInterfaceConfig config) {
        this.config = config;
    }

    private final OkHttpUtil okHttpUtil = OkHttpUtil.getInstance();

    private final HttpRequest httpRequest = new HttpRequest.Builder().build();


    private final String xzUserCode = "S-************-0100-00156";

    private final String xsUserCode = "S-************-0100-00155";



    /**
     * token 缓存到内存中
     */
    private static Cache<String, Token> tokenCache;


    /**
     * 初始化缓存
     */
    @PostConstruct
    public void initLocalCache() {
        //过期时间  默认五个小时
        //注:鉴权接口6小时内调用一次即可，请勿每次请求都重新调用，否则可能导致调用性能下降问题。
        tokenCache = Caffeine.newBuilder()
                .expireAfterWrite(300L, TimeUnit.MINUTES)
                .maximumSize(10)
                .build();
    }

    /**
     * 获取st刑事案件-基本信息接口返回信息
     *
     * @param idCard 身份证号码
     * @return XsXzAjVO
     */
    public List<XsXzAjVO> requestXsAjBaseData(String idCard) {
        //2.封装请求体
        Map<String, String> headersMap = new HashMap<>();
        buildBaseHeaders(headersMap);
        headersMap.put("serviceId", config.getXsServerId());
        Map<String, Object> body = new HashMap<>(2);
        setBody(body, headersMap);
        body.put("condition",String.format("(ZBR_GMSFHM='%s')", idCard));
        String url = String.format("%s/%s", config.getUrl(), xsUserCode);
        //3.执行请求
        String result = okHttpUtil.postData(url, JSONObject.toJSONString(body), headersMap);
        //4.解析结构
        JSONObject jsonNode = JSON.parseObject(result);
        if (Objects.isNull(jsonNode) || !"405".equals(jsonNode.getString("status"))) {
            // 打印结果的前100个字符
            String result2 = result.length() > 100 ? result.substring(0, 50) + "..." : result;
            log.error("获取st刑事案件-基本信息接口返回信息返回异常,异常信息为:{}", result2);
            return null;
        }
        JSONObject data = jsonNode.getJSONObject("data");
        JSONArray results = data.getJSONArray("results");
        List<XsXzAjVO> voList = convertJsonArrayToList(results,
                jsonString -> JSON.parseObject(jsonString, XsXzAjVO.class));
        return voList;
    }



    /**
     * 获取st行政案件-基础信息接口返回信息
     *
     * @param idCard 身份证号码
     * @return XsXzAjVO
     */
    public List<XsXzAjVO> requestXzAjBaseData(String idCard) {
        //2.封装请求体
        Map<String, String> headersMap = new HashMap<>();
        buildBaseHeaders(headersMap);
        headersMap.put("serviceId", config.getXzServerId());
        Map<String, Object> body = new HashMap<>(2);
        setBody(body, headersMap);
        body.put("condition",String.format("(ZBR_GMSFHM='%s' OR XBR_GMSFHM = '%s')", idCard));
        String url = String.format("%s/%s", config.getUrl(), xzUserCode);
        //3.执行请求
        String result = okHttpUtil.postData(url, JSONObject.toJSONString(body), headersMap);
        //4.解析结构
        JSONObject jsonNode = JSON.parseObject(result);
        if (Objects.isNull(jsonNode) || !"405".equals(jsonNode.getString("status"))) {
            // 打印结果的前100个字符
            String result2 = result.length() > 100 ? result.substring(0, 50) + "..." : result;
            log.error("获取st案件-基础信息接口返回信息返回异常,异常信息为:{}", result2);
            return null;
        }
        JSONObject data = jsonNode.getJSONObject("data");
        JSONArray results = data.getJSONArray("results");
        List<XsXzAjVO> voList = convertJsonArrayToList(results,
                jsonString -> JSON.parseObject(jsonString, XsXzAjVO.class));
        return voList;
    }

    private void buildBaseHeaders(Map<String, String> headersMap) {
        headersMap.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headersMap.put("appToken", getToken().getAppToken());
        headersMap.put("userToken", getToken().getUserToken());
        headersMap.put("userId", config.getUserId());
        headersMap.put("policeNo", config.getPoliceNo());
        headersMap.put("subId", config.getSubId());
        headersMap.put("senderId", config.getSenderId());
        JSONObject userInfo = new JSONObject();
        userInfo.put("userId", config.getSenderId());
        headersMap.put("userInfo",userInfo.toJSONString());
        headersMap.put("groupId", config.getGroupId());
    }

    private void setBody(Map<String, Object> body, Map<String, String> headersMap) {
        String uuid = UUID.randomUUID().toString();
        JSONArray parafs = getParafs();
        body.put("messageSequence",uuid);
        body.put("orderParafs",new JSONObject());
        body.put("parafs",parafs);
        body.put("areaCode","************");
        body.put("maxReturnNum",1000);
    }


    private static JSONArray getParafs() {
        JSONArray parafs = new JSONArray();
        parafs.add("JYAQ");
        parafs.add("AJMC");
        parafs.add("FXDD_DZMC");
        parafs.add("AFDD_DZMC");
        parafs.add("AFDD_DQJD");
        parafs.add("AFDD_DQWD");
        parafs.add("BADW_GAJGMC");
        parafs.add("BADW_GAJGJGDM");
        parafs.add("SLSJ");
        parafs.add("AJBH");
        parafs.add("FXDD_XZQHDM");
        parafs.add("AFDD_XZQHDM");
        parafs.add("LRSJ");
        return parafs;
    }


    /**
     * 获取token
     *
     * @return Token
     */
    private Token getToken() {
        Token token = tokenCache.getIfPresent("token");
        if (Objects.nonNull(token) && token.getAppToken() != null && token.getUserToken() != null) {
            return token;
        }
        token = generateToken();
        tokenCache.put("token", token);
        return token;
    }

    /**
     * 生成token  token可重复使用，缓存6个小时
     *
     * @return Token
     */
    private Token generateToken() {
        //设置header
        Map<String, String> headersMap = new HashMap<>(1);
        headersMap.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        //组装到request中
        JSONObject userTokenObj = new JSONObject();
        userTokenObj.put("type", "user");
        userTokenObj.put("policeNo",config.getPoliceNo());
        userTokenObj.put("userId", config.getUserId());

        JSONObject appTokenObj = new JSONObject();
        appTokenObj.put("appId", config.getSubId());
        appTokenObj.put("userToken", userTokenObj.toJSONString());
        //设置表单请求体
        FormBody.Builder formBuilder = new FormBody.Builder();
        formBuilder.add("userToken", userTokenObj.toJSONString());
        formBuilder.add("appToken", appTokenObj.toJSONString());
        FormBody requestBody = formBuilder.build();

        //执行请求
        String url = config.getUrl();
        String result = "";
        try {
            // 创建一个RequestBody(参数1：数据类型 参数2传递的json串)
            log.info("获取token,请求的body={}", requestBody);
            result = httpRequest.doPost(url, requestBody);
        } catch (Exception e) {
            log.error("获取token发生异常,请求结果为:{}", result, e);
        }
        JSONObject jsonNode = JSONObject.parseObject(result);
        Token token = new Token();
        if ("500".equals(jsonNode.getString("status"))) {
            log.info("请求st获接口失败。获取token失败,省厅返回的结果为:{}", result);
            return null;
        }
        JSONObject appTokenId = jsonNode.getJSONObject("appTokenId");
        JSONObject userTokenId = jsonNode.getJSONObject("userTokenId");
        token.setAppToken(appTokenId.getString("appToken"));
        token.setUserToken(userTokenId.getString("userToken"));
        log.info("获取token成功,token为:{}", token);
        return token;
    }


    /**
     * 接口返回数据的转化
     *
     * @param jsonArray jsonArray
     * @param converter converter
     * @param <T> 结果列表中元素的类型
     * @return List
     */
    public static <T> List<T> convertJsonArrayToList(JSONArray jsonArray, Function<String, T> converter) {
        if (jsonArray == null) {
            return new ArrayList<>();
        }
        return jsonArray.stream()
                .map(object -> (JSONObject) object)
                .map(jsonObject -> {
                    Map<String, Object> camelCaseMap = new HashMap<>();
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        camelCaseMap.put(underlineToCamel(entry.getKey()), entry.getValue());
                    }
                    return converter.apply(JSON.toJSONString(camelCaseMap));
                })
                .collect(Collectors.toList());
    }

    /**
     * token
     */
    @Data
    private static class Token {
        /**
         * userToken
         */
        private String userToken;
        /**
         * appToken
         */
        private String appToken;
    }
}
