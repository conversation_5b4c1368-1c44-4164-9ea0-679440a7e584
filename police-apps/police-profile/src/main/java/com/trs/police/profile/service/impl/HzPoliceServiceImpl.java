package com.trs.police.profile.service.impl;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.profile.constant.enums.PoliceAgeRangeEnum;
import com.trs.police.profile.constant.enums.PoliceFuJsEnum;
import com.trs.police.profile.domain.dto.zhzg.HzPoliceDto;
import com.trs.police.profile.domain.dto.zhzg.HzPoliceExportDto;
import com.trs.police.profile.domain.vo.HzPoliceVO;
import com.trs.police.profile.mapper.zhzg.ProfilePoliceMapper;
import com.trs.police.profile.service.HzPoliceService;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 慧政 police service
 */
@Service
public class HzPoliceServiceImpl implements HzPoliceService {

    private static Map<Long,String> zfqwZjMap;

    private static Map<Long,String> rzzwMap;

    private static Map<Long,String> jszgMap;


    @Autowired
    private ProfilePoliceMapper profilePoliceMapper;

    @Autowired
    private DictService dictService;

    @Autowired
    private OssService ossService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 是否初始化了map
     */
    private static Boolean initiated = false;

    void init(){
        List<DictDto> zfqwList = dictService.getDictListByType("police_zj");
        List<DictDto> zjList = new ArrayList<>();
        for (DictDto dictDto : zfqwList) {
            zjList.addAll(dictDto.getChildren());
        }
        zfqwZjMap = CollectionUtils.isEmpty(zjList) ? new HashMap<>()
                : zjList.stream().collect(Collectors.toMap(DictDto::getCode,DictDto::getName));
        List<DictDto> rzzwList = dictService.getDictListByType("police_ll_rzzw");
        rzzwMap = CollectionUtils.isEmpty(rzzwList) ? new HashMap<>()
                : rzzwList.stream().collect(Collectors.toMap(DictDto::getCode,DictDto::getName));
        List<DictDto> jszgList = dictService.getDictListByType("police_emergency_status");
        jszgMap = CollectionUtils.isEmpty(jszgList) ? new HashMap<>()
                : jszgList.stream().collect(Collectors.toMap(DictDto::getCode,DictDto::getName));
        initiated = true;
    };
    @Override
    public RestfulResultsV2<HzPoliceVO> list(HzPoliceDto dto) {
        dealDto(dto);
        Page page = new Page(dto.getPageNum(),dto.getPageSize());
        IPage<HzPoliceVO> resultPage = profilePoliceMapper.selectPageList(page,dto);
        if (CollectionUtils.isEmpty(resultPage.getRecords())){
            return RestfulResultsV2.ok(new ArrayList<>()).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize())
                    .addTotalCount(0L);
        }
        if (!initiated){
            init();
        }
        //晋升资格list
        List<HzPoliceVO> records = resultPage.getRecords();
        List<Long> deptIds = new ArrayList<>();
        for (HzPoliceVO record : records) {
            deptIds.addAll(CollectionUtils.isEmpty(record.getDeptIds()) ? new ArrayList<>() : record.getDeptIds());
        }
        List<DeptDto> deptList = CollectionUtils.isEmpty(deptIds) ? new ArrayList<>()
                : permissionService.getDeptByIds(deptIds);
        Map<Long, String> deptMap = CollectionUtils.isEmpty(deptList) ? new HashMap<>()
                : deptList.stream().collect(Collectors.toMap(DeptDto::getId, DeptDto::getShortName));
        for (HzPoliceVO vo : records) {
            vo.setCurrentPosition(rzzwMap.get(vo.getCurrentPositionCode()));
            vo.setPromotionQualification(jszgMap.get(vo.getPromotionQualificationCode()));
            vo.setCurrentRank(zfqwZjMap.get(vo.getCurrentRankCode()));
            vo.setDeptNames(CollectionUtils.isEmpty(vo.getDeptIds()) ? new ArrayList<>()
                    : vo.getDeptIds().stream().map(e->deptMap.get(e)).collect(Collectors.toList()));
        }
        return RestfulResultsV2.ok(records).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize())
                .addTotalCount(resultPage.getTotal());
    }

    @Override
    public RestfulResultsV2<Boolean> export(HzPoliceExportDto dto, HttpServletResponse response) {
        String fileId = BeanFactoryHolder.getEnv().getProperty("com.trs.lzzhzg.downloadFileId");
        ResponseEntity<byte[]> entity = ossService.download(fileId);
        if (entity.getStatusCode().is2xxSuccessful()) {
            byte[] fileBytes = entity.getBody();
            try {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setContentLength(fileBytes.length);
                String encodedFileName = URLEncoder.encode("警员档案信息表", StandardCharsets.UTF_8.toString());
                response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName + ".xlsx");
                response.getOutputStream().write(fileBytes);
                response.getOutputStream().flush();
                return RestfulResultsV2.ok(true);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            // 处理下载失败的情况
            response.setStatus(entity.getStatusCode().value());
        }
        return RestfulResultsV2.ok(false);
    }


    private void dealDto(HzPoliceDto dto) {
        if (dto.getAgeRange() != null){
            PoliceAgeRangeEnum policeAgeRangeEnum = PoliceAgeRangeEnum.codeOf(dto.getAgeRange());
            dto.setStartAge(Objects.nonNull(policeAgeRangeEnum) ? policeAgeRangeEnum.getStart() : null);
            dto.setEndAge(Objects.nonNull(policeAgeRangeEnum) ? policeAgeRangeEnum.getEnd() : null);
        }
        if (dto.getJsZj() != null){
            PoliceFuJsEnum policeFuJsEnum = PoliceFuJsEnum.codeOf(dto.getJsZj());
            dto.setFhjsZjCode(policeFuJsEnum == null ? null : policeFuJsEnum.getFhjsCodes());
        }
    }
}
