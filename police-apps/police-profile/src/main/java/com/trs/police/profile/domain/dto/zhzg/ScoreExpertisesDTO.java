package com.trs.police.profile.domain.dto.zhzg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 积分计算专业技术DTO
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreExpertisesDTO {

    /**
     * 获取时间（对应界面“获取时间”）
     */
    private LocalDate acquisitionTime;
    /**
     * 专业技术名称, 码表, type= police_zyjs
     */
    private Integer technology;
    /**
     * 描述（对应界面“描述”输入的具体情况）
     */
    private String description;
    /**
     * 上传材料
     */
    private String materialPath;

    public ScoreExpertisesDTO(Integer technology) {
        this.technology = technology;
    }
}
