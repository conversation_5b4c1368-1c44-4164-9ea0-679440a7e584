package com.trs.police.profile.domain.dto.zhzg;

import com.trs.police.profile.domain.entity.zhzg.PoliceFamilyRelation;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 智慧政工人员档案DTO
 */

@Data
public class ZhzgPersonArchiveDTO {

    private Long id;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 证件号码
     */
    private String idNumber;

    /**
     * 证件类型：1：身份证，2：护照
     */
    private Integer idType;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 晋升状态，码表，type = police_jszt,符合晋升：1；不符合晋升：2；不得晋升：3；暂缓晋升：4
     */
    private Integer promotionStatus;

    /**
     * 曾用名
     */
    private String formerName;

    /**
     * 民族
     */
    private Integer nation;

    /**
     * 政治面貌
     */
    private Integer politicalStatus;

    /**
     * 婚姻状况
     */
    private Integer martialStatus;

    /**
     * 户籍地区域代码
     */
    private String registeredResidence;

    /**
     * 户籍地详细地址
     */
    private String registeredResidenceDetail;

    /**
     * 现住址区域代码
     */
    private String currentResidence;

    /**
     * 现住址详细地址
     */
    private String currentResidenceDetail;

    /**
     * 照片（JSON格式存储）
     */
    private String photo;

    /**
     * 所属部门（JSON格式存储）
     */
    private String deptIds;

    /**
     * 联系方式（JSON格式存储）
     */
    private String tel;

    /**
     * 家庭关系id（JSON格式存储）
     */
    private String familyRelationIds;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Boolean deleted;

    /**
     * 警号（警员特有字段）
     */
    private String policeNumber;

    /**
     * 籍贯（警员特有字段）
     */
    private String nativePlace;

    /**
     * 参加工作日期（警员特有字段）
     */
    private LocalDate joinWorkDate;

    /**
     * 参加公安工作日期（警员特有字段）
     */
    private LocalDate joinPublicSecurityWorkDate;

    /**
     * 职级信息
     */
    private List<ScoreRankDTO> ranks;

    /**
     * 履历信息
     */
    private List<ScoreResumeDTO> resumes;

    /**
     * 援藏援疆经历
     */
    private List<ScoreSupportTO> supports;

    /**
     * 教育经历
     */
    private List<ScoreEducationDTO> educations;

    /**
     * 家庭关系
     */
    private List<PoliceFamilyRelation> families;

    /**
     * 专业技术
     */
    private List<ScoreExpertisesDTO> expertises;

    /**
     * 立功受奖
     */
    private List<ScoreLgsjDTO> awards;

    /**
     * 违纪违规
     */
    private List<ScoreWgwjDTO> violations;

    /**
     * 年度考核
     */
    private List<ScoreNdkhDTO> assessments;

    /**
     * 民主测评
     */
    private List<ScoreEvaluationDTO> votes;
}
