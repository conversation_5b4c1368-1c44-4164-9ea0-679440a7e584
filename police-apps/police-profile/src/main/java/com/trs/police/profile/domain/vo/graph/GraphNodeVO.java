package com.trs.police.profile.domain.vo.graph;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 关系图谱节点VO
 *
 * <AUTHOR>
 * @date 2025/4/28
 * @param <T> 节点属性类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GraphNodeVO<T extends AbstractNodeProperties> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点类型编码
     */
    private String type;

    /**
     * 节点类型名称
     */
    private String typeName;

    /**
     * 节点属性
     */
    private T properties;
}
