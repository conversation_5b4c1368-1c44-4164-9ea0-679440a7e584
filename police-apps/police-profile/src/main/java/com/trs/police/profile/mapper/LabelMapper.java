package com.trs.police.profile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.common.core.entity.Label;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 档案标签
 *
 * <AUTHOR>
 */
@Mapper
public interface LabelMapper extends BaseMapper<Label> {

    /**
     * 依据模块和专题查找标签
     *
     * @param module 模块
     * @return 标签信息
     */
    @Select("select * from t_profile_label where module = #{module} and status = 1 order by show_order, id")
    List<Label> findByModuleAndSubjectId(@Param("module") String module);

    /**
     * 删除所以id不在列表中的
     *
     * @param ids    id列表
     * @param module 模块名称
     * @return 标签列表
     */
    @Select("select * from t_profile_label where module = #{module}")
    List<Label> findBySubjectIdAndModuleAndIdNotIn(@Param("module") String module, List<String> ids);

    /**
     * 查询标签树形结构
     *
     * @param module 模块
     * @return 标签树
     */
    List<Label> selectTree(@Param("module") String module);


    /**
     * 查询字树
     *
     * @param id 根节点id
     * @return 子树
     */
    List<Label> findAllByParentId(@Param("id") Long id);


    /**
     * 查询所有父节点
     *
     * @return 父节点
     */
    @Select("select * from t_profile_label where pid is null ")
    List<Label> findAllParent();

    /**
     * 获取风险细类
     *
     * @param module 模块
     * @param ids    标签id
     * @return 风险细类
     */
    String getFxxl(@Param("module") String module, @Param("ids") List<Long> ids);

    /**
     * 获取风险类别
     *
     * @param module 模块
     * @param ids    标签id
     * @return 风险类别
     */
    String getFxlb(@Param("module") String module, @Param("ids") List<Long> ids);

    /**
     * 根据id信息获取下级标签信息
     *
     * @param id 标签id
     * @return 所有下级标签信息
     */
    @Select("select * from t_profile_label where pid = #{id} and status = 1")
    List<Label> selectByPid(@Param("id") Long id);

    /**
     * 根据标签类型和名称获取标签详细内容
     *
     * @param type    标签类型
     * @param labelId 标签名称
     * @return 标签实体类
     */
    @Select("select * from t_profile_label where module = #{type} and id = #{labelId}")
    Label selectByIdAndType(@Param("type") String type, @Param("labelId") Long labelId);

    /**
     * 获取人档中使用指定标签的数据
     *
     * @param label 标签
     * @return 数据
     */
    @Select("select count(1) from t_profile_person where JSON_OVERLAPS(person_label,#{label})")
    long getPersonCountByLabel(String label);

    /**
     * 获取群档中使用指定标签的数据
     *
     * @param label 标签
     * @return 数据
     */
    @Select("select count(1) from t_profile_group where JSON_OVERLAPS(group_label,#{label})")
    long getGroupCountByLabel(String label);

    /**
     * 获取线索档案中使用指定标签的数据
     *
     * @param label 标签
     * @return 数据
     */
    @Select("select count(1) from t_profile_clue where JSON_OVERLAPS(clue_label,#{label})")
    long getClueCountByLabel(String label);

    /**
     * 获取事件档案中使用指定标签的数据
     *
     * @param label 标签
     * @return 数据
     */
    @Select("select count(1) from t_profile_event where JSON_OVERLAPS(event_label,#{label})")
    long getEventCountByLabel(String label);

    /**
     * 获取常控中使用指定标签的数据
     *
     * @param label 标签
     * @return 数据
     */
    @Select("select count(1) from t_control_regular_monitor where JSON_OVERLAPS(label_ids,#{label})")
    long getRegularCountByLabel(String label);
    /**
     * 根据名称获取标签
     *
     * @param label 标签
     * @return 数据
     */
    @Select("select * from t_profile_label where name = #{label.name} and pid = #{label.pid}")
    Label selectLabelByName(@Param("label") Label label);

    /**
     * 根据名称列表获取标签
     *
     * @param names 标签名称
     * @param module 模块
     * @return 数据
     */
    List<Label> selectLabelByNames(@Param("names") List<String> names, @Param("module") String module);
}
