package com.trs.police.profile.domain.vo.person;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.profile.domain.vo.ProfileExportListVO;
import lombok.Data;

import java.util.Date;

/**
 * 人员-12345记录
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
public class PersonHotlineVO extends ProfileExportListVO {
    /**
     * 受理编号
     */
    private String slbh;

    /**
     * 受理类型
     */
    private String sllx;

    /**
     * 受理内容
     */
    private String slnr;

    /**
     * 受理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date slsj;

    /**
     * 热点全称
     */
    private String rdmc;

    /**
     * 承办意见
     */
    private String cbyj;
}
