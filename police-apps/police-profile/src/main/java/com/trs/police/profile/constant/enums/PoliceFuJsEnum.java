package com.trs.police.profile.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 警员职级枚举
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Getter
@AllArgsConstructor
public enum PoliceFuJsEnum {

    YJJY(10,"一级警员",Arrays.asList(11),"",null, 2),
    SIJIJINGZHANG(9,"四级警长",Arrays.asList(10,21),"",null, 2),
    SANJIJINGZHANG(8,"三级警长",Arrays.asList(9,20),"",null, 2),
    EJJINGZHANG(7,"二级警长",Arrays.asList(8,19),"",null, 2),
    YIJIJINGZHANG(6,"一级警长",Arrays.asList(7,18),"",null, 2),
    SIJIHIGHJINGZHANG(5,"四级高级警长",Arrays.asList(6,17),"",null, 2),
    SANJIHIGHJINGZHANG(4,"三级高级警长",Arrays.asList(5,16),"",null, 2),
    ERJIHIGHJINGZHANG(3,"二级高级警长",Arrays.asList(4,15),"",null,2),
    YIJIHIGHJINGZHANG(2,"一级高级警长",Arrays.asList(3,14),"",null,3),
    SIJIZHUGUAN(20,"四级主管",Arrays.asList(21),"police_rz_qualifications",1,2),
    SANJIZHUGUAN(19,"三级主管",Arrays.asList(20),"police_rz_qualifications",2,2),
    ERJIZHUGUAN(18,"二级主管",Arrays.asList(19),"police_rz_qualifications",2,2),
    YIJIZHUGUAN(17,"一级主管",Arrays.asList(18),"police_rz_qualifications",2,2),
    SIJIZHUREN(16,"四级主任  ",Arrays.asList(17),"police_rz_qualifications",3,2),
    SANJIZHUREN(15,"三级主任",Arrays.asList(16),"police_rz_qualifications",3,2),
    ERJIZHUREN(14,"二级主任",Arrays.asList(15),"police_rz_qualifications",4,2),
    YIJIZHUREN(13,"一级主任",Arrays.asList(14),"police_rz_qualifications",4,3);

    /**
     * 职级代码
     */
    private final Integer code;

    /**
     * 职级名称
     */
    private final String name;

    /**
     * 符合晋升codes
     */
    private final List<Integer> fhjsCodes;

    /**
     * 要求的资格type
     */
    private final String zgDictType;

    /**
     * 要求的资格code
     */
    private final Integer zgType;

    /**
     * 要求任职年数
     */
    private final Integer years;

    /**
     * code to name
     *
     * @param code code
     * @return PoliceFuJsEnum
     */
    public static PoliceFuJsEnum codeOf(Integer code){
        if (Objects.nonNull(code)){
            for (PoliceFuJsEnum value : PoliceFuJsEnum.values()) {
                if (code.equals(value.getCode())){
                    return value;
                }
            }
        }
        return null;
    }
}
