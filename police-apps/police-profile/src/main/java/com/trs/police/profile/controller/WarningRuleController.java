package com.trs.police.profile.controller;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.profile.domain.vo.JqListVO;
import com.trs.police.profile.domain.dto.WarningRuleDTO;
import com.trs.police.profile.service.WarningRuleService;
import com.trs.police.profile.vo.WarningRuleVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 预警规则控制器
 *
 * <AUTHOR>
 * @date 2024/10/17
 */
@RestController
@RequestMapping("/warning")
public class WarningRuleController {

    @Resource
    private WarningRuleService warningRuleService;

    /**
     * 获取预警规则
     *
     * @return 预警规则
     */
    @GetMapping("/rule/detail")
    public WarningRuleVO getWarningRule() {
        return warningRuleService.getWarningRule();
    }

    /**
     * 保存预警规则
     *
     * @param warningRuleDTO 预警规则DTO
     */
    @PostMapping("/rule/save")
    public void saveWarningRule(@RequestBody WarningRuleDTO warningRuleDTO) {
        warningRuleService.saveWarningRule(warningRuleDTO);
    }

    /**
     * 获取警情预警列表
     *
     * @param request 请求参数
     * @return 警情预警列表
     */
    @PostMapping("/jq/list")
    public PageResult<JqListVO> getWarningList(@RequestBody ListParamsRequest request) {
        return warningRuleService.getWarningList(request);
    }

    /**
     * 标记警情为已读
     *
     * @param dataType 数据类型
     * @param bh 接警单编号或
     * @return 是否成功
     */
    @PostMapping("/read")
    public boolean markAsRead(String dataType, String bh) {
        return warningRuleService.markAsRead(dataType, bh);
    }

    /**
     * 保存声音提醒开关
     *
     * @param soundAlert 声音提醒开关
     */
    @PostMapping("/rule/saveSoundAlert")
    public void saveSaveSoundAlert(@RequestParam Integer soundAlert) {
        warningRuleService.saveSaveSoundAlert(soundAlert);
    }
}
