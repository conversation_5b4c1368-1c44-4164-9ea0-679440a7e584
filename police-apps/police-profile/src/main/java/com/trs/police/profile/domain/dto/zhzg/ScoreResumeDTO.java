package com.trs.police.profile.domain.dto.zhzg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 积分计算履历信息DTO
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreResumeDTO {

    /**
     * 任职开始时间
     */
    private LocalDate startTime;
    /**
     * 任职结束时间，若勾选“至今”，则可为空
     */
    private LocalDate endTime;
    /**
     * 任职职务，码表，type = police_ll_rzzw
     */
    private Integer position;
    /**
     * 任职职务级别，码表，type = police_ll_rzzwjb
     */
    private Integer positionLevel;
    /**
     * 任职部门（如xxx支队xxx大队）
     */
    private String department;

    public ScoreResumeDTO(LocalDate startTime, LocalDate endTime, Integer position, Integer positionLevel) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.position = position;
        this.positionLevel = positionLevel;
    }
}
