package com.trs.police.profile.domain.dto.zhzg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 积分计算职务信息DTO
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreResumeDTO {

    /**
     * 任职开始时间
     */
    private LocalDate startTime;
    /**
     * 任职结束时间，若勾选“至今”，则可为空
     */
    private LocalDate endTime;
    /**
     * 任职职务，码表，type = police_ll_rzzw
     */
    private Integer position;
    /**
     * 任职职务级别，码表，type = police_ll_rzzwjb
     * 1: 县处级正职
     * 2: 县处级副职
     * 3: 部门正职
     * 4: 正科级
     * 5: 副科级
     * 6: 民警
     */
    private Integer positionLevel;
    /**
     * 任职部门（如xxx支队xxx大队）
     */
    private String department;
    /**
     * 人员类别 码表，type = police_ll_person_type
     * 1：一级部门正职
     * 2：一级部门副职
     * 3：二级部门正职
     * 4：二级部门副职
     * 5：民警
     */
    private Integer personType;

    public ScoreResumeDTO(LocalDate startTime, LocalDate endTime, Integer position, Integer positionLevel) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.position = position;
        this.positionLevel = positionLevel;
    }
}
