package com.trs.police.profile.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.DelayMessageTypeEnum;
import com.trs.police.common.core.dto.CommonConfigDTO;
import com.trs.police.common.core.entity.CommonConfigEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.mgr.ICommonConfigMgr;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.KeyNameVO;
import com.trs.police.common.core.vo.message.ScheduleMessageVO;
import com.trs.police.common.rabbitmq.starter.utils.RabbitmqUtils;
import com.trs.police.common.schedule.starter.annotation.ScheduleResult;
import com.trs.police.profile.constant.CommonConstants;
import com.trs.police.profile.constant.RiskScoreDeductPointsConstants;
import com.trs.police.profile.converter.RiskScoreDeductPointsConvert;
import com.trs.police.profile.domain.dto.*;
import com.trs.police.profile.domain.entity.*;
import com.trs.police.profile.domain.vo.*;
import com.trs.police.profile.mapper.*;
import com.trs.police.profile.mgr.BaseJuvenilesItemScoreMgr;
import com.trs.police.profile.service.BaseRiskScoreTotalCountService;
import com.trs.police.profile.service.IRiskService;
import com.trs.police.profile.util.ConverterHelper;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.builder.util.KeyMgrFactory;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotNull;
import static com.trs.police.common.schedule.starter.constant.ExchangeConstant.SCHEDULE_EXCHANGE;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * RiskServiceImpl
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/7 17:46
 * @since 1.0
 */
@Service
@Slf4j
public class RiskServiceImpl implements IRiskService {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RiskLabelMapper riskLabelMapper;

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private PersonEventRelationMapper personEventRelationMapper;

    @Autowired
    private RiskScoreItemMapper riskScoreItemMapper;

    @Autowired
    private ICommonConfigMgr commonConfigMgr;

    @Autowired
    private GroupWorkRecordMapper groupWorkRecordMapper;

    @Resource
    private JuvenilesLabelMapper juvenilesLabelMapper;

    @Resource
    private JuvenilesScoreMapper juvenilesScoreMapper;

    /**
     * addOrUpdateLabel<BR>
     *
     * @param save 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/7 16:57
     */
    @Override
    public Report<String> addOrUpdateLabel(RiskLabelSaveDTO save) throws ServiceException {
        save.isValid();
        final CurrentUser user = AuthHelper.getNotNullUser();
        Long id = Optional.ofNullable(save.getId()).orElse(0L);
        if (riskLabelMapper.countByNameNotEqId(save.getType(), save.getLabelName(), id) > 0) {
            throw new ServiceException("存在同名标签");
        }
        RiskLabelEntity entity;
        if (id > 0L) {
            entity = riskLabelMapper.selectById(id);
            checkNotNull(entity, new ServiceException("根据ID[" + id + "]无法找到对应数据"));
            checkArgument(save.getType().equals(entity.getLabelType()), new ServiceException("分类不一致"));
            entity.updateUser(user);
        } else {
            entity = new RiskLabelEntity(user);
        }
        entity.setLabelType(save.getType());
        entity.setLabelName(save.getLabelName());
        entity.setLabelDesc(save.getLabelDesc());
        entity.setLabelScore(save.getLabelScore());
        entity.setLabelScene(save.getLabelScene());
        if (id > 0L) {
            riskLabelMapper.updateById(entity);
        } else {
            riskLabelMapper.insert(entity);
        }
        return new Report<>("新增/编辑标签", "成功新增/编辑标签");
    }

    /**
     * deleteLabel<BR>
     *
     * @param delete 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/7 16:58
     */
    @Override
    public Report<String> deleteLabel(RiskLabelDeleteDTO delete) throws ServiceException {
        delete.isValid();
        var ids = Arrays.stream(StringUtils.showEmpty(delete.getIds()).split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        checkArgument(!ids.isEmpty(), new ParamInvalidException("待删除的ID串不能为空"));
        var eventName = riskLabelMapper.findLabelUseInEvent(ids);
        if (CollectionUtils.isNotEmpty(eventName)) {
            return new Report<>(
                    "删除标签",
                    String.format(
                            "标签在[%s]等事件档案中被使用了，请先从对应事件中移除后再进行删除",
                            String.join(StringUtils.SEPARATOR_COMMA, eventName)
                    ),
                    RESULT.FAIL
            );
        }
        riskLabelMapper.deleteByLabelTypeAndIds(delete.getType(), ids);
        return new Report<>("删除标签", "成功删除标签");
    }

    /**
     * labelList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/7 17:23
     */
    @Override
    public RestfulResultsV2<RiskLabelVo> labelList(RiskLabelQueryDTO dto) throws ServiceException {
        dto.isValid();
        final Set<String> labelScenes = Arrays.stream(
                        StringUtils.showEmpty(dto.getLabelScene()).split(StringUtils.SEPARATOR_COMMA))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        final List<OrderDTO> orders = dto.makeOrder();
        final List<LabelSceneVo> scene = labelSceneList(dto.getType());
        RestfulResultsV2<RiskLabelVo> resultsV2;
        if (dto.getPageSize() == -1) {
            var list = riskLabelMapper.findList(dto, labelScenes, orders)
                    .stream()
                    .map(it -> it.toVo(scene))
                    .collect(Collectors.toList());
            resultsV2 = RestfulResultsV2.ok(list)
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(Integer.valueOf(list.size()).longValue());
        } else {
            IPage<RiskLabelEntity> page = riskLabelMapper.findPage(
                    new Page<>(dto.getPageNum(), dto.getPageSize()),
                    dto,
                    labelScenes,
                    orders
            );
            resultsV2 = RestfulResultsV2.ok(page.getRecords()
                            .stream()
                            .map(it -> it.toVo(scene))
                            .collect(Collectors.toList()))
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(page.getTotal());
        }
        return resultsV2;
    }

    /**
     * labelSceneList<BR>
     *
     * @param type 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/7 17:34
     */
    @Override
    public List<LabelSceneVo> labelSceneList(String type) throws ServiceException {
        PreConditionCheck.checkNotEmpty(type, new ParamInvalidException("分类不能为空"));
        String content = BeanFactoryHolder
                .getEnv()
                .getProperty(
                        "profile.risk.label.scene.mapping",
                        "{\"person\":[{\"dictCode\":\"0\",\"dictDesc\":\"未发生事件\"},{\"dictCode\":\"1\",\"dictDesc\":\"已发生事件\"}]}"
                );
        if (JsonUtils.isValidObject(content)) {
            String value = JSON.parseObject(content).getString(type);
            if (JsonUtils.isValidArray(value)) {
                return JsonUtil.parseArray(value, LabelSceneVo.class);
            }
        }
        return Collections.emptyList();
    }

    /**
     * personScoreAndCount<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    @Override
    public PersonScoreAndCountVO personScoreAndCount(RiskScoreAndCountDTO dto) throws ServiceException {
        dto.isValid();
        return ConverterHelper.findRiskScoreTotalCountServiceByType(dto.getType()).personScoreAndCount(dto);
    }

    /**
     * findRiskScoreBatch<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/26 14:16
     */
    @Override
    public List<RiskScoreBatchVo> findRiskScoreBatch(RiskScoreBatchDTO dto) throws ServiceException {
        dto.isValid();
        List<RiskScoreBatchEntity> list = ConverterHelper.findRiskScoreTotalCountServiceByType(dto.getType())
                .findRiskScoreBatch(dto);
        return list.stream()
                .map(it -> RiskScoreBatchVo.of(it, dto.getOnlyInteger()))
                .collect(Collectors.toList());
    }

    @Override
    public List<RiskScoreBatchGroupVo> findRiskScoreBatchGroupByDay(RiskScoreBatchDTO dto) throws ServiceException {
        var data = findRiskScoreBatch(dto);
        var exist = data.stream()
                .map(it -> TimeUtils.dateToString(it.getCrTime(), TimeUtils.YYYYMMDD))
                .collect(Collectors.toSet());
        if (dto.getNeedExtendEmpty()) {
            PreConditionCheck.checkNotEmpty(dto.getStartTime(), new ParamInvalidException("开始时间不能为空"));
            PreConditionCheck.checkNotEmpty(dto.getEndTime(), new ParamInvalidException("结束时间不能为空"));
            TimeUtils.getDateList(dto.getStartTime(), dto.getEndTime(), TimeUtils.YYYYMMDD, false, false)
                    .forEach(date -> {
                        if (!exist.contains(date)) {
                            data.add(RiskScoreBatchVo.ofEmpty(
                                    TimeUtils.stringToDate(date, TimeUtils.YYYYMMDD),
                                    dto.getType(),
                                    dto.getType(),
                                    dto.getId()
                            ));
                            exist.add(date);
                        }
                    });
        }
        return data.stream()
                .collect(Collectors.groupingBy(it -> TimeUtils.dateToString(it.getCrTime(), TimeUtils.YYYYMMDD)))
                .entrySet()
                .stream()
                .map(it -> {
                    int size = it.getValue().size();
                    if (dto.getOnlyLast() > 0 && size > dto.getOnlyLast()) {
                        return RiskScoreBatchGroupVo.of(
                                it.getKey(),
                                it.getValue().subList(size - dto.getOnlyLast(), size)
                        );
                    }
                    return RiskScoreBatchGroupVo.of(it.getKey(), it.getValue());
                })
                .sorted(Comparator.comparing(it -> TimeUtils.stringToDate(it.getDay())))
                .collect(Collectors.toList());
    }


    @Override
    public List<Report> changeEventHappened(ChangeEventHappenedDTO dto) throws ServiceException {
        dto.isValid();
        List<Report> reports = new ArrayList<>();
        List<String> ids = Stream.of(dto.getIds().split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        List<ProfileEvent> list = riskLabelMapper.findEventHappenedValueByIds(ids);
        final Set<Long> eventIds = new HashSet<>(list.size());
        final Set<Long> personIds = new HashSet<>(list.size());
        for (ProfileEvent entry : list) {
            if (!Objects.equals(entry.getEventHappened().toString(), dto.getEventHappened())) {
                riskLabelMapper.updateEventHappenedValueByIds(ids, dto.getEventHappened());
                List<Long> idByFindNotInSetLabelScene = riskLabelMapper.findIdByFindNotInSetLabelScene(
                        dto.getEventHappened()
                );
                List<PersonEventRelation> personEventRelations = personEventRelationMapper
                        .findListByEventIdAndRiskLabelIds(dto.getIds());
                for (PersonEventRelation personEventRelation : personEventRelations) {
                    personIds.add(personEventRelation.getPersonId());
                    eventIds.add(personEventRelation.getEventId());
                    if (StringUtils.isNotEmpty(personEventRelation.getRiskLabelIds())) {
                        List<String> collect = Arrays.stream(
                                        StringUtils.showEmpty(personEventRelation.getRiskLabelIds())
                                                .split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON)
                                ).filter(StringUtils::isNotEmpty)
                                .collect(Collectors.toList());
                        idByFindNotInSetLabelScene.forEach(o -> collect.remove(o.toString()));
                        personEventRelation.setRiskLabelIds(String.join(StringUtils.SEPARATOR_COMMA, collect));
                        personEventRelationMapper.updateById(personEventRelation);
                    }
                }
            }
            eventIds.add(entry.getId());
            Report report = new Report<>("eventId:" + entry.getId());
            report.setResult(RESULT.SUCCESS);
            reports.add(report);
        }
        // 发送消息及时更新积分
        ConverterHelper.sendMessageToMq(rabbitTemplate, "person", personIds);
        ConverterHelper.sendMessageToMq(rabbitTemplate, "event", eventIds);
        return reports;
    }

    /**
     * 风险分数列表<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/11 09:33
     */
    @Override
    public RestfulResultsV2<RiskScoreItemVo> scoreList(RiskScoreItemSearchDTO dto) {
        return Try.of(() -> {
                    dto.isValid();
                    IPage<RiskScoreItemEntity> page = riskScoreItemMapper.selectPage(
                            new Page<>(dto.getPageNum(), dto.getPageSize()),
                            new QueryWrapper<RiskScoreItemEntity>()
                                    .lambda()
                                    .eq(RiskScoreItemEntity::getItemType, dto.getType())
                                    .eq(RiskScoreItemEntity::getRecordId, dto.getRecordId())
                                    .orderByDesc(RiskScoreItemEntity::getCrTime)
                                    .orderByDesc(RiskScoreItemEntity::getId)
                    );
                    var data = page.getRecords()
                            .stream()
                            .map(RiskScoreItemEntity::toVo)
                            .collect(Collectors.toList());
                    return RestfulResultsV2.ok(data)
                            .addTotalCount(page.getTotal())
                            .addPageNum(dto.getPageNum())
                            .addPageSize(dto.getPageSize());
                }).onFailure(e -> log.error("获取风险分数列表失败", e))
                .getOrElseGet(e -> RestfulResultsV2.error(e.getMessage()));
    }

    /**
     * 分数计算任务<BR>
     *
     * @param message 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/11 10:51
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(autoDelete = "true"),
            exchange = @Exchange(value = SCHEDULE_EXCHANGE, type = ExchangeTypes.FANOUT)))
    @ScheduleResult
    public void countScoreTask(String message) throws ServiceException {
        ScheduleMessageVO action = RabbitmqUtils.getMessage(message, ScheduleMessageVO.class);
        if (DelayMessageTypeEnum.SYNCHRONIZE == action.getOperation()) {
            switch (action.getModule()) {
                case COUNT_RISK_SCORE_FOR_PERSON:
                case COUNT_RISK_SCORE_FOR_EVENT:
                case COUNT_RISK_SCORE_FOR_GROUP:
                case COUNT_JUVENILES_RISK_SCORE:
                case COUNT_JUVENILES_RISK_LABEL:
                    BaseRiskScoreTotalCountService countService = ConverterHelper
                            .findRiskScoreTotalCountServiceByMessageOperateModule(action.getModule());
                    log.info(
                            "调度了定时任务[{}],relatedId=[{}],relatedIdStr=[{}]",
                            action.getModule(),
                            action.getRelatedId(),
                            action.getRelatedIdStr()
                    );
                    final Long relatedId = action.getRelatedId();
                    final String relatedIdStr = action.getRelatedIdStr();
                    if (relatedId == null && StringUtils.isEmpty(relatedIdStr)) {
                        countService.countAllItemScore();
                    } else {
                        if (Objects.nonNull(relatedId)) {
                            countService.countTargetItemScore(relatedId);
                        } else if (StringUtils.isNotEmpty(relatedIdStr)) {
                            countService.countTargetItemScore(relatedIdStr);
                        }
                    }
                    return;
                default:
            }
        }
    }

    @Override
    public DeductConfigVO getDeductConfig(String type) throws ServiceException {
        if (StringUtils.isNotEmpty(type)) {
            if (!RiskScoreDeductPointsConstants.WORK_METHOD_ADAPT_ALL.equals(type)
                    && !RiskScoreDeductPointsConstants.WORK_METHOD_ADAPT_PERSON.equals(type)
                    && !RiskScoreDeductPointsConstants.WORK_METHOD_ADAPT_GROUP.equals(type)) {
                throw new ServiceException("参数type不正确!");
            }
        }
        DeductConfigVO result = new DeductConfigVO();
        CommonConfigDTO dto = new CommonConfigDTO();
        dto.setCKey(CommonConstants.CONFIG_KEY_DEDUCT_DAILY);
        dto.setCType("profile");
        CommonConfigEntity dailyConfig = commonConfigMgr.getConfig(dto);
        if (Objects.nonNull(dailyConfig)) {
            result.setDailyConfig(dailyConfig.getCValue());
        }
        dto.setCKey(CommonConstants.CONFIG_KEY_DEDUCT_JUDGE);
        CommonConfigEntity judgeConfig = commonConfigMgr.getConfig(dto);
        if (Objects.nonNull(judgeConfig)) {
            String config = judgeConfig.getCValue();
            if (StringUtils.isEmpty(type)
                    || RiskScoreDeductPointsConstants.WORK_METHOD_ADAPT_ALL.equals(type)) {
                result.setJudgeConfig(config);
            } else {
                List<JSONObject> tmp = new ArrayList<>(0);
                JSONArray jsonArray = JSON.parseArray(config);
                for (int i = 0; i < jsonArray.size(); i++) {
                    String configType = jsonArray.getJSONObject(i).getString("type");
                    if (StringUtils.isEmpty(configType)) {
                        log.error("风险分扣减配置[{}]中，参数type不正确或未配置!", config);
                        throw new ServiceException("工作方法可见范围未配置，请前往[设置-业务参数-档案-风险分扣减配置]进行配置！");
                    }
                    if (configType.equals(type)
                            || RiskScoreDeductPointsConstants.WORK_METHOD_ADAPT_ALL.equals(configType)) {
                        tmp.add(jsonArray.getJSONObject(i));
                    }
                }
                result.setJudgeConfig(JSON.toJSONString(tmp));
            }

        }
        return result;
    }

    @Override
    public void saveOrUpdateConfig(DeductConfigDTO dto) throws ServiceException {
        dto.isValid();
        CommonConfigDTO configDTO = new CommonConfigDTO();
        configDTO.setCType("profile");
        configDTO.setCKey(dto.getType().equals(CommonConstants.TYPE_DAILY)
                ? CommonConstants.CONFIG_KEY_DEDUCT_DAILY
                : CommonConstants.CONFIG_KEY_DEDUCT_JUDGE);
        configDTO.setCValue(dto.getType().equals(CommonConstants.TYPE_DAILY)
                ? dto.getDailyConfig()
                : dealJudgeConfig(dto.getJudgeConfig()));
        configDTO.setCDesc("风控积分扣减配置");
        if (CommonConstants.TYPE_JUDGE.equals(dto.getType())) {
            CommonConfigEntity config = commonConfigMgr.getConfig(configDTO);
            if (Objects.nonNull(config)) {
                JSONArray oldConfig = JSON.parseArray(config.getCValue());
                JSONArray newConfig = JSON.parseArray(dto.getJudgeConfig());
                if (oldConfig.size() > newConfig.size()) {
                    //此时 说明有删除行为 需要校验待删除的数据是否被关联了
                    List<String> oldKeys = new ArrayList<>(0);
                    for (int i = 0; i < oldConfig.size(); i++) {
                        JSONObject item = oldConfig.getJSONObject(i);
                        oldKeys.add(item.getString("key"));
                    }
                    List<String> newKeys = new ArrayList<>(0);
                    for (int i = 0; i < newConfig.size(); i++) {
                        JSONObject item = newConfig.getJSONObject(i);
                        newKeys.add(item.getString("key"));
                    }
                    List<String> list = oldKeys
                            .stream()
                            .filter(it -> !newKeys.contains(it))
                            .collect(Collectors.toList());
                    if (isRelated(list)) {
                        throw new ServiceException("该配置已被关联，请解除关联后再删除！");
                    }
                }
            }
        }
        commonConfigMgr.saveOrUpdate(configDTO);
    }

    /**
     * juvenilesLabelList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 13:43
     */
    @Override
    public RestfulResultsV2<JuvenilesLabelVo> juvenilesLabelList(JuvenilesLabelSearchDTO dto) {
        return Try.of(() -> {
                    dto.isValid();
                    final List<String> sex = dto.makeSex();
                    final List<String> zjhm = dto.makeZjhm();
                    final List<String> label = StringUtils.getList(dto.getLabel(), true);
                    final Tuple2<Integer, Integer> tuple2 = dto.makeMinAndMaxYear();
                    final var page = new LambdaQueryChainWrapper<>(juvenilesLabelMapper)
                            .in(CollectionUtils.isNotEmpty(zjhm), JuvenilesLabelEntity::getZjhm, zjhm)
                            .in(CollectionUtils.isNotEmpty(label), JuvenilesLabelEntity::getLabel, label)
                            .in(CollectionUtils.isNotEmpty(sex), JuvenilesLabelEntity::getSex, sex)
                            .ge(TimeUtils.isValid(dto.getStartTime()), JuvenilesLabelEntity::getWarnTime, dto.getStartTime())
                            .le(TimeUtils.isValid(dto.getEndTime()), JuvenilesLabelEntity::getWarnTime, dto.getEndTime())
                            .ge(Objects.nonNull(tuple2._1()), JuvenilesLabelEntity::getYearOfBirth, tuple2._1())
                            .le(Objects.nonNull(tuple2._2()), JuvenilesLabelEntity::getYearOfBirth, tuple2._2())
                            .and(
                                    StringUtils.isNotEmpty(dto.getSearchType()) && StringUtils.isNotEmpty(dto.getKeyword()),
                                    i -> i.like(
                                            Objects.equals("all", dto.getSearchType()) || Objects.equals("zjhm", dto.getSearchType()),
                                            JuvenilesLabelEntity::getZjhm, dto.getKeyword()
                                    ).or().like(
                                            Objects.equals("all", dto.getSearchType()) || Objects.equals("name", dto.getSearchType()),
                                            JuvenilesLabelEntity::getName, dto.getKeyword()
                                    ).or().like(
                                            Objects.equals("all", dto.getSearchType()) || Objects.equals("address", dto.getSearchType()),
                                            JuvenilesLabelEntity::getAddress, dto.getKeyword()
                                    )
                            ).orderByDesc(JuvenilesLabelEntity::getWarnTime)
                            .page(new Page<>(dto.getPageNum(), dto.getPageSize()));
                    final var map = juvenilesLabel()
                            .stream()
                            .collect(Collectors.toMap(i -> i.getKey(), i -> i.getName(), (a, b) -> a));
                    return RestfulResultsV2.ok(page.getRecords()
                            .stream()
                            .map(RiskScoreDeductPointsConvert.CONVERT::entityToVo)
                            .peek(i -> i.setReason(StringUtils.showEmpty(map.get(i.getLabel()))))
                            .collect(Collectors.toList())
                    ).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(page.getTotal());
                }).onFailure(e -> log.error("获取标签列表出错", e))
                .getOrElseGet(e -> RestfulResultsV2.error(e.getMessage()));
    }

    /**
     * juvenilesLabel<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 19:27
     */
    @Override
    public List<KeyNameVO> juvenilesLabel() {
        return JsonUtil.parseArray(
                BeanFactoryHolder.getEnv().getProperty("profile.juveniles.warn.labels", "[]"),
                KeyNameVO.class
        );
    }

    @Override
    public List<KeyNameVO> juvenilesScoreClass() throws ServiceException {
        return KeyMgrFactory.getMgrs(BaseJuvenilesItemScoreMgr.class)
                .stream()
                .sorted(Comparator.comparingInt(BaseJuvenilesItemScoreMgr::order))
                .map(it -> KeyNameVO.builder()
                        .key(it.secondCategory())
                        .name(it.classFilterShowName())
                        .build()
                )
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * juvenilesScoreList<BR>
     *
     * @param dto 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 13:43
     */
    @Override
    public RestfulResultsV2<JuvenilesScoreVo> juvenilesScoreList(JuvenilesScoreSearchDTO dto) {
        return Try.of(() -> {
                    dto.isValid();
                    final List<String> sex = dto.makeSex();
                    final List<String> zjhm = dto.makeZjhm();
                    final List<String> searchKeys = dto.makeSearchKeys();
                    final List<String> level = StringUtils.getList(dto.getLevel(), true);
                    final Tuple2<Integer, Integer> tuple2 = dto.makeMinAndMaxYear();
                    var query = new LambdaQueryChainWrapper<>(juvenilesScoreMapper)
                            .in(CollectionUtils.isNotEmpty(zjhm), JuvenilesScoreEntity::getZjhm, zjhm)
                            .in(CollectionUtils.isNotEmpty(level), JuvenilesScoreEntity::getLevel, level)
                            .in(CollectionUtils.isNotEmpty(sex), JuvenilesScoreEntity::getSex, sex)
                            .ge(TimeUtils.isValid(dto.getStartTime()), JuvenilesScoreEntity::getWarnTime, dto.getStartTime())
                            .le(TimeUtils.isValid(dto.getEndTime()), JuvenilesScoreEntity::getWarnTime, dto.getEndTime())
                            .ge(Objects.nonNull(dto.getMinScore()), JuvenilesScoreEntity::getScore, dto.getMinScore())
                            .le(Objects.nonNull(dto.getMaxScore()), JuvenilesScoreEntity::getScore, dto.getMaxScore())
                            .ge(Objects.nonNull(tuple2._1()), JuvenilesScoreEntity::getYearOfBirth, tuple2._1())
                            .le(Objects.nonNull(tuple2._2()), JuvenilesScoreEntity::getYearOfBirth, tuple2._2())
                            .and(
                                    StringUtils.isNotEmpty(dto.getSearchType()) && StringUtils.isNotEmpty(dto.getKeyword()),
                                    i -> i.like(
                                            Objects.equals("all", dto.getSearchType()) || Objects.equals("zjhm", dto.getSearchType()),
                                            JuvenilesScoreEntity::getZjhm, dto.getKeyword()
                                    ).or().like(
                                            Objects.equals("all", dto.getSearchType()) || Objects.equals("name", dto.getSearchType()),
                                            JuvenilesScoreEntity::getName, dto.getKeyword()
                                    ).or().like(
                                            Objects.equals("all", dto.getSearchType()) || Objects.equals("address", dto.getSearchType()),
                                            JuvenilesScoreEntity::getAddress, dto.getKeyword()
                                    )
                            ).and(
                                    CollectionUtils.isNotEmpty(searchKeys),
                                    i -> searchKeys.forEach(it -> i.or()
                                            .apply("FIND_IN_SET({0}, search_keys)", it))
                            );
                    final List<OrderDTO> orders = dto.makeOrder();
                    if (CollectionUtils.isNotEmpty(orders)) {
                        for (OrderDTO order : orders) {
                            query = query.orderBy(Objects.equals("warnTime", order.getFieldName()), order.isAsc(), JuvenilesScoreEntity::getWarnTime)
                                    .orderBy(Objects.equals("score", order.getFieldName()), order.isAsc(), JuvenilesScoreEntity::getScore)
                                    .orderBy(Objects.equals("updateTime", order.getFieldName()), order.isAsc(), JuvenilesScoreEntity::getUpdateTime);
                        }
                    } else {
                        query = query.orderByDesc(JuvenilesScoreEntity::getWarnTime);
                    }
                    final var page = query.page(new Page<>(dto.getPageNum(), dto.getPageSize()));
                    return RestfulResultsV2.ok(page.getRecords()
                            .stream()
                            .map(RiskScoreDeductPointsConvert.CONVERT::entityToVo)
                            .collect(Collectors.toList())
                    ).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(page.getTotal());
                }).onFailure(e -> log.error("获取分数列表出错", e))
                .getOrElseGet(e -> RestfulResultsV2.error(e.getMessage()));
    }

    /**
     * 判断是否被关联
     *
     * @param list 参数
     * @return 结果
     */
    private boolean isRelated(List<String> list) {
        for (String key : list) {
            JSONObject object = new JSONObject();
            object.put("key", key);
            int count = groupWorkRecordMapper.countKeyRelated(object.toJSONString());
            if (count > 0) {
                return true;
            }
        }
        return false;
    }

    private String dealJudgeConfig(String jsonArray) {
        List<JSONObject> list = new ArrayList<>(0);
        JSON.parseArray(jsonArray).forEach(item -> {
            JSONObject obj = (JSONObject) item;
            String key = obj.getString("key");
            if (Objects.nonNull(key)) {
                list.add(obj);
            } else {
                obj.put("key", String.valueOf(UUID.randomUUID()));
                list.add(obj);
            }
        });
        return JSON.toJSONString(list);
    }

}
