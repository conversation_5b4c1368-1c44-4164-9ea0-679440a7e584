package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description:
 *
 * @author: tang.shuai
 * @create: 2025-01-10 17:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_profile_group_risk_other", autoResultMap = true)
public class ProfileGroupRiskOtherEntity extends AbstractBaseEntity {
    private static final long serialVersionUID = -1448068997369861646L;
    /**
     * 群体id
     */
    private Long groupId;
    /**
     * 风险背景
     */
    private String mainDemand;
    /**
     * 化解难点
     */
    private String petitionInfo;
    /**
     * 敏感时间节点
     */
    @TableField(value = "sensitive_time_ids", typeHandler = JsonToLongListHandler.class)
    private List<Long> sensitiveTimeIds;
    /**
     * 警种类型
     */
    private Integer policeKind;

    @TableLogic
    private Boolean deleted;
}
