package com.trs.police.profile.schema.vo;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.CodeNameTypeVO;
import com.trs.police.common.core.vo.DistrictTreeVO;
import com.trs.police.common.core.vo.JwzhDictVO;
import com.trs.police.common.core.vo.TreeVO;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.profile.LabelVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.profile.domain.vo.StringCodeNameVO;
import com.trs.police.profile.mapper.LabelMapper;
import com.trs.police.profile.service.CaseService;
import com.trs.police.profile.service.LabelService;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.trs.police.common.core.utils.JsonUtil.OBJECT_MAPPER;

/**
 * <AUTHOR>
 * @date 2021/07/30
 */
@Data
public class ListFilterVO implements Serializable {

    private static final long serialVersionUID = -7464129632094649430L;

    /**
     * 默认值
     */
    private Object defaultValue;

    /**
     * 中文名称
     */
    private String displayName;

    /**
     * 控件类型
     */
    private String type;

    /**
     * 绑定的key
     */
    private String key;

    /**
     * 列表/树 中label和name字段的名称
     */
    private JsonNode fieldNames;
    /**
     * 联动选项
     */
    private List<String> linkedKey;

    /**
     * 联动时传递的参数名，不传前端默认值为 key的数据
     */
    private String queryKey;
    /**
     * 请求url
     */
    private String url;
    /**
     * 初始值
     */
    @JsonDeserialize(using = FilterValueDeserializer.class)
    private ArrayNode value;

    private static class FilterValueDeserializer extends JsonDeserializer<ArrayNode> {

        private static final String DICT_PATTERN = "^%%.*?%%$";
        private static final String TABLE_PATTERN = "^&&.*?&&$";
        private static final String IMPORT_PATTERN = "^##.*?##$";
        private static final String CASE_PATTERN = "^@@.*?@@$";

        @Override
        public ArrayNode deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            JsonNode json = parser.getCodec().readTree(parser);
            ArrayNode values = OBJECT_MAPPER.createArrayNode();
            json.forEach(node -> {
                if (node.isTextual()) {
                    String valueText = node.asText();
                    //字典
                    if (valueText.matches(DICT_PATTERN)) {
                        ArrayNode arrayValue = OBJECT_MAPPER.valueToTree(getDictValues(valueText));
                        values.addAll(arrayValue);
                    }
                    //表
                    else if (valueText.matches(TABLE_PATTERN)) {
                        if (Objects.nonNull(AuthHelper.getCurrentUser())) {
                            ArrayNode arrayNode = OBJECT_MAPPER.valueToTree(getTableValues(valueText));
                            values.addAll(arrayNode);
                        }
                    }
                    //导入的数据
                    else if (valueText.matches(IMPORT_PATTERN)) {
                        ArrayNode arrayNode = OBJECT_MAPPER.valueToTree(getImportValues(valueText));
                        values.addAll(arrayNode);
                        //案件标签
                    } else if (valueText.matches(CASE_PATTERN)) {
                        ArrayNode arrayNode = OBJECT_MAPPER.valueToTree(getCaseValues(valueText));
                        values.addAll(arrayNode);
                    }
                } else {
                    values.add(node);
                }
            });
            return values;
        }

        private List<StringCodeNameVO> getCaseValues(String expression) {
            String type = expression.replace("@@", "");
            CaseService caseService = BeanUtil.getBean(CaseService.class);
            return caseService.getCaseLabelByType(type);
        }

        private List<CodeNameTypeVO> getImportValues(String expression) {
            String type = expression.replace("##", "");
            DictService dictService = BeanUtil.getBean(DictService.class);
            return dictService.getDictTree("jwzh_" + type)
                .stream()
                .map(JwzhDictVO::toVO).collect(Collectors.toList());
        }

        private List<TreeVO> getTableValues(String expression) {
            String type = expression.replace("&&", "");
            switch (type) {
                case "dept": {
                    PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
                    List<DeptVO> deptVos = permissionService.getTreeByDataPermission();
                    return new ArrayList<>(deptVos);
                }
                //只包含派出所
                case "police_station": {
                    PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
                    List<DeptVO> deptVos = permissionService.getTreeByDataPermission();
                    deptVos.forEach(dept -> this.filterChildren(dept,
                            (d) -> Objects.nonNull(d.getDeptType()) && d.getDeptType() <= 3L
                                    && d.getShortName().contains("派出所")));
                    return new ArrayList<>(deptVos);
                }
                //只包含警署（高新专用）
                case "police_kind": {
                    PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
                    List<DeptVO> deptVos = permissionService.getDeptTreeAll();
                    deptVos.forEach(dept -> this.filterChildren(dept,
                        (d) -> Objects.nonNull(d.getDeptType()) && d.getDeptType() > 5L));
                    return new ArrayList<>(deptVos);
                }
                case "person_label":
                case "group_label":
                case "event_label": {
                    String permissionType = type.split("_")[0];
                    LabelService labelService = BeanUtil.getBean(LabelService.class);
                    List<LabelVO> labels = labelService.getLabelListPermission(permissionType);
                    return new ArrayList<>(labels);
                }
                case "qgzdry_label":{
                    LabelMapper labelMapper = BeanUtil.getBean(LabelMapper.class);
                    return labelMapper.selectTree("qgzdry").stream()
                        .map(Label::toVO)
                        .collect(Collectors.toList());
                }
                case "clue_label": {
                    LabelMapper labelMapper = BeanUtil.getBean(LabelMapper.class);
                    return labelMapper.selectTree("clue").stream()
                        .map(Label::toVO)
                        .collect(Collectors.toList());
                }
//                case "event_label": {
//                    LabelMapper labelMapper = BeanUtil.getBean(LabelMapper.class);
//                    return labelMapper.selectTree("event").stream()
//                        .map(Label::toVO)
//                        .collect(Collectors.toList());
//                }
                case "district": {
                    DictService dictService = BeanUtil.getBean(DictService.class);
                    return dictService.getDistrictTree("all").stream()
                        .map(DistrictTreeVO::toVO)
                        .collect(Collectors.toList());
                }
                default: {
                    return Collections.emptyList();
                }
            }
        }

        private void filterChildren(DeptVO dept, Predicate<DeptVO> predicate) {
            if (CollectionUtils.isEmpty(dept.getChildren())) {
                return;
            }
            //只保留市局、分局、派出所
            List<DeptVO> children = dept.getChildren().stream().filter(predicate)
                .collect(Collectors.toList());
            dept.setChildren(children);
            dept.getChildren().forEach(d -> this.filterChildren(d, predicate));
        }

        private List<DictDto> getDictValues(String expression) {
            String type = expression.replace("%%", "");
            return BeanUtil.getBean(DictService.class).getDictListByType(type);
        }
    }

}
