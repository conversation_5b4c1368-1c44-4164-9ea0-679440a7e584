package com.trs.police.profile.domain.dto.zhzg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 积分计算立功受奖DTO
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreLgsjDTO {

    /**
     * 立功受奖时间（对应界面“获取时间”）
     */
    private LocalDate acquisitionTime;
    /**
     * 立功受奖, 码表, type= police_lgsj
     */
    private Integer lgsj;
    /**
     * 描述（对应界面“描述”输入的具体情况）
     */
    private String description;
    /**
     * 上传材料
     */
    private String materialPath;

    public ScoreLgsjDTO(Integer lgsj) {
        this.lgsj = lgsj;
    }
}
