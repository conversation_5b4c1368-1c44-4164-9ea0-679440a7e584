package com.trs.police.profile.util.zhzg;

import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智慧政工积分计算工具类
 */
@Slf4j
public class ZhzgScoreUtil {

    /**
     * 构建树形结构（泛型版本）
     *
     * @param nodes 节点列表
     * @param <T> 节点类型
     * @param <ID> ID类型
     * @return 树形结构（根节点列表）
     */
    public static <T extends TreeNode<T, ID>, ID> List<T> buildTree(List<T> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return new ArrayList<>();
        }

        Map<ID, T> nodeMap = new HashMap<>();
        List<T> rootNodes = new ArrayList<>();

        // 初始化节点映射和子节点列表
        for (T node : nodes) {
            nodeMap.put(node.getId(), node);
            if (node.getChildren() == null) {
                node.setChildren(new ArrayList<>());
            }
        }

        // 构建树形结构
        for (T node : nodes) {
            ID parentId = node.getParentId();
            if (parentId == null || (parentId instanceof Number && ((Number) parentId).longValue() == 0)) {
                // 根节点
                rootNodes.add(node);
            } else {
                // 子节点
                T parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(node);
                } else {
                    log.warn("找不到父节点，节点ID：{}，父节点ID：{}", node.getId(), parentId);
                    // 如果找不到父节点，将其作为根节点处理
                    rootNodes.add(node);
                }
            }
        }

        return rootNodes;
    }

    /**
     * 构建规则树（保持向后兼容）
     *
     * @param rules 规则列表
     * @return 规则树（根节点列表）
     */
    public static List<ZhzgScoreRuleDTO> buildRuleTree(List<ZhzgScoreRuleDTO> rules) {
        return buildTree(rules);
    }

    /**
     * 验证规则树的完整性
     *
     * @param rules 规则列表
     * @return 验证结果
     */
    public static RuleValidationResult validateRules(List<ZhzgScoreRuleDTO> rules) {
        RuleValidationResult result = new RuleValidationResult();
        
        if (CollectionUtils.isEmpty(rules)) {
            result.addError("规则列表为空");
            return result;
        }

        Set<Long> ruleIds = new HashSet<>();
        Map<Long, ZhzgScoreRuleDTO> ruleMap = new HashMap<>();

        // 检查规则ID重复
        for (ZhzgScoreRuleDTO rule : rules) {
            if (rule.getId() == null) {
                result.addError("规则ID不能为空：" + rule.getName());
                continue;
            }
            
            if (ruleIds.contains(rule.getId())) {
                result.addError("规则ID重复：" + rule.getId());
            } else {
                ruleIds.add(rule.getId());
                ruleMap.put(rule.getId(), rule);
            }
        }

        // 检查父子关系
        for (ZhzgScoreRuleDTO rule : rules) {
            if (rule.getParentId() != null && rule.getParentId() != 0) {
                if (!ruleMap.containsKey(rule.getParentId())) {
                    result.addError("找不到父规则，规则：" + rule.getName() + "，父规则ID：" + rule.getParentId());
                }
            }
        }

        // 检查叶子节点规则类型
        for (ZhzgScoreRuleDTO rule : rules) {
            if (rule.getIsLeaf() != null && rule.getIsLeaf()) {
                if (rule.getRuleType() == null || rule.getRuleType().trim().isEmpty()) {
                    result.addWarning("叶子节点缺少规则类型：" + rule.getName());
                }
            }
        }

        return result;
    }

    /**
     * 计算规则树的总分上限
     *
     * @param rules 规则树
     * @return 总分上限
     */
    public static double calculateMaxScore(List<ZhzgScoreRuleDTO> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return 0.0;
        }

        double totalMaxScore = 0.0;
        for (ZhzgScoreRuleDTO rule : rules) {
            if (rule.getScore() != null) {
                totalMaxScore += rule.getScore();
            }
        }

        return totalMaxScore;
    }

    /**
     * 获取所有叶子节点规则
     *
     * @param rules 规则树
     * @return 叶子节点规则列表
     */
    public static List<ZhzgScoreRuleDTO> getLeafRules(List<ZhzgScoreRuleDTO> rules) {
        List<ZhzgScoreRuleDTO> leafRules = new ArrayList<>();
        collectLeafRules(rules, leafRules);
        return leafRules;
    }

    /**
     * 递归收集叶子节点规则
     *
     * @param rules    规则列表
     * @param leafRules 叶子节点规则列表
     */
    private static void collectLeafRules(List<ZhzgScoreRuleDTO> rules, List<ZhzgScoreRuleDTO> leafRules) {
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        for (ZhzgScoreRuleDTO rule : rules) {
            if (rule.getIsLeaf() != null && rule.getIsLeaf()) {
                leafRules.add(rule);
            } else if (!CollectionUtils.isEmpty(rule.getChildren())) {
                collectLeafRules(rule.getChildren(), leafRules);
            }
        }
    }

    /**
     * 按规则类型分组详细结果
     *
     * @param details 详细结果列表
     * @return 按规则类型分组的结果
     */
    public static Map<String, List<ZhzgRuleScoreDetailVO>> groupDetailsByRuleType(List<ZhzgRuleScoreDetailVO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return new HashMap<>();
        }

        return details.stream()
                .filter(detail -> detail.getRuleType() != null)
                .collect(Collectors.groupingBy(ZhzgRuleScoreDetailVO::getRuleType));
    }

    /**
     * 计算详细结果的统计信息
     *
     * @param details 详细结果列表
     * @return 统计信息
     */
    public static ScoreStatistics calculateStatistics(List<ZhzgRuleScoreDetailVO> details) {
        ScoreStatistics statistics = new ScoreStatistics();
        
        if (CollectionUtils.isEmpty(details)) {
            return statistics;
        }

        for (ZhzgRuleScoreDetailVO detail : details) {
            statistics.totalRules++;
            
            if (detail.getSuccess() != null && detail.getSuccess()) {
                statistics.successRules++;
                
                if (detail.getIsHit() != null && detail.getIsHit()) {
                    statistics.hitRules++;
                    
                    if (detail.getScore() != null) {
                        statistics.totalScore += detail.getScore();
                        
                        if (detail.getScore() > 0) {
                            statistics.positiveScore += detail.getScore();
                        } else if (detail.getScore() < 0) {
                            statistics.negativeScore += detail.getScore();
                        }
                    }
                }
            } else {
                statistics.failedRules++;
            }
        }

        return statistics;
    }

    /**
     * 规则验证结果
     */
    public static class RuleValidationResult {
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();

        /**
         * 添加错误信息
         *
         * @param error 错误信息
         */
        public void addError(String error) {
            errors.add(error);
        }

        /**
         * 添加警告信息
         *
         * @param warning 警告信息
         */
        public void addWarning(String warning) {
            warnings.add(warning);
        }

        /**
         * 是否有效
         *
         * @return 是否有效
         */
        public boolean isValid() {
            return errors.isEmpty();
        }

        /**
         * 获取错误信息
         *
         * @return 错误信息列表
         */
        public List<String> getErrors() {
            return errors;
        }

        /**
         * 获取警告信息
         *
         * @return 警告信息列表
         */
        public List<String> getWarnings() {
            return warnings;
        }
    }

    /**
     * 积分统计信息
     */
    @Getter
    public static class ScoreStatistics {
        // Getters
        private int totalRules = 0;
        private int successRules = 0;
        private int failedRules = 0;
        private int hitRules = 0;
        private double totalScore = 0.0;
        private double positiveScore = 0.0;
        private double negativeScore = 0.0;

        /**
         * 获取成功率
         *
         * @return 成功率
         */
        public double getSuccessRate() {
            return totalRules > 0 ? (double) successRules / totalRules : 0.0;
        }

        /**
         * 获取命中率
         *
         * @return 命中率
         */
        public double getHitRate() {
            return successRules > 0 ? (double) hitRules / successRules : 0.0;
        }
    }

    /**
     * 获取最终得分，考虑规则配置的最大分值限制
     *
     * @param calculatedScore 计算出的原始得分
     * @param fullScore       规则配置的最大分值
     * @return 最终得分
     */
    public static double getFinalScore(double calculatedScore, double fullScore) {
        return (Math.abs(fullScore) > 0 && Math.abs(calculatedScore) > Math.abs(fullScore))
                ? fullScore
                : calculatedScore;
    }
}
