package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.common.core.utils.NumberUtil;
import com.trs.police.profile.domain.dto.zhzg.ScoreEvaluationDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 教育经历积分计算策略
 * 规则描述：根据最高学历计分，博士20分，硕士15分，本科10分，专科5分
 */
@Slf4j
@Component
public class EvaluationScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "民主测评得分";
    private static final String RULE_TYPE = "EVALUATION";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算民主测评积分，人员：{}，规则：{}", personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("民主测评数据验证失败")
                        .build();
            }

            double calculatedScore;

            // 计算积分
            List<ScoreEvaluationDTO> votes = personArchive.getVotes();
            if (CollectionUtils.isEmpty(votes)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无民主测评记录")
                        .build();
            }

            ScoreEvaluationDTO vote = votes.get(0);
            if (Objects.isNull(vote)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无民主测评记录")
                        .build();
            }

            //优秀票数
            Integer yxCount = vote.getYxCount();
            //称职票数
            Integer czCount = vote.getCzCount();
            //基本称职票数
            Integer jbczCount = vote.getJbczCount();
            //不称职票数
            Integer bczCount = vote.getBczCount();
            //总票数
            Integer total = yxCount + czCount + jbczCount + bczCount;

            //民主测评得分＝（10分×“优秀”得票数＋6分×“称职”得票数＋2分×“基本称职”得票数+0分×“不称职”得票数）÷总票数
            //如果“优秀”和“称职”得票率低于70%（含），或者“不称职”得票率高于10%（含），则为0分
            double yxczRate = NumberUtil.getRateDouble(yxCount + czCount, total);
            double bczRate = NumberUtil.getRateDouble(bczCount, total);
            if (yxczRate < 70 || bczRate > 10) {
                calculatedScore = 0.0;
            } else {
                calculatedScore = (double) (10 * yxCount + 6 * czCount + 2 * jbczCount) / total;
            }



            // 应用规则配置的最大分值限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("优秀和称职得票率%.3f，不称职得票率%.3f", yxczRate, bczRate);
            String calculateDescription = String.format("优秀和称职得票率%.3f，不称职得票率%.3f，得分：%.1f分", yxczRate, bczRate, calculatedScore);

            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.3f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算民主测评积分失败，人员：{}，规则：{}，错误：{}",
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return true;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
