package com.trs.police.profile.listener;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.constant.enums.ApprovalStatusEnum;
import com.trs.police.common.core.constant.enums.EnvEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.rabbitmq.starter.utils.RabbitmqUtils;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.schema.service.approval.ApprovalServiceFactory;
import com.trs.police.profile.schema.service.approval.common.PersonApprovalService;
import com.trs.police.profile.schema.service.approval.common.EventApprovalService;
import com.trs.police.profile.schema.service.approval.common.GoodsEditApprovalService;
import com.trs.police.profile.service.impl.PersonMonitorServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.trs.police.common.core.constant.log.OperateModule.*;
import static com.trs.police.common.core.constant.message.FightCompositeExchangeConstant.APPROVAL_EXCHANGE;

/**
 * rabbitMQ消息消费者
 *
 * <AUTHOR>
 * @since 2022/04/06
 */
@Slf4j
@Component
public class RabbitMessageListener {

    @Autowired
    private PersonApprovalService personApprovalService;

    @Autowired
    private EventApprovalService goodsApprovalService;

    @Autowired
    private GoodsEditApprovalService goodsEditApprovalService;

    @Autowired
    private PersonMonitorServiceImpl personMonitorService;

    @Autowired
    private DictService dictService;

    @Autowired
    private ApprovalServiceFactory approvalServiceFactory;

    /**
     * 接收审批信息
     *
     * @param message 消息
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(autoDelete = "true"),
            exchange = @Exchange(value = APPROVAL_EXCHANGE)))
    public void receiveApprovalMessage(String message) {
        ApprovalActionVO actionVO = RabbitmqUtils.getMessage(message, ApprovalActionVO.class);
        if (!OperateModule.PROFILE_PERSON.equals(actionVO.getService())) {
            return;
        }
        log.info("档案消息：{}", JSON.toJSONString(actionVO));
        switch (actionVO.getAction()) {
            case ADD:
                personApprovalService.finishApproval(actionVO);
                break;
            case REOPEN:
                Person person = personApprovalService.reopen(actionVO);
                // 审批通过发起布控
                if (ApprovalStatusEnum.PASSED.equals(actionVO.getApprovalStatus())) {
                    // 发起常控
                    personMonitorService.regular(person);
                }
                break;
            case FINISH_ARCHIVE:
                personApprovalService.finishArchive(actionVO);
                break;
            default:
                log.error("审批信息类型错误！审批信息：{}", actionVO);
                break;
        }
    }

    /**
     * 接收审批信息
     *
     * @param message 消息
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(autoDelete = "true"),
            exchange = @Exchange(value = APPROVAL_EXCHANGE)))
    public void receiveProfileGoodsApprovalMessage(String message) {
        ApprovalActionVO actionVO = RabbitmqUtils.getMessage(message, ApprovalActionVO.class);
        if (!OperateModule.PROFILE_GOODS.equals(actionVO.getService())) {
            return;
        }
        log.info("物品档案消息：{}", JSON.toJSONString(actionVO));
        switch (actionVO.getAction()) {
            case ADD:
                goodsApprovalService.finishApproval(actionVO);
                break;
            case REGULAR_COMMIT:
                goodsApprovalService.finishApproval(actionVO);
                break;
            case EDIT:
                goodsEditApprovalService.finishApproval(actionVO);
                break;
            default:
                log.error("审批信息类型错误！审批信息：{}", actionVO);
                break;
        }
    }

    /**
     * 接收审批信息
     *
     * @param message 消息
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(autoDelete = "true"),
            exchange = @Exchange(value = APPROVAL_EXCHANGE)))
    public void zgApprovalMessage(String message) {
        // 自贡的审批消费
        EnvEnum envEnum = EnvEnum.ofCode(dictService.getEnvCode()).orElse(null);
        if (!EnvEnum.ZG.equals(envEnum)) {
            return;
        }
        List<OperateModule> allow = Arrays.asList(PROFILE_GROUP_V2, PROFILE_EVENT_V2, PROFILE_PERSON_V2);
        ApprovalActionVO actionVO = RabbitmqUtils.getMessage(message, ApprovalActionVO.class);
        if (!allow.contains(actionVO.getService())) {
            return;
        }
        approvalServiceFactory.ofOperateModule(actionVO.getService())
                .ifPresent(service -> service.finishApproval(actionVO));
    }

}
