package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 违规违纪表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_professional_wgwj_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceProfessionalWgwjRelation extends AbstractBaseEntity {

    /**
     * 记录日期
     */
    private Date recordTime;
    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;
    /**
     * 问题类型, 码表，type = police_wgwj
     */
    private Integer wtType;
    /**
     * 详情
     */
    private String description;
    /**
     * 上传材料
     */
    private String materialPath;
    /**
     * 是否删除
     */
    private Boolean deleted;
}

