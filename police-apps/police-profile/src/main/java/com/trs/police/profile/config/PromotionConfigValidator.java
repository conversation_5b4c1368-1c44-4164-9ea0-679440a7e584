package com.trs.police.profile.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * 职级晋升配置验证器
 * 
 * 用于启动时验证配置是否正确加载
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class PromotionConfigValidator implements CommandLineRunner {

    @Autowired
    private PromotionRankMappingConfig promotionConfig;
    
    @Autowired
    private Environment environment;

    @Override
    public void run(String... args) {
        log.info("=== 职级晋升配置验证开始 ===");
        
        try {
            // 验证配置是否正确加载
            validateConfiguration();
            
            log.info("=== 职级晋升配置验证完成 ===");
        } catch (Exception e) {
            log.error("=== 职级晋升配置验证失败 ===", e);
            // 不抛出异常，避免影响应用启动
        }
    }
    
    private void validateConfiguration() {
        // 检查配置对象是否为空
        if (promotionConfig == null) {
            log.error("PromotionRankMappingConfig 配置对象为空");
            return;
        }
        
        // 检查 rank-mapping 配置
        if (promotionConfig.getRankMapping() == null) {
            log.error("rank-mapping 配置为空");
        } else {
            log.info("rank-mapping 配置加载成功，包含 {} 个映射关系", 
                    promotionConfig.getRankMapping().size());
            
            // 输出前几个映射关系作为示例
            promotionConfig.getRankMapping().entrySet().stream()
                    .limit(3)
                    .forEach(entry -> log.debug("映射关系: {} -> {}", entry.getKey(), entry.getValue()));
        }
        
        // 检查 rank-strategies 配置
        if (promotionConfig.getRankStrategies() == null) {
            log.error("rank-strategies 配置为空");
        } else {
            log.info("rank-strategies 配置加载成功，包含 {} 个策略配置", 
                    promotionConfig.getRankStrategies().size());
        }
        
        // 检查 rank-tenure-requirements 配置
        if (promotionConfig.getRankTenureRequirements() == null) {
            log.error("rank-tenure-requirements 配置为空");
        } else {
            log.info("rank-tenure-requirements 配置加载成功，包含 {} 个年限要求", 
                    promotionConfig.getRankTenureRequirements().size());
        }
        
        // 检查环境变量中的配置
        checkEnvironmentProperties();
    }
    
    private void checkEnvironmentProperties() {
        log.info("=== 环境配置检查 ===");
        
        // 检查是否有相关的配置属性
        String[] propertiesToCheck = {
            "police.promotion.rank-mapping",
            "police.promotion.rank-strategies", 
            "police.promotion.rank-tenure-requirements"
        };
        
        for (String property : propertiesToCheck) {
            String value = environment.getProperty(property);
            if (value != null) {
                log.info("环境属性 {} 存在，值类型: {}", property, value.getClass().getSimpleName());
                log.debug("环境属性 {} 值: {}", property, value);
            } else {
                log.warn("环境属性 {} 不存在", property);
            }
        }
        
        // 检查活动的配置文件
        String[] activeProfiles = environment.getActiveProfiles();
        log.info("当前活动的配置文件: {}", String.join(", ", activeProfiles));
        
        // 检查默认配置文件
        String[] defaultProfiles = environment.getDefaultProfiles();
        log.info("默认配置文件: {}", String.join(", ", defaultProfiles));
    }
}
