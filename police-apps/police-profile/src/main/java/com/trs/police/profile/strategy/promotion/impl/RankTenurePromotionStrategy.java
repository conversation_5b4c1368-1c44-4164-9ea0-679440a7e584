package com.trs.police.profile.strategy.promotion.impl;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.profile.config.PromotionRankMappingConfig;
import com.trs.police.profile.constant.enums.*;
import com.trs.police.profile.domain.entity.zhzg.PoliceProfessionalNdkhRelation;
import com.trs.police.profile.domain.entity.zhzg.PoliceRankRelation;
import com.trs.police.profile.domain.entity.zhzg.PoliceResumeRelation;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.mapper.zhzg.PoliceProfessionalNdkhRelationMapper;
import com.trs.police.profile.mapper.zhzg.PoliceRankRelationMapper;
import com.trs.police.profile.mapper.zhzg.PoliceResumeRelationMapper;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import com.trs.police.profile.strategy.promotion.model.PositionTenureInfo;
import com.trs.police.profile.strategy.promotion.model.RankTenureInfo;
import com.trs.police.profile.strategy.promotion.model.TenureEvaluationResult;
import com.trs.police.profile.strategy.promotion.model.TenureInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 职级/职务任职年限晋升规则策略
 * <p>
 * 该策略检查警员的职级任职年限和职务任职年限，满足其中一个条件即可通过。
 * 检查逻辑：
 * 1. 检查当前职级的任职年限是否满足要求
 * 2. 检查当前职务级别的任职年限是否满足要求
 * 3. 两个条件是"或"的关系，满足其中一个即可
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Component
public class RankTenurePromotionStrategy implements PromotionRuleStrategy {

    private static final String STRATEGY_NAME = "职级/职务任职年限规则";
    private static final String RULE_TYPE = "RANK_TENURE";

    @Autowired
    private PoliceRankRelationMapper policeRankRelationMapper;

    @Autowired
    private PoliceResumeRelationMapper policeResumeRelationMapper;

    @Autowired
    private PromotionRankMappingConfig rankMappingConfig;

    @Autowired
    private PoliceProfessionalNdkhRelationMapper ndkhRelationMapper;


    @Override
    public PromotionRuleDetailVO evaluateRule(ProfilePolice police, PoliceRankEnum targetRank) {
        log.debug("开始评估职级/职务任职年限规则，警员：{}，目标职级：{}", police.getName(), targetRank);

        PromotionRuleDetailVO.PromotionRuleDetailVOBuilder builder = PromotionRuleDetailVO.builder()
                .ruleName(getStrategyName())
                .description(getDescription())
                .ruleType(getSupportedRuleType())
                .required(true)  // 任职年限是必要条件
                .success(true);

        try {
            // 根据目标职级代码获取职级名称
            String targetRankName = targetRank.getName();

            PoliceFuJsEnum policeFuJsEnum = PoliceFuJsEnum.codeOf(targetRank.getCode());
            // 从配置中获取任职年限要求
            //Map<String, Integer> requirements = rankMappingConfig.getTenureRequirements(targetRankName);
            if (policeFuJsEnum == null) {
                return builder.isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .calculateDescription("未找到该职级的任职年限要求配置")
                        .build();
            }
            // 评估职级任职年限
            //TenureEvaluationResult rankResult = evaluateRankTenure(police, targetRankName, requirements);
            TenureEvaluationResult rankResult = evaluateRankTenure(police, targetRankName, policeFuJsEnum);
            // 评估职务任职年限
            // TenureEvaluationResult positionResult = evaluatePositionTenure(police, targetRankName, requirements);

            // 判断是否满足条件（职级年限 OR 职务年限）
            boolean isQualified = rankResult.isQualified(); //&& positionResult.isQualified();
            PromotionStatusEnum result = isQualified ? PromotionStatusEnum.QUALIFIED : PromotionStatusEnum.NOT_QUALIFIED;

            // 构建详细描述
            String calculateDescription = buildCalculateDescription(rankResult, isQualified);
            String hitData = buildHitData(rankResult);

            return builder.isHit(!isQualified)
                    .ruleResult(result)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("评估职级/职务任职年限规则失败，警员：{}，目标职级：{}，错误：{}",
                    police.getName(), targetRank.getName(), e.getMessage(), e);
            return builder.isHit(false)
                    .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                    .success(false)
                    .errorMessage("评估失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }


    /**
     * 评估职级任职年限
     *
     * @param police         警员信息
     * @param targetRankName 目标职级名称
     * @param requirements   年限要求配置
     * @return TenureEvaluationResult 评估结果
     */
    private TenureEvaluationResult evaluateRankTenure(ProfilePolice police, String targetRankName,
                                                      Map<String, Integer> requirements) {
        try {
            // 获取警员当前职级和任职时间
            PoliceRankRelation currentRankRelation = getCurrentRankRelation(police);
            if (currentRankRelation == null) {
                return new TenureEvaluationResult(false, "缺少当前职级信息", "", "无法获取职级信息");
            }

            PoliceRankEnum currentRank = PoliceRankEnum.getByCode(currentRankRelation.getRankSeries(),
                    currentRankRelation.getRank());
            if (currentRank == null) {
                return new TenureEvaluationResult(false, "无法识别当前职级", "", "职级代码无效");
            }

            // 创建职级任职信息对象
            RankTenureInfo tenureInfo = new RankTenureInfo(currentRankRelation, currentRank);

            // 调用通用评估方法
            return evaluateTenureCommon(tenureInfo, targetRankName, PoliceFuJsEnum.EJJINGZHANG, police);

        } catch (Exception e) {
            log.error("评估职级任职年限失败，警员：{}，错误：{}", police.getName(), e.getMessage(), e);
            return new TenureEvaluationResult(false, "职级年限评估失败", "", e.getMessage());
        }
    }

    /**
     * 评估职级任职年限
     *
     * @param police         警员信息
     * @param targetRankName 目标职级名称
     * @param policeFuJsEnum   符合晋升要求枚举类
     * @return TenureEvaluationResult 评估结果
     */
    private TenureEvaluationResult evaluateRankTenure(ProfilePolice police, String targetRankName,
                                                      PoliceFuJsEnum policeFuJsEnum) {
        try {
            // 获取警员当前职级和任职时间
            PoliceRankRelation currentRankRelation = getCurrentRankRelation(police);
            if (currentRankRelation == null) {
                return new TenureEvaluationResult(false, "缺少当前职级信息", "", "无法获取职级信息");
            }

            PoliceRankEnum currentRank = PoliceRankEnum.getByCode(currentRankRelation.getRankSeries(),
                    currentRankRelation.getRank());
            if (currentRank == null) {
                return new TenureEvaluationResult(false, "无法识别当前职级", "", "职级代码无效");
            }

            // 创建职级任职信息对象
            RankTenureInfo tenureInfo = new RankTenureInfo(currentRankRelation, currentRank);

            // 调用通用评估方法
            return evaluateTenureCommon(tenureInfo, targetRankName, policeFuJsEnum, police);

        } catch (Exception e) {
            log.error("评估职级任职年限失败，警员：{}，错误：{}", police.getName(), e.getMessage(), e);
            return new TenureEvaluationResult(false, "职级年限评估失败", "", e.getMessage());
        }
    }

    /**
     * 获取警员当前职级关系记录
     * 查询 t_police_rank_relation 表中 end_time 为空的记录
     *
     * @param police 警员信息
     * @return PoliceRankRelation
     */
    private PoliceRankRelation getCurrentRankRelation(ProfilePolice police) {
        try {
            LambdaQueryWrapper<PoliceRankRelation> queryWrapper = new LambdaQueryWrapper<PoliceRankRelation>()
                    .eq(PoliceRankRelation::getProfileId, police.getId())
                    .orderByDesc(PoliceRankRelation::getStartTime).last("LIMIT 1");

            return policeRankRelationMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("查询警员当前职级关系失败，警员ID：{}，错误：{}", police.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 评估职务任职年限
     *
     * @param police         警员信息
     * @param targetRankName 目标职级名称
     * @param requirements   年限要求配置
     * @return TenureEvaluationResult 评估结果
     */
    private TenureEvaluationResult evaluatePositionTenure(ProfilePolice police, String targetRankName,
                                                          Map<String, Integer> requirements) {
        try {
            // 获取警员当前履历关系记录
            PoliceResumeRelation currentResumeRelation = getCurrentResumeRelation(police);
            if (currentResumeRelation == null) {
                return new TenureEvaluationResult(false, "缺少当前职务信息", "", "无法获取履历信息");
            }

            // 获取职务级别
            Integer positionLevelCode = currentResumeRelation.getPositionLevel();
            if (positionLevelCode == null) {
                return new TenureEvaluationResult(false, "缺少职务级别信息", "", "职务级别为空");
            }

            PositionLevelEnum positionLevel = PositionLevelEnum.getByCode(positionLevelCode);
            if (positionLevel == null) {
                return new TenureEvaluationResult(false, "无法识别职务级别", "", "职务级别代码无效");
            }

            // 创建职务任职信息对象
            PositionTenureInfo tenureInfo = new PositionTenureInfo(currentResumeRelation, positionLevel);

            // 调用通用评估方法
            return evaluateTenureCommon(tenureInfo, targetRankName, PoliceFuJsEnum.EJJINGZHANG, police);

        } catch (Exception e) {
            log.error("评估职务任职年限失败，警员：{}，错误：{}", police.getName(), e.getMessage(), e);
            return new TenureEvaluationResult(false, "职务年限评估失败", "", e.getMessage());
        }
    }

    /**
     * 通用任职年限评估方法
     * <p>
     * 提取职级和职务评估的公共逻辑，避免代码重复
     *
     * @param tenureInfo     任职信息（职级或职务）
     * @param targetRankName 目标职级名称
     * @param policeFuJsEnum   年限要求配置
     * @param police     警员信息（用于日志）
     * @return TenureEvaluationResult 评估结果
     */
    private TenureEvaluationResult evaluateTenureCommon(TenureInfo tenureInfo, String targetRankName,
                                                        PoliceFuJsEnum policeFuJsEnum, ProfilePolice police) {
        try {
            // 获取任职开始时间
            LocalDate startDate = tenureInfo.getStartDate();
            if (startDate == null) {
                return new TenureEvaluationResult(false,
                        String.format("缺少%s任职时间信息", tenureInfo.getTenureType()),
                        "", "任职时间为空");
            }

            // 检查是否满足年限要求
            String tenureName = tenureInfo.getTenureName();
            Integer requiredYears = policeFuJsEnum.getYears();
            if (requiredYears == null) {
                return new TenureEvaluationResult(false,
                        String.format("当前%s%s不符合晋升到职级%s的前置条件",
                                tenureInfo.getTenureType(), tenureName, targetRankName),
                        "", tenureInfo.getTenureType() + "不匹配");
            }
            //获取年度考核
            List<PoliceProfessionalNdkhRelation> ndkhList = ndkhRelationMapper
                    .selectList(new QueryWrapper<PoliceProfessionalNdkhRelation>()
                    .eq("profile_id", police.getId()));
            //是否包含不符合晋升
            PoliceProfessionalNdkhRelation bfhjsRelation = ndkhList.stream()
                    .filter(e -> PoliceNdkhEnum.JBCZ.getCode().equals(e.getAssessmentResult())
                    || PoliceNdkhEnum.BCZ.getCode().equals(e.getAssessmentResult())).findFirst().orElse(null);
            if (bfhjsRelation != null){
                return new TenureEvaluationResult(false, "任职年间至少存在一次基本称职或不称职",
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(bfhjsRelation.getAssessmentTime()));
            }
            //是否包含优秀
            List<PoliceProfessionalNdkhRelation> yxList = ndkhList.stream()
                    .filter(e -> PoliceNdkhEnum.JBCZ.getCode().equals(e.getAssessmentResult()))
                    .collect(Collectors.toList());
            Double reliefAllYears = Double.valueOf(requiredYears);
            if (CollectionUtils.isEmpty(yxList)){
                Double reliefYears = rankMappingConfig.getReliefYears();
                reliefAllYears = reliefYears - reliefYears * yxList.size();
            }
            // 计算任职年限
            LocalDate currentDate = LocalDate.now();
            Period period = Period.between(startDate, currentDate);
            int actualYears = period.getYears();
            int actualMonths = period.getMonths();

            // 判断是否满足要求
            boolean isQualified = actualYears >= reliefAllYears;
            String hitData = String.format("%s任职时间：%d年%d个月", tenureInfo.getTenureType(), actualYears, actualMonths);
            String description;

            if (isQualified) {
                description = String.format("%s%s任职%d年%d个月，满足要求（≥%d年）",
                        tenureInfo.getTenureType(), tenureName, actualYears, actualMonths, requiredYears);
            } else {
                description = String.format("%s%s任职%d年%d个月，不满足要求（≥%d年）",
                        tenureInfo.getTenureType(), tenureName, actualYears, actualMonths, requiredYears);
            }

            return new TenureEvaluationResult(isQualified, description, hitData);

        } catch (Exception e) {
            log.error("评估{}任职年限失败，警员：{}，错误：{}", tenureInfo.getTenureType(), police.getName(), e.getMessage(), e);
            return new TenureEvaluationResult(false,
                    tenureInfo.getTenureType() + "年限评估失败",
                    "", e.getMessage());
        }
    }

    /**
     * 获取警员当前履历关系记录
     * 查询 t_police_resume_relation 表中 end_time 为空的记录
     *
     * @param police 警员信息
     * @return PoliceResumeRelation
     */
    private PoliceResumeRelation getCurrentResumeRelation(ProfilePolice police) {
        try {
            LambdaQueryWrapper<PoliceResumeRelation> queryWrapper = new LambdaQueryWrapper<PoliceResumeRelation>()
                    .eq(PoliceResumeRelation::getProfileId, police.getId())
                    .isNull(PoliceResumeRelation::getEndTime)
                    .orderByDesc(PoliceResumeRelation::getStartTime)
                    .last("LIMIT 1");

            return policeResumeRelationMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("查询警员当前履历关系失败，警员ID：{}，错误：{}", police.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建计算描述信息
     *
     * @param rankResult     职级评估结果
     * @param positionResult 职务评估结果
     * @param isQualified    最终是否合格
     * @return 计算描述
     */
    private String buildCalculateDescription(TenureEvaluationResult rankResult,
                                             TenureEvaluationResult positionResult,
                                             boolean isQualified) {
        StringBuilder sb = new StringBuilder();

        // 添加职级评估结果
        sb.append("【职级年限】").append(rankResult.getDescription());

        // 添加职务评估结果
        sb.append("；【职务年限】").append(positionResult.getDescription());

        // 添加最终结论
        sb.append("；【结论】");
        if (isQualified) {
            sb.append("职级年限或职务年限满足要求，符合晋升条件");
        } else {
            sb.append("职级年限和职务年限均不满足要求，不符合晋升条件");
        }

        return sb.toString();
    }

    /**
     * 构建计算描述信息
     *
     * @param rankResult     职级评估结果
     * @param isQualified    最终是否合格
     * @return 计算描述
     */
    private String buildCalculateDescription(TenureEvaluationResult rankResult,
                                             boolean isQualified) {
        StringBuilder sb = new StringBuilder();

        // 添加职级评估结果
        sb.append("【职级年限】").append(rankResult.getDescription());

        // 添加最终结论
        sb.append("；【结论】");
        if (isQualified) {
            sb.append("职级年限或职务年限满足要求，符合晋升条件");
        } else {
            sb.append("职级年限和职务年限均不满足要求，不符合晋升条件");
        }

        return sb.toString();
    }

    /**
     * 构建命中数据信息
     *
     * @param rankResult     职级评估结果
     * @param positionResult 职务评估结果
     * @return 命中数据
     */
    private String buildHitData(TenureEvaluationResult rankResult, TenureEvaluationResult positionResult) {
        StringBuilder sb = new StringBuilder();

        if (rankResult.getHitData() != null && !rankResult.getHitData().isEmpty()) {
            sb.append(rankResult.getHitData());
        }

        if (positionResult.getHitData() != null && !positionResult.getHitData().isEmpty()) {
            if (sb.length() > 0) {
                sb.append("；");
            }
            sb.append(positionResult.getHitData());
        }

        return sb.toString();
    }

    /**
     * 构建命中数据信息
     *
     * @param rankResult     职级评估结果
     * @return 命中数据
     */
    private String buildHitData(TenureEvaluationResult rankResult) {
        StringBuilder sb = new StringBuilder();

        if (rankResult.getHitData() != null && !rankResult.getHitData().isEmpty()) {
            sb.append(rankResult.getHitData());
        }
        return sb.toString();
    }
}
