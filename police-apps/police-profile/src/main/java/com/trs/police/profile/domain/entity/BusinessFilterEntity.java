package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 业务参数
 *
 * <AUTHOR>
 * @since 2022/10/10 10:18
 **/
@Data
@TableName("t_business_filter")
public class BusinessFilterEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String type;

    private String module;

    private String urlType;
}
