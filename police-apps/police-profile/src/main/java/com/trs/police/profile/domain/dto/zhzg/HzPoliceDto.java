package com.trs.police.profile.domain.dto.zhzg;

import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 慧政 police dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HzPoliceDto extends BaseDTO {
    /**
     * 职级序列，码表，type = police_rz_xl，传code
     */
    private Integer zjxl;
    /**
     * 职级，码表，职级序列为执法勤务警员时，type=police_zj；职级序列为警务技术职务时，type = police_jwjs_zj
     */
    private Integer zj;
    /**
     * 晋升职级，码表，职级序列为执法勤务警员时，type=police_zj；职级序列为警务技术职务时，type = police_jwjs_zj
     */
    private Integer jsZj;
    /**
     * 符合晋升职级codes，由jsZj，决定
     */
    private List<Integer> fhjsZjCode;
    /**
     * 晋升资格情况，码表，type = police_emergency_status，传code
     */
    private Integer jszgqk;
    /**
     * 部门id
     */
    private Integer deptId;
    /**
     * 年龄范围，码表，type = police_age_range，传code
     */
    private Integer ageRange;
    /**
     * 姓名: name; 身份证: idCard; 手机号: tel
     */
    private String searchKey;
    private String searchValue;

    /**
     * 分数排序，desc，asc
     */
    private String scoreOrder;

    /**
     * 人员类别，码表 type = police_ll_person_type，传code
     */
    private Integer personType;

    private Integer startAge;

    private Integer endAge;
}
