package com.trs.police.profile.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.profile.converter.RiskScoreDeductPointsConvert;
import com.trs.police.profile.domain.dto.RiskScoreAndCountDTO;
import com.trs.police.profile.domain.entity.JuvenilesLabelEntity;
import com.trs.police.profile.domain.entity.JuvenilesScoreEntity;
import com.trs.police.profile.domain.entity.RiskScoreItemEntity;
import com.trs.police.profile.domain.entity.es.DwsWcnTags;
import com.trs.police.profile.domain.vo.PersonScoreAndCountVO;
import com.trs.police.profile.domain.vo.RiskScoreItemVo;
import com.trs.police.profile.service.BaseJuvenilesTotalCountService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/24 16:04
 * @since 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
@Getter
public class JuvenilesRiskLabelTotalCountServiceImpl extends BaseJuvenilesTotalCountService<JuvenilesScoreEntity, JuvenilesLabelEntity> {

    /**
     * convertOther<BR>
     *
     * @param list 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:22
     */
    @Override
    public JSONObject convertOther(List<RiskScoreItemEntity> list) {
        final JSONObject data = new JSONObject();
        List<DwsWcnTags> tags = list.stream()
                .map(i -> JsonUtil.parseObject(i.getItemOther(), DwsWcnTags.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        data.put("tags", tags);
        return data;
    }

    /**
     * 获取待计算的对象<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/8 15:35
     */
    @Override
    public List<JuvenilesScoreEntity> findCountObjs() throws ServiceException {
        final String today = TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD);
        final long pageSize = BeanFactoryHolder.getEnv()
                .getProperty("profile.juveniles.score.count.pageSize", Long.class, 5000L);
        long total = new LambdaQueryChainWrapper<>(getJuvenilesScoreMapper())
                .eq(JuvenilesScoreEntity::getWarnDate, today)
                .eq(JuvenilesScoreEntity::getLevel, "高风险")
                .count();
        log.info("[{}]待计算的数据量为[{}]", desc(), total);
        long pageNum = 0L;
        while (pageNum * pageSize < total) {
            var page = new LambdaQueryChainWrapper<>(getJuvenilesScoreMapper())
                    .eq(JuvenilesScoreEntity::getWarnDate, today)
                    .eq(JuvenilesScoreEntity::getLevel, "高风险")
                    .select(JuvenilesScoreEntity::getZjhm)
                    .page(new Page<>(pageNum + 1, pageSize));
            pageNum++;
            log.info("[{}]第[{}]页的数据量为[{}]", desc(), pageNum, page.getSize());
            pushToConsumer(page.getRecords());
        }
        return List.of();
    }

    /**
     * findCountObj<BR>
     *
     * @param id 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/11 09:01
     */
    @Override
    public JuvenilesScoreEntity findCountObj(Serializable id) throws ServiceException {
        PreConditionCheck.checkNotNull(id, new ServiceException("ID信息不能为空"));
        if (id instanceof Long) {
            return getJuvenilesScoreMapper().selectById(id);
        } else if (id instanceof String) {
            return new LambdaQueryChainWrapper<>(getJuvenilesScoreMapper())
                    .eq(JuvenilesScoreEntity::getZjhm, id)
                    .oneOpt()
                    .orElseThrow(() -> new ServiceException("未找到身份证号码为[" + id + "]的数据"));
        } else if (id instanceof JuvenilesScoreEntity) {
            return (JuvenilesScoreEntity) id;
        }
        throw new ServiceException("不支持的ID类型");
    }

    /**
     * personScoreAndCount<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/11 17:09
     */
    @Override
    public PersonScoreAndCountVO personScoreAndCount(RiskScoreAndCountDTO dto) throws ServiceException {
        throw new ServiceException("暂不支持");
    }

    /**
     * messageOperateModule<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/13 19:00
     */
    @Override
    public OperateModule messageOperateModule() {
        return OperateModule.COUNT_JUVENILES_RISK_LABEL;
    }

    /**
     * 排序<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/8 15:17
     */
    @Override
    public Integer order() {
        return 0;
    }

    /**
     * 所属type<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/8 15:23
     */
    @Override
    public String type() {
        return "juvenilesLabel";
    }

    /**
     * getRecordIdFromEntity<BR>
     *
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/8 15:33
     */
    @Override
    public String getRecordIdFromEntity(JuvenilesScoreEntity entity) {
        return entity.getZjhm();
    }

    /**
     * 更新结果<BR>
     *
     * @param entity 参数
     * @param items  参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/8 15:27
     */
    @Override
    public void updateRiskScore(JuvenilesScoreEntity entity, List<RiskScoreItemVo> items) throws ServiceException {
        if (!items.isEmpty()) {
            final JuvenilesLabelEntity out = findEntity(entity);
            if (out == null) {
                log.warn("[{}]未找到记录,{}", desc(), entity.getZjhm());
                return;
            }
            final RiskScoreItemVo vo = items.get(0);
            final List<DwsWcnTags> tags = (List<DwsWcnTags>) vo.getOther().get("tags");
            final var set = tags.stream()
                    .map(DwsWcnTags::getTag)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());
            final Map<String, JuvenilesLabelEntity> map = new HashMap<>(set.size());
            if (CollectionUtils.isNotEmpty(set)) {
                new LambdaQueryChainWrapper<>(getJuvenilesLabelMapper())
                        .eq(JuvenilesLabelEntity::getZjhm, entity.getZjhm())
                        .eq(JuvenilesLabelEntity::getWarnDate, entity.getWarnDate())
                        .in(JuvenilesLabelEntity::getLabel, set)
                        .list()
                        .forEach(i -> map.put(i.getLabel(), i));
            }
            out.setCrTime(new Date());
            for (DwsWcnTags tag : tags) {
                final JuvenilesLabelEntity tmp = RiskScoreDeductPointsConvert.CONVERT
                        .copy(map.getOrDefault(tag.getTag(), out));
                tmp.setUpdateTime(new Date());
                tmp.setWarnTime(new Date());
                tmp.setWarnDate(TimeUtils.stringToDate(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD)));
                tmp.setLabel(tag.getTag());
                tmp.setWarnCount(tag.getFrequency());
                tmp.setWarnDetail(JsonUtil.toJsonString(tag));
                if (Objects.nonNull(tmp.getId())) {
                    getJuvenilesLabelMapper().updateById(tmp);
                } else {
                    getJuvenilesLabelMapper().insert(tmp);
                }
            }
        } else {
            log.warn("[{}]未能计算出标签,{}", desc(), entity.getZjhm());
        }
        new LambdaQueryChainWrapper<>(getJuvenilesScoreMapper())
                .eq(JuvenilesScoreEntity::getZjhm, entity.getZjhm())
                .oneOpt()
                .ifPresent(it -> {
                    // 更新预警标签数量
                    final Long warnLabelNum = new LambdaQueryChainWrapper<>(getJuvenilesLabelMapper())
                            .eq(JuvenilesLabelEntity::getZjhm, it.getZjhm())
                            .count();
                    new LambdaUpdateChainWrapper<>(getJuvenilesScoreMapper())
                            .set(JuvenilesScoreEntity::getWarnLabelNum, warnLabelNum)
                            .eq(JuvenilesScoreEntity::getId, it.getId())
                            .update();
                });
    }

    @Override
    public String desc() {
        return "未成年预警标签";
    }

    /**
     * makeEmptyEntity<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:31
     */
    @Override
    protected JuvenilesLabelEntity makeEmptyEntity() {
        return new JuvenilesLabelEntity();
    }
}
