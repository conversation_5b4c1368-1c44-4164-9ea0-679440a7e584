package com.trs.police.profile.domain.entity.zhzg;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 教育经历表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_police_education_experience_relation", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class PoliceEducationExperienceRelation extends AbstractBaseEntity {

    /**
     * 关联警员档案表（t_police_profile）的主键
     */
    private Long profileId;
    /**
     * 在校开始时间（对应界面“在校时间”的起始日期）
     */
    private Date startTime;
    /**
     * 在校结束时间（对应界面“在校时间”的结束日期）
     */
    private Date endTime;

    /**
     * 学历，码表，type = t_degree
     */
    private Integer degree;

    private Integer academic_degree;
    /**
     * 毕业学校（对应界面“毕业学校”输入值）
     */
    private String graduationSchool;
    /**
     * 专业（对应界面“专业”输入值）
     */
    private String major;
    /**
     *
     */
    private Boolean deleted;
}

