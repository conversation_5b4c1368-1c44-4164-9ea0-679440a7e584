package com.trs.police.profile.service;

import com.trs.police.common.core.params.ExportParams;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/5/29
 */
public interface GroupExportService {

    /**
     * 批量导出群体
     *
     * @param response 响应体
     * @param params   导出参数
     * @param moduleId 动态模块id
     */
    void groupExport(HttpServletResponse response, ExportParams params, Long moduleId) throws IOException;

    /**
     * 批量导出群体V2
     *
     * @param response 响应体
     * @param params   导出参数
     */
    void groupExportV2(HttpServletResponse response, ExportParams params);

    /**
     * 下载群体档案
     *
     * @param response HttpServletResponse
     * @param groupId  群体档案id
     */
    void downloadGroupRecord(HttpServletResponse response, Long groupId);


    /**
     * 下载群体档案v2
     *
     * @param response HttpServletResponse
     * @param policeKind 归属警钟
     * @param groupId  群体档案id
     */
    void downloadGroup(HttpServletResponse response, Long groupId, Integer policeKind) throws Exception;

}
