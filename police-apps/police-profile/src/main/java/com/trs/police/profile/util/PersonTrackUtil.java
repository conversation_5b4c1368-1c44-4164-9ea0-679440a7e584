package com.trs.police.profile.util;

import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.OkHttpUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.PersonAllTrackVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.profile.domain.vo.PersonTrackRequest;
import com.trs.police.profile.domain.vo.TrackActivityTime;
import com.trs.police.profile.domain.vo.TrackPointVO;
import com.trs.police.profile.properties.SubscribeProperties;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 工具
 *
 * <AUTHOR>
 * @since 2022/11/21 17:20
 **/
@Component
@Slf4j
public class PersonTrackUtil {

    public static final Integer ALL_DAY = 24;
    public static final Integer ZERO = 0;
    public static final String FAIL_TO_GET_PERSON_ALL_TRACK = "查询人员全量轨迹失败！";
    public static final String FAIL_TO_GET = "查询失败！";
    private static final String TIME_PARAMS = "timeParams";

    OkHttpUtil okHttpUtil = OkHttpUtil.getInstance();
    @Resource
    private SubscribeProperties subscribeProperties;

    @Resource
    private DictService dictService;

    /**
     * 请求
     *
     * @param idNumber          身份证
     * @param listParamsRequest 请求参数
     * @return 请求参数
     */
    public PersonTrackRequest getRequest(String idNumber, ListParamsRequest listParamsRequest) {
        final TimeParams timeParams = listParamsRequest.getFilterParams().stream()
            .filter(keyValueTypeVO -> keyValueTypeVO.getType().equals(TIME_PARAMS))
            .map(vo -> (TimeParams) (vo.getValue()))
            .findFirst().orElse(new TimeParams());
        PersonTrackRequest personTrackRequest = new PersonTrackRequest();
        personTrackRequest.setIdNumber(idNumber);
        personTrackRequest.setBeginTime(TimeUtil.getSubscribeTime(timeParams.getBeginTime()));
        personTrackRequest.setEndTime(TimeUtil.getSubscribeTime(timeParams.getEndTime()));
        return personTrackRequest;
    }

    /**
     * 轨迹
     *
     * @param personTrackRequest 参数
     * @param pageParams  分页参数
     * @return 列表
     */
    public PageResult<TrackPointVO> getTrack(PersonTrackRequest personTrackRequest, PageParams pageParams) {
        String requestStr = JsonUtil.toJsonString(personTrackRequest);
        try {
            String response = okHttpUtil.postData(subscribeProperties.getTrackQueryUrl(), requestStr,
                subscribeProperties.getHeadersMap());
            Long total = getTotal(response);
            List<TrackPointVO> page = getTrackPoint(response);
            return PageResult.of(page, pageParams.getPageNumber(), total, pageParams.getPageSize());
        } catch (Exception e) {
            log.error(FAIL_TO_GET_PERSON_ALL_TRACK, e);
        }
        return PageResult.empty(pageParams);
    }

    private List<TrackPointVO> getTrackPoint(String response) {
        List<PersonAllTrackVO> result = getList(response);
        return result.stream().map(track -> {
            TrackPointVO pointVO = new TrackPointVO();
            pointVO.setTime(TimeUtil.getSimpleTime(LocalDateTime.parse(track.getHdsj(), TimeUtil.WARNING_MESSAGE_PATTERN)));
            pointVO.setLocation(track.getHddz());
            pointVO.setLng(track.getJdwgs84());
            pointVO.setLat(track.getWdwgs84());
            if (StringUtils.isNotBlank(track.getGzylx())) {
                DictDto sourceType = dictService.getDictByTypeAndDesc("control_warning_source_type", track.getGzylx());
                pointVO.setType(sourceType.getCode());
                pointVO.setSourceName(sourceType.getName());
            }
            return pointVO;
        }).collect(Collectors.toList());
    }

    private List<PersonAllTrackVO> getList(String responseBody) {
        Map<String, Object> responseMap = JsonUtil.parseMap(responseBody, Object.class);
        Map<String, Object> page = JsonUtil.parseMap(JsonUtil.toJsonString(responseMap.get("data")), Object.class);
        return JsonUtil.parseArray(JsonUtil.toJsonString(page.get("data")), PersonAllTrackVO.class);
    }

    private Long getTotal(String responseBody) {
        Map<String, Object> responseMap = JsonUtil.parseMap(responseBody, Object.class);
        Map<String, Object> page = JsonUtil.parseMap(JsonUtil.toJsonString(responseMap.get("data")), Object.class);
        return Long.valueOf(page.get("total").toString());
    }

    /**
     * 获取不分页
     *
     * @param request 请求
     * @return 不分页
     */
    public List<TrackPointVO> getTrackList(PersonTrackRequest request) {
        try {
            String requestStr = JsonUtil.toJsonString(request);
            String response = okHttpUtil.postData(subscribeProperties.getGeneralSearchUrl(), requestStr,
                subscribeProperties.getHeadersMap());
            return getTrackPoint(response);
        } catch (Exception e) {
            throw new TRSException(FAIL_TO_GET + e.getMessage());
        }
    }

    /**
     * 活动时段
     *
     * @param request 请求
     * @return 列表
     */
    public List<TrackActivityTime> getTrackTime(PersonTrackRequest request) {
        String requestMy = JsonUtil.toJsonString(request);
        String response = okHttpUtil.postData(subscribeProperties.getGeneralSearchUrl(), requestMy,
            subscribeProperties.getHeadersMap());
        List<PersonAllTrackVO> list = getList(response);
        Map<Integer, List<PersonAllTrackVO>> getNumByHour = list.stream().collect(
            Collectors.groupingBy(a -> LocalDateTime.parse(a.getHdsj(), TimeUtil.WARNING_MESSAGE_PATTERN).getHour()));
        List<TrackActivityTime> trackActivityTimes = new ArrayList<>();
        setTrackActivityTime(getNumByHour, trackActivityTimes);
        return trackActivityTimes;
    }

    private void setTrackActivityTime(Map<Integer, List<PersonAllTrackVO>> getNumByHour,
        List<TrackActivityTime> trackActivityTimes) {
        for (int i = ZERO; i < ALL_DAY; i++) {
            TrackActivityTime trackActivityTime = new TrackActivityTime();
            trackActivityTime.setNode(i);
            setTime(getNumByHour, i, trackActivityTime);
            trackActivityTimes.add(trackActivityTime);
        }
    }

    private void setTime(Map<Integer, List<PersonAllTrackVO>> getNumByHour, int i,
        TrackActivityTime trackActivityTime) {
        setAllDay(getNumByHour, trackActivityTime, i);
    }

    private void setAllDay(Map<Integer, List<PersonAllTrackVO>> getNumByHour, TrackActivityTime trackActivityTime,
        Integer time) {
        if (Objects.nonNull(getNumByHour.get(time))) {
            trackActivityTime.setNum(getNumByHour.get(time).size());
        } else {
            trackActivityTime.setNum(ZERO);
        }
    }
}
