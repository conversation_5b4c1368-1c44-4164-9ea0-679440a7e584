package com.trs.police.profile.strategy.zhzg.impl;

import com.alibaba.nacos.common.utils.Objects;
import com.trs.police.profile.domain.dto.zhzg.ScoreEducationDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 教育经历积分计算策略
 * 规则描述：根据最高学历计分，博士20分，硕士15分，本科10分，专科5分
 */
@Slf4j
@Component
public class EducationScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "学历学位得分";
    private static final String RULE_TYPE = "EDUCATION";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算教育经历积分，人员：{}，规则：{}", personArchive.getName(), rule.getName());
        EducationType educationType = EducationType.valueOf(rule.getRuleSubType());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage(educationType.getName() + "数据验证失败")
                        .build();
            }

            double calculatedScore = 0.0;

            // 计算积分
            List<ScoreEducationDTO> educations = personArchive.getEducations();
            if (CollectionUtils.isEmpty(educations)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription(educationType.getName() + "无教育经历记录")
                        .build();
            }

            ScoreEducationDTO firstEducation = educations.get(0);

            if (Objects.nonNull(firstEducation)) {
                //学历
                Integer degree = firstEducation.getDegree();
                //学位
                Integer degree2 = firstEducation.getDegree2();

                switch (educationType) {
                    case A:
                        //获得博士研究生学历及学位
                        if (degree == 7&& degree2 == 7) {
                            calculatedScore += rule.getScore();
                        }
                        break;
                    case B:
                        //获得博士学位且没货的博士研究生学历
                        if (degree != 7 && degree2 == 7) {
                            calculatedScore += rule.getScore();
                        }
                        break;
                    case C:
                        //获得硕士研究生学历及相应学位
                        if (degree == 6 && degree2 == 6) {
                            calculatedScore += rule.getScore();
                        }
                        break;
                    case D:
                        //获得硕士研究生学历或者学位
                        if ((degree == 6 && degree2 != 6) || (degree != 6 && degree2 == 6)) {
                            calculatedScore += rule.getScore();
                        }
                        break;
                    default:
                        break;
                }
            }

            // 应用规则配置的最大分值限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("最高学历：%s", calculatedScore);
            String calculateDescription = String.format("最高学历为%s，得分：%.1f分", calculatedScore, calculatedScore);

            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.3f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算教育经历积分失败，人员：{}，规则：{}，错误：{}", 
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Getter
    @AllArgsConstructor
    enum EducationType {
        A("获得博士研究生学历及学位"),
        B("获得博士学位"),
        C("获得硕士研究生学历及相应学位"),
        D("获得硕士研究生学历或者学位"),
        ;
        private final String name;
    }


    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return true;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
