package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ScoreWgwjDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 违纪违规扣分策略
 * 规则描述：每个违纪违规记录扣10分
 */
@Slf4j
@Component
public class ViolationDeductStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "违纪违规";
    private static final String RULE_TYPE = "VIOLATION";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算违纪违规扣分，人员：{}，规则：{}", personArchive.getName(), rule.getName());
        ViolationType violationType = ViolationType.valueOf(rule.getRuleSubType());
        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取违纪违规记录
            List<ScoreWgwjDTO> violations = personArchive.getViolations();
            if (CollectionUtils.isEmpty(violations)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription(violationType.getName() + "无违纪违规记录")
                        .build();
            }

            // 计算积分，过滤掉2012年11月8日以前的记录
            long hitCount = violations.stream()
                    .filter(dto -> dto.getRecordTime().isAfter(LocalDate.of(2012, 11, 8)))
                    .filter(dto -> Objects.equals(dto.getWtType(), violationType.getType()))
                    .count();

            double calculatedScore = hitCount > 0 ? rule.getScore() : 0.0;

            // 应用规则配置的最大扣分限制（注意：这里是负分，所以比较逻辑相反）
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("有%s相关违纪违规记录：%d条", violationType.getName(), hitCount);
            String calculateDescription = String.format("%d条%s相关违纪违规记录 × %.1f分/条 = %.1f分",
                    hitCount, violationType.getName(), rule.getScore(), calculatedScore);

            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则扣分上限限制，最终得分：%.1f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore < 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算违纪违规扣分失败，人员：{}，规则：{}，错误：{}", 
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    /**
     * 违纪违规类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ViolationType {
        GOVERNMENT_WARNING(1, "政务处分警告"),
        GOVERNMENT_DEMERIT(2, "政务处分记过"),
        GOVERNMENT_MAJOR_DEMERIT(3, "政务处分记大过"),
        GOVERNMENT_DEMOTION(4, "政务处分降级"),
        PARTY_WARNING(5, "党纪处分警告"),
        PARTY_SERIOUS_WARNING(6, "党纪处分严重警告"),
        PARTY_DISMISSAL(7, "党纪处分撤销党内职务"),
        PARTY_PROBATION(8, "党纪处分留党察看"),
        ;

        private final int type;
        private final String name;

        /**
         * 根据类型获取枚举
         *
         * @param type 类型
         * @return 对应的枚举，如果不存在则返回null
         */
        public static ViolationType getByType(int type) {
            for (ViolationType violationType : values()) {
                if (violationType.getType() == type) {
                    return violationType;
                }
            }
            return null;
        }

        /**
         * 根据名称获取枚举
         *
         * @param name 名称
         * @return 对应的枚举，如果不存在则返回null
         */
        public static ViolationType getByName(String name) {
            for (ViolationType violationType : values()) {
                if (violationType.getName().equals(name)) {
                    return violationType;
                }
            }
            return null;
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return true;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
