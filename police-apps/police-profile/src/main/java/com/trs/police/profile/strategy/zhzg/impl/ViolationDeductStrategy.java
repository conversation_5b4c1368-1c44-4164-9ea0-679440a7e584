package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ScoreWgwjDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 违纪违规扣分策略
 * 规则描述：每个违纪违规记录扣10分
 */
@Slf4j
@Component
public class ViolationDeductStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "违纪违规";
    private static final String RULE_TYPE = "VIOLATION";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算违纪违规扣分，人员：{}，规则：{}", personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取违纪违规记录
            List<ScoreWgwjDTO> violations = personArchive.getViolations();
            if (CollectionUtils.isEmpty(violations)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无违纪违规记录")
                        .build();
            }

            // 计算扣分
            int violationCount = violations.size();
            double calculatedScore = violationCount * rule.getScore();

            // 应用规则配置的最大扣分限制（注意：这里是负分，所以比较逻辑相反）
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("违纪违规记录：%d条", violationCount);
            String calculateDescription = String.format("%d条违纪违规记录 × %.1f分/条 = %.1f分", 
                    violationCount, rule.getScore(), calculatedScore);

            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则扣分上限限制，最终得分：%.1f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore < 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算违纪违规扣分失败，人员：{}，规则：{}，错误：{}", 
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return false;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
