
package com.trs.police.profile.service.zhzg.statistic;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.profile.domain.dto.zhzg.HzBigScreenDto;
import com.trs.police.profile.mapper.zhzg.ProfilePoliceMapper;
import com.trs.police.profile.service.secne.ProfileStatisticScene;
import com.trs.police.statistic.domain.bean.CountItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * xx
 */
@Service
public class ZzmmStatistic implements ProfileStatisticScene<CountItem, HzBigScreenDto> {

    @Autowired
    private DictService dictService;

    @Autowired
    private ProfilePoliceMapper profilePoliceMapper;

    @Override
    public List<CountItem> search(HzBigScreenDto hzBigScreenDto) {
        if (StringUtils.isNotEmpty(hzBigScreenDto.getAreaCode())){
            hzBigScreenDto.setAreaCode(AreaUtils.areaPrefix(hzBigScreenDto.getAreaCode()));
        }
        List<DictDto> dictList = dictService.getDictListByType("profile_political_status");
        List<CountItem> resultList = profilePoliceMapper.statisticZzmm(hzBigScreenDto);
        if (CollectionUtils.isEmpty(dictList)){
            return new ArrayList<>();
        }
        Map<String, Long> resultMap = CollectionUtils.isEmpty(resultList) ? new HashMap<>()
                : resultList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        List<CountItem> list = new ArrayList<>();
        for (DictDto dictDto : dictList) {
            CountItem countItem = new CountItem();
            countItem.setKey(String.valueOf(dictDto.getCode()));
            countItem.setName(dictDto.getName());
            countItem.setShowName(dictDto.getName());
            countItem.setCount(resultMap.getOrDefault(String.valueOf(dictDto.getCode()),0L));
            list.add(countItem);
        }
        return list;
    }

    @Override
    public String key() {
        return "zzmmStatistic";
    }

    @Override
    public String desc() {
        return "政治面貌";
    }
}
