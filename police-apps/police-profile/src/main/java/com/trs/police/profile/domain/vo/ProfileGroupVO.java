package com.trs.police.profile.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Description: 下载群体档案-基本信息
 *
 * @author: lv.bo
 * @create: 2024-03-06 09:52
 */
@Data
public class ProfileGroupVO {

    /**
     *  群体名
     */
    private String name;

    /**
     * 群体列表
     */
    private String groupLabel;

    /**
     * 群体级别
     */
    private String controlLevel;

    /**
     * 录入时间
     */
    private String createTime;

    /**
     * 录入单位
     */
    private String createDept;

    /**
     * 工作目标
     */
    private String workTarget;

    /**
     * 基本情况
     */
    private String basicInfo;

    /**
     * 风险背景
     */
    private String mainDemand;

    /**
     * 工作措施
     */
    private String workMeasures;

    /**
     * 化解难点
     */
    private String petitionInfo;

    /**
     * 维权及打处情况
     */
    private String punishInfo;

    /**
     *  现实动向
     */
    private String realtimeTrend;

    /**
     *  党政责任部门
     */
    private String controlGovernment;

    /**
     * 党政责任人
     */
    private String controlGovernmentPerson;

    /**
     * 党政责任人联系方式
     */
    private String controlGovernmentContact;

    /**
     * 化解责任单位
     */
    private String defuseGovernment;

    /**
     * 化解责任人
     */
    private String defuseGovernmentPerson;

    /**
     * 化解责任人联系方式
     */
    private String defuseGovernmentContact;

    /**
     * 责任街道社区
     */
    private String controlCommunity;

    /**
     * 社区责任人
     */
    private String controlCommunityPerson;

    /**
     * 社区责任人联系方式
     */
    private String controlCommunityContact;

    /**
     * 责任分局
     */
    private String controlBureau;

    /**
     * 责任分局领导
     */
    private String controlBureauLeader;

    /**
     * 责任分局联系方式
     */
    private String controlBureauContact;

    /**
     * 责任警种部门
     */
    private String controlPolice;

    /**
     * 责任警种部门领导
     */
    private String controlPoliceLeader;

    /**
     * 责任警种部门联系方式
     */
    private String controlPoliceContact;

    /**
     * 责任派出所
     */
    private String controlStation;

    /**
     * 责任派出所领导
     */
    private String controlStationLeader;

    /**
     *  责任民警
     */
    private String dutyPolice;

    /**
     * 群体材料
     */
    private String groupFiles;

    /**
     * 化解难度名称
     */
    private String resolveDifficultyName;

    /**
     * 经营类别
     */
    private String businessCategory;

    /**
     * 领头人
     */
    private String leaderName;

    /**
     * 领头人性别名称
     */
    private String leaderGenderName;

    /**
     * 领头人身份证号码
     */
    private String leaderIdNumber;

    /**
     * 领头人户籍地名称
     */
    private String leaderRegisteredResidenceName;

    /**
     * 领头人基础摸排地址名称
     */
    private String leaderBasicInvestigationAddressName;

    /**
     * 领头人务工地址名称
     */
    private String leaderWorkAddressName;

    /**
     * 领头人工作类别
     */
    private String leaderWorkCategory;

    /**
     * 领头人职业类别
     */
    private String leaderOccupationCategory;

    /**
     * 领头人电话
     */
    private String leaderTel;

    /**
     * 领头人其他违法犯罪情况
     */
    private String leaderOtherIllegalActivities;

    /**
     * 领头人流入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date leaderInflowTime;

    /**
     * 领头人具体研判情况
     */
    private String leaderSpecificAnalysis;


    /**
     * 领头人是否十类人员名称
     */
    private String leaderIsSlrylbName;
}
