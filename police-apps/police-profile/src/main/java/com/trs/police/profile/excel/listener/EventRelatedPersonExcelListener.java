package com.trs.police.profile.excel.listener;


import com.alibaba.excel.context.AnalysisContext;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.mq.utils.CollectionUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.ImportFailVO;
import com.trs.police.profile.constant.ExcelConstant;
import com.trs.police.profile.domain.entity.*;
import com.trs.police.profile.excel.model.EventRelatedPersonModel;
import com.trs.police.profile.mapper.*;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * Excel 读取类 配合 {@link com.alibaba.excel.EasyExcelFactory} 的 read() 使用
 * 事件的导入
 *
 * <AUTHOR>
 * @date 2024/4/2 14:41
 */
@Slf4j
@Builder
public class EventRelatedPersonExcelListener extends BaseReadExcelListener<EventRelatedPersonModel> {

    private PersonMapper personMapper;
    private Long eventId;

    private PersonEventRelationMapper personEventRelationMapper;

    /**
     * 重复策略 1-不导入, 2-覆盖导入，3-继续导入
     */
    private final String repeatStrategy;

    private Event event;

    private List<PersonEventRelation> relationList;

    private void init(){
        relationList = personEventRelationMapper.selectList(new QueryWrapper<PersonEventRelation>().eq("event_id",eventId));
    }

    @Override
    @Transactional
    public void invoke(EventRelatedPersonModel model, AnalysisContext analysisContext) {
        //扫描的索引记录
        curIndex.incrementAndGet();
        //导入数据的校验：群体名称
        if (!checkAttribute(model)) {
            return;
        }
        if (CollectionUtils.isEmpty(relationList)){
            init();
        }
        Person person = personMapper.selectByPersonIdNumber(model.getIdCard());
        if (Objects.isNull(person)){
            return;
        }
        //新增，不需要考虑策略
        PersonEventRelation personEventRelation = relationList.stream().filter(e -> e.getPersonId().equals(person.getId()) && e.getEventId().equals(event.getId()))
                .findFirst().orElse(null);
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(personEventRelation)){
            personEventRelation = new PersonEventRelation();
            personEventRelation.setCreateTime(LocalDateTime.now());
            personEventRelation.setCreateUserId(currentUser.getId());
            personEventRelation.setCreateDeptId(currentUser.getDeptId());
            personEventRelation.setUpdateTime(LocalDateTime.now());
            personEventRelation.setUpdateUserId(currentUser.getId());
            personEventRelation.setUpdateDeptId(currentUser.getDeptId());
            personEventRelation.setPersonId(person.getId());
            personEventRelation.setEventId(event.getId());
            personEventRelation.setEventCode(event.getEventCode());
            personEventRelationMapper.insert(personEventRelation);
            successCount.incrementAndGet();
            allCount.incrementAndGet();
            return;
        }
        if (repeatStrategy.equalsIgnoreCase(ExcelConstant.REPEAT_STRATEGY_STOP)) {
            ImportFailVO importFailVO = ImportFailVO.builder()
                    .status(ExcelConstant.IMPORT_STATUS_FAILED)
                    .rowIndex(curIndex.get())
                    .desc("【" + "事件：" + event.getName() + "和" + "人员：" + model.getName() + "】的关联关系已存在！")
                    .build();
            importFailVOList.add(importFailVO);
            allCount.incrementAndGet();
        } else if (repeatStrategy.equalsIgnoreCase(ExcelConstant.REPEAT_STRATEGY_REPLACE)) {
            //将model的属性拷贝给实体
            personEventRelation.setPersonId(person.getId());
            personEventRelation.setEventId(event.getId());
            personEventRelation.setUpdateTime(LocalDateTime.now());
            personEventRelation.setUpdateUserId(currentUser.getId());
            personEventRelation.setUpdateDeptId(currentUser.getDeptId());
            personEventRelationMapper.updateById(personEventRelation);
            successCount.incrementAndGet();
            allCount.incrementAndGet();
        } else {
            log.error("没有指定导入策略，不做处理！");
            successCount.incrementAndGet();
            allCount.incrementAndGet();
        }
    }

    /**
     * 属性检测
     *
     * @param eventModel 当前导入的数据--对应excel中的一行数据
     * @return boolean
     */
    private boolean checkAttribute(EventRelatedPersonModel eventModel) {
        if (StringUtils.isEmpty(eventModel.getName())) {
            //总数+1，失败数据记录
            allCount.incrementAndGet();
            ImportFailVO importFailVO = ImportFailVO.builder()
                    .status(ExcelConstant.IMPORT_STATUS_FAILED)
                    .rowIndex(curIndex.get())
                    .desc("文件格式有误，请检查")
                    .build();
            importFailVOList.add(importFailVO);
            return false;
        }
        return true;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
