package com.trs.police.profile.schema.controller;

import com.trs.police.profile.schema.service.DynamicTableService;
import com.trs.police.profile.schema.vo.DynamicTableResultVO;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 动态表格controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/table")
public class DynamicTableController {

    @Resource
    private DynamicTableService dynamicTableService;

    /**
     * 动态表格查询
     *
     * @param moduleId 模块id
     * @param relatedId 对象id
     * @return {@link DynamicTableResultVO}
     */
    @GetMapping("/{moduleId}")
    public DynamicTableResultVO selectTableSchema(@PathVariable("moduleId") Long moduleId, Long relatedId) {
        return dynamicTableService.selectTableSchema(moduleId, relatedId);
    }

    /**
     * 动态表格查询
     *
     * @param moduleId 模块id
     * @param relatedId 对象id
     * @return {@link DynamicTableResultVO}
     */
    @GetMapping("/list/{moduleId}")
    public List<DynamicTableResultVO> selectListTableSchema(@PathVariable("moduleId") Long moduleId, Long relatedId) {
        return dynamicTableService.selectListTableSchema(moduleId, relatedId);
    }
}
