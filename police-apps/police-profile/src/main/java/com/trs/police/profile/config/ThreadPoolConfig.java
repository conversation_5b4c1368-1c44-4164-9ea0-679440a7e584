package com.trs.police.profile.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @date 2021/8/2 15:09
 */
@Configuration
public class ThreadPoolConfig {

    /**
     * 在容器中创建名为composite的线程池执行器用于处理线程池任务
     *
     * @return {@link Executor}
     */
    @Bean(name = "executorService")
    public Executor executorService() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(5);
        // 设置最大线程数
        executor.setMaxPoolSize(30);
        // 设置队列长度
        executor.setQueueCapacity(100);
        // 设置线程存活时间
        executor.setKeepAliveSeconds(600);
        // 设置线程前缀
        executor.setThreadNamePrefix("profile-update-");
        //设置最长等待时间
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(1800);
        //设置丢弃策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 线程池初始化
        executor.initialize();
        return executor;
    }
}
