package com.trs.police.profile.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.entity.BigScreenPinDataEntity;
import com.trs.police.common.core.mapper.BigScreenPinDataMapper;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大屏服务
 *
 * <AUTHOR>
 */
@Service
public class ScreenService {

    @Resource
    private BigScreenPinDataMapper bigScreenPinDataMapper;

    /**
     * 是否需要显示大屏信息
     *
     * @param request 请求参数
     * @return 是否需要显示大屏信息
     */
    public Boolean needShowScreen(ListParamsRequest request) {
        Optional<KeyValueTypeVO> needShowName = findKey(request, "needShowName");
        if (needShowName.isEmpty()) {
            return false;
        }
        return "1".equals(String.valueOf(needShowName.get().getValue()));
    }

    private Optional<KeyValueTypeVO> findKey(ListParamsRequest request, String key) {
        return request.getFilterParams()
                .stream()
                .filter(r -> Objects.equals(key, r.getKey()))
                .findAny();
    }

    /**
     * 查找大屏关联表数据
     *
     * @param type 大屏关联类型
     * @param records 记录
     * @param getId 钉住的主键
     * @param <T> 数据类型
     * @return 大屏数据列表
     */
    public <T> List<BigScreenPinDataEntity> findScreenRelation(String type, List<T> records, Function<T, String> getId) {
        if (Objects.isNull(records) || records.isEmpty()) {
            return new ArrayList<>();
        }
        // 地域
        String code = AuthHelper.getCurrentUser().getDept().getDistrictCode();
        String codePrefix = AreaUtils.areaKey(code);
        List<String> ids = records.stream()
                .map(getId)
                .distinct()
                .collect(Collectors.toList());
        QueryWrapper<BigScreenPinDataEntity> wrapper = new QueryWrapper<BigScreenPinDataEntity>()
                .eq("obj_type", type)
                .likeRight("district_code", codePrefix)
                .in("obj_id", ids);
        List<BigScreenPinDataEntity> screenRelation = bigScreenPinDataMapper.selectList(wrapper);
        return screenRelation;
    }
}
