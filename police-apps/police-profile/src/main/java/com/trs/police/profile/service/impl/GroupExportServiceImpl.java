package com.trs.police.profile.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.PoliceKindEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.FileInfo;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.core.vo.ExportExcelVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.permission.DataPermissionInfo;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.vo.GroupListVO;
import com.trs.police.profile.constant.ExportListInfo;
import com.trs.police.profile.constant.PoliceKindConstants;
import com.trs.police.profile.constant.enums.ProfileStatus;
import com.trs.police.profile.domain.entity.*;
import com.trs.police.profile.domain.vo.*;
import com.trs.police.profile.mapper.*;
import com.trs.police.profile.mgr.ProfileExportMgr;
import com.trs.police.profile.schema.field.DictProperty;
import com.trs.police.profile.schema.field.ListMapperFactory;
import com.trs.police.profile.schema.mapper.SchemaMapper;
import com.trs.police.profile.schema.module.ListModuleSchema;
import com.trs.police.profile.schema.module.ModuleEntity;
import com.trs.police.profile.schema.service.SchemaService;
import com.trs.police.profile.service.GroupExportService;
import com.trs.police.profile.service.ListResultHelper;
import com.trs.police.profile.util.EnvUtil;
import com.trs.police.profile.util.ExcelUtil;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/29
 */
@Data
@Slf4j
@Service
public class GroupExportServiceImpl implements GroupExportService {
    @Resource
    private GroupMapper groupMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private DictService dictService;
    @Resource
    SchemaService schemaService;
    @Resource
    SchemaMapper schemaMapper;
    @Resource
    LabelMapper labelMapper;
    @Resource
    private ProfileExportMgr exportMgr;
    @Resource
    private ProfileGroupGovControlMapper profileGroupGovControlMapper;
    @Resource
    private GroupDzControlEntityMapper groupDzControlEntityMapper;
    @Resource
    private ProfileGroupPoliceControlMapper profileGroupPoliceControlMapper;
    @Resource
    private ProfileSensitiveTimeMapper profileSensitiveTimeMapper;
    @Resource
    private PersonGroupRelationMapper personGroupRelationMapper;
    @Resource
    private GroupClueRelationMapper groupClueRelationMapper;
    @Resource
    private GroupFileRelationMapper groupFileRelationMapper;
    @Resource
    private EventGroupRelationMapper eventGroupMapper;
    @Resource
    private ProfileGroupPunishInfoRelationMapper groupPunishInfoRelationMapper;
    @Resource
    private ProfileGroupWorkMeasuresRelationMapper groupWorkMeasuresRelationMapper;
    @Resource
    private ProfileGroupRealtimeTrendRelationMapper groupRealtimeTrendRelationMapper;
    @Resource
    private ProfileGroupBaseInfoRelationMapper groupBaseInfoRelationMapper;
    @Resource
    private ProfileGroupInfoMapper groupInfoMapper;
    @Autowired
    private GroupWorkRecordMapper groupWorkRecordMapper;
    @Resource
    private OssService ossService;

    @Override
    public void groupExport(HttpServletResponse response, ExportParams params, Long moduleId) throws IOException {
        final String fileName = URLEncoder.encode("群体列表导出", StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        List<Long> ids;
        if (Boolean.FALSE.equals(params.getIsAll())) {
            if (params.getIds().isEmpty()) {
                return;
            }
            ids = params.getIds();
            //查全部
        } else {
            ModuleEntity module = schemaService.getModuleSchema(moduleId);
            if (java.util.Objects.isNull(module) || java.util.Objects.isNull(module.getListSchema())) {
                return;
            }
            final ListModuleSchema schema = module.getListSchema();
            ListParamsRequest listParamsRequest = params.getListParamsRequest();
            final DataPermissionInfo dataPermission = permissionService.getCurrentUserDataPermissionInfo();
            ids = schemaMapper.doSelectIds(schema, listParamsRequest.getFilterParams(),
                    listParamsRequest.getSearchParams(), module.getDatabaseRelation(), null,
                    dataPermission);
        }
        List<GroupExportVO> allCollect = groupMapper.selectAll();
        AtomicInteger index = new AtomicInteger(1);
        List<GroupExportVO> collect = ids.parallelStream().map(id -> {
            GroupExportVO groupExport = groupMapper.getGroupExport(id);
            if (StringUtils.isNotBlank(groupExport.getGroupLabels()) && !JsonUtil.parseArray(groupExport.getGroupLabels(), Long.class).isEmpty()) {
                List<Long> labelIds = JsonUtil.parseArray(groupExport.getGroupLabels(), Long.class);
                groupExport.setFxxl(labelMapper.getFxxl("group", labelIds));
                groupExport.setFxlb(labelMapper.getFxlb("group", labelIds));
            }
            return groupExport;
        }).collect(Collectors.toList());
        collect.forEach(e -> e.setXh(index.getAndIncrement()));
        try {
            InputStream inputStream = new ClassPathResource("/template/groupListExport.xlsx").getInputStream();
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inputStream).build();
            writeTotal(excelWriter, allCollect, response);
            writeDate(excelWriter, collect, response);
            excelWriter.finish();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void groupExportV2(HttpServletResponse response, ExportParams params) {
        Object policeKind = params.getListParamsRequest().getFilterParams().stream()
                .filter(f -> "policeKind".equals(f.getKey()))
                .map(KeyValueTypeVO::getValue)
                .findFirst().get();
        String name = "群体列表导出-" + PoliceKindEnum.codeOf((Integer) policeKind).getName();
        final String fileName = URLEncoder.encode(name, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        List<GroupListVO> items = new ArrayList<>();
        List<GroupNewExportVO> newExportList = new ArrayList<>();
        if (Boolean.FALSE.equals(params.getIsAll())) {
            if (params.getIds().isEmpty()) {
                return;
            }
            items = groupMapper.selectByIds(params.getIds(), (Integer) policeKind, params.getListParamsRequest().getSortParams());
            buildDeptInfo(items, policeKind);
            buildInfo(items, params.getListParamsRequest());
        } else {
            //查全部 调用群体列表接口
            items = BeanUtil.getBean(GroupServiceImpl.class).groupList(params.getListParamsRequest()).getItems();
        }
        newExportList = items.stream()
                .map(item -> {
                    GroupNewExportVO vo = new GroupNewExportVO();
                    BeanUtils.copyProperties(item, vo);
                    vo.setGroupLabel(CollectionUtils.isEmpty(item.getGroupLabel()) ? null : String.join(",", item.getGroupLabel()));
                    vo.setCreateTime(TimeUtils.dateToString(item.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                    vo.setUpdateTime(TimeUtils.dateToString(item.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
                    vo.setControlPoliceName(CollectionUtils.isEmpty(item.getControlPoliceName()) ? null : String.join(",", item.getControlPoliceName()));
                    vo.setPoliceKindName(String.join(",", item.getPoliceKindName()));
                    vo.setControlZaBureauName(CollectionUtils.isEmpty(item.getControlBureauName()) ? null : String.join(",", item.getControlBureauName()));
                    vo.setControlBureauName(CollectionUtils.isEmpty(item.getControlBureauName()) ? null : String.join(",", item.getControlBureauName()));
                    return vo;
                })
                .collect(Collectors.toList());
        try {
            String exportFilePath = buildExportFilePath((Integer) policeKind);
            InputStream inputStream = new ClassPathResource(exportFilePath).getInputStream();
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();
            excelWriter.fill(newExportList, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void writeDate(ExcelWriter excelWriter, List<GroupExportVO> collect, HttpServletResponse response) {
        try {
            WriteSheet writeSheet = EasyExcel.writerSheet("重点群体").build();
            excelWriter.fill(collect, writeSheet);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void writeTotal(ExcelWriter excelWriter, List<GroupExportVO> allCollect, HttpServletResponse response) {
        try {
            FillData fillData = setFillInfo(allCollect);
            WriteSheet writeSheet = EasyExcel.writerSheet("首页").build();
            excelWriter.fill(fillData, writeSheet);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private FillData setFillInfo(List<GroupExportVO> collect) {
        FillData fillData = new FillData();
        fillData.setDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        fillData.setCount(Long.valueOf(collect.stream().map(GroupExportVO::getId).distinct().collect(Collectors.toList()).size()));
        //统计管控警钟对应数量
        List<String> controlPolice = collect.stream().filter(e -> StringUtils.isNotEmpty(e.getControlPolice()))
                .map(GroupExportVO::getControlPolice).collect(Collectors.toList());
        Map<String, Long> controlPoliceMap = controlPolice.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        fillData.setControlPoliceInfo(controlPoliceMap.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .map(entry -> entry.getKey() + entry.getValue() + "人")
                .collect(Collectors.joining("、")));
        //统计地区对应数量
        List<String> area = collect.stream().filter(e -> StringUtils.isNotEmpty(e.getControlBureau()))
                .map(GroupExportVO::getControlBureau).collect(Collectors.toList());
        Map<String, Long> areaMap = area.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        fillData.setAreaCountInfo(areaMap.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .map(entry -> entry.getKey() + entry.getValue() + "人")
                .collect(Collectors.joining("、")));
        //统计群体类别前五
        List<String> groupList = Arrays.asList(collect.stream().filter(e -> StringUtils.isNotEmpty(e.getName()))
                .map(GroupExportVO::getName)
                .collect(Collectors.joining(",")).split(","));
        Map<String, Long> groupMap = groupList.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        fillData.setGroupInfo(groupMap.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> entry.getKey())
                .collect(Collectors.joining("、")));
        fillData.setInfo(MessageFormat.format(ExportListInfo.GROUP_INFO
                , fillData.getDate()
                , fillData.getCount().toString()
                , StringUtils.isEmpty(fillData.getControlPoliceInfo()) ? "" : ("其中" + fillData.getControlPoliceInfo())
                , StringUtils.isEmpty(fillData.getGroupInfo()) ? "" : ("涉及群体" + fillData.getGroupInfo())
                , StringUtils.isEmpty(fillData.getAreaCountInfo()) ? "" : ("从辖区看，" + fillData.getAreaCountInfo())
        ));
        return fillData;
    }

    private void buildDeptInfo(List<GroupListVO> records, Object policeKind) {
        List<Long> groupIdList = records.stream().map(GroupListVO::getId).collect(Collectors.toList());
        List<GroupListVO> data = new ArrayList<>();
        if (policeKind.equals(4)) {
            data = groupMapper.buildZaDeptInfo(groupIdList, policeKind);
        } else {
            data = groupMapper.buildDeptInfo(groupIdList, policeKind);
        }
        Map<Long, String> controlBureauMap = data.stream()
                .filter(record -> !Objects.isNull(record.getControlBureau()))
                .collect(Collectors.toMap(GroupListVO::getId, GroupListVO::getControlBureau));
        Map<Long, String> controlPoliceMap = data.stream()
                .filter(record -> !Objects.isNull(record.getControlPolice()))
                .collect(Collectors.toMap(GroupListVO::getId, GroupListVO::getControlPolice));
        Map<Long, List<Long>> controlPersonMap = data.stream()
                .filter(record -> !Objects.isNull(record.getControlPersonList()))
                .collect(Collectors.toMap(GroupListVO::getId, GroupListVO::getControlPersonList));
        records.forEach(record -> {
            record.setControlBureau(controlBureauMap.get(record.getId()));
            record.setControlPolice(controlPoliceMap.get(record.getId()));
            record.setControlPersonList(controlPersonMap.get(record.getId()));
        });
    }

    private void buildInfo(List<GroupListVO> records, ListParamsRequest paramsRequest) {
        List<String> deptCodeList = records.stream()
                .flatMap(vo -> {
                    List<String> codeList = new ArrayList<>();
                    if (!Objects.isNull(vo.getControlPolice())) {
                        codeList.add(vo.getControlPolice());
                    }
                    if (!Objects.isNull(vo.getControlBureau())) {
                        codeList.add(vo.getControlBureau());
                    }
                    return codeList.stream();
                })
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> deptMap;
        if (!CollectionUtils.isEmpty(deptCodeList)) {
            deptMap = permissionService.getDeptByCodes(deptCodeList)
                    .stream().collect(Collectors.toMap(DeptDto::getCode, DeptDto::getShortName));
        } else {
            deptMap = new HashMap<>();
        }
        Map<Long, String> labelMap = labelMapper.findByModuleAndSubjectId("group")
                .stream().collect(Collectors.toMap(Label::getId, Label::getName));
        Map<Long, String> dictMap = dictService.commonSearch("police_kind", null, null, null)
                .stream().collect(Collectors.toMap(Dict2VO::getCode, Dict2VO::getName));
        records.forEach(groupListVO -> {
            //明文回填
            ProfileStatus.ofCode(groupListVO.getProfileStatus()).map(ProfileStatus::getName).ifPresent(groupListVO::setProfileStatusName);
            List<Long> policeKind = groupListVO.getPoliceKind();
            if (!CollectionUtils.isEmpty(policeKind)) {
                List<String> policeKindName = new ArrayList<>();
                policeKind.forEach(kind -> policeKindName.add(dictMap.getOrDefault(kind, "")));
                groupListVO.setPoliceKindName(policeKindName);
            }

            List<Long> groupLabel = groupListVO.getGroupType();
            if (!CollectionUtils.isEmpty(groupLabel)) {
                List<String> groupLabelName = new ArrayList<>();
                groupLabel.forEach(label -> groupLabelName.add(labelMap.getOrDefault(label, "")));
                groupListVO.setGroupLabel(groupLabelName);
            }
            if (!Objects.isNull(groupListVO.getControlBureau())) {
                List<String> controlBureauList = Arrays.stream(groupListVO.getControlBureau().split(","))
                        .map(String::trim)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(controlBureauList) || !CollectionUtils.isEmpty(deptMap)) {
                    List<String> controlBureauName = new ArrayList<>();
                    controlBureauList.forEach(bureau -> controlBureauName.add(deptMap.getOrDefault(bureau, "")));
                    groupListVO.setControlBureauName(controlBureauName);
                }
            }
            if (!Objects.isNull(groupListVO.getControlPolice())) {
                List<String> controlPoliceList = Arrays.stream(groupListVO.getControlPolice().split(","))
                        .map(String::trim)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(controlPoliceList) || !CollectionUtils.isEmpty(deptMap)) {
                    List<String> controlPoliceName = new ArrayList<>();
                    controlPoliceList.forEach(police -> controlPoliceName.add(deptMap.getOrDefault(police, "")));
                    groupListVO.setControlPoliceName(controlPoliceName);
                }
            }
        });
        // 返回审批状态
        ListResultHelper.buildApprovalStatus(paramsRequest, records, GroupListVO::getApprovalDetailStr, GroupListVO::setApprovalDetail);
        ListMapperFactory.getMapper(ListMapperFactory.DISTRICT_TO_NAME).mappingDisplayValue(records, null, GroupListVO::getLeaderBasicInvestigationAddress, GroupListVO::setLeaderBasicInvestigationAddressName);
        ListMapperFactory.getMapper(ListMapperFactory.DISTRICT_TO_NAME).mappingDisplayValue(records, null, GroupListVO::getLeaderWorkAddress, GroupListVO::setLeaderWorkAddressName);
        ListMapperFactory.getMapper(ListMapperFactory.DISTRICT_TO_NAME).mappingDisplayValue(records, null, GroupListVO::getLeaderRegisteredResidence, GroupListVO::setLeaderRegisteredResidenceName);
        ListMapperFactory.getMapper(ListMapperFactory.DICT_CODE_TO_NAME).mappingDisplayValue(records, DictProperty.ofType("gender"), GroupListVO::getLeaderGender, GroupListVO::setLeaderGenderName);
        ListMapperFactory.getMapper(ListMapperFactory.DICT_CODE_TO_NAME).mappingDisplayValue(records, DictProperty.ofType("yes_or_not"), GroupListVO::getLeaderIsSlrylb, GroupListVO::setLeaderIsSlrylbName);
        ListMapperFactory.getMapper(ListMapperFactory.USER_ID_TO_NAME).mappingDisplayValueWithListKey(records, null, GroupListVO::getControlPersonList, GroupListVO::setControlPersonName);
    }

    private String buildExportFilePath(Integer policeKind) {
        switch (policeKind) {
            case 3:
                return "/template/jzGroupListExport.xlsx";
            case 4:
                return "/template/zaGroupListExport.xlsx";
            case 99:
            default:
                return "/template/jtGroupListExport.xlsx";
        }
    }


    @Override
    public void downloadGroupRecord(HttpServletResponse response, Long groupId) {
        Group group = groupMapper.selectById(groupId);
        if (group == null) {
            throw new TRSException("获取群体档案详情出错！");
        }
        try {
            ProfileGroupVO profileGroupVO = buildProfileGroupVO(group, null);
            List<ProfileSensitiveExportVO> sensitiveExportList = buildSensitiveExportList(group, null);
            List<PersonGroupExportVO> personGroupExportList = getGroupPersonList(group, null);
            List<PersonClueExportVO> groupClueExportList = getGroupClueList(group, null);
            List<PersonEventExportVO> eventExportList = getEventList(group);
            Map<String, Object> data = new HashMap<>();
            data.put("profileGroupVO", profileGroupVO);
            data.put("sensitive", sensitiveExportList);
            data.put("pGroup", personGroupExportList);
            data.put("pEvent", eventExportList);
            data.put("gClue", groupClueExportList);
            ExportExcelVO vo = new ExportExcelVO();
            String excelName = "downloadGroupTemplate.xlsx";
            if(EnvUtil.isDy()){
                excelName = "downloadGroupTemplateDy.xlsx";
            }
            vo.setTemplateFilePath("/template/"+excelName);
            vo.setResponse(response);
            vo.setData(data);
            vo.setExcelName("重点群体档案-" + group.getName() + ".xlsx");
            List<FileInfo> fileInfos = groupFileRelationMapper.queryGroupFileInfoList(groupId);
            if (CollectionUtils.isEmpty(fileInfos)){
                ExcelUtil.downloadRecordExcel(vo);
            }else {
                profileGroupVO.setGroupFiles("见压缩文件");
                Map<String, Object> finalHashMap = new HashMap<>(2);
                //群体材料文件map
                HashMap<Object, Object> map = new HashMap<>();
                fileInfos.forEach(e -> {
                    ResponseEntity<byte[]> download = ossService.download(String.valueOf(e.getId()));
                    byte[] body = download.getBody();
                    if (body == null || body.length == 0){
                        return;
                    }
                    map.put(e.getName(), body);
                });
                finalHashMap.put("群体材料", map);
                vo.setFileMap(finalHashMap);
                vo.setZipName("重点群体档案-"+ group.getName() +".zip");
                ExcelUtil.downloadRecordExcel(vo);
            }
        } catch (Exception e) {
            log.error("下载群体档案失败:[{}]！", e.getMessage(), e);
            throw new TRSException("下载群体档案失败，请重试！");
        }
    }

    @Override
    public void downloadGroup(HttpServletResponse response, Long groupId, Integer policeKind) throws Exception {
        Group group = groupMapper.selectByPoliceKind(groupId, null);
        if (group == null) {
            throw new TRSException("获取群体档案详情出错！");
        }
        List<Long> policeKindList = group.getPoliceKind();
        if (CollectionUtils.isEmpty(policeKindList)) {
            throw new TRSException("群体未关联管控警种！");
        }
        ExportExcelVO vo = new ExportExcelVO();
        vo.setTemplateFilePath("/template/downloadNewGroupTemplate.xlsx");
        vo.setResponse(response);
        vo.setMergeRowIndex(3);
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Map<String, Map<String, Object>> sheetData = new HashMap<>();
        policeKindList.stream().parallel().forEach(police -> {
            //塞入认证信息，使子线程能获取到用户
            SecurityContextHolder.setContext(securityContext);
            Tuple2<String, Map<String, Object>> sd = groupRecordByPoliceKind(group, police,vo);
            sheetData.put(sd._1, sd._2);
        });
        vo.setSheetData(sheetData);
        List<String> exportNameList = Arrays.asList("治安", "经侦", "其他");
        String policeKindExportName = exportNameList.stream()
                .filter(name -> vo.getSheetData().keySet().contains(name))
                .map(key -> PoliceKindConstants.POLICE_KIND_CNNAME_EXPORTNAME_MAP.get(key)).collect(Collectors.joining("-"));
        vo.setExcelName("重点人员档案-" + policeKindExportName + "-" + group.getName() + ".xlsx");
        ExcelUtil.downloadRecordExcelWithMultiSheet(vo);
    }

    /**
     * 构建ProfileGroupVO
     *
     * @param group      群体
     * @param policeKind policeKind
     * @return ProfileGroupVO
     */
    public ProfileGroupVO buildProfileGroupVO(Group group, Long policeKind) {
        if (group == null) {
            return null;
        }
        ProfileGroupVO vo = new ProfileGroupVO();
        vo.setName(group.getName());
        vo.setGroupLabel(exportMgr.listToString(exportMgr.labelIdArrayToName(group.getGroupLabel())));
        vo.setControlLevel(exportMgr.dictCodeToName(group.getControlLevel(), "profile_person_control_level"));
        vo.setCreateTime(TimeUtil.getSimpleTime(group.getCreateTime()));
        vo.setCreateDept(exportMgr.deptIdToDeptName(group.getCreateDeptId()));
        if (Objects.nonNull(policeKind)) {
            //处理党政管控信息
            dealGroupDzControlInfo(group, vo, policeKind);
            //处理新公安管控信息
            dealNewGroupControlInfo(group, vo, policeKind);
        } else {
            buildProfileGroup(group, vo);
            //处理公安管控信息
            dealGroupControlInfo(group, vo);
        }
        vo.setResolveDifficultyName(exportMgr.dictCodeToName(group.getResolveDifficulty(), "profile_resolve_difficulty"));
        return vo;
    }

    private void dealGroupDzControlInfo(Group group, ProfileGroupVO vo, Long policeKind) {
        if (policeKind.equals(4L)) {
            processControlEntities(vo, groupDzControlEntityMapper.selectList(
                    new QueryWrapper<ProfileGroupDzControlEntity>().eq("group_id", group.getId())
            ), policeKind);
        } else {
            QueryWrapper<ProfileGroupGovControlEntity> policeQueryWrapper = new QueryWrapper<>();
            policeQueryWrapper.eq("group_id", group.getId());
            policeQueryWrapper.eq("police_kind", policeKind);
            processControlEntities(vo, profileGroupGovControlMapper.selectList(policeQueryWrapper), policeKind);
        }
    }

    private void processControlEntities(ProfileGroupVO vo, List<?> controlEntities, Long policeKind) {
        if (CollectionUtils.isEmpty(controlEntities)) {
            return;
        }
        StringBuilder controlGovernment = new StringBuilder();
        StringBuilder controlGovernmentPerson = new StringBuilder();
        StringBuilder controlGovernmentContact = new StringBuilder();
        StringBuilder controlCommunity = new StringBuilder();
        StringBuilder controlCommunityPerson = new StringBuilder();
        StringBuilder controlCommunityContact = new StringBuilder();
        StringBuilder defuseGovernment = new StringBuilder();
        StringBuilder defuseGovernmentPerson = new StringBuilder();
        StringBuilder defuseGovernmentContact = new StringBuilder();
        for (Object entity : controlEntities) {
            if (entity instanceof ProfileGroupDzControlEntity) {
                ProfileGroupDzControlEntity dzControlEntity = (ProfileGroupDzControlEntity) entity;
                appendIfNotEmpty(controlGovernment, dzControlEntity.getControlGovernment());
                appendIfNotEmpty(controlGovernmentPerson, dzControlEntity.getControlGovernmentPerson());
                appendIfNotEmpty(controlGovernmentContact, dzControlEntity.getControlGovernmentContact());
                appendIfNotEmpty(defuseGovernment, dzControlEntity.getDefuseGovernment());
                appendIfNotEmpty(defuseGovernmentPerson, dzControlEntity.getDefuseGovernmentPerson());
                appendIfNotEmpty(defuseGovernmentContact, dzControlEntity.getDefuseGovernmentContact());
            } else if (entity instanceof ProfileGroupGovControlEntity) {
                ProfileGroupGovControlEntity govControlEntity = (ProfileGroupGovControlEntity) entity;
                appendIfNotEmpty(controlGovernment, govControlEntity.getControlGovernment());
                appendIfNotEmpty(controlGovernmentPerson, govControlEntity.getControlGovernmentPerson());
                appendIfNotEmpty(controlGovernmentContact, govControlEntity.getControlGovernmentContact());
                appendIfNotEmpty(controlCommunity, govControlEntity.getControlCommunity());
                appendIfNotEmpty(controlCommunityPerson, govControlEntity.getControlCommunityPerson());
                appendIfNotEmpty(controlCommunityContact, govControlEntity.getControlCommunityContact());
            }
        }
        vo.setControlGovernment(removeTrailingComma(controlGovernment.toString()));
        vo.setControlGovernmentPerson(removeTrailingComma(controlGovernmentPerson.toString()));
        vo.setControlGovernmentContact(removeTrailingComma(controlGovernmentContact.toString()));
        vo.setControlCommunity(removeTrailingComma(controlCommunity.toString()));
        vo.setControlCommunityPerson(removeTrailingComma(controlCommunityPerson.toString()));
        vo.setControlCommunityContact(removeTrailingComma(controlCommunityContact.toString()));
        if (policeKind.equals(4L)) {
            vo.setDefuseGovernment(removeTrailingComma(defuseGovernment.toString()));
            vo.setDefuseGovernmentPerson(removeTrailingComma(defuseGovernmentPerson.toString()));
            vo.setDefuseGovernmentContact(removeTrailingComma(defuseGovernmentContact.toString()));
        }
    }

    private void appendIfNotEmpty(StringBuilder sb, String value) {
        if (StringUtils.isNotEmpty(value)) {
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append(value);
        }
    }

    private String removeTrailingComma(String str) {
        if (str != null && str.endsWith(",")) {
            return str.substring(0, str.length() - 1);
        }
        return str;
    }

    private void dealNewGroupControlInfo(Group group, ProfileGroupVO vo, Long policeKind) {
        QueryWrapper<ProfileGroupPoliceControlEntity> policeQueryWrapper = new QueryWrapper<>();
        policeQueryWrapper.eq("group_id", group.getId());
        policeQueryWrapper.eq("police_kind", policeKind);
        List<ProfileGroupPoliceControlEntity> policeControlEntityList = profileGroupPoliceControlMapper.selectList(policeQueryWrapper);
        if (CollectionUtils.isEmpty(policeControlEntityList)) {
            return;
        }
        StringBuilder dutyPolice = new StringBuilder();
        StringBuilder controlBureau = new StringBuilder();
        StringBuilder controlBureauLeader = new StringBuilder();
        StringBuilder controlBureauContact = new StringBuilder();
        StringBuilder controlPolice = new StringBuilder();
        StringBuilder controlPoliceLeader = new StringBuilder();
        StringBuilder controlPoliceContact = new StringBuilder();
        StringBuilder controlStation = new StringBuilder();
        StringBuilder controlStationLeader = new StringBuilder();
        for (ProfileGroupPoliceControlEntity policeControlEntity : policeControlEntityList) {
            appendIfNotEmpty(dutyPolice, exportMgr.userIdArrayToUserName(policeControlEntity.getControlPerson()));
            appendIfNotEmpty(controlBureau, exportMgr.deptCodeToDeptName(policeControlEntity.getControlBureau()));
            appendIfNotEmpty(controlBureauLeader, exportMgr.userIdToUserName(policeControlEntity.getControlBureauLeader()));
            appendIfNotEmpty(controlBureauContact, policeControlEntity.getControlBureauContact());
            appendIfNotEmpty(controlPolice, exportMgr.deptCodeToDeptName(policeControlEntity.getControlPolice()));
            appendIfNotEmpty(controlPoliceLeader, exportMgr.userIdToUserName(policeControlEntity.getControlPoliceLeader()));
            appendIfNotEmpty(controlPoliceContact, policeControlEntity.getControlPoliceContact());
            appendIfNotEmpty(controlStation, exportMgr.deptCodeToDeptName(policeControlEntity.getControlStation()));
            appendIfNotEmpty(controlStationLeader, exportMgr.userIdToUserName(policeControlEntity.getControlStationLeader()));
        }
        vo.setDutyPolice(removeTrailingComma(dutyPolice.toString()));
        vo.setControlBureau(removeTrailingComma(controlBureau.toString()));
        vo.setControlBureauLeader(removeTrailingComma(controlBureauLeader.toString()));
        vo.setControlBureauContact(removeTrailingComma(controlBureauContact.toString()));
        vo.setControlPolice(removeTrailingComma(controlPolice.toString()));
        vo.setControlPoliceLeader(removeTrailingComma(controlPoliceLeader.toString()));
        vo.setControlPoliceContact(removeTrailingComma(controlPoliceContact.toString()));
        vo.setControlStation(removeTrailingComma(controlStation.toString()));
        vo.setControlStationLeader(removeTrailingComma(controlStationLeader.toString()));
    }

    /**
     * 单个警种群体档案数据
     *
     * @param group 群体
     * @param policeKind 管控警种
     * @param vo vo
     *
     * @return sheet名称，sheet数据
     */
    public Tuple2<String, Map<String, Object>> groupRecordByPoliceKind(Group group, Long policeKind,ExportExcelVO vo) {
        AuthHelper.getCurrentUser();
        PreConditionCheck.checkNotNull(policeKind, "管控警种不能为空");
        String policeKindStr = PoliceKindConstants.POLICE_KIND_CODE_CNNAME_MAP.get(policeKind);
        PreConditionCheck.checkNotNull(policeKindStr, "未知的管控警种");
        try {
            ProfileGroupVO profileGroupVO = buildProfileGroupVO(group, policeKind);
            List<ProfileGroupBasicInfoExportVO> basicInfoExportList = buildBasicInfoExportList(group);
            List<ProfileGroupWorkMeasuresExportVO> workMeasuresExportList = buildWorkMeasuresExportList(group);
            List<ProfileGroupPunishInfoExportVO> punishInfoExportList = buildPunishInfoExportList(group);
            List<ProfileGroupRealtimeTrendExportVO> realtimeTrendExportList = buildRealtimeTrendExportList(group);
            List<ProfileSensitiveExportVO> sensitiveExportList = buildSensitiveExportList(group, policeKind);
            List<PersonGroupExportVO> personGroupExportList = getGroupPersonList(group, policeKind);
            List<PersonClueExportVO> groupClueExportList = getGroupClueList(group, policeKind);
            List<ProfileGroupInfoVo> groupInfoList = getGroupInfo(group);
            List<GroupWorkRecordVo> workRecordList = getWorkRecord(group);
            Map<String, Object> data = new HashMap<>();
            data.put("profileGroupVO", profileGroupVO);
            data.put("gBasic", basicInfoExportList);
            data.put("gWork", workMeasuresExportList);
            data.put("gPunish", punishInfoExportList);
            data.put("gRealtime", realtimeTrendExportList);
            data.put("sensitive", sensitiveExportList);
            data.put("pGroup", personGroupExportList);
            data.put("gClue", groupClueExportList);
            data.put("groupInfo", groupInfoList);
            data.put("groupWorkRecord", workRecordList);
            if (policeKind == 4) {
                List<PersonEventExportVO> eventExportList = getEventList(group);
                data.put("pEvent", eventExportList);
            }
            List<FileInfo> fileInfos = groupFileRelationMapper.queryGroupFileInfoList(group.getId());
            if (!CollectionUtils.isEmpty(fileInfos)) {
                profileGroupVO.setGroupFiles("见压缩文件");
                Map<String, Object> finalHashMap = new HashMap<>(2);
                //群体材料文件map
                HashMap<Object, Object> map = new HashMap<>();
                fileInfos.forEach(e -> {
                    ResponseEntity<byte[]> download = ossService.download(String.valueOf(e.getId()));
                    byte[] body = download.getBody();
                    if (body == null || body.length == 0) {
                        return;
                    }
                    map.put(e.getName(), body);
                });
                finalHashMap.put("群体材料", map);
                vo.setFileMap(finalHashMap);
                vo.setZipName("重点人员档案-" + group.getName() + ".zip");
            }
            return Tuple.of(policeKindStr, data);
        } catch (Exception e) {
            log.error("下载人员档案失败:[{}]！", e.getMessage(), e);
            throw new TRSException("下载人员档案失败，请重试！");
        }
    }

    /**
     * 获取管控工作记录
     *
     * @param group group
     * @return 管控工作记录
     * @throws Exception exception
     */
    private List<GroupWorkRecordVo> getWorkRecord(Group group) throws Exception {
        if (group == null) {
            return null;
        }
        QueryWrapper<GroupWorkRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", group.getId());
        List<GroupWorkRecord> groupWorkRecords = groupWorkRecordMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(groupWorkRecords)) {
            return exportMgr.buildExportEmptyList(GroupWorkRecordVo.class, "管控工作记录");
        }
        List<GroupWorkRecordVo> voList = new ArrayList<>(groupWorkRecords.size());
        for (int i = 0; i < groupWorkRecords.size(); i++) {
            GroupWorkRecord groupWorkRecord = groupWorkRecords.get(i);
            GroupWorkRecordVo vo = new GroupWorkRecordVo();
            vo.setNum(i + 1);
            vo.setCellHead("管控工作记录");
            vo.setWorkDetail(groupWorkRecord.getWorkDetail());
            vo.setDestination(groupWorkRecord.getDestination());
            vo.setStatusName(exportMgr.workRecordToName(groupWorkRecord.getStatus()));
            RiskScoreConfigVO[] workMethodNew = groupWorkRecord.getWorkMethodNew();
            vo.setWorkMethodName(Arrays.stream(workMethodNew)
                    .map(RiskScoreConfigVO::getDesc)
                    .collect(Collectors.joining(",")));
            SimpleUserVO[] workPolice = groupWorkRecord.getWorkPolice();
            vo.setWorkPoliceName(Arrays.stream(workPolice)
                    .map(SimpleUserVO::getUserName)
                    .collect(Collectors.joining(",")));
            LocalDateTime startTime = groupWorkRecord.getCreateTime();
            vo.setCreateTime(startTime == null ? "--" : startTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 群组管理
     *
     * @param group group
     * @return 群组管理
     * @throws Exception exception
     */
    private List<ProfileGroupInfoVo> getGroupInfo(Group group) throws Exception {
        if (group == null) {
            return null;
        }
        QueryWrapper<ProfileGroupInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", group.getId());
        List<ProfileGroupInfoEntity> groupControlInfos = groupInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(groupControlInfos)) {
            return exportMgr.buildExportEmptyList(ProfileGroupInfoVo.class, "群组管理");
        }
        List<ProfileGroupInfoVo> voList = new ArrayList<>(groupControlInfos.size());
        for (int i = 0; i < groupControlInfos.size(); i++) {
            ProfileGroupInfoEntity groupInfoEntity = groupControlInfos.get(i);
            ProfileGroupInfoVo vo = new ProfileGroupInfoVo();
            vo.setCellHead("群组管理");
            vo.setNum(i + 1);
            vo.setGroupInfoId(groupInfoEntity.getGroupInfoId());
            vo.setGroupInfoName(groupInfoEntity.getGroupInfoName());
            vo.setGroupInfoTypeName(exportMgr.groupInfoTypeToName(groupInfoEntity.getGroupInfoType()));
            vo.setGroupInfoCount(Long.valueOf(groupInfoEntity.getGroupInfoCount()));
            vo.setGroupInfoLabelName(groupInfoEntity.getGroupInfoLabel() == 0 ? "主" : "副");
            voList.add(vo);
        }
        return voList;
    }

    private List<ProfileGroupRealtimeTrendExportVO> buildRealtimeTrendExportList(Group group) throws Exception {
        if (group == null) {
            return null;
        }
        List<ProfileGroupRealtimeTrendRelationEntity> realtimeTrendRelationEntities = groupRealtimeTrendRelationMapper.selectList(
                new QueryWrapper<ProfileGroupRealtimeTrendRelationEntity>()
                        .in("group_id", group.getId())
                        .orderByDesc("update_time"));
        if (CollectionUtils.isEmpty(realtimeTrendRelationEntities)) {
            return exportMgr.buildExportEmptyList(ProfileGroupRealtimeTrendExportVO.class, "现实动向");
        }
        List<ProfileGroupRealtimeTrendExportVO> voList = new ArrayList<>(realtimeTrendRelationEntities.size());
        for (int i = 0; i < realtimeTrendRelationEntities.size(); i++) {
            ProfileGroupRealtimeTrendRelationEntity punishInfoRelationEntity = realtimeTrendRelationEntities.get(i);
            ProfileGroupRealtimeTrendExportVO vo = new ProfileGroupRealtimeTrendExportVO();
            vo.setCellHead("现实动向");
            vo.setNum(i + 1);
            vo.setRealtimeTrend(punishInfoRelationEntity.getRealtimeTrend());
            LocalDateTime startTime = punishInfoRelationEntity.getCreateTime();
            vo.setCreateTime(startTime == null ? "--" : startTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            vo.setCreateDept(exportMgr.deptIdToDeptName(punishInfoRelationEntity.getCreateDeptId()));
            voList.add(vo);
        }
        return voList;
    }

    private List<ProfileGroupPunishInfoExportVO> buildPunishInfoExportList(Group group) throws Exception {
        if (group == null) {
            return null;
        }
        List<ProfileGroupPunishInfoRelationEntity> punishInfoRelationEntities = groupPunishInfoRelationMapper.selectList(
                new QueryWrapper<ProfileGroupPunishInfoRelationEntity>()
                        .in("group_id", group.getId())
                        .orderByDesc("update_time"));
        if (CollectionUtils.isEmpty(punishInfoRelationEntities)) {
            return exportMgr.buildExportEmptyList(ProfileGroupPunishInfoExportVO.class, "打处情况");
        }
        List<ProfileGroupPunishInfoExportVO> voList = new ArrayList<>(punishInfoRelationEntities.size());
        for (int i = 0; i < punishInfoRelationEntities.size(); i++) {
            ProfileGroupPunishInfoRelationEntity punishInfoRelationEntity = punishInfoRelationEntities.get(i);
            ProfileGroupPunishInfoExportVO vo = new ProfileGroupPunishInfoExportVO();
            vo.setCellHead("打处情况");
            vo.setNum(i + 1);
            vo.setPunishInfo(punishInfoRelationEntity.getPunishInfo());
            LocalDateTime startTime = punishInfoRelationEntity.getCreateTime();
            vo.setCreateTime(startTime == null ? "--" : startTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            vo.setCreateDept(exportMgr.deptIdToDeptName(punishInfoRelationEntity.getCreateDeptId()));
            voList.add(vo);
        }
        return voList;
    }

    private List<ProfileGroupWorkMeasuresExportVO> buildWorkMeasuresExportList(Group group) throws Exception {
        if (group == null) {
            return null;
        }
        List<ProfileGroupWorkMeasuresRelationEntity> workMeasuresRelationEntities = groupWorkMeasuresRelationMapper.selectList(
                new QueryWrapper<ProfileGroupWorkMeasuresRelationEntity>()
                        .in("group_id", group.getId())
                        .orderByDesc("update_time"));
        if (CollectionUtils.isEmpty(workMeasuresRelationEntities)) {
            return exportMgr.buildExportEmptyList(ProfileGroupWorkMeasuresExportVO.class, "工作措施");
        }
        List<ProfileGroupWorkMeasuresExportVO> voList = new ArrayList<>(workMeasuresRelationEntities.size());
        for (int i = 0; i < workMeasuresRelationEntities.size(); i++) {
            ProfileGroupWorkMeasuresRelationEntity workMeasuresRelation = workMeasuresRelationEntities.get(i);
            ProfileGroupWorkMeasuresExportVO vo = new ProfileGroupWorkMeasuresExportVO();
            vo.setCellHead("工作措施");
            vo.setNum(i + 1);
            vo.setWorkMeasures(workMeasuresRelation.getWorkMeasures());
            LocalDateTime startTime = workMeasuresRelation.getCreateTime();
            vo.setCreateTime(startTime == null ? "--" : startTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            vo.setCreateDept(exportMgr.deptIdToDeptName(workMeasuresRelation.getCreateDeptId()));
            voList.add(vo);
        }
        return voList;
    }

    private List<ProfileGroupBasicInfoExportVO> buildBasicInfoExportList(Group group) throws Exception {
        if (group == null) {
            return null;
        }
        List<ProfileGroupBaseInfoRelationEntity> baseInfoRelationEntities = groupBaseInfoRelationMapper.selectList(
                new QueryWrapper<ProfileGroupBaseInfoRelationEntity>()
                        .in("group_id", group.getId())
                        .orderByDesc("update_time"));
        if (CollectionUtils.isEmpty(baseInfoRelationEntities)) {
            return exportMgr.buildExportEmptyList(ProfileGroupBasicInfoExportVO.class, "基本情况");
        }
        List<ProfileGroupBasicInfoExportVO> voList = new ArrayList<>(baseInfoRelationEntities.size());
        for (int i = 0; i < baseInfoRelationEntities.size(); i++) {
            ProfileGroupBaseInfoRelationEntity baseInfoRelation = baseInfoRelationEntities.get(i);
            ProfileGroupBasicInfoExportVO vo = new ProfileGroupBasicInfoExportVO();
            vo.setCellHead("基本情况");
            vo.setNum(i + 1);
            vo.setBasicInfo(baseInfoRelation.getBasicInfo());
            LocalDateTime startTime = baseInfoRelation.getCreateTime();
            vo.setCreateTime(startTime == null ? "--" : startTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            vo.setCreateDept(exportMgr.deptIdToDeptName(baseInfoRelation.getCreateDeptId()));
            voList.add(vo);
        }
        return voList;
    }

    private List<PersonEventExportVO> getEventList(Group group) throws Exception {
        List<Event> eventList = eventGroupMapper.getEventList(group.getId());
        if (CollectionUtils.isEmpty(eventList)) {
            return exportMgr.buildExportEmptyList(PersonEventExportVO.class, "相关事件");
        }
        List<PersonEventExportVO> list = new ArrayList<>();
        for (int i = 0; i < eventList.size(); i++) {
            Event event = eventList.get(i);
            PersonEventExportVO vo = new PersonEventExportVO();
            vo.setCellHead("相关事件");
            vo.setNum(i + 1);
            vo.setName(event.getName());
            vo.setCreateTime(TimeUtil.getSimpleTime(event.getCreateTime()));
            vo.setCreateDept(exportMgr.deptIdToDeptName(event.getCreateDeptId()));
            list.add(vo);
        }
        return list;
    }

    private void buildProfileGroup(Group group, ProfileGroupVO vo) {
        vo.setWorkTarget(exportMgr.dictCodeToName(group.getWorkTarget(), "profile_work_target"));
        vo.setBasicInfo(group.getBasicInfo());
        vo.setMainDemand(group.getMainDemand());
        vo.setWorkMeasures(group.getWorkMeasures());
        vo.setPetitionInfo(group.getPetitionInfo());
        vo.setPunishInfo(group.getPunishInfo());
        vo.setRealtimeTrend(group.getRealtimeTrend());
        QueryWrapper<ProfileGroupGovControlEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", group.getId());
        ProfileGroupGovControlEntity govControlEntity = profileGroupGovControlMapper.selectOne(queryWrapper);
        if (!Objects.isNull(govControlEntity)) {
            vo.setControlGovernment(govControlEntity.getControlGovernment());
            vo.setControlGovernmentPerson(govControlEntity.getControlGovernmentPerson());
            vo.setControlGovernmentContact(govControlEntity.getControlGovernmentContact());
            vo.setControlCommunity(govControlEntity.getControlCommunity());
            vo.setControlCommunityPerson(govControlEntity.getControlCommunityPerson());
            vo.setControlCommunityContact(govControlEntity.getControlCommunityContact());
        }
    }

    /**
     * 处理公安管控信息
     *
     * @param group 群档
     * @param vo    vo
     */
    private void dealGroupControlInfo(Group group, ProfileGroupVO vo) {
        QueryWrapper<ProfileGroupPoliceControlEntity> policeQueryWrapper = new QueryWrapper<>();
        policeQueryWrapper.eq("group_id", group.getId());
        List<ProfileGroupPoliceControlEntity> policeControlEntityList
                = profileGroupPoliceControlMapper.selectList(policeQueryWrapper);
        StringBuilder dutyPolice = new StringBuilder();
        StringBuilder controlBureau = new StringBuilder();
        StringBuilder controlBureauLeader = new StringBuilder();
        StringBuilder controlPolice = new StringBuilder();
        StringBuilder controlPoliceLeader = new StringBuilder();
        StringBuilder controlStation = new StringBuilder();
        StringBuilder controlStationLeader = new StringBuilder();
        for (ProfileGroupPoliceControlEntity policeControlEntity : policeControlEntityList) {
            dutyPolice.append(exportMgr.userIdArrayToUserName(policeControlEntity.getControlPerson())).append(",");
            controlBureau.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlBureau())).append(",");
            controlBureauLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlBureauLeader()));
            controlPolice.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlPolice())).append(",");
            controlPoliceLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlPoliceLeader())).append(",");
            controlStation.append(exportMgr.deptCodeToDeptName(policeControlEntity.getControlStation())).append(",");
            controlStationLeader.append(exportMgr.userIdToUserName(policeControlEntity.getControlStationLeader())).append(",");
        }
        vo.setDutyPolice(dutyPolice.toString());
        vo.setControlBureau(controlBureau.toString());
        vo.setControlBureauLeader(controlBureauLeader.toString());
        vo.setControlPolice(controlPolice.toString());
        vo.setControlPoliceLeader(controlPoliceLeader.toString());
        vo.setControlStation(controlStation.toString());
        vo.setControlStationLeader(controlStationLeader.toString());
    }

    /**
     * 获取敏感时间节点
     *
     * @param group      群体
     * @param policeKind policeKind
     * @return 敏感时间节点
     */
    private List<ProfileSensitiveExportVO> buildSensitiveExportList(Group group, Long policeKind) throws Exception {
        if (group == null) {
            return null;
        }
        QueryWrapper<ProfileSensitiveTime> queryWrapper = new QueryWrapper<>();
        if (Objects.nonNull(policeKind)) {
            queryWrapper.eq("group_id", group.getId())
                    .eq("police_kind", policeKind)
                    .orderByDesc("update_time");
        } else {
            List<Long> sensitiveTimeIds = group.getSensitiveTimeIds();
            if (CollectionUtils.isEmpty(sensitiveTimeIds)) {
                return exportMgr.buildExportEmptyList(ProfileSensitiveExportVO.class, "敏感时间节点");
            }
            queryWrapper.in("id", sensitiveTimeIds)
                    .orderByDesc("update_time").orderByDesc("id");
        }
        List<ProfileSensitiveTime> sensitiveTimes = profileSensitiveTimeMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(sensitiveTimes)) {
            return exportMgr.buildExportEmptyList(ProfileSensitiveExportVO.class, "敏感时间节点");
        }
        List<ProfileSensitiveExportVO> voList = new ArrayList<>(sensitiveTimes.size());
        for (int i = 0; i < sensitiveTimes.size(); i++) {
            ProfileSensitiveTime sensitiveTime = sensitiveTimes.get(i);
            ProfileSensitiveExportVO vo = new ProfileSensitiveExportVO();
            vo.setCellHead("敏感时间节点");
            vo.setNum(i + 1);
            vo.setName(sensitiveTime.getName());
            vo.setRemark(sensitiveTime.getRemark());
            LocalDateTime startTime = sensitiveTime.getStartTime();
            vo.setStartTime(startTime == null ? "--" : startTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            LocalDateTime endTime = sensitiveTime.getEndTime();
            vo.setEndTime(endTime == null ? "--" : endTime.format(TimeUtil.DEFAULT_TIME_PATTERN));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取群体相关人员
     *
     * @param group      群体
     * @param policeKind policeKind
     * @return 相关人员
     */
    public List<PersonGroupExportVO> getGroupPersonList(Group group, Long policeKind) throws Exception {
        if (group == null) {
            return null;
        }
        List<PersonGroupVO> personGroupVOList = personGroupRelationMapper.getPersonGroupListByGroupId(group.getId(), policeKind);
        if (CollectionUtils.isEmpty(personGroupVOList)) {
            return exportMgr.buildExportEmptyList(PersonGroupExportVO.class, "相关人员");
        }
        List<PersonGroupExportVO> voList = new ArrayList<>();
        for (int i = 0; i < personGroupVOList.size(); i++) {
            PersonGroupVO groupVO = personGroupVOList.get(i);
            PersonGroupExportVO vo = new PersonGroupExportVO();
            vo.setCellHead("相关人员");
            vo.setNum(i + 1);
            vo.setName(groupVO.getName());
            vo.setIdNumber(groupVO.getIdNumber());
            String riskScore = groupVO.getRiskScore();
            BigDecimal score = new BigDecimal(riskScore);
            BigDecimal roundedScore = score.setScale(0, RoundingMode.HALF_UP);
            if (Objects.isNull(policeKind)) {
                vo.setRiskScore(StringUtils.isEmpty(riskScore) ? "0" : roundedScore.toString());
                vo.setRiskLevel(groupVO.getRiskLevel());
            }
            vo.setPersonLabels(exportMgr.labelIdArrayToName(groupVO.getPersonLabel()));
            vo.setActivityLevel(exportMgr.dictCodeToName(groupVO.getActivityLevel(), "profile_activity_level"));
            vo.setCreateDept(exportMgr.deptIdToDeptName(groupVO.getCreateDeptId()));
            vo.setRegisteredResidence(exportMgr.districtCodeToName(groupVO.getRegisteredResidence()));
            vo.setRegisteredResidenceDetail(groupVO.getRegisteredResidenceDetail());
            vo.setTel(exportMgr.listToString(groupVO.getTel()));
            vo.setIdentity(exportMgr.dictCodeListToName(groupVO.getIdentity(), "person_group_relation_identity"));
            vo.setIsSlrylb(exportMgr.dictCodeToName(groupVO.getIsSlrylb(), "yes_or_not"));
            vo.setInflowTime(groupVO.getInflowTime());
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 获取群体线索
     *
     * @param group      group
     * @param policeKind policeKind
     * @return 线索
     */
    private List<PersonClueExportVO> getGroupClueList(Group group, Long policeKind) throws Exception {
        List<Clue> clueList = groupClueRelationMapper.getGroupClueList(group.getId(), policeKind);
        if (CollectionUtils.isEmpty(clueList)) {
            return exportMgr.buildExportEmptyList(PersonClueExportVO.class, "相关线索");
        }
        List<PersonClueExportVO> list = new ArrayList<>();
        for (int i = 0; i < clueList.size(); i++) {
            Clue clue = clueList.get(i);
            PersonClueExportVO vo = new PersonClueExportVO();
            vo.setCellHead("相关线索");
            vo.setNum(i + 1);
            vo.setName(clue.getName());
            vo.setClueLabel(exportMgr.listToString(exportMgr.labelIdArrayToName(clue.getClueLabel())));
            vo.setEmergencyLevel(exportMgr.dictCodeToName(clue.getEmergencyLevel(), "profile_clue_emergency_level"));
            vo.setSource(exportMgr.dictCodeToName(clue.getSource(), "profile_clue_source"));
            vo.setCreateTime(TimeUtil.getSimpleTime(clue.getCreateTime()));
            vo.setDisposalStatus(exportMgr.dictCodeToName(clue.getDisposalStatus(), "profile_clue_disposal_status"));
            list.add(vo);
        }
        return list;
    }
}
