package com.trs.police.profile.converter;

import com.trs.police.profile.domain.dto.zhzg.*;
import com.trs.police.profile.domain.entity.zhzg.*;
import com.trs.police.profile.util.ConverterHelper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 智慧政工积分计算DTO转换类
 *
 */
@Mapper(componentModel = "spring", imports = ConverterHelper.class)
public interface ZhzgScoreConverter {

    /**
     * 档案信息转换为计算积分用的DTO，只包含第一层
     *
     * @param profilePolice 人员信息
     * @return 计算积分用的DTO
     */
    @Mappings({
            @Mapping(target = "birthday", expression = "java(ConverterHelper.dateToLocalDate(profilePolice.getBirthday()))"),
            @Mapping(target = "joinWorkDate", expression = "java(ConverterHelper.dateToLocalDate(profilePolice.getJoinWorkDate()))"),
            @Mapping(target = "joinPublicSecurityWorkDate", expression = "java(ConverterHelper.dateToLocalDate(profilePolice.getJoinPublicSecurityWorkDate()))"),
    })
    ZhzgPersonArchiveDTO toArchive(ProfilePolice profilePolice);

    /**
     * 年度考核转换为计算积分用的DTO
     *
     * @param ndkh 年度考核信息
     * @return 计算积分用的DTO
     */
    @Mappings({
            @Mapping(target = "assessmentTime", expression = "java(ConverterHelper.dateToLocalDate(ndkh.getAssessmentTime()))"),
    })
    ScoreNdkhDTO toNdkhDTO(PoliceProfessionalNdkhRelation ndkh);

    /**
     * 违纪违纪转换为计算积分用的DTO
     *
     * @param wgwj 违纪违纪信息
     * @return 计算积分用的DTO
     */
    @Mappings({
            @Mapping(target = "recordTime", expression = "java(ConverterHelper.dateToLocalDate(wgwj.getRecordTime()))"),
    })
    ScoreWgwjDTO toWgwjDTO(PoliceProfessionalWgwjRelation wgwj);

    /**
     * 立功受奖转换为计算积分用的DTO
     *
     * @param lgsj 立功受奖信息
     * @return 计算积分用的DTO
     */
    @Mappings({
            @Mapping(target = "acquisitionTime", expression = "java(ConverterHelper.dateToLocalDate(lgsj.getAcquisitionTime()))"),
    })
    ScoreLgsjDTO toLgsjDTO(PoliceProfessionalLgsjRelation lgsj);

    /**
     * 专业技术转换为计算积分用的DTO
     *
     * @param expertises 专业技术信息
     * @return 计算积分用的DTO
     */
    @Mappings({
            @Mapping(target = "acquisitionTime", expression = "java(ConverterHelper.dateToLocalDate(expertises.getAcquisitionTime()))"),
    })
    ScoreExpertisesDTO toExpertisesDTO(PoliceProfessionalTechnologyRelation expertises);

    /**
     * 职级转换为计算积分用的DTO
     *
     * @param rankRelation 职级信息
     * @return 计算积分用的DTO
     */
    @Mappings({
            @Mapping(target = "startTime", expression = "java(ConverterHelper.dateToLocalDate(rankRelation.getStartTime()))"),
            @Mapping(target = "endTime", expression = "java(ConverterHelper.dateToLocalDate(rankRelation.getEndTime()))"),
    })
    ScoreRankDTO toRankDTO(PoliceRankRelation rankRelation);

    /**
     * 履历转换为计算积分用的DTO
     *
     * @param resumeRelation 履历信息
     * @return 计算积分用的DTO
     */
    @Mappings({
            @Mapping(target = "startTime", expression = "java(ConverterHelper.dateToLocalDate(resumeRelation.getStartTime()))"),
            @Mapping(target = "endTime", expression = "java(ConverterHelper.dateToLocalDate(resumeRelation.getEndTime()))"),
    })
    ScoreResumeDTO toResumeDTO(PoliceResumeRelation resumeRelation);

    /**
     * 援藏援疆转换为计算积分用的DTO
     *
     * @param supportRelation 援藏援疆信息
     * @return 计算积分用的DTO
     */
    @Mappings({
            @Mapping(target = "serviceStartTime", expression = "java(ConverterHelper.dateToLocalDate(supportRelation.getServiceStartTime()))"),
            @Mapping(target = "serviceEndTime", expression = "java(ConverterHelper.dateToLocalDate(supportRelation.getServiceEndTime()))"),
    })
    ScoreSupportTO toSupportDTO(PoliceAssistTibetXinjiangRelation supportRelation);
}
