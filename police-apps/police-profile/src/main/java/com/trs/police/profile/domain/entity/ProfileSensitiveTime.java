package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-03-06 11:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_profile_sensitive_time", autoResultMap = true)
public class ProfileSensitiveTime extends AbstractBaseEntity {
    private static final long serialVersionUID = -1448068997369861646L;

    /**
     * 敏感时间节点名称
     */
    private String name;

    /**
     * 敏感时间节点备注
     */
    private String remark;

    /**
     * 起始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     *  有效范围 0 默认有效 1 选择有效
     */
    private Integer scope;

    /**
     *  0 每年 1 固定时间
     */
    private Integer timeType;

    /**
     * 群体id
     */
    private Long groupId;

    /**
     * 归属警种
     */
    private Integer policeKind;

    @TableLogic
    private Boolean deleted;
}
