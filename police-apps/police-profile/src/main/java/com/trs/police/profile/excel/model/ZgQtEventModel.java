package com.trs.police.profile.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 自贡其他事件批量导入
 *
 * <AUTHOR>
 * @date 2025年01月15日 14:56
 */
@Data
public class ZgQtEventModel {

    @ExcelProperty("事件名称")
    private String name;
    //是事件类别ID，用英文逗号分割
    @ExcelProperty("事件类别")
    private String eventLabel;
    @ExcelProperty("归属地")
    private String belongLocation;
    @ExcelProperty("估计人数")
    private String personEstimation;
    @ExcelProperty("风险等级")
    private String riskLevel;
    @ExcelProperty("事件详情")
    private String detail;
    @ExcelProperty("处置结果")
    private String disposalResult;
    @ExcelProperty("简要说明")
    private String description;
    @ExcelProperty("事件来源")
    private String source;
    @ExcelProperty("维权开始时间")
    private LocalDateTime relatedTime;
    @ExcelProperty("维权结束时间")
    private LocalDateTime relatedEndTime;
    @ExcelProperty("原发时间")
    private LocalDateTime sourceTime;
    @ExcelProperty("事件级别")
    private String eventLevel;
    @ExcelProperty("主管单位")
    private String controlStation;
    @ExcelProperty("主责警种")
    private String controlPolice;
    @ExcelProperty("涉及人员")
    private String relatedPerson;
    @ExcelProperty("涉及群体")
    private String relatedGroup;
    @ExcelProperty("相关线索")
    private String relatedClue;


    @Override
    public String toString() {
        return name + "@" + super.toString();
    }


}
