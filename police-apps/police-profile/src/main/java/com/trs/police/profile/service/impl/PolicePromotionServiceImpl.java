package com.trs.police.profile.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trs.police.profile.config.PromotionRankMappingConfig;
import com.trs.police.profile.constant.enums.PoliceRankEnum;
import com.trs.police.profile.constant.enums.PromotionStatusEnum;
import com.trs.police.profile.domain.dto.promotion.PromotionEvaluationRequest;
import com.trs.police.profile.domain.entity.zhzg.PoliceRankRelation;
import com.trs.police.profile.domain.entity.zhzg.ProfilePolice;
import com.trs.police.profile.domain.vo.promotion.PromotionEvaluationResult;
import com.trs.police.profile.domain.vo.promotion.PromotionRuleDetailVO;
import com.trs.police.profile.factory.promotion.PromotionRuleStrategyFactory;
import com.trs.police.profile.mapper.zhzg.PoliceRankRelationMapper;
import com.trs.police.profile.mapper.zhzg.ProfilePoliceMapper;
import com.trs.police.profile.service.PolicePromotionService;
import com.trs.police.profile.strategy.promotion.PromotionRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 警员职级晋升服务实现
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Service
public class PolicePromotionServiceImpl implements PolicePromotionService {

    @Autowired
    private ProfilePoliceMapper profilePoliceMapper;

    @Autowired
    private PromotionRuleStrategyFactory strategyFactory;

    @Autowired
    private PromotionRankMappingConfig rankMappingConfig;

    @Autowired
    private PoliceRankRelationMapper policeRankRelationMapper;


    /**
     * 评估警员晋升资格
     * <p>
     * 这是核心的晋升评估方法，执行完整的晋升资格评估流程：
     * 1. 验证请求参数的有效性
     * 2. 查询警员档案信息
     * 3. 获取警员当前职级信息
     * 4. 根据当前职级确定目标晋升职级
     * 5. 获取适用的晋升策略规则
     * 6. 执行所有策略规则的评估
     * 7. 根据规则评估结果计算最终晋升状态
     * 8. 构建并返回评估结果
     *
     * @param request 晋升评估请求对象，包含警员ID等必要信息
     * @return PromotionEvaluationResult 晋升评估结果，包含评估状态、规则详情、建议等
     * @throws Exception 当评估过程中发生系统错误时抛出异常
     */
    @Override
    public PromotionEvaluationResult evaluatePromotion(PromotionEvaluationRequest request) {
        log.info("开始评估警员晋升资格，警员ID：{}", request.getPoliceId());

        try {
            // 验证请求参数
            if (!validateRequest(request)) {
                return buildErrorResult(request, "请求参数验证失败");
            }

            // 查询警员档案
            ProfilePolice police = profilePoliceMapper.selectById(request.getPoliceId());
            if (police == null) {
                return buildErrorResult(request, "未找到警员档案信息");
            }

            // 获取警员当前职级关系信息
            PoliceRankRelation currentRankRelation = getCurrentRankRelation(police);
            if (currentRankRelation == null || currentRankRelation.getRank() == null) {
                return buildErrorResult(request, "当前警员没有职级信息");
            }

            // 确定目标晋升职级
            List<PoliceRankEnum> targetRanks = determineTargetRank(currentRankRelation);
            if (CollectionUtils.isEmpty(targetRanks)) {
                return buildErrorResult(request, "无法确定目标晋升职级");
            }
            List<PromotionRuleDetailVO> ruleDetails = new ArrayList<>();
            for (PoliceRankEnum targetRank : targetRanks) {
                // 获取晋升策略
                List<PromotionRuleStrategy> strategies = getPromotionStrategies(targetRank);
                if (CollectionUtils.isEmpty(strategies)) {
                    return buildErrorResult(request, "未找到适用的晋升策略");
                }

                // 执行规则评估
                List<PromotionRuleDetailVO> result = evaluateStrategies(police, targetRank, strategies);
                ruleDetails.addAll(result);
            }

            // 计算最终晋升状态
            // todo 查看是否正确
            PromotionStatusEnum finalStatus = calculateFinalStatus(ruleDetails);
            List<Integer> targetCodes = targetRanks.stream().map(e -> e.getCode()).collect(Collectors.toList());
            // 构建评估结果
            return buildSuccessResult(police, targetCodes, finalStatus, ruleDetails);

        } catch (Exception e) {
            log.error("评估警员晋升资格失败，警员ID：{}，错误：{}", request.getPoliceId(), e.getMessage(), e);
            return buildErrorResult(request, "评估失败：" + e.getMessage());
        }
    }


    /**
     * 根据警员ID评估晋升资格
     * <p>
     * 这是一个便捷方法，通过警员档案ID直接进行晋升评估。
     * 内部会构建标准的评估请求对象，然后调用核心评估方法。
     *
     * @param policeId 警员档案ID，不能为空
     * @return PromotionEvaluationResult 晋升评估结果
     */
    @Override
    public PromotionEvaluationResult evaluatePromotionById(Long policeId) {
        // 构建评估请求对象
        PromotionEvaluationRequest request = PromotionEvaluationRequest.builder()
                .policeId(policeId)
                .build();
        // 调用核心评估方法
        return evaluatePromotion(request);
    }

    /**
     * 根据身份证号评估晋升资格
     * <p>
     * 通过身份证号查询警员档案，然后进行晋升评估。
     * 适用于只知道身份证号而不知道档案ID的场景。
     * <p>
     * 处理流程：
     * 1. 验证身份证号是否为空
     * 2. 根据身份证号查询警员档案（只查询未删除的记录）
     * 3. 如果找到档案，调用按ID评估的方法
     * 4. 如果未找到档案，返回错误结果
     *
     * @param idNumber 身份证号，不能为空
     * @return PromotionEvaluationResult 晋升评估结果
     */
    @Override
    public PromotionEvaluationResult evaluatePromotionByIdNumber(String idNumber) {
        // 验证身份证号
        if (!StringUtils.hasText(idNumber)) {
            return PromotionEvaluationResult.builder()
                    .success(false)
                    .errorMessage("身份证号不能为空")
                    .evaluationTime(LocalDateTime.now())
                    .build();
        }

        // 根据身份证号查询警员档案
        LambdaQueryWrapper<ProfilePolice> queryWrapper = new LambdaQueryWrapper<ProfilePolice>()
                .eq(ProfilePolice::getIdNumber, idNumber)
                .eq(ProfilePolice::getDeleted, false);

        ProfilePolice police = profilePoliceMapper.selectOne(queryWrapper);
        if (police == null) {
            return PromotionEvaluationResult.builder()
                    .idNumber(idNumber)
                    .success(false)
                    .errorMessage("未找到对应的警员档案")
                    .evaluationTime(LocalDateTime.now())
                    .build();
        }

        // 调用按ID评估的方法
        return evaluatePromotionById(police.getId());
    }

    /**
     * 更新警员晋升状态
     * <p>
     * 更新指定警员的晋升状态字段。
     * 该方法使用事务保证数据一致性，如果更新失败会回滚。
     *
     * @param policeId        警员档案ID，不能为空
     * @param promotionStatus 晋升状态代码，不能为空
     * @return boolean 更新是否成功，true表示成功，false表示失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePromotionStatus(Long policeId, Integer promotionStatus) {
        // 参数验证
        if (policeId == null || promotionStatus == null) {
            return false;
        }

        // 构建更新对象
        ProfilePolice police = new ProfilePolice();
        police.setId(policeId);
        police.setPromotionStatus(promotionStatus);

        // 执行更新操作
        int updateCount = profilePoliceMapper.updateById(police);
        boolean success = updateCount > 0;

        log.info("更新警员晋升状态，警员ID：{}，状态：{}，结果：{}", policeId, promotionStatus, success ? "成功" : "失败");
        return success;
    }

    /**
     * 批量更新警员晋升状态
     * <p>
     * 批量更新多个警员的晋升状态，提高操作效率。
     * 该方法会逐个调用单个更新方法，统计成功更新的数量。
     * 使用事务保证整个批量操作的一致性。
     *
     * @param policeIds       警员档案ID列表，不能为空
     * @param promotionStatus 晋升状态代码，不能为空
     * @return int 成功更新的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdatePromotionStatus(List<Long> policeIds, Integer promotionStatus) {
        // 参数验证
        if (CollectionUtils.isEmpty(policeIds) || promotionStatus == null) {
            return 0;
        }

        // 逐个更新并统计成功数量
        int successCount = 0;
        for (Long policeId : policeIds) {
            if (updatePromotionStatus(policeId, promotionStatus)) {
                successCount++;
            }
        }

        log.info("批量更新警员晋升状态完成，总数：{}，成功：{}，状态：{}",
                policeIds.size(), successCount, promotionStatus);
        return successCount;
    }

    /**
     * 执行晋升状态批量计算任务
     * <p>
     * 对所有警员执行晋升状态的批量计算和更新。
     * 该方法会：
     * 1. 查询所有未删除的警员档案
     * 2. 逐个进行晋升评估
     * 3. 将评估结果更新到数据库
     * 4. 统计处理成功的数量
     * <p>
     * 适用于定期的批量数据更新或系统维护场景。
     *
     * @return int 成功处理的警员数量
     */
    @Override
    public int executePromotionStatusCalculation() {
        log.info("开始执行晋升状态批量计算任务");

        // 查询所有未删除的警员档案
        LambdaQueryWrapper<ProfilePolice> queryWrapper = new LambdaQueryWrapper<ProfilePolice>()
                .eq(ProfilePolice::getDeleted, false);

        List<ProfilePolice> policeList = profilePoliceMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(policeList)) {
            log.info("未找到需要计算的警员档案");
            return 0;
        }

        // 逐个处理警员档案
        int processedCount = 0;
        for (ProfilePolice police : policeList) {
            try {
                // 评估晋升资格
                PromotionEvaluationResult result = evaluatePromotionById(police.getId());
                // 如果评估成功且有状态结果，则更新数据库
                if (result.getSuccess() && result.getPromotionStatus() != null) {
                    updatePromotionStatus(police.getId(), result.getPromotionStatusCode());
                    processedCount++;
                }
            } catch (Exception e) {
                log.error("计算警员晋升状态失败，警员ID：{}，错误：{}", police.getId(), e.getMessage(), e);
            }
        }

        log.info("晋升状态批量计算任务完成，总数：{}，处理成功：{}", policeList.size(), processedCount);
        return processedCount;
    }


    /**
     * 验证请求参数
     * <p>
     * 检查评估请求对象的基本有效性。
     * 主要验证请求对象不为空且包含必要的警员ID。
     *
     * @param request 晋升评估请求对象
     * @return boolean 验证是否通过，true表示有效，false表示无效
     */
    private boolean validateRequest(PromotionEvaluationRequest request) {
        return request != null && request.getPoliceId() != null;
    }

    /**
     * 确定目标晋升职级
     * <p>
     * 根据警员当前职级关系信息，确定其下一级的目标晋升职级。
     * 通过职级映射配置获取当前职级对应的目标晋升职级。
     *
     * @param currentRankRelation 当前职级关系信息
     * @return PoliceRankEnum 目标晋升职级枚举，如果无法确定则返回null
     */
    private List<PoliceRankEnum> determineTargetRank(PoliceRankRelation currentRankRelation) {
        // 根据职级系列和职级代码获取当前职级枚举
        PoliceRankEnum currentRankCode = PoliceRankEnum.getByCode(currentRankRelation.getRankSeries(), currentRankRelation.getRank());
        List<String> targetRanks = rankMappingConfig.getTargetRank(currentRankCode.getName());
        List<PoliceRankEnum> list = new ArrayList<>();
        for (String name : targetRanks) {
            PoliceRankEnum targetRank = PoliceRankEnum.getByName(name);
            list.add(targetRank);
        }
        // 通过配置获取目标晋升职级
        return list;
    }

    /**
     * 获取晋升策略
     * <p>
     * 根据目标职级获取适用的晋升规则策略列表。
     * 从配置中获取策略类名，然后通过工厂获取策略实例。
     *
     * @param targetRank 目标晋升职级
     * @return List PromotionRuleStrategy 适用的策略列表
     */
    private List<PromotionRuleStrategy> getPromotionStrategies(PoliceRankEnum targetRank) {
        // 从配置中获取策略类名列表
        List<String> strategyClassNames = rankMappingConfig.getStrategiesByTargetRank(targetRank.getName());
        // 根据类名获取策略实例
        return strategyFactory.getStrategiesByClassNames(strategyClassNames);
    }

    /**
     * 执行策略评估
     * <p>
     * 对所有策略进行逐个评估，收集评估结果。
     * 如果某个策略执行失败，会创建错误结果记录，确保评估过程的完整性。
     * 最后会按照必要条件优先的原则对结果进行排序。
     *
     * @param police         警员档案信息
     * @param targetRankCode 目标晋升职级
     * @param strategies     策略列表
     * @return List PromotionRuleDetailVO 规则评估详情列表
     */
    private List<PromotionRuleDetailVO> evaluateStrategies(ProfilePolice police, PoliceRankEnum targetRankCode,
                                                           List<PromotionRuleStrategy> strategies) {
        List<PromotionRuleDetailVO> ruleDetails = new ArrayList<>();

        // 逐个执行策略评估
        for (PromotionRuleStrategy strategy : strategies) {
            try {
                // 调用策略的评估方法
                PromotionRuleDetailVO ruleDetail = strategy.evaluateRule(police, targetRankCode);
                if (ruleDetail != null) {
                    ruleDetails.add(ruleDetail);
                }
            } catch (Exception e) {
                log.error("执行策略评估失败，策略：{}，警员ID：{}，错误：{}",
                        strategy.getStrategyName(), police.getId(), e.getMessage(), e);

                // 创建错误结果，确保评估过程的完整性
                PromotionRuleDetailVO errorDetail = PromotionRuleDetailVO.builder()
                        .ruleName(strategy.getStrategyName())
                        .description(strategy.getDescription())
                        .ruleType(strategy.getSupportedRuleType())
                        .isHit(false)
                        .ruleResult(PromotionStatusEnum.NOT_QUALIFIED)
                        .success(false)
                        .errorMessage("策略执行失败：" + e.getMessage())
                        .build();
                ruleDetails.add(errorDetail);
            }
        }

        // 按必要条件排序（必要条件优先）
        ruleDetails.sort(Comparator.comparing(detail ->
                Boolean.TRUE.equals(detail.getRequired()) ? 0 : 1));

        return ruleDetails;
    }

    /**
     * 计算最终晋升状态
     * <p>
     * 根据所有规则评估结果计算最终的晋升状态。
     * 计算逻辑：
     * 1. 如果没有评估结果，返回不符合条件
     * 2. 默认状态为符合条件
     * 3. 如果是必要条件且不满足，直接返回该条件的结果
     * 4. 按优先级比较所有结果：不得晋升 > 暂缓晋升 > 有资格/无资格
     *
     * @param ruleDetails 规则评估详情列表
     * @return PromotionStatusEnum 最终晋升状态
     */
    private PromotionStatusEnum calculateFinalStatus(List<PromotionRuleDetailVO> ruleDetails) {
        // 如果没有评估结果，返回不符合条件
        if (CollectionUtils.isEmpty(ruleDetails)) {
            return PromotionStatusEnum.NOT_QUALIFIED;
        }

        // 默认状态为符合条件
        PromotionStatusEnum finalStatus = PromotionStatusEnum.QUALIFIED;

        // 遍历所有规则评估结果
        for (PromotionRuleDetailVO detail : ruleDetails) {
            // 跳过执行失败或结果为空的规则
            if (!detail.getSuccess() || detail.getRuleResult() == null) {
                continue;
            }

            // 如果是必要条件且不满足，直接返回不符合
            if (Boolean.TRUE.equals(detail.getRequired())
                    && detail.getRuleResult() != PromotionStatusEnum.QUALIFIED) {
                return detail.getRuleResult();
            }

            // 按优先级比较状态
            finalStatus = finalStatus.compareWithPriority(detail.getRuleResult());
        }

        return finalStatus;
    }

    /**
     * 构建成功结果
     * <p>
     * 构建评估成功时的结果对象，包含完整的评估信息。
     * 包括警员基本信息、目标职级、评估状态、规则详情、结果描述和建议等。
     *
     * @param police         警员档案信息
     * @param targetRankCodes 目标职级代码
     * @param finalStatus    最终晋升状态
     * @param ruleDetails    规则评估详情列表
     * @return PromotionEvaluationResult 评估成功结果
     */
    private PromotionEvaluationResult buildSuccessResult(ProfilePolice police, List<Integer> targetRankCodes,
                                                         PromotionStatusEnum finalStatus,
                                                         List<PromotionRuleDetailVO> ruleDetails) {
        return PromotionEvaluationResult.builder()
                .policeId(police.getId())
                .policeName(police.getName())
                .idNumber(police.getIdNumber())
                .targetRankCode(targetRankCodes)
                .promotionStatus(finalStatus)
                .ruleDetails(ruleDetails)
                .evaluationTime(LocalDateTime.now())
                .success(true)
                .resultDescription(buildResultDescription(finalStatus, ruleDetails))
                .suggestion(buildSuggestion(finalStatus, ruleDetails))
                .build();
    }

    /**
     * 构建错误结果
     * <p>
     * 构建评估失败时的结果对象，包含错误信息。
     * 用于处理评估过程中出现的各种异常情况。
     *
     * @param request      原始评估请求
     * @param errorMessage 错误信息
     * @return PromotionEvaluationResult 评估失败结果
     */
    private PromotionEvaluationResult buildErrorResult(PromotionEvaluationRequest request, String errorMessage) {
        return PromotionEvaluationResult.builder()
                .policeId(request.getPoliceId())
                .idNumber(request.getIdNumber())
                .evaluationTime(LocalDateTime.now())
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * 构建结果描述
     * <p>
     * 根据最终状态和规则详情构建可读的结果描述文本。
     * 包含评估结果、规则总数和命中规则数量等信息。
     *
     * @param finalStatus 最终晋升状态
     * @param ruleDetails 规则评估详情列表
     * @return String 结果描述文本
     */
    private String buildResultDescription(PromotionStatusEnum finalStatus, List<PromotionRuleDetailVO> ruleDetails) {
        StringBuilder sb = new StringBuilder();
        sb.append("晋升评估结果：").append(finalStatus.getName()).append("。");

        // 统计命中的规则数量
        long hitCount = ruleDetails.stream().filter(detail -> Boolean.TRUE.equals(detail.getIsHit())).count();
        sb.append("共评估").append(ruleDetails.size()).append("个规则，");
        sb.append("命中").append(hitCount).append("个规则。");

        return sb.toString();
    }

    /**
     * 构建建议信息
     * <p>
     * 根据评估结果构建针对性的建议信息。
     * 如果符合条件，给出恭喜信息；如果不符合，列出需要改进的方面。
     *
     * @param finalStatus 最终晋升状态
     * @param ruleDetails 规则评估详情列表
     * @return String 建议信息文本
     */
    private String buildSuggestion(PromotionStatusEnum finalStatus, List<PromotionRuleDetailVO> ruleDetails) {
        // 如果符合条件，返回恭喜信息
        if (finalStatus == PromotionStatusEnum.QUALIFIED) {
            return "恭喜！您符合晋升条件，可以申请晋升。";
        }

        // 构建改进建议
        StringBuilder sb = new StringBuilder();
        sb.append("暂不符合晋升条件，建议：");

        // 列出未满足的规则及其建议
        for (PromotionRuleDetailVO detail : ruleDetails) {
            if (!Boolean.TRUE.equals(detail.getIsHit())
                    && detail.getRuleResult() != PromotionStatusEnum.QUALIFIED) {
                sb.append(detail.getCalculateDescription()).append("；");
            }
        }

        return sb.toString();
    }

    /**
     * 获取警员当前职级关系记录
     * <p>
     * 查询 t_police_rank_relation 表中 end_time 为空的记录，表示当前有效的职级。
     * 按开始时间倒序排列，取最新的一条记录。
     * <p>
     * 查询逻辑：
     * 1. 根据警员档案ID查询职级关系
     * 2. 过滤 end_time 为空的记录（当前有效职级）
     * 3. 按开始时间倒序排列
     * 4. 限制返回1条记录
     *
     * @param police 警员档案信息
     * @return PoliceRankRelation 当前职级关系记录，如果查询失败返回null
     */
    private PoliceRankRelation getCurrentRankRelation(ProfilePolice police) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<PoliceRankRelation> queryWrapper = new LambdaQueryWrapper<PoliceRankRelation>()
                    .eq(PoliceRankRelation::getProfileId, police.getId())
//                    .isNull(PoliceRankRelation::getEndTime)
                    .orderByDesc(PoliceRankRelation::getStartTime)
                    .last("LIMIT 1");

            return policeRankRelationMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("查询警员当前职级关系失败，警员ID：{}，错误：{}", police.getId(), e.getMessage(), e);
            return null;
        }
    }
}
