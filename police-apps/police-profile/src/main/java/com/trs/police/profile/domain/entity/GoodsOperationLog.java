package com.trs.police.profile.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 档案操作记录表
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_profile_goods_operation_log")
public class GoodsOperationLog extends AbstractBaseEntity {

    /**
     * 修改申请信息
     */
    @TableField(value = "detail")
    private String detail;

    /**
     * 审批状态
     */
    @TableField(value = "approval_status")
    private  Long approvalStatus;

    /**
     * 创建单位code
     */
    @TableField(value = "create_dept_code")
    private String createDeptCode;


    /**
     * 审批状态名称
     */
    @TableField(value = "approval_status_name")
    private String approvalStatusName;

    /**
     * 创建用户姓名
     */
    @TableField(value = "create_user_name")
    private String createUserName;

    /**
     * 创建单位名称
     */
    @TableField(value = "create_dept_name")
    private String createDeptName;

    /**
     * 物品id
     */
    @TableField(value = "goods_id")
    private Long goodsId;
}
