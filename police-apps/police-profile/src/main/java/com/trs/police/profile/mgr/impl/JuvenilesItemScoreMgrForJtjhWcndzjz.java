package com.trs.police.profile.mgr.impl;

import com.trs.police.profile.domain.entity.es.DwsWcnPoints;
import com.trs.police.profile.mgr.BaseJuvenilesItemScoreCountByExistsMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/24 20:59
 * @since 1.0
 */
@Component
public class JuvenilesItemScoreMgrForJtjhWcndzjz extends BaseJuvenilesItemScoreCountByExistsMgr {
    /**
     * firstCategory<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 17:59
     */
    @Override
    public String firstCategory() {
        return "家庭监护";
    }

    /**
     * secondCategory<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 17:59
     */
    @Override
    public String secondCategory() {
        return "未成年独自居住";
    }

    /**
     * order<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/31 09:16
     */
    @Override
    public Integer order() {
        return 8;
    }

    /**
     * makeOneScore<BR>
     *
     * @param item 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/24 18:05
     */
    @Override
    public Long makeOneScore(Optional<DwsWcnPoints> item) {
        return 8L;
    }
}
