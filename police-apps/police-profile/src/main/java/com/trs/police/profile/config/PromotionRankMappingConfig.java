package com.trs.police.profile.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职级晋升映射配置
 *
 * <AUTHOR> Assistant
 * @date 2025/01/27
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "police.promotion")
public class PromotionRankMappingConfig {

    /**
     * 职级晋升映射关系
     * key: 当前职级名称, value: 目标晋升职级名称
     */
    private Map<String, List<String>> rankMapping = new HashMap<>();

    /**
     * 职级对应的策略配置
     * key: 目标职级名称, value: 策略类名列表
     */
    private Map<String, List<String>> rankStrategies = new HashMap<>();

    /**
     * 职级任职年限要求配置
     * key: 目标职级名称, value: Map 前置职级名称, 要求年限
     */
    private Map<String, Map<String, Integer>> rankTenureRequirements = new HashMap<>();

    /**
     * 获得优秀时，减免年数
     */
    private Double reliefYears = 0.5;


    /**
     * 根据当前职级名称获取目标晋升职级名称
     *
     * @param currentRankName 当前职级名称
     * @return 目标晋升职级名称，如果没有配置则返回null
     */
    public List<String> getTargetRank(String currentRankName) {
        if (currentRankName == null || currentRankName.trim().isEmpty()) {
            return null;
        }
        return rankMapping.get(currentRankName.trim());
    }

    /**
     * 根据职级类别和目标职级名称获取策略类名列表
     *
     * @param targetRankName 目标职级名称
     * @return 策略类名列表
     */
    public List<String> getStrategiesByTargetRank(String targetRankName) {
        return rankStrategies.getOrDefault(targetRankName.trim(), new ArrayList<>());
    }

    /**
     * 根据目标职级名称获取任职年限要求
     *
     * @param targetRankName 目标职级名称
     * @return 任职年限要求映射（前置职级名称 -> 要求年限）
     */
    public Map<String, Integer> getTenureRequirements(String targetRankName) {
        if (targetRankName == null || targetRankName.trim().isEmpty()) {
            return new HashMap<>();
        }
        return rankTenureRequirements.getOrDefault(targetRankName.trim(), new HashMap<>());
    }
}
