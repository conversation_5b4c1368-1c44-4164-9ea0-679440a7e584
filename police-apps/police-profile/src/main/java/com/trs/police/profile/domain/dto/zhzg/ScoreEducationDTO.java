package com.trs.police.profile.domain.dto.zhzg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 积分计算年度考核DTO
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreEducationDTO {

    /**
     * 在校开始时间（对应界面“在校时间”的起始日期）
     */
    private LocalDate startTime;
    /**
     * 在校结束时间（对应界面“在校时间”的结束日期）
     */
    private LocalDate endTime;

    /**
     * 学历，码表，type = t_degree
     * 1: 小学
     * 2: 初中
     * 3: 高中
     * 4: 大专
     * 5: 大学本科
     * 6: 博士研究生
     * 7: 硕士研究生
     */
    private Integer degree;

    /**
     * 学位，码表，type = t_degree
     * 1: 小学
     * 2: 初中
     * 3: 高中
     * 4: 大专
     * 5: 大学本科
     * 6: 博士研究生
     * 7: 硕士研究生
     */
    private Integer degree2;
    /**
     * 毕业学校（对应界面“毕业学校”输入值）
     */
    private String graduationSchool;
    /**
     * 专业（对应界面“专业”输入值）
     */
    private String major;

}
