package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ScoreExpertisesDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 专业技术积分计算策略
 */
@Slf4j
@Component
public class ExpertisesScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "专业技术";
    private static final String RULE_TYPE = "EXPERTISES";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算{}积分，人员：{}，规则：{}", STRATEGY_NAME, personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取记录
            List<ScoreExpertisesDTO> expertises = personArchive.getExpertises();
            if (CollectionUtils.isEmpty(expertises)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无" + STRATEGY_NAME + "记录")
                        .build();
            }

            // 计算积分
            ExpertisesType expertisesType = ExpertisesType.valueOf(rule.getRuleSubType());
            long hitCount = expertises.stream().filter(dto -> Objects.equals(dto.getTechnology(), expertisesType.getType()))
                    .count();

            double calculatedScore = hitCount > 0 ? rule.getScore() : 0.0;

            // 应用规则配置的最大分值限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("获得%s记录：%d条", expertisesType.getName(), hitCount);
            String calculateDescription = String.format("%d条获得%s记录 × %.3f分/条 = %.3f分",
                    hitCount, expertisesType.getName(), rule.getScore(), calculatedScore);
            
            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.3f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算{}积分失败，人员：{}，规则：{}，错误：{}",
                    STRATEGY_NAME, personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Getter
    @AllArgsConstructor
    enum ExpertisesType {
        GJJRC("国家级人才", 1),
        GJKXJSJ("国家科学技术奖", 2),
        QGGAJGBZYYWJNDBW("全国公安机关本专业业务技能大比武", 3),
        XXZLQ("新型专利权", 4),
        JWJSZWRZZGPW("警务技术职务任职资格评委", 5),
        GABJWJSZJK("公安部警务技术专家库", 6),
        ZCSBJYSKYXMYFGZ("主持省（部）级以上科研项目研发工作", 7),
        ZCBZBZYPXJCKJ("主持编著本专业培训教材课件", 8),
        ZWHXQKSFBBZYLW("中文核心期刊上发表本专业论文", 9),
        CYQCZXDZYJXGZYBZ("参与起草制修订专业及相关专业标准", 10);

        private final String name;
        private final Integer type;
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return true;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
