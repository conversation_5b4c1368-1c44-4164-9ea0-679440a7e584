package com.trs.police.profile.service;

import com.trs.police.common.core.params.ExportParams;
import com.trs.police.profile.domain.dto.GroupJqDTO;
import com.trs.police.profile.domain.dto.SaveGtDTO;
import com.trs.police.profile.domain.vo.RelatedGtVO;
import com.trs.police.profile.domain.vo.RelatedGtWarningPersonVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 群体service层
 *
 * <AUTHOR>
 * @date 2022/08/31
 */
public interface FkGroupService {
    /**
     * 获取群体关联预警人员
     *
     * @param groupId 群体id
     * @return 结果
     */
    List<RelatedGtWarningPersonVO> getRelatedGtWarningPerson(Long groupId);

    /**
     * 批量导出群体
     *
     * @param response 响应体
     * @param params   导出参数
     */
    void fkGroupExport(HttpServletResponse response, ExportParams params) throws IOException;

    /**
     * 获取群体关联杆体
     *
     * @param groupId 群体id
     * @return 结果
     */
    List<RelatedGtVO> getRelatedGt(Long groupId);

    /**
     * 保存群体关联杆体
     *
     * @param dto 保存参数
     */
    void saveRelatedGt(SaveGtDTO dto);

    /**
     * 下载fk群体档案
     *
     * @param response HttpServletResponse
     * @param groupId  群体档案id
     */
    void downloadGroup(HttpServletResponse response, Long groupId) throws Exception;

    /**
     * 保存群体关联警情
     *
     * @param dto 保存参数
     */
    void saveRelatedJq(GroupJqDTO dto);
}
