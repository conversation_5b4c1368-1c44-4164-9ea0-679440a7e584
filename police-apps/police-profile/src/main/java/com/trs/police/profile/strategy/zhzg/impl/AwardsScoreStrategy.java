package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ScoreLgsjDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 立功受奖积分计算策略
 */
@Slf4j
@Component
public class AwardsScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "立功受奖";
    private static final String RULE_TYPE = "AWARDS";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算立功受奖积分，人员：{}，规则：{}", personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取立功受奖记录
            List<ScoreLgsjDTO> awards = personArchive.getAwards();
            if (CollectionUtils.isEmpty(awards)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无立功受奖记录")
                        .build();
            }

            // 计算积分
            AwardsType awardsType = AwardsType.valueOf(rule.getRuleSubType());
            long hitCount = awards.stream().filter(dto -> Objects.equals(dto.getLgsj(), awardsType.getType()))
                    .count();

            double calculatedScore = hitCount > 0 ? rule.getScore() : 0.0;

            // 应用规则配置的最大分值限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("获得%s记录：%d条", awardsType.getName(), hitCount);
            String calculateDescription = String.format("%d条获得%s记录 × %.1f分/条 = %.1f分",
                    hitCount, awardsType.getName(), rule.getScore(), calculatedScore);
            
            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.3f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算立功受奖积分失败，人员：{}，规则：{}，错误：{}", 
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Getter
    @AllArgsConstructor
    enum AwardsType {
        GJJLM("国家级劳模", 1),
        SLM("省劳模", 2),
        QGTJYXRMJC("全国特级优秀人民警察", 3),
        QGYXRMJC("全国优秀人民警察", 4),
        GRYDG("个人一等功", 5),
        GREDG("个人二等功", 6),
        GRSDG("个人三等功", 7),
        XJGR("先进个人", 8),
        YXGCDY("优秀共产党员", 9),
        TXDWGZZ("优秀党务工作者", 10);

        private final String name;
        private final Integer type;
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return false;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
