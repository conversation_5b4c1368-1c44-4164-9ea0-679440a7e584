package com.trs.police.profile.controller;

import com.trs.police.common.core.dto.WqkDto;
import com.trs.police.common.core.entity.WqkPerson;
import com.trs.police.profile.service.WqkSearchService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 武器库controller
 */
@RestController
@RequestMapping("/wqk")
public class WqkSearchController {

    @Autowired
    private WqkSearchService wqkSearchService;

    /**
     * 人员检索
     *
     * @param dto dto
     * @return jieguo
     */
    @PostMapping("/personSearch")
    public RestfulResultsV2<WqkPerson> personSearch(@RequestBody WqkDto dto){
        return RestfulResultsV2.ok(wqkSearchService.personSearch(dto));
    }
}
