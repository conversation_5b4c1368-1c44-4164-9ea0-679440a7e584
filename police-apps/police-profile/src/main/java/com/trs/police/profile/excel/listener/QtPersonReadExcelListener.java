package com.trs.police.profile.excel.listener;

import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.domain.entity.person.PersonDcqk;
import com.trs.police.profile.domain.entity.person.PersonGzcs;
import com.trs.police.profile.domain.entity.person.PersonRiskOther;
import com.trs.police.profile.excel.enums.QtPersonImportFieldEnum;
import com.trs.police.profile.mapper.person.PersonDcqkMapper;
import com.trs.police.profile.mapper.person.PersonGzcsMapper;
import com.trs.police.profile.mapper.person.PersonRiskOtherMapper;
import lombok.Builder;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2025/1/9
 */
@Builder
public class QtPersonReadExcelListener extends BasePersonReadExcelListener{

    private PersonRiskOtherMapper personRiskOtherMapper;
    private PersonGzcsMapper personGzcsMapper;
    private PersonDcqkMapper personDcqkMapper;

    /**
     * 人员等级
     */
    private Map<String, Integer> personLevelMap;

    @Override
    protected void init(Map<Integer, String> headMap) {
        super.init(headMap);
        personLevelMap = dictMap("profile_person_control_level");
        headMap.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    QtPersonImportFieldEnum qtPersonImportFieldEnum = QtPersonImportFieldEnum.cnNameOf(entry.getValue());
                    if (Objects.nonNull(qtPersonImportFieldEnum)) {
                        columnMap.put(entry.getKey(), qtPersonImportFieldEnum);
                    }
                });
    }

    @Override
    protected boolean checkHeaderOther(Map<Integer, String> headMap) {
        return super.checkHeaderOther(headMap) && headMap.values().stream().anyMatch(value -> Objects.nonNull(QtPersonImportFieldEnum.cnNameOf(value)));
    }

    @Override
    protected void checkOther(Map<Integer, String> rows, AtomicBoolean validate) {
        checkDict(rows, validate, QtPersonImportFieldEnum.PERSON_LEVEL, personLevelMap);
    }


    @Override
    protected void processOther(Person person, Map<String, String> personMap) {
        super.processOther(person, personMap);
        processWithPoliceKind(person, personMap, personRiskOtherMapper, PersonRiskOther.class);
        processDetail(person, personMap, personGzcsMapper, PersonGzcs.class);
        processDetail(person, personMap, personDcqkMapper, PersonDcqk.class);
    }

    @Override
    protected Long policeKind() {
        return 99L;
    }
}
