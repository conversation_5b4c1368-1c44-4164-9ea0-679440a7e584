package com.trs.police.profile.service.zhzg.statistic;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.profile.domain.dto.zhzg.HzBigScreenDto;
import com.trs.police.profile.service.secne.ProfileStatisticScene;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * xx
 */
@Service
public class DataOverview implements ProfileStatisticScene<CountItem, HzBigScreenDto> {
    @Override
    public List<CountItem> search(HzBigScreenDto hzBigScreenDto) {
        String count = BeanFactoryHolder.getEnv().getProperty("profile.lz.zhzg.personRiskLabel",
                "{\"sg\":32L, \"bmzz\":32L, \"zkj\":32L, \"fkj\":32L,\"mj\":32L,\"sanjzr\":32L,\"sjzr\":32L}");
        HashMap<String,Long> hashMap = JSONObject.parseObject(count, HashMap.class);
        CountItem sgItem = new CountItem();
        sgItem.setKey("sgCount");
        sgItem.setName("三高");
        sgItem.setShowName("三高");
        sgItem.setCount(hashMap.get("sg"));
        Map<String, CountItem> childrenKeyMap = new HashMap<>();
        childrenKeyMap.put("sgCount",sgItem);

        CountItem bmzjItem = new CountItem();
        bmzjItem.setKey("bmzjCount");
        bmzjItem.setName("部门正职");
        bmzjItem.setShowName("部门正职");
        bmzjItem.setCount(hashMap.get("bmzz"));
        childrenKeyMap.put("bmzjCount",bmzjItem);

        CountItem zkjItem = new CountItem();
        zkjItem.setKey("zkjCount");
        zkjItem.setName("正科级");
        zkjItem.setShowName("正科级");
        zkjItem.setCount(hashMap.get("zkj"));
        childrenKeyMap.put("zkjCount",zkjItem);

        CountItem fkjItem = new CountItem();
        fkjItem.setKey("fkjCount");
        fkjItem.setName("副科级");
        fkjItem.setShowName("副科级");
        fkjItem.setCount(hashMap.get("fkj"));
        childrenKeyMap.put("fkjCount",fkjItem);

        CountItem mjItem = new CountItem();
        mjItem.setKey("mjCount");
        mjItem.setName("民警");
        mjItem.setShowName("民警");
        mjItem.setCount(hashMap.get("mj"));
        childrenKeyMap.put("mjCount",mjItem);

        CountItem sjzrItem = new CountItem();
        sjzrItem.setKey("sanjzrCount");
        sjzrItem.setName("三级主任");
        sjzrItem.setShowName("三级主任");
        sjzrItem.setCount(hashMap.get("sanjzr"));
        childrenKeyMap.put("sanjzrCount",sjzrItem);

        CountItem sijzrItem = new CountItem();
        sijzrItem.setKey("sijzrCount");
        sijzrItem.setName("四级主任");
        sijzrItem.setShowName("四级主任");
        sijzrItem.setCount(hashMap.get("sjzr"));
        childrenKeyMap.put("sijzrCount",sijzrItem);
        CountItem countItem = new CountItem();
        countItem.setChildrenKeyMap(childrenKeyMap);
        return List.of(countItem);
    }

    @Override
    public String key() {
        return "dataOverview";
    }

    @Override
    public String desc() {
        return "数据总览";
    }
}
