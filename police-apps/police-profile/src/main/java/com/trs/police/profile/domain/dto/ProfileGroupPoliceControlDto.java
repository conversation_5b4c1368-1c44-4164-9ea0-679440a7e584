package com.trs.police.profile.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 人员档案公安管控
 *
 * <AUTHOR>
 * @since 2024/02/27 15:51
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfileGroupPoliceControlDto extends AbstractBaseEntity {

    private static final long serialVersionUID = -6602037492973471568L;

    /**
     * 群体id
     */
    private Long groupId;

    /**
     * 责任分局code
     */
    private String controlBureau;
    /**
     * 责任分局领导id
     */
    private Long controlBureauLeader;

    /**
     * 责任分局联系方式
     */
    private String controlBureauContact;

    /**
     * 责任警种部门id
     */
    private String controlPolice;

    /**
     * 责任警种部门领导id
     */
    private Long controlPoliceLeader;

    /**
     * 责任分局联系方式
     */
    private String controlPoliceContact;

    /**
     * 责任派出所id
     */
    private String controlStation;
    /**
     * 责任派出所领导id
     */
    private Long controlStationLeader;
    /**
     * 责任民警
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> controlPerson;

    /**
     * 警种类型
     */
    private Integer policeKind;

    /**
     * 模块id
     */
    private Long moduleId;
}
