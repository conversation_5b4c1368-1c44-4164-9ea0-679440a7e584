package com.trs.police.profile.excel.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 人员档案-其他，补充字段枚举
 *
 * <AUTHOR>
 */
public enum QtPersonImportFieldEnum implements IPersonImportFieldEnum{

    PERSON_LEVEL("personLevel","人员级别"),
    MAIN_DEMAND("mainDemand","风险背景"),
    PETITION_INFO("petitionInfo","化解难点"),

    GZCS("gzcs","工作措施"),
    DCQK("dcqk","打处情况")

    ;


    /**
     * 属性名
     */
    @Getter
    private final String field;
    /**
     * 中文名
     */
    @Getter
    private final String cnName;

    /**
     * 私有构造方法
     *
     * @param field  属性名
     * @param cnName 中文名
     */
    QtPersonImportFieldEnum(String field, String cnName) {
        this.field = field;
        this.cnName = cnName;
    }


    /**
     * 英文属性名转换表结构枚举
     *
     * @param field 英文属性名
     * @return 人员信息表结构枚举
     */
    public static QtPersonImportFieldEnum fieldOf(String field) {

        if (StringUtils.isNotBlank(field)) {
            for (QtPersonImportFieldEnum fieldEnum : QtPersonImportFieldEnum.values()) {
                if (StringUtils.equals(fieldEnum.field, field)) {
                    return fieldEnum;
                }
            }
        }
        return null;
    }
    /**
     * 通过中文转换
     *
     * @param cnName 中文名
     * @return 人员信息表结构枚举
     */
    public static QtPersonImportFieldEnum cnNameOf(String cnName) {

        if (StringUtils.isNotBlank(cnName)) {
            cnName = cnName.replace("（必填）", "");
            for (QtPersonImportFieldEnum person : QtPersonImportFieldEnum.values()) {
                if (StringUtils.equals(person.cnName, cnName)) {
                    return person;
                }
            }
        }
        return null;
    }
}
