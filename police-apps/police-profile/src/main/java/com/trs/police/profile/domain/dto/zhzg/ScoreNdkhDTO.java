package com.trs.police.profile.domain.dto.zhzg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 积分计算年度考核DTO
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreNdkhDTO {

    /**
     * 考核日期
     */
    private LocalDate assessmentTime;
    /**
     * 考核结果, 码表，type = police_assessment_result 1:优秀 2:称职 3:基本称职 4:不称职
     */
    private Integer assessmentResult;

}
