package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.profile.domain.dto.zhzg.ScoreNdkhDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 年度考核积分计算策略
 */
@Slf4j
@Component
public class AssessmentsScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "年度考核";
    private static final String RULE_TYPE = "ASSESSMENT";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算年度考核积分，人员：{}，规则：{}", personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取年度考核记录
            List<ScoreNdkhDTO> assessments = personArchive.getAssessments();
            if (CollectionUtils.isEmpty(assessments)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无年度考核记录")
                        .build();
            }

            // 统计assessmentResult=1的记录数量
            long excellentCount = assessments.stream()
                    .filter(assessment -> assessment.getAssessmentResult() != null && assessment.getAssessmentResult() == 1)
                    .count();

            if (excellentCount == 0) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .calculateDescription("无优秀考核记录（assessmentResult=1）")
                        .build();
            }

            // 计算积分：每个优秀考核记录增加rule中的score值
            double scorePerExcellent = rule.getScore() != null ? rule.getScore() : 0.0;
            double calculatedScore = excellentCount * scorePerExcellent;

            // 应用满分上限限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("年度考核记录总数：%d条，优秀记录：%d条",
                    assessments.size(), excellentCount);
            String calculateDescription = String.format("%d条优秀考核记录 × %.1f分/条 = %.1f分",
                    excellentCount, scorePerExcellent, calculatedScore);

            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.1f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算年度考核积分失败，人员：{}，规则：{}，错误：{}",
                    personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return false;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
