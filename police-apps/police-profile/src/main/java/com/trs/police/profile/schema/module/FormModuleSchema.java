package com.trs.police.profile.schema.module;

import static com.trs.police.common.core.utils.JsonUtil.OBJECT_MAPPER;
import static com.trs.police.profile.schema.field.BaseField.UI_OPTIONS;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.police.profile.schema.db.ExtendField;
import com.trs.police.profile.schema.field.BaseField;
import com.trs.police.profile.schema.field.FormField;
import com.trs.police.profile.schema.field.FormFieldSchema;
import com.trs.police.profile.schema.vo.FormSchemaVO;
import com.trs.police.profile.schema.vo.FormSchemaVO.Schema;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/12/05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FormModuleSchema extends BaseModuleSchema {

    private static final long serialVersionUID = -5025381159563612640L;
    private ArrayNode required;

    private FormField[] fields;

    /**
     * 默认字段配置
     */
    private ExtendField[] extendFields;

    /**
     * 生成FormSchema
     *
     * @return formSchema
     */

    public FormSchemaVO toVO() {
        Schema schema = new Schema();
        schema.setRequired(required);
        ObjectNode properties = OBJECT_MAPPER.createObjectNode();
        ObjectNode uiSchema = OBJECT_MAPPER.createObjectNode();
        ObjectNode errorSchema = OBJECT_MAPPER.createObjectNode();
        Arrays.stream(this.fields).forEach(field -> {
            FormFieldSchema formSchema = field.getFormSchema();
            properties.set(field.getName(), formSchema.getSchema());
            uiSchema.set(field.getName(), formSchema.getUi());
            if(formSchema.getErrorSchema()!=null){
                errorSchema.set(field.getName(), formSchema.getErrorSchema());
            }
        });
        schema.setProperties(properties);
        FormSchemaVO result = new FormSchemaVO();
        result.setSchema(schema);
        result.setUiSchema(uiSchema);
        result.setErrorSchema(errorSchema);
        return result;
    }


    /**
     * 生成FormSchema，并过滤掉部分熟悉
     *
     * @param isAdd 是否是新增操作
     * @return formSchema
     */
    public FormSchemaVO toVoV2(Boolean isAdd) {
        Schema schema = new Schema();
        schema.setRequired(required);
        ObjectNode properties = OBJECT_MAPPER.createObjectNode();
        ObjectNode uiSchema = OBJECT_MAPPER.createObjectNode();
        ObjectNode errorSchema = OBJECT_MAPPER.createObjectNode();
        Arrays.stream(this.fields).forEach(field -> {
            FormFieldSchema formSchema = field.getFormSchema();
            properties.set(field.getName(), formSchema.getSchema());
            excludeField(isAdd, formSchema);
            uiSchema.set(field.getName(), formSchema.getUi());
            if(formSchema.getErrorSchema()!=null){
                errorSchema.set(field.getName(), formSchema.getErrorSchema());
            }
        });
        schema.setProperties(properties);
        FormSchemaVO result = new FormSchemaVO();
        result.setSchema(schema);
        result.setUiSchema(uiSchema);
        result.setErrorSchema(errorSchema);
        return result;
    }

    /**
     * 删除掉不需要的属性
     *
     * @param isAdd 新增的schema才进行删除
     * @param formSchema schema
     */
    private void excludeField(Boolean isAdd, FormFieldSchema formSchema) {
        ObjectNode jsonNodes = formSchema.getUi();
        ObjectNode jsonNode = (ObjectNode) jsonNodes.get(UI_OPTIONS);
        JsonNode excludeNode = jsonNode.get("excludeField");
        if (isAdd && Objects.nonNull(excludeNode)) {
            String[] strings = excludeNode.asText().split(",");
            for (String excludeField : strings) {
                jsonNode.remove(excludeField);
            }
        }
        jsonNode.remove("excludeField");
    }

    /**
     * 填充默认值
     *
     * @param resultSet 数据库查询结果
     */
    public void fillDefaultValue(Map<String, Object> resultSet) {
        Arrays.stream(this.fields).forEach(field -> {
            Object value = resultSet.get(field.getName());
            field.fillDefaultValue(value);
        });
    }

    @Override
    public BaseField getField(String fieldName) {
        return Arrays.stream(fields).filter(field -> field.getName().equals(fieldName)).findFirst().orElse(null);
    }
}
