# 档案

## 18.2
- XMKFB-8612 【高新】布控中心，发起群体布控时查看群体的未布控人员报错
-  XMKFB-8783 - 政工系统大屏调整
- XMKFB-8790 - 后-【泸州】- 智慧政工人员列表检索优化

## 18.1
- XMKFB-8665[XMKFB-8623] 后-【泸州】- 警员档案的新增、编辑、详情等schame对接
- XMKFB-8605 【高新】人员积分异常

## RC20250606
- XMKFB-8662【内江】查看案件档案详情页里的警综详情数据报错
- XMKFB-8662【内江】查看案件档案详情页里的警综详情数据报错

## 17.4
- XMKFB-8541 【高新】高新云控人员轨迹异常
- XMKFB-8496 【内江】案件档案详情页报错
- XMKFB-8423[XMKFB-7620] 后-【高新】- 提供FK 群体档案相关接口
- XMKFB-8600 【德阳】任务，人员自动建档以后在人员档案列表里未作显示

## 17.3
- 
- XMKFB-8390[XMKFB-8185] 后-【省厅情指】- 指挥大屏-预警规则配置、预警查询接口提供
- XMKFB-8473 广安-未成年人积分模型调整
- XMKFB-8419[XMKFB-7620] 后-【高新】- 人员预警相关接口提供
- XMKFB-8420[XMKFB-7620] 后-【高新】- 提供FK人员档案相关接口
- XMKFB-8460[XMKFB-8399] 后-【自贡】- 事件关联人员的详情页为空

## 17.2

- XMKFB-8289[XMKFB-8277] 后 -任务支持手动关联群体的行为
- XMKFB-8341 自贡-人员档案部分细览报错问题处理

## RC20250513

- XMKFB-8316 【自贡】【事件档案】【验收】【软测】物品档案报错

## 17.1

-XMKFB-8164 【德阳】人员、群体档案新增需求
-XMKFB-8173 【自贡】【事件档案（旧）】【验收】事件档案修改
-XMKFB-8207 【自贡】【治安形势分析】【验收】治安形势分析版本升级
-XMKFB-8203 【自贡】【治安形势分析】【验收】bug处理
-XMKFB-8208[XMKFB-8227] 后-【自贡】- 单点登录接口文档提供
-XMKFB-8168 【自贡】【警情档案】档案详情页下载档案报错
-XMKFB-8278[XMKFB-7918] 后-群体和事件需要支持在详情页获取审批详情
-XMKFB-8240 【自贡】审批，警种群体档案审批详情页的群体名称和基本情况显示为空
-XMKFB-8241 【自贡】审批，警种事件档案详情页里显示的事件类别和风险等级为空
-XMKFB-7951 【省厅协作】对接大数据3.0的刑事、行政案件信息

## 16.5

-XMKFB-7883 对接绵阳的jq数据
-XMKFB-8075 【德阳】屏蔽事件档案列表里的事件级别列和筛选项
-XMKFB-8084 【德阳】人员档案下载模板需显示“相关任务”模块
-XMKFB-8070 【德阳】人员档案，批量导出表格里统计人员的范围有误
-XMKFB-8079 【德阳】事件档案下载模板的调整与优化
-XMKFB-8078 【德阳】批量导入和下载事件档案模板里没有屏蔽掉事件级别字段
-XMKFB-8042 【自贡】【群体分析】【验收】群体分析功能页面开发
-XMKFB-8176[XMKFB-8187] 后-需要添加字段支持全部

## 16.4

- XMKFB-7913 【自贡】审批类型需补充添加群体、事件档案审批
- XMKFB-7998 自贡-档案审批相关调整
- XMKFB-8049[XMKFB-7918] 后-提供重新提交接口
- XMKFB-7759 【自贡】【事件档案】事件档案导入人员存在问题
- XMKFB-7767 【自贡】【人员档案】人员档案导出优化
- XMKFB-7914 自贡-档案调整
- XMKFB-7768 【自贡】【人员档案】增加关注功能
- XMKFB-8072 新、旧人员档案列表里的责任民警数据为空
- XMKFB-7796 【自贡】事件档案，比中人员编辑弹窗里的人员档案数据显示为了旧版人员档案
- XMKFB-7673 【自贡】人员档案，经侦、治安、其他警种人员下载档案需进行调整优化
- XMKFB-8089 【自贡】相关事件编辑弹窗里的事件开始时间应禁止输入
- XMKFB-8073 【德阳】屏蔽事件档案里关联人员的是否劝返和劝返情况必填标识符
- XMKFB-8088 【自贡】经侦、其他警种人员档案里关联事件档案显示的事件开始时间为空
-

## 16.3

- XMKFB-7798[XMKFB-7698] 后-【德阳】- 完成文档中任务相关的调整
- XMKFB-7791 【德阳】常控中心+人员档案优化需求
- XMKFB-7672 【自贡】人员档案的相关事件数据为旧版事件档案数据
- XMKFB-7673 【自贡】人员档案，经侦、治安、其他警种人员下载档案需进行调整优化
- XMKFB-7698 【德阳】五个板块的需求整改意见

# sql更新 仅德阳

```sql
INSERT INTO t_profile_module
(cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES('相关任务', 'relatedRenwu', 'person', 13, 1, 24, NULL, NULL, '{"name": "相关任务", "type": "LIST_SCHEMA", "table": "v_profile_renwu", "fields": [{"db": {"table": "v_profile_renwu", "column": "data_title", "jdbcType": "string"}, "name": "dataTitle", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "任务名称"}, "properties": {"href": "/ys-app/assignment/list?dataId={value}", "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "v_profile_renwu", "column": "task_no", "jdbcType": "string"}, "name": "taskNo", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "任务编号"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 2}}}, {"db": {"table": "v_profile_renwu", "column": "task_source", "jdbcType": "string"}, "name": "taskSource", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "任务来源"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "v_profile_renwu", "column": "target_time", "jdbcType": "string"}, "name": "targetTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "指向时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "v_profile_renwu", "column": "zxdd", "jdbcType": "string"}, "name": "zxdd", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "指向地点"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "v_profile_renwu", "column": "cr_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "crTime", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}], "selectable": false, "searchFields": [{"key": "dataTitle", "name": "任务名称"}]}', 1, '{"type": "RELATION_TABLE", "table": "tb_intelligence_renwu_related_person_mapping", "joinTo": {"table": "v_profile_renwu", "column": "id", "joinColumn": "task_id"}, "joinFrom": {"table": "t_profile_person", "column": "id_number", "joinColumn": "zjhm", "primaryKey": "id"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 0, 1, 1, 0);
```

## 16.2

- XMKFB-7606 后-【泸州】- 组织机构、用户管理调整
- XMKFB-7625 人员档案，人员轨迹—全量轨迹里轨迹趋势列表中按时间和轨迹总数排序无效
- XMKFB-7786【省厅情指】警情数据的语音播放地址没有更新数据

## 15.4

## 16.1

- XMKFB-7607 自贡-出入境档案导入导出模板及下载模板补充
- XMKFB-7667【省厅情指】所有警情数据均无法查看音频
- XMKFB-7529 【自贡】出入境人员档案详情页顶部字段调整

## RC20250401

- XMKFB-7369 【自贡】完成新增档案的审批流程实现
- XMKFB-7495 后-【自贡】- 紧急警情数据入库
- XMKFB-7483 【省厅情指】将警情的原始录音放出来，且可播放
- XMKFB-7469 【人员档案】批量导出问题
- XMKFB-7486 【自贡】【人员档案】人员档案增加关联事件
- XMKFB-7513 【自贡】出入境人员档案，查看出入境人员档案报错
- XMKFB-7541 【自贡】新版人员档案，人员档案列表未显示出常控标签数据
- XMKFB-7516 【自贡】出入境人员档案，批量导出出入境人员档案报错
- XMKFB-7538 【自贡】出入境人员档案，新增、编辑档案时的居住地所在区范围调整为自贡市
- XMKFB-7526 【自贡】出入境人员档案，档案详情页里的出生日期需显示出年份
- XMKFB-7528 【自贡】出入境人员档案，新增出入境人员弹窗里工作记录里开展日期的优化与调整
- XMKFB-7530 【自贡】出入境人员档案，详情页里的签证类型数据显示为空
- XMKFB-7532 【自贡】出入境人员档案详情页缺少人员轨迹模块
- XMKFB-7533 【自贡】出入境人员档案，将工作记录里的“管控措施”改为“工作描述”
- XMKFB-7463 本地相片数据处理
- XMKFB-7491【自贡】事件档案，批量导入比中人员模板调整与优化
- XMKFB-7568 群体布控脏数据
- XMKFB-7576[XMKFB-7559] 后 - 优化积分行为
-

## 15.3

- XMKFB-7036 【自贡】档案，档案操作记录里需记录新增、导入档案操作
- XMKFB-7355 【德阳】人员档案，下载和批量导入人员档案模板的调整与优化
- XMKFB-7360 【德阳】群体档案，下载和批量导入群体档案模板的调整与优化
- XMKFB-7268 合 -【广安】- 紧急警情表单及功能优化
- XMKFB-7199 自贡-人员档案-新增出入境档案
- XMKFB-7450[XMKFB-7446] 未成年人积分预警模型系统界面开发
-

## 配置文件更改

```properties
# 只配置德阳环境
profile.env=dy
```

## RC20250312

- XMKFB-7040[XMKFB-7126] 后-【省厅情指】- 警情相关接口需要支持按照管辖单位地域检索

## 配置文件更改

```properties
# 省厅情指环境独有
# 省厅情指大屏每个角色能看的重大警情类型
profile.stqzdp.jqTypes.pcs=
profile.stqzdp.jqTypes.province=010301,010101
profile.stqzdp.jqTypes.city=010301,010302,010303,010405,010105,010101,010104
profile.stqzdp.jqTypes.county=010301,010302,010303,010405,010105,010101,010104,01,02
```

## 15.2

- XMKFB-7036 【自贡】档案，档案操作记录里需记录新增档案操作
- XMKFB-7209 合 -【广安】- 人员档案详情-全量轨迹-热力图、轨迹趋势报错问题处理
- XMKFB-7169 群体档案，群体架构图里人员背景信息里的现住址和户籍地数据显示为空
- XMKFB-7139 后-【高新】- 人员档案性别信息有误
- XMKFB-7153 合 -【省厅情指】- 发起协战，关联警情，新增筛选项
- XMKFB-7325 【自贡】新版档案批量导入模板中标签信息未同步更新
- XMKFB-7270【自贡】刑侦人员档案，调整刑侦人员档案批量导入模板
- XMKFB-7265【自贡】刑侦人员档案，编辑管控措施里的管控到期日期数据需支持手动选择

``` nacos profile.yaml 下添加如下配置 具体环境需要配置对应环境的信息
profile:
  person:
    track:
      list:
        data-source: mysql
        searchRepositoryType: mysql
        searchRepositoryHost:  ************
        searchRepositoryPort: 3306
        searchRepositoryUser: ys
        searchRepositoryPassword: 123456 
        searchRepositoryPrincipal: ""
        searchRepositoryKrb5Path: ""
        searchRepositoryUserKeytabPath: "" 
```

## RC20250312

- XMKFB-7040[XMKFB-7126] 后-【省厅情指】- 警情相关接口需要支持按照管辖单位地域检索

## 配置文件更改

```properties
# 省厅情指环境独有
# 省厅情指大屏每个角色能看的重大警情类型
profile.stqzdp.jqTypes.pcs=
profile.stqzdp.jqTypes.province=010301,010101
profile.stqzdp.jqTypes.city=010301,010302,010303,010405,010105,010101,010104
profile.stqzdp.jqTypes.county=010301,010302,010303,010405,010105,010101,010104,01,02
```

## 15.1

- XMKFB-7072[XMKFB-7071]后-【广安】- 人员档案列表接口支持
- XMKFB-7025 【自贡】人员档案，所有警种的人员档案导入模板里目前所在地提示文字的“在京”应改为“在北京”
- XMKFB-7024 【自贡】人员档案，批量导入人员档案时输入多个手机号时无法导入
- XMKFB-7028 【自贡】人员档案，批量导入经侦档案时判定的风险等级有误
- XMKFB-7029 新版和旧版人员档案，人员档案下载模板里的照片放置位置需做调整
- XMKFB-7153 合 -【省厅情指】- 发起协战，关联警情，新增筛选项

## RC20250227

- XMKFB-7018 后-【自贡】- 人员档案数据权限问题排查

## RC20250226

- XMKFB-6974 后-【德阳】- 人员、群体档案增加化解难点

## 14.4

- XMKFB-6961 档案，批量导出群体档案和下载事件档案报错
- XMKFB-6968 - 【自贡】事件档案，批量导入比中人员的户籍地为空

## RC20250227

- XMKFB-7018 后-【自贡】- 人员档案数据权限问题排查

## RC20250226

- XMKFB-6974 后-【德阳】- 人员、群体档案增加化解难点

## RC20250224

-XMKFB-6915 【高新】云控，近期机器人自动发起的常控数据没有责任警种、发起人和发起单位数据

## 配置文件更改

```properties
# 高新环境独有
# 事件人员同步：默认管控警种
profile.event.download.controlPolice=2
# 模块常控创建人
profile.event.download.regularCreateUser=管理员
# 模块常控单位
profile.event.download.regularCreateDept=高新区分局
```

## 14.3

- XMKFB-6865 合 -【高新】- 人员档案优化
- XMKFB-6894 自贡-人员档案-刑侦档案调整
- XMKFB-6853 省厅情指-协同作战PC+大屏功能和页面调整
- XMKFB-6859【自贡】新版人员档案，批量导入时不同警种之间身份证号不需要作判重校验

## RC20250211

-XMKFB-6784 新增人员档案报错

## 14.2

-XMKFB-6721 【自贡】新版群体档案，下载档案的表格内容样式需做优化处理
-XMKFB-6722 【自贡】新版群体档案下载后的档案表格内容存在的问题
-XMKFB-6748[XMKFB-6716] 自贡-群体档案-下载档案调整
-XMKFB-6716 自贡-人员/群体/事件档案-下载档案调整

## RC20250211

-XMKFB-6784 新增人员档案报错

## 13.4

-XMKFB-6387[XMKFB-6278] 后-【自贡】- 完成新版群体档案批量导出功能
-XMKFB-6440 新增或编辑人员档案时会把已被删除的人员数据进行重复校验，导致操作失败
-XMKFB-6438 人员档案详情页中显示的电话号码格式需作优化处理
-XMKFB-6278 自贡-新版档案批量导出模板更新
-XMKFB-6336 - 【自贡】群体档案，群体架构图中返回的人员等级仍是风险等级
-XMKFB-6372 - 【自贡】事件档案，批量导入比中人员失败
-XMKFB-6005 【自贡】事件档案详情页里顶部显示的基本信息数据需做调整

## 13.3

-XMKFB-6254[XMKFB-5897] 【自贡】- 新版本群体档案支持批量导入
-XMKFB-6376[XMKFB-6333] 后-【省厅情指】协作作战大屏时间轴
-XMKFB-5944 【自贡】档案，档案详情页中的警种Tab分类是否可见需作优化调整
-XMKFB-6283[XMKFB-5896] 后-【自贡】- 新版本群体档案详情下载
-XMKFB-6338 【自贡】群体档案，屏蔽掉详情页中相关人员的风险分值和风险等级数据
-XMKFB-6255 后-【自贡】- 新版事件档案支持批量导入
-XMKFB-6284 后-【自贡】- 新版本事件档案详情下载
-XMKFB-6372 【自贡】事件档案，批量导入比中人员失败
-XMKFB-6328 线索档案，将线索详情页顶部显示的线索内容进行屏蔽
-XMKFB-5896 【自贡】在档案详情页中下载档案报错
-XMKFB-5897 【自贡】档案，批量导入档案后导入到了旧版档案列表中
-XMKFB-6149 人员档案，档案详情页中的全量轨迹列表及详情显示的类型数据为空

## 13.2

-XMKFB-5148 【泸州】5110工单协作详情里未反显警情、案件、涉案人员数据
-XMKFB-5890 【自贡】群体档案里的基本情况信息输入方式应作调整
-XMKFB-5895 【自贡】治安警种群体档案，党政管控信息字段的展示样式
-XMKFB-5899 【自贡】群体档案，新增/编辑群体档案时显示的基本情况、工作措施等输入框的宽度应加宽
-XMKFB-5900 【自贡】群体档案，在群体档案中新增公安管控信息后未作显示
-XMKFB-5902 【自贡】群体档案详情页中治安和其他警种字段展示存在的问题
-XMKFB-6161 人员档案，新增或编辑人员档案时需要对电话号码进行格式校验
-XMKFB-5929 【自贡】人员档案，治安和其他警种人员档案详情页中显示的级别字段数据有误
-XMKFB-6249 自贡】治安警种人员档案里部分文案与需求预期不一致
-XMKFB-6210[XMKFB-5918] 【后】—— 标签多选类的控件schema修改
-XMKFB-5885 【自贡】人员档案，治安警种人员档案的相关线索展示字段需作调整
-XMKFB-6248 【自贡】治安警种人员档案里当输入的描述为空时保存提交失败
-XMKFB-5898 【自贡】新增\编辑档案时部分输入框限制字符长度较小
-XMKFB-6177 【自贡】人员档案，人员档案列表中未显示出人员、管控级别数据
-XMKFB-6303 自贡-档案-群体档案详情中治安警种的公安管控信息展示样式需调整
-XMKFB-6251 【自贡】编辑事件档案详情页中的风险点信息报错
-XMKFB-5947 【自贡】事件档案，编辑涉及群体时加载群体档案数据的接口报错
-XMKFB-5961 【自贡】治安警种事件档案里风险点信息字段数据和样式的调整与优化
-XMKFB-5963 【自贡】事件档案里估计人数可选择或输入为负数
-XMKFB-5965 【自贡】经侦、其他警种事件档案列表中显示的主管单位数据为空
-XMKFB-5966 【自贡】治安警种事件档案列表中的主管单位和主责警种取值有误
-XMKFB-5973 【自贡】事件档案，屏蔽涉及人员列表中风险分值的相关字段
-XMKFB-6256 后-【自贡】-经侦事件档案列表需补充事件指向地点敏感度字段和筛选项
-XMKFB-5964 【自贡】经侦事件档案里风险点信息的主管单位和主责警种展示位置应作置换

## RC20240107

- 人员档案列表检索优化

## 13.1

-XMKFB-5985 【高新】群体档案详情页中需补充相关预警模块显示群体关联人员的全部预警信息
-XMKFB-6172 后-【自贡】- 人员档案责任分局录入错误
-XMKFB-6167[XMKFB-6124] 后-【自贡】- 人员档案细览页，增加矛盾纠纷记录
-XMKFB-6092 后-【自贡】- 完成人员档案与省厅线索的自动关联
-XMKFB-6116 【自贡】矛盾纠纷预防与化解大屏，风险预警情况需默认按照总数降序排列
-XMKFB-6075 - 合 -【自贡】- 事件档案涉及人员支持批量导入
-XMKFB-6078 后 - 接口支持

## RC20241231

- XMKFB-6065 自贡-人员档案、事件档案优化
- XMKFB-6132 后-【自贡】- 人员档案被删除后录入无显示
- XMKFB-6147 后-【自贡】- 矛盾纠纷大屏风险人员数量对应不上

## RC20242030

- XMKFB-6101 后-【自贡】- 特殊行业人员标签要移动其他警种下
- XMKFB-6090 【自贡】矛盾纠纷预防与化解大屏，人员分布类型中的数据未按照数量从高到底排序

## 12.4

- XMKFB-5934[XMKFB-5927] 后-【自贡】- 特殊行业人员接口提供
- XMKFB-5866 【自贡】人员档案，在涉毒、刑侦人员档案详情页中编辑相关案件存在的问题
- XMKFB-5882 【自贡】治安警种人员档案详情页中相关事件显示的事件类别数据为空
- XMKFB-5854 后-【德阳】- 档案优化需求（德阳定制需求）
- XMKFB-5888 【自贡】治安警种人员档案中工作措施列表里的来源数据应调整为选择框
- XMKFB-5885 【自贡】人员档案，治安警种人员档案的相关线索展示字段需作调整
- XMKFB-5884 【自贡】档案，在相关线索弹窗中勾选线索后未显示出线索标签数据
- XMKFB-5881 【自贡】治安人员档案中相关群体的群体分工数据未显示出来
- XMKFB-5879 【自贡】编辑档案警种信息后更新时间未变化
- XMKFB-5874 【自贡】治安警种人员档案中三跨三分离人员数据显示为空
- XMKFB-5857 后-【自贡】- 人员档案bug/优化
- XMKFB-6039[XMKFB-5935] 后端修改详情页头部的”户籍地“字段为”户籍所在地“
- XMKFB-6014 【自贡】- 事件档案检索支持按事件详情检索

## 配置文件更改

```properties
# 自贡环境添加、特俗人员标签根节点
profile.statistic.riskPersonRootLabel=
```

## 12.3

- XMKFB-5773 【自贡】档案，编辑档案中的相关时间数据失败
- XMKFB-5748 【自贡】人员档案，人员基本信息字段展示样式的调整与补充
- XMKFB-5775 【自贡】人员档案，档案列表显示的风险级别、管控级别数据为空
- XMKFB-5795 【自贡】人员档案，新增/编辑人员档案时显示的民族顺序位置应作调整
- XMKFB-5787 【自贡】人员档案，经侦人员档案详情页中的离退休时间展示格式应作调整
- XMKFB-5789 【自贡】人员档案，人员档案详情页中的风险分值和人员级别字段应作屏蔽
- XMKFB-5769 【自贡】档案，新增人员档案报错
- XMKFB-5764 【自贡】人员档案，政府管控信息字段的调整与优化
- XMKFB-5756 【自贡】人员档案，经侦风险点信息字段的优化与调整
- XMKFB-5749 【自贡】人员档案，车辆信息缺少车辆特征数据
- XMKFB-5751 【自贡】人员档案的家庭关系列表字段缺少职务类型、现住址、联系方式三个字段
- XMKFB-5759 【自贡】人员档案，经侦人员风险点信息中的现位置可选数据显示为了德阳市的数据
- XMKFB-5762 【高新】群体/人员档案优化（高新特有）
- XMKFB-5757 后-【高新】- 机器人数据接入问题排查
- XMKFB-5758 后-【高新】- 机器人对接新字段到事件详情
- XMKFB-5668 后-【自贡】- 警情档案详情页优化
- XMKFB-5869 【自贡】人员档案，详情页显示的责任派出所需做排重过滤
- XMKFB-5875 【自贡】人员档案，同一人员多警种档案编辑相关群体、案件、事件、人员、线索时会重置其他的警种
- XMKFB-5608 后-【泸州】- 人员档案列表检索代码优化

```yaml
# 德阳环境更新
# 人员导入模版名称
profile:
  import:
    template:
      person: importPersonDy.xlsx
```

```yaml
# 自贡环境更新
profile:
  env: zg
```

```yaml
# 德阳环境更新
# 人员导入模版名称
profile:
  import:
    template:
      person: importPersonDy.xlsx
```

## 12.2

- XMKFB-5575 后-【高新】- 人员档案中所有人都发起常控
- XMKFB-5608 后-【泸州】- 人员档案列表检索代码优化
- XMKFB-5644 【高新】人员、群体、事件档案，风险分趋势图里的数据应正常返回风险分数为0的日期
- XMKFB-5549 德阳GA-批量导入人员档案的模板新增“人员级别”字段
- XMKFB-5586 后-【德阳】- 德阳人员常控需求
- XMKFB-5646 【高新】人员、群体、事件档案，优化/profile/risk/findRiskScoreBatchGroupByDay接口响应速
- XMKFB-5606[XMKFB-5600] 【自贡】——后端提供操作记录按警种查询的接口
- XMKFB-5637[XMKFB-5632] 【前】—— 人员档案，列表页问题
- XMKFB-5683[XMKFB-5632] 【后】人员档案——删除某个警种档案时需要同时删除详情页返回目录中对应警种的信息
- XMKFB-5660[XMKFB-5632]  后 - 警种档案目录菜单默认需要返回0
- XMKFB-5634[XMKFB-5632] 【后】—— 人员档案，户籍所在地码表不对
- XMKFB-5548 人员档案批量导入时判定的单位需使用精准匹配
- XMKFB-5578 德阳GA-导出人员档案的模板，“管控责任人”应对应责任民警

## 12.1

- XMKFB-5449[XMKFB-5411] 后-【自贡】- 群体档案相关接口提供
- XMKFB-5510 后-【高新】- 人员档案落脚点/全量轨迹接口优化
- XMKFB-5481 后-【高新】- 管控记录同步后问题排查
- XMKFB-5586 后-【德阳】- 德阳人员常控需求
- XMKFB-5450 后-【自贡】- 事件档案相关接口提供
- XMKFB-5448[XMKFB-5411 ] 后-【自贡】- 完成人员档案相关接口提供

## 11.4

- XMKFB-5382[XMKFB-5324] 后 - 完善批次数据入库跟列表查询
- XMKFB-5381[XMKFB-5237] 后-【南充大屏】增加接口支持
- 泸州接入三台合一数据优化
- XMKFB-5458 【高新】群体档案详情页，风险积分明细中显示的积分存在小数
- XMKFB-5339 后-【德阳】-北新机械厂-群体档案-上传附件bug
- XMKFB-5383 - 【高新】云控，事件档案详情页涉及人员缺少风险标识
- XMKFB-5300 - 德阳GA-配合人员档案、群体档案导出，需要部分前端修改
- XMKFB-5337 合-【德阳】- 人员/群体档案需求

## v11.3

- XMKFB-5226 后-【高新】-机器人-工作管控记录数据对接
- XMKFB-5184 【德阳】人员档案，责任单位筛选匹配的逻辑应做调整
- XMKFB-5272 后-【德阳】- 档案新增需求

## RC20241119

- 警情导入增加省厅实现

## v11.2

- XMKFB-5079 后-【高新】- 机器人同步过来的人员需要自动发起常控
- XMKFB-5148 【泸州】5110工单协作详情里未反显警情、案件、涉案人员数据
- XMKFB-4710 【前】—— 屏蔽风险积分相关内容
- XMKFB-4940 合-【高新】-群体档案导出风险分值少一分问题
- XMKFB-5129 后 - 群体积分规则优化
- XMKFB-5161 合-【德阳】- 档案优化
- XMKFB-4705 【后】—— 人员档案-导出带统计功能
- XMKFB-5046 【德阳】群体档案，群体架构图中返回的人员活跃程度数据均为空值

```yaml
# 高新环境更新
profile:
  event:
    download:
      # 默认常控级别：关注
      regularMonitorLevel: 5
      # 默认常控标签：关注
      regularMonitorLabelIds: 1190
```

## 11.1

- XMKFB-4940 合-【高新】-人员/事件/群体档案优化
- XMKFB-4947[XMKFB-4940] 后-【高新】-机器人事件人员入库逻辑优化

sql更新,全环境

```sql
#群体、事件档案细览页-相关人员：按风险分值从高到低排列显示
update t_profile_module set list_schema = '{
    "name": "相关人员",
    "type": "LIST_SCHEMA",
    "table": "t_profile_person",
    "fields": [
        {
            "db": {
                "table": "t_profile_person",
                "column": "name",
                "jdbcType": "string"
            },
            "name": "name",
            "listSchema": {
                "style": {
                    "align": "center",
                    "width": 104
                },
                "schema": {
                    "type": "string",
                    "title": "人员名称"
                },
                "properties": {
                    "href": "/ys-app/archives/person/details?id={value}",
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": false
                }
            }
        },
        {
            "db": {
                "table": "t_profile_person",
                "column": "risk_level",
                "jdbcType": "string"
            },
            "name": "riskLevel",
            "listSchema": {
                "style": {
                    "align": "center",
                    "width": 128
                },
                "schema": {
                    "type": "string",
                    "title": "风险等级"
                },
                "properties": {
                    "colorMap": {
                        "关注": "#333333 ",
                        "中风险": "#FFCE60",
                        "低风险": "#6088D6",
                        "高风险": "#FA8C34",
                        "重中之重": "#EC3939"
                    },
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": false,
                    "instrLength": 1
                }
            }
        },
        {
            "db": {
                "table": "t_profile_person",
                "column": "id_number",
                "jdbcType": "number"
            },
            "name": "idNumber",
            "listSchema": {
                "style": {
                    "align": "center",
                    "width": 224
                },
                "schema": {
                    "type": "string",
                    "title": "身份证号"
                },
                "properties": {
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": false,
                    "instrLength": 1
                }
            }
        },
        {
            "db": {
                "exist": false,
                "table": "t_profile_person_event_relation",
                "column": "risk_label_ids",
                "jdbcType": "string",
                "databaseRelation": {
                    "type": "SCORE_SUM",
                    "table": "t_profile_person_event_relation",
                    "joinTo": {
                        "table": "t_profile_person",
                        "column": "id",
                        "joinColumn": "person_id"
                    },
                    "joinFrom": {
                        "table": "t_profile_event",
                        "column": "id",
                        "joinColumn": "event_id"
                    },
                    "joinSumTable": {
                        "table": "tb_risk_label",
                        "sumColumn": "label_score",
                        "joinColumn": "id"
                    }
                }
            },
            "name": "riskScore",
            "listSchema": {
                "style": {
                    "align": "left",
                    "width": 128
                },
                "schema": {
                    "type": "string",
                    "title": "风险分值"
                },
                "properties": {
                    "color": "#FF6C6C",
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": true,
                    "instrLength": 1,
                    "sortDefault": "descending"
                }
            }
        },
        {
            "db": {
                "table": "t_profile_person",
                "column": "person_label",
                "mapping": "label_id_array_to_name",
                "jdbcType": "label_id_array"
            },
            "name": "personLabel",
            "listSchema": {
                "style": {
                    "align": "left",
                    "width": 237
                },
                "filter": {
                    "key": "personLabel",
                    "type": "multiple-tree",
                    "value": [
                        "&&person_label&&"
                    ],
                    "fieldNames": {
                        "label": "name",
                        "value": "id",
                        "children": "children"
                    },
                    "displayName": "人员标签"
                },
                "schema": {
                    "type": "array",
                    "title": "人员标签"
                },
                "properties": {
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": false,
                    "instrLength": 2
                }
            }
        }
    ],
    "selectable": false,
    "searchFields": [
        {
            "key": "name",
            "name": "人员名称"
        }
    ],
     "defaultSortParams": {
        "sortField": "riskScore",
        "sortDirection": "descending"
    }
}'
where id =203;

update t_profile_module set database_relation = '{
    "type": "SCORE_SUM",
    "table": "t_profile_person_event_relation",
    "joinTo": {
        "table": "t_profile_person",
        "column": "id",
        "joinColumn": "person_id"
    },
    "joinFrom": {
        "table": "t_profile_event",
        "column": "id",
        "joinColumn": "event_id"
    }
}'
where id =203;

update t_profile_module set list_schema = '{
    "name": "相关人员",
    "type": "LIST_SCHEMA",
    "table": "t_profile_person",
    "fields": [
        {
            "db": {
                "table": "t_profile_person",
                "column": "name",
                "jdbcType": "string"
            },
            "name": "name",
            "listSchema": {
                "style": {
                    "align": "center"
                },
                "schema": {
                    "type": "string",
                    "title": "人员名称"
                },
                "properties": {
                    "href": "/ys-app/archives/person/details?id={value}",
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": false
                }
            }
        },
        {
            "db": {
                "table": "t_profile_person",
                "column": "risk_score",
                "mapping": "number_half_adjust_to_string",
                "jdbcType": "number"
            },
            "name": "riskScore",
            "listSchema": {
                "style": {
                    "align": "left",
                    "width": 128
                },
                "schema": {
                    "type": "string",
                    "title": "风险分值"
                },
                "properties": {
                    "color": "#FF6C6C",
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": true,
                    "instrLength": 1,
                    "sortDefault": "descending"
                }
            }
        },
        {
            "db": {
                "table": "t_profile_person",
                "column": "risk_level",
                "jdbcType": "string"
            },
            "name": "riskLevel",
            "listSchema": {
                "style": {
                    "align": "center",
                    "width": 128
                },
                "schema": {
                    "type": "string",
                    "title": "风险等级"
                },
                "properties": {
                    "colorMap": {
                        "关注": "#333333 ",
                        "中风险": "#FFCE60",
                        "低风险": "#6088D6",
                        "高风险": "#FA8C34",
                        "重中之重": "#EC3939"
                    },
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": false,
                    "instrLength": 1
                }
            }
        },
        {
            "db": {
                "table": "t_profile_person",
                "column": "person_label",
                "mapping": "label_id_array_to_name",
                "jdbcType": "label_id_array"
            },
            "name": "personLabel",
            "listSchema": {
                "style": {
                    "align": "left"
                },
                "filter": {
                    "key": "personLabel",
                    "type": "multiple-tree",
                    "value": [
                        "&&person_label&&"
                    ],
                    "fieldNames": {
                        "label": "name",
                        "value": "id",
                        "children": "children"
                    },
                    "displayName": "人员标签"
                },
                "schema": {
                    "type": "array",
                    "title": "人员标签"
                },
                "properties": {
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": false,
                    "instrLength": 2
                }
            }
        },
        {
            "db": {
                "table": "t_profile_person_group_relation",
                "column": "activity_level",
                "mapping": "dict_code_to_name",
                "jdbcType": "number",
                "databaseRelation": {
                    "type": "RELATION_TABLE",
                    "table": "t_profile_person_group_relation",
                    "joinTo": {
                        "table": "t_profile_person",
                        "column": "id",
                        "joinColumn": "person_id"
                    },
                    "joinFrom": {
                        "table": "t_profile_group",
                        "column": "id",
                        "joinColumn": "group_id"
                    }
                }
            },
            "dict": {
                "type": "profile_activity_level"
            },
            "name": "activityLevel",
            "listSchema": {
                "style": {
                    "align": "center"
                },
                "schema": {
                    "type": "select",
                    "title": "活跃程度"
                },
                "properties": {
                    "copyable": false,
                    "editable": true,
                    "required": false,
                    "sortable": false,
                    "instrLength": 1
                }
            }
        },
        {
            "db": {
                "table": "t_profile_person",
                "column": "create_dept_id",
                "mapping": "dept_id_to_dept_name",
                "jdbcType": "number"
            },
            "name": "createDept",
            "listSchema": {
                "style": {
                    "align": "center"
                },
                "filter": {
                    "key": "createDept",
                    "type": "tree",
                    "value": [
                        "&&dept&&"
                    ],
                    "fieldNames": {
                        "label": "shortName",
                        "value": "id",
                        "children": "children"
                    },
                    "displayName": "录入单位"
                },
                "schema": {
                    "type": "string",
                    "title": "录入单位"
                },
                "properties": {
                    "copyable": false,
                    "editable": false,
                    "required": false,
                    "sortable": false,
                    "instrLength": 1
                }
            }
        }
    ],
    "selectable": false,
    "searchFields": [
        {
            "key": "name",
            "name": "人员名称"
        }
    ],
     "defaultSortParams": {
        "sortField": "riskScore",
        "sortDirection": "descending"
    }
}'
where id = 109;
```

## RC 20241108

- XMKFB-5030 后-【高新】- 事件关联人员未同步到其对应的群体中

## 10.5

- XMKFB-4851[XMKFB-4824] 后-【自贡】-警务协作-紧急警情表单修改

## 10.4

- XMKFB-4692[XMKFB-4690] 后 - 优化分数计算起止时间
- XMKFB-4753 后-【高新】-事件档案-相关人员数据入库问题
- XMKFB-4702[XMKFB-4669] 后-【德阳】-完成人员档案相关调整
- XMKFB-4783 [XMKFB-4722] 后-【广安】-预警和警情接口增加按ID检索
- XMKFB-4704[XMKFB-4669] 后-【德阳】-完成群体档案相关调整

sql更新，只更新德阳环境

```sql
#德阳人员档案详情去掉风险积分,风险等级
update t_profile_module set list_schema = '{
  "name": "人员信息",
  "type": "LIST_SCHEMA",
  "table": "t_profile_person",
  "fields": [
    {
      "db": {
        "table": "t_profile_person",
        "column": "photo",
        "mapping": "json_to_image_array",
        "jdbcType": "json_object_array"
      },
      "name": "photo",
      "listSchema": {
        "style": {
          "align": "center",
          "fixed": "left",
          "ellipsis": true
        },
        "schema": {
          "type": "photo",
          "title": "照片"
        },
        "properties": {
          "isPhoto": true,
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false,
          "isRelatedShow": true
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person",
        "column": "name",
        "jdbcType": "string"
      },
      "name": "name",
      "listSchema": {
        "style": {
          "align": "center",
          "ellipsis": true
        },
        "schema": {
          "type": "string",
          "title": "姓名"
        },
        "properties": {
          "isName": true,
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false,
          "isRelatedShow": true
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person",
        "column": "id_number",
        "jdbcType": "string"
      },
      "name": "idNumber",
      "listSchema": {
        "style": {
          "align": "center",
          "ellipsis": false
        },
        "schema": {
          "type": "string",
          "title": "证件号码"
        },
        "properties": {
          "href": "/ys-app/archives/person/details?id={value}",
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false,
          "isRelatedShow": true,
          "validateOption": {
            "pattern": "/[0-9]/"
          }
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person",
        "column": "person_label",
        "mapping": "label_id_array_to_name",
        "jdbcType": "label_id_array"
      },
      "name": "personLabel",
      "listSchema": {
        "style": {
          "align": "center",
          "width": 400,
          "ellipsis": true
        },
        "filter": {
          "key": "personLabel",
          "type": "multiple-tree",
          "value": [
            "&&person_label&&"
          ],
          "fieldNames": {
            "label": "name",
            "value": "id",
            "children": "children"
          },
          "displayName": "人员标签"
        },
        "schema": {
          "type": "array",
          "title": "人员标签"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false,
          "instrLength": 2,
          "isRelatedShow": true
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person",
        "column": "control_level",
        "mapping": "dict_code_to_name",
        "jdbcType": "string"
      },
      "dict": {
        "type": "profile_person_control_level"
      },
      "name": "control_level",
      "listSchema": {
        "style": {
          "align": "center"
        },
        "filter": {
          "key": "control_level",
          "type": "select",
          "value": [
            "%%profile_person_control_level%%"
          ],
          "fieldNames": {
            "label": "name",
            "value": "code"
          },
          "displayName": "人员级别"
        },
        "schema": {
          "type": "string",
          "title": "人员级别"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false,
          "instrLength": 1
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person_police_control",
        "column": "control_person",
        "mapping": "user_id_array_to_user_name_array",
        "jdbcType": "user_id_array",
        "databaseRelation": {
          "type": "FOREIGN_KEY",
          "table": "t_profile_person_control",
          "column": "person_id"
        }
      },
      "name": "dutyPolice",
      "listSchema": {
        "style": {
          "align": "center"
        },
        "schema": {
          "type": "array",
          "title": "责任民警"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false,
          "instrLength": 1
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person_police_control",
        "column": "control_station",
        "mapping": "dept_code_to_dept_name",
        "jdbcType": "dept_code",
        "databaseRelation": {
          "type": "FOREIGN_KEY",
          "table": "t_profile_person_control",
          "column": "person_id"
        }
      },
      "name": "dutyPoliceStation",
      "listSchema": {
        "style": {
          "align": "center"
        },
        "filter": {
          "key": "dutyPoliceStation",
          "type": "multiple-tree",
          "value": [
            "&&dept&&"
          ],
          "fieldNames": {
            "label": "shortName",
            "value": "deptCode",
            "children": "children"
          },
          "displayName": "责任派出所"
        },
        "schema": {
          "type": "string",
          "title": "责任派出所"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false,
          "isRelatedShow": true
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person",
        "column": "registered_residence",
        "mapping": "district_code_to_name",
        "jdbcType": "district"
      },
      "name": "registeredResidence",
      "listSchema": {
        "style": {
          "align": "center"
        },
        "filter": {
          "key": "registeredResidence",
          "type": "multiple-tree",
          "value": [
            "&&district&&"
          ],
          "fieldNames": {
            "label": "name",
            "value": "id",
            "children": "children"
          },
          "displayName": "户籍地"
        },
        "schema": {
          "type": "string",
          "title": "户籍地"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": false
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person",
        "column": "create_time",
        "mapping": "date_time_to_text",
        "jdbcType": "datetime"
      },
      "name": "createTime",
      "listSchema": {
        "style": {
          "align": "center"
        },
        "filter": {
          "key": "createTime",
          "type": "timeParams",
          "value": [
            {
              "id": "1",
              "name": "今天"
            },
            {
              "id": "11",
              "name": "昨天"
            },
            {
              "id": "2",
              "name": "本周"
            },
            {
              "id": "12",
              "name": "上周"
            },
            {
              "id": "3",
              "name": "本月"
            },
            {
              "id": "13",
              "name": "上月"
            },
            {
              "id": "4",
              "name": "本季"
            },
            {
              "id": "14",
              "name": "上季"
            },
            {
              "id": "99",
              "name": "自定义"
            }
          ],
          "fieldNames": {
            "label": "name",
            "value": "id"
          },
          "displayName": "录入时间"
        },
        "schema": {
          "type": "string",
          "title": "录入时间"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": true
        }
      }
    },
    {
      "db": {
        "table": "t_profile_person",
        "column": "update_time",
        "mapping": "date_time_to_text",
        "jdbcType": "datetime"
      },
      "name": "updateTime",
      "listSchema": {
        "style": {
          "align": "center"
        },
        "filter": {
          "key": "updateTime",
          "type": "timeParams",
          "value": [
            {
              "id": "1",
              "name": "今天"
            },
            {
              "id": "11",
              "name": "昨天"
            },
            {
              "id": "2",
              "name": "本周"
            },
            {
              "id": "12",
              "name": "上周"
            },
            {
              "id": "3",
              "name": "本月"
            },
            {
              "id": "13",
              "name": "上月"
            },
            {
              "id": "4",
              "name": "本季"
            },
            {
              "id": "14",
              "name": "上季"
            },
            {
              "id": "99",
              "name": "自定义"
            }
          ],
          "fieldNames": {
            "label": "name",
            "value": "id"
          },
          "displayName": "更新时间"
        },
        "schema": {
          "type": "string",
          "title": "更新时间"
        },
        "properties": {
          "copyable": false,
          "editable": false,
          "required": false,
          "sortable": true,
          "sortDefault": "descending"
        }
      }
    }
  ],
  "selectable": true,
  "searchFields": [
    {
      "key": "idNumber",
      "name": "身份证号"
    },
    {
      "key": "name",
      "name": "姓名"
    }
  ],
  "profileDataPermission": [
    {
      "db": {
        "table": "t_profile_person",
        "column": "person_label",
        "jdbcType": "string"
      },
      "type": "personLabel"
    }
  ]
}'
where id = 1
```

## 10.3

- XMKFB-4465[XMKFB-4378] 后 - 优化人工扣分记录
- XMKFB-4469[XMKFB-4378] 后 - 【云哨】人员档案详情增加schema
- XMKFB-4432 后-【自贡】-警情档案-警情详情优化
- XMKFB-4468 后-【南充】-大屏数据范围分级
- XMKFB-4605[XMKFB-4147] 后-【高新】-群体档案-群体关联人员接口需要分页
- XMKFB-4624 【高新】事件统计显示的风险积分有误
- XMKFB-4618 事件关联人员失败，接口报错
- XMKFB-4691[XMKFB-4690] 后 - 人员减分项的累加，保留小数点后两位，详见后附图；
-

## v10.2

- XMKFB-4290 后-【高新】-事件档案-积分自动减分 bug处理
- XMKFB-4177[XMKFB-4175] 后 - 后端调整适配
- XMKFB-4292[XMKFB-3925] 后-【检索】自动扣减记录弹窗接口需要返回已失效的数量
- XMKFB-4256 【高新】人员档案列表中的风险分值显示为了小数
- XMKFB-4333[XMKFB-4312] 后 - 增加累计扣分数据返回
- XMKFB-4340[XMKFB-4312] 后 - 事件档案详情页调整
- XMKFB-4324 后 - 事件人员自动关联群体人员
- XMKFB-4394[XMKFB-4312] 后 - 优化工作记录保存
- XMKFB-4393[XMKFB-4378] 后 - 优化相关数据查询跟保存
- XMKFB-4433[XMKFB-4312] 后 - 优化自动扣分记录
- XMKFB-4472 后 - 排查敏感日问题

## v9.4

- XMKFB-4054 前-【高新】-人员档案照片展示bug
- XMKFB-4075 合-【泸州】-档案-警情档案新增
- XMKFB-3992 后-【高新】-人员档案风险积分规则优化
- XMKFB-4151 后-【高新】-事件、人员机器人对接需求
- XMKFB-4146 后-【高新】-群体档案关联人员重复问题处理
- XMKFB-4259 后-【高新】-对接第三方机器人系统-管控单位录入优化
- XMKFB-4255 【高新】自动扣减的积分未统计进入风险总积分中

新增nacos配置

```properties
#只更新gx环境
#是否开启事件下载
profile.event.download.enable=true
#下载最近几天的数据
profile.event.download.days=3
#事件下载环境
profile.event.download.downloadType=gx
#默认事件类别：2分事件
profile.event.download.eventLabel=140
#默认事件来源：平台线索
profile.event.download.source=2
#默认人员标签，涉稳
profile.event.download.personLabel=100
#默认人员级别，关注
profile.event.download.personLevel=5
#默认管控单位
profile.event.download.controlStation=510109310000
#默认管控领导
profile.event.download.controlLeader=40
#gx单位对应领导映射
profile.event.download.controlLeaderMap='{"510109490000":45,"510109530000":30,"510109500000":56,"510109400000":144,"510109450000":54,"510109480000":25,"510109510000":119,"510109430000":261,"510109410000":94,"510109570000":28,"510109540000":42}'
```

## v9.3

- XMKFB-3992 后-【高新】-人员档案风险积分规则优化
- XMKFB-3994 批量导入人员档案模板里的责任分局和责任警种字段应作调整
- XMKFB-4005 后-【测试环境】-发起作战，查询相关案件接口报错
- XMKFB-4063 添加工单-事项认证，案件档案列表的数据为空
- XMKFB-3991[XMKFB-3925] 后 - 扣分逻辑开发
- XMKFB-4144 后-【高新】-人员档案编辑报错
-

## v9.2

- XMKFB-3907 前-【广安】-发起协作关联案件优化
- XMKFB-3784 合-【高新】-群体档案增加归档功能
- XMKFB-3754[XMKFB-3385] 后-【南充】-提供警情、案件、线索相关接口
- XMKFB-3876 后-【高新】-群体档案列表筛选项：新增-主责警种和主责单位
- XMKFB-3833 后-【高新】-事件档案-关联人员支持批量录入
- XMKFB-3918[XMKFB-3812]  后 - 【云哨】对应接口返回值需要增加 createTime 字段
- XMKFB-3640 泸州5110工单系统
- XMKFB-3989[XMKFB-3925] 后 -增加对应配置项的保存
-

新增nacos配置

```properties
#只更新南充环境
#敏感警情关键词
profile.jq.sensitiveKey=不想活,自杀,精神病,打架斗殴,亡,械,火车,炸,毒,牺牲,讨薪,上访,静坐,请愿,聚集,游行,冲击,冲撞,砍,捅,刺,死,强奸,烧,枪,尸体,堵路,猥亵,杀,武器,刀,斧,肇事肇祸,赴省,进京,罢课,罢市,罢运,极端,示威,敏感关键字,抢劫,绑架,挟持,恐吓,胁迫,猎杀,走私,拐卖,黑社会,黑势力,恶势力,地震,洪涝,泥石流,山体滑坡,国家,领导,横幅,学生,孩,政治,政权,外国,领馆,使馆,港,澳,记者,袭击,攻击,子弹,弹药,罢工,罢教,起火,跳,台湾,动车,铁,5万,50000,艾滋病,自焚,扬言
```

## v9.1

- XMKFB-3716 后-【德阳】-人员档案支持责任分局检索
- XMKFB-3642 后-【高新】-人员档案批量导入模版
- XMKFB-3176 数据权限-业务范围设置，业务标签为空时仍能查看全部的档案数据
- XMKFB-3693 后-【德阳】-后台-标签处理

## RC20240903

> XMKFB-3683 后-【德阳】-【重要】云哨-人员档案查看的数据权限逻辑优化

## v8.5

> XMKFB-3238 后-【南充】-警情档案bug
> XMKFB-3461 合-【高新】-人员档案增加归档流程

## 发版后

```
南充环境发版后，需访问接口
/profile/public/dealHistoryJqbq
更新历史警情标签
```

## v8.4

> XMKFB-3256[XMKFB-3253] 后-【高新】-档案相关问题排查

## v8.3

> XMKFB-3055[XMKFB-3049] 后-【广安】-指挥大屏高风险预警无经纬度排查  
> XMKFB-3053 后-【广安】-案件列表加载慢问题排查  
> XMKFB-2981 处突，人员档案数据未展示人员照片
> XMKFB-3057 合-【广安】-合成作战相关优化  
> XMKFB-3054[XMKFB-3049] 后-【广安】-指挥大屏警情数据问题排查

## v8.2

> 电子围栏无数据问题处理

## v8.1

> XMKFB-2876 后-【省厅】-发送政务微信消息覆盖整个系统模块

## RC20240724 发版文件

> findByIdentifier 接口慢问题处理

## v7.3 发版文件

> XMKFB-2539 类型选择样式和类型名称应作调整  
> XMKFB-2647 后-【高新】-人员档案优化  
> XMKFB-2649 后-【高新】-后台-数据权限问题排查

## v7.2 发版文件

> XMKFB-2512 档案导出的sheet表名称应作调整  
> XMKFB-2513 修改物品状态的审批无效  
> XMKFB-2502 新增、编辑物品档案时补充物品状态字段  
> XMKFB-2499 后-【高新】-档案数据权限问题处理

## v7.1 发版文件

> XMKFB-2379 [XMKFB-1725]审批列表和详情页中标题里的物品档案名称显示为了档案ID  
> XMKFB-2392 [XMKFB-1725] 物品档案详情导出表格样式的调整与修改

## RC 20240702

- XMKFB-2427 【泸州】云墙首页跳云哨无法自动登入

## v6.4 发版文件

> XMKFB-2286): 后-【自贡公安】-使用用户账号登录进入档案报错  
> XMKFB-2249 后-人员档案列表检索优化  
> XMKFB-2315 编辑处突页面的关联预警数据加载失败

## v6.3 发版文件

> XMKFB-2138 【泸州】FX人员档案优化  
> XMKFB-2180 后-人员档案数据处理

## v6.2RC20240619

> XMKFB-2207 【高新】高新云控-人员档案bug（责任派出所检索没效果）

## v6.2 发版日志

# 20240605

> XMKFB-1892 [XMKFB-1725]后-物品档案列表开发
> XMKFB-2054[XMKFB-1848] 后 - 增加根据证件号码反查人档的行为
> XMKFB-1835 [XMKFB-1725] 物品档案开发

## v6.1

> XMKFB-1774 【广安】线索导入规则调整  
> XMKFB-1879 【高新】感知引擎订阅  
> XMKFB-1907[XMKFB-1725] 后-自贡大屏-今日重点关注案件、事件列表检索接口提供

## v5.4 RC20240603

> XMKFB-1871 后-[高新]档查询bug

## v5.4 发版日志

# 20240529

> XMKFB-1793、路由配置项增加meta字段增加 hideGlobalTabBar 配置  
> XMKFB-1632 【高新】人员、群体档案

## v5.4 配置更新

```yaml
profile:
  person:
    personModuleId: 3
    tagDeptTypeMapping: '{}'
    qzDeptType: 1
    personProfileDefaultConfig: profile-person-approval-default
    personProfileQzConfig: profile-person-approval-qz
    personProfileDutyConfig: profile-person-approval-duty
    personProfilePoliceConfig: profile-person-approval-police
    personRegularLevel: '{"1":"1", "2":"2", "3":"3", "4":"4",}'
    personRegularDefault: 5
    personRegularTag: 62
```

## v5.3 发版日志

# 20240520

> XMKFB-1619、责任派出所枚举值，去掉治安署、刑侦署、政经文保署，只保留11个派出所；

## v5.2 发版日志

# 20240517

> XMKFB-1595、风险列表慢问题处理

## 5.1

- XMKFB-1471、后-【达州】批量导入人员后，所属警种、责任派出所为同一单位

## 1.5

- # 2024-04-28
- XMKFB-1159、档案，当自定义时间筛选值为空时筛选结果报错
- XMKFB-1186、人员档案下载文件内容中相关预警列表下的预警类型数据错误
- XMKFB-1181、人员档案导入公安管控信息处理
- # 2024-04-26
- XMKFB-1162、线索编号是否已经存在接口提供
- # 2024-04-24
- XMKFB-1127、事件档案导入失败
- XMKFB-1122、群档敏感时间bug处理
- XMKFB-1213、后-事件和案件的下载接口的提供
- XMKFB-1132、人员、群体、事件、线索档案，选择不导入创建方式后导入重复数据的导入结果显示有误
- # 2024-04-23
- XMKFB-1177、群体档案导入数据未对责任派出所、责任派出所领导和责任民警字段作空值校验
- XMKFB-1182、群体档案导入模板的调整与优化
- XMKFB-1107、人档导入返回值优化
- XMKFB-1104、当人员、群体档案中存在多个公安管控信息时下载档案失败
- XMKFB-1098、批量导出本页的人员档案数据，导出接口报错
- # 2024-04-22
- XMKFB-1108、当人员档案有多个公安管控信息时，人员列表责任派出所字段下的数据显示为空
- # 2024-04-19
- XMKFB-1000、后-线索档案导入功能开发
- XMKFB-1123、群体档案导入，导入存在多个群体类别的群体数据失败
- XMKFB-1037、查看发起常控列表中的数据接口报错
- XMKFB-1124、群体档案导入，当导入的单个相关人员号码存在多个人员档案数据时导入失败
- # 2024-04-18
- XMKFB-1020、【广安】【南充】线索档案接收第三方数据
- XMKFB-1038、当人员的布控数据被删除后，布控人员档案详情页中的信息显示有误
- # 2024-04-16
- XMKFB-1131 线索导入模板缺少涉及人员、涉及群体信息的填写
- XMKFB-1128 事件档案导入模板的优化与调整
- XMKFB-1106 群体档案下载文件内容的优化处理
- # 2024-04-12
- XMKFB-1046 【后】—— 需要提供标签删除的接口
- XMKFB-1043 后-相关人员和相关事件接口提供
- # 2024-04-10
- XMKFB-1041、人员档案列表页中缺少“风险分值”字段
- XMKFB-980、档案导入问题
- XMKFB-742 人员档案列表中的UI样式优化
- XMKFB-890 后 - 风险积分定时计算优化
- XMKFB-938 【南充】警情数据时间问题
- XMKFB-973 【南充】警情档案websocket问题
- XMKFB-1066 后-【高新公安】-警情表增加维度时间字段
- XMKFB-1228 人员档案，新增或导入人员档案数据显示的风险等级为空
-

## 1.4

- XMKFB-886 后 - 风险分数更新时导致档案对象其他属性也被更新了

## v1.3 发版日志

- # 2024-03-21
- XMKFB-845、后-身份证批量粘贴
- # 2024-03-18
- XMKFB-751、后-高新导入人档不成功
- # 2024-03-14
- XMKFB-740、后-广安实现获取三台合一警情的获取接口
- # 2024-03-12
- XMKFB-698、后-排查不生效原因
- # 2024-03-11
- XMKFB-697、后 - 接口需要新增对应排序参数
- # 2024-03-08
- XMKFB-681、后-档案下载优化
- # 2024-03-06
- XMKFB-564、高新GA-重点人员、群体档案下载
- XMKFB-684[XMKFB-615] 后 - 风险积分计算
- XMKFB-677[XMKFB-615] 后 - 修改事件是否发生状态接口开发
- XMKFB-702[XMKFB-615] 后 - 调整schema
- # 高新： 2024-03-12 RC发版
- XMKFB-714 【优化】人员、群体、事件档案档案列表中风险分值字段下的数据应靠左对齐
- XMKFB-724[XMKFB-723] 【后】—— 风险分数不要小数
- XMKFB-723 对人员档案详情页中风险分值查看明细窗口的展示样式做兼容处理
- XMKFB-718 当人员的风险标识变化后，人员对应的风险分值也应同步计算更新
- XMKFB-716 事件、人员档案被删除后对应的风险积分仍存在
- XMKFB-726 群体档案下载后的表格数据应新增风险积分相关的数据
- # 高新： 2024-03-15 发版
-

### 新增配置项

```yaml
profile:
  RiskLevelScore:
    config: '[{"min":0,"max":1,"name":"关注","color":"#333333"},{"min":1,"max":30,"name":"低风险","color":"#6088D6"},{"min":30,"max":60,"name":"中风险","color":"#FFCE60"},{"min":60,"max":80,"name":"高风险","color":"#FA8C34"},{"min":80,"name":"重中之重","color":"#EC3939"}]'
# 高新： 2024-03-12 RC发版
```

## v1.2 发版日志

- # 2024-02-29
- XMKFB-581、人档和群档管控信息优化
- # 2024-02-19
- XMKFB-541、南充高风险警情异常
- XMKFB-606、处理群体一直处于审核中状态

## v1.1 发版日志

- # 2024-02-04
- XMKFB-509、【高新fk专题】已建档用户未跟人员档案同步问题
- # 2024-02-02
- XMKFB-508、后-群体档案导入模版的模版中的标签有问题

## v1.0 发版日志

- # 2024-01-29
- XMKFB-445、后-目前群体导入下载模版报错，需要解决报错
- XMKFB-440、后-未建档涉恐人员建档接口提供
- XMKFB-447、后-群体导入功能开发

### nacos配置更新

```

```