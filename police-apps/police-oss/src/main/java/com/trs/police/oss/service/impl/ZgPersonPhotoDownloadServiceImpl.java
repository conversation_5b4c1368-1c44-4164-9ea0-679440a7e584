package com.trs.police.oss.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.oss.service.PersonPhotoDownloadService;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * 下载人员照片
 *
 * <AUTHOR>
 * @date 2025/03/31 22:14
 */
@Service
@Slf4j
public class ZgPersonPhotoDownloadServiceImpl implements PersonPhotoDownloadService {

    @Resource
    private MinioClient minioClient;

    private static final String BUCKET = "photo";

    @Override
    public Boolean downloadPersonPhoto(String idCard) {
        try {
            Boolean enable = BeanFactoryHolder.getEnv().getProperty("oss.zg.photo.enabled", Boolean.class, false);
            if (!Boolean.TRUE.equals(enable)) {
                return Boolean.FALSE;
            }
            return doDownloadPersonPhotoFromZg(idCard);
        } catch (Exception e) {
            log.info("下载头像失败", e);
            return Boolean.FALSE;
        }
    }

    @Override
    public String getServiceEnv() {
        return "zg";
    }

    /**
     * 自贡图片下载
     *
     * @param idNumber 身份证号码
     * @return 是否成果
     */
    public Boolean doDownloadPersonPhotoFromZg(String idNumber) {
        try {
            final RestTemplate restTemplate = new RestTemplate();
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 创建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("bucket", "zyzh-hjzp");
            requestBody.put("img_name", String.format("%s.jpg", idNumber));
            requestBody.put("result_type", "0");
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送POST请求
            String apiUrl = BeanFactoryHolder.getEnv().getProperty("oss.zg.photo.service");
            ResponseEntity<JSONObject> u = restTemplate.exchange(
                    apiUrl,
                    HttpMethod.POST,
                    requestEntity,
                    JSONObject.class
            );
            // 返回响应数据
            JSONObject data = u.getBody();
            log.info("zg头像返回结果：{}", data);
            // 修改doDownloadPersonPhotoFromZg方法中的资源获取方式
            try {
                String imageUrl = data.getString("data");
                // 使用URI构造器处理特殊字符
                URI uri = new URI(imageUrl);
                // 直接获取字节数组避免类型转换问题
                ResponseEntity<byte[]> response = restTemplate.exchange(
                        uri,
                        HttpMethod.GET,
                        null,
                        byte[].class
                );

                if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                    try (InputStream inputStream = new ByteArrayInputStream(response.getBody())) {
                        String fileName = idNumber + ".jpg";
                        // 使用实际内容长度
                        minioClient.putObject(PutObjectArgs.builder()
                                .bucket(BUCKET)
                                .object(fileName)
                                .stream(inputStream, response.getBody().length, -1)
                                .build());
                    }
                    return Boolean.TRUE;
                }
            } catch (URISyntaxException e) {
                log.error("URL格式错误: {}", data, e);
            }
        } catch (Exception e) {
            log.error("zg头像查询失败", e);
            return Boolean.FALSE;
        }
        return Boolean.FALSE;
    }
}
