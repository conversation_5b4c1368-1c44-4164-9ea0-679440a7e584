package com.trs.police.docsonline.police.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.docsonline.authapi.User;
import com.trs.police.docsonline.authapi.UserService;
import com.trs.police.docsonline.common.constant.SysConstant;
import com.trs.police.docsonline.core.VO.WorkSheetDbVO;
import com.trs.police.docsonline.dbapi.JfGridConfigModel;
import com.trs.police.docsonline.dbapi.db.IRecordDataInsertHandle;
import com.trs.police.docsonline.dbapi.db.IRecordSelectHandle;
import com.trs.police.docsonline.mysql.mpservice.SheetMpService;
import com.trs.police.docsonline.police.DTO.BatchCreateDTO;
import com.trs.police.docsonline.police.DTO.BookQueryDTO;
import com.trs.police.docsonline.police.DTO.RelationQueryDTO;
import com.trs.police.docsonline.police.DTO.SaveRelationDTO;
import com.trs.police.docsonline.police.VO.BookListVO;
import com.trs.police.docsonline.police.convertor.BookConvertor;
import com.trs.police.docsonline.police.convertor.RelationConvertor;
import com.trs.police.docsonline.police.entity.WorkBook;
import com.trs.police.docsonline.police.entity.WorkBookRelationDO;
import com.trs.police.docsonline.police.service.IWorkBookService;
import com.trs.police.docsonline.police.service.RelationMpService;
import com.trs.police.docsonline.police.service.WorkBookMpService;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class WorkBookServiceImpl implements IWorkBookService {

    @Autowired
    private UserService userService;

    @Autowired
    private WorkBookMpService workBookMpService;

    @Autowired
    private IRecordDataInsertHandle recordDataInsertHandle;

    @Autowired
    private IRecordSelectHandle recordSelectHandle;

    @Autowired
    private RelationMpService relationMpService;

    @Autowired
    private SheetMpService sheetMpService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(String templateId, String name) throws IOException {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        ServletServerHttpRequest servletServerHttpRequest = new ServletServerHttpRequest(request);
        User user = userService.fromRequestHeader(servletServerHttpRequest);
        return create(templateId, name, user);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> batchCreate(List<BatchCreateDTO> dto) throws IOException {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        ServletServerHttpRequest servletServerHttpRequest = new ServletServerHttpRequest(request);
        User user = userService.fromRequestHeader(servletServerHttpRequest);
        List<String> result = new ArrayList<>();
        for (BatchCreateDTO d : dto) {
            result.add(create(d.getTemplateId(), d.getName(), user));
        }
        return result;
    }

    public String create(String templateId, String name, User user) throws IOException {
        WorkBook workBook = new WorkBook(name, user);
        workBookMpService.save(workBook);
        String listId = workBook.getId().toString();

        if (StringUtils.isEmpty(templateId)) {
            createDefaultSheet(listId);
        } else {
            createSheetByTemplate(templateId, listId);
        }
        return listId;
    }

    private void createDefaultSheet(String listId) throws IOException{
        // 新建一个sheet
        WorkSheetDbVO vo = new WorkSheetDbVO();
        vo.setRow_col("5_5");
        vo.setList_id(listId);
        vo.setIndex("0");
        vo.setStatus(Integer.valueOf(SysConstant.STATUS.Valid));
        vo.setBlock_id(JfGridConfigModel.FirstBlockID);
        String string = IOUtils.toString(this.getClass().getClassLoader().getResourceAsStream("blankSheet.json"));
        JSONObject jsonData = JSON.parseObject(string);
        vo.setJson_data(jsonData);
        vo.setOrder(0);
        recordDataInsertHandle.InsertBatchDbByVO(Arrays.asList(vo));
    }

    private void createSheetByTemplate(String templateListId, String listId) throws IOException {
        List<WorkSheetDbVO> vos = recordSelectHandle.getBlockAllByGridKeyV2(templateListId, null);
        for (WorkSheetDbVO vo : vos) {
            vo.setId(null);
            vo.setList_id(listId);
        }
        recordDataInsertHandle.InsertBatchDbByVO(vos);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveRelation(SaveRelationDTO dto) {
        Objects.requireNonNull(dto.getDataId());
        Objects.requireNonNull(dto.getRelationType());
        Objects.requireNonNull(dto.getListId());
        // 删除旧的
        List<WorkBookRelationDO> saved = relationMpService.lambdaQuery()
                .eq(WorkBookRelationDO::getDataId, dto.getDataId())
                .eq(WorkBookRelationDO::getRelationType, dto.getRelationType())
                .list();
        relationMpService.removeBatchByIds(saved);
        // 保留新的
        List<WorkBookRelationDO> dos = Stream.of(dto.getListId().split(",|;"))
                .distinct()
                .map(item -> {
                    WorkBookRelationDO aDo = RelationConvertor.CONVERTER.dtoToDO(dto);
                    aDo.setListId(item);
                    return aDo;
                })
                .collect(Collectors.toList());
        relationMpService.saveBatch(dos);
        return dto.getListId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PageResult<BookListVO> list(BookQueryDTO queryDTO, PageParams pageParams, SortParams sortParams) {
        Page<WorkBook> page = workBookMpService.lambdaQuery()
                .ge(Objects.nonNull(queryDTO.getCreateStartTime()), WorkBook::getCreateTime, queryDTO.getCreateStartTime())
                .le(Objects.nonNull(queryDTO.getCreateEndTime()), WorkBook::getCreateTime, queryDTO.getCreateEndTime())
                .like(Objects.nonNull(queryDTO.getKeyword()), WorkBook::getTitle, queryDTO.getKeyword())
                .orderBy("create_time".equals(sortParams.getSortField()), sortParams.isAsc(), WorkBook::getCreateTime)
                .page(new Page<WorkBook>(pageParams.getPageNumber(), pageParams.getPageSize()));
        List<BookListVO> voList = page.getRecords()
                .stream()
                .map(BookConvertor.CONVERTER::doToVO)
                .collect(Collectors.toList());
        return PageResult.of(voList, pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PageResult<BookListVO> listByRelation(RelationQueryDTO queryDTO, PageParams pageParams, SortParams sortParams) {
        List<String> ids = relationMpService.lambdaQuery()
                .eq(WorkBookRelationDO::getRelationType, queryDTO.getRelationType())
                .eq(WorkBookRelationDO::getDataId, queryDTO.getDataId())
                .list()
                .stream()
                .map(WorkBookRelationDO::getListId)
                .collect(Collectors.toList());
        if (ids.isEmpty()) {
            return PageResult.of(new ArrayList<>(), pageParams.getPageNumber(), 0L, pageParams.getPageSize());
        }
        Page<WorkBook> page = workBookMpService.lambdaQuery()
                .ge(Objects.nonNull(queryDTO.getCreateStartTime()), WorkBook::getCreateTime, queryDTO.getCreateStartTime())
                .le(Objects.nonNull(queryDTO.getCreateEndTime()), WorkBook::getCreateTime, queryDTO.getCreateEndTime())
                .in(WorkBook::getId, ids)
                .like(Objects.nonNull(queryDTO.getKeyword()), WorkBook::getTitle, queryDTO.getKeyword())
                .orderBy("create_time".equals(sortParams.getSortField()), sortParams.isAsc(), WorkBook::getCreateTime)
                .page(new Page<WorkBook>(pageParams.getPageNumber(), pageParams.getPageSize()));
        List<BookListVO> voList = page.getRecords()
                .stream()
                .map(BookConvertor.CONVERTER::doToVO)
                .collect(Collectors.toList());
        return PageResult.of(voList, pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, String> names(String listIds) {
        List<Integer> ids = Stream.of(listIds.split(",|;"))
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        Map<String, String> map = workBookMpService.lambdaQuery()
                .select(WorkBook::getId, WorkBook::getTitle)
                .in(WorkBook::getId, ids)
                .list()
                .stream()
                .collect(Collectors.toMap(bk -> String.valueOf(bk.getId()), bk -> bk.getTitle()));
        return map;
    }
}
