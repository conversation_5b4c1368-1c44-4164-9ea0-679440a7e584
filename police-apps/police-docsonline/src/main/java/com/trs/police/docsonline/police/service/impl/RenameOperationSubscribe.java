package com.trs.police.docsonline.police.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.docsonline.core.entity.enummodel.SheetOperationEnum;
import com.trs.police.docsonline.core.service.OperationSubscribe;
import com.trs.police.docsonline.police.entity.WorkBook;
import com.trs.police.docsonline.police.service.WorkBookMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.trs.police.docsonline.core.entity.enummodel.SheetOperationEnum.na;

@Component
public class RenameOperationSubscribe implements OperationSubscribe {

    @Autowired
    private WorkBookMpService workBookMpService;

    @Override
    public Boolean acceptEvent(SheetOperationEnum op) {
        try {
            return na.equals(op);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void onEvent(String gridKey, JSONObject event) {
        Objects.requireNonNull(gridKey);
        String name = event.getString("v");
        workBookMpService.lambdaUpdate()
                .set(WorkBook::getTitle, name)
                .eq(WorkBook::getId, gridKey)
                .update();
    }
}
