# permission
- XMKFB-8808[XMKFB-8702] 增加警号的返回

## 18.1
- feat: 增加获取展示提示词接口  

## 17.1
- XMKFB-8148 后-【自贡】- 后端接口支持

## RC20250509
- XMKFB-8284 云哨导航栏参照权限中心放置AI机器人

## 16.5
- XMKFB-8018 警务协作，只要有一个单位有审批人权限其他单位也会有相同权限

## 16.4
- XMKFB-7977 【泸州】大数据权限中心，审核记录列表里显示的申请人信息有误
- XMKFB-7971 【泸州】大数据权限中心，审批驳回后无法再次发起权限申请
- XMKFB-7926 【泸州】大数据权限中心大屏里未过滤已停用的平台
- XMKFB-7967 【泸州】大数据权限中心大屏里统计显示的授权警种人数和授权区县人数有误
- XMKFB-7969 【泸州】大数据权限中心大屏顶部统计的平台数量未过滤已停用的平台数
- 【泸州】权限中心添加AI问答功能  
- XMKFB-8094[XMKFB-8052] 【泸州】大数据权限中心，日志审核列表存在的问题


# RC20250425
- XMKFB-8108 后-【广安】- 协作自动审批、指令单/审批表映射优化

## RC20250423
- XMKFB-8018 警务协作，只要有一个单位有审批人权限其他单位也会有相同权限

## 16.3
- XMKFB-7884 【泸州】数据权限中心，用户分配弹窗里未过滤显示真实组织
- XMKFB-7889 【泸州】数据权限中心，用户信息里的所属组织会显示虚拟组织
- XMKFB-7902 【泸州】数据权限中心，系统后台能够检索出虚拟组织单位
- XMKFB-7892 【泸州】数据权限中心，用户授权无效
- XMKFB-7886 【泸州】数据权限中心，编辑、新增用户弹窗里角色信息存在的问题
- XMKFB-7895 【泸州】数据权限中心，禁用平台后前台仍显示了已禁用的系统
- 【泸州】数据权限中心，平台管理列表筛选未接入数据的筛选结果有误
- 【泸州】数据权限中心，平台授权列表筛选出的无权限人员有误
- 【泸州】大数据权限中心，平台授权的人员列表里每一页显示的数据都相同
- XMKFB-7955 【泸州】移除用户所属组织单位无效
- XMKFB-7933 【泸州】大数据权限中心，所在单位没有配置申请权限审批流程时页面会直接报错
- 权限中心流程返回部门名称

## 16.2
- XMKFB-7606 后-【泸州】- 组织机构、用户管理调整
- XMKFB-7592[XMKFB-7567] 后-【泸州】- 完成审批相关接口接口提供

## 15.4
- XMKFB-7370 【自贡】角色详情显示优化

## RC20250325
- XMKFB-7497 合-【省厅情指】- 省厅情指作战消息bug

## 15.3
- XMKFB-7279 角色权限，角色列表中的数据排序方式应作优化

## 15.2
- XMKFB-7277[XMKFB-7276] 后-允许移动到根目录
- XMKFB-7185 后-【自贡】- 登陆支持根据手机号+验证码登陆

## 15.1
- XMKFB-7002 自贡GA-后台权限管理新增左侧目录树+展示描述

## RC20250226
- XMKFB-6992 后 - 【省厅情指】布控任务-部门筛选需要接口支持返回平铺数据

## v14.3
- XMKFB-6856 【自贡】门户首页，系统集列表搜索无效
- feat: XMKFB-6863[XMKFB-6853] 后 -【省厅情指】- 人员导入模板增加岗位字段

## v14.2
- XMKFB-6771 后-【省厅情指】- 指挥日志-值班领导/副班等检索优化

## v14.1
- XMKFB-6682[XMKFB-6616] 门户接口返回权限字段

## v13.4
- XMKFB-6524 后-【省厅情指】- 指挥日志 选值班人的时候 岗位可选人范围需要改为本地区
- XMKFB-6447 【省厅情指】狼烟大屏，值班人员中的人员下拉框无法查看最后一级组织的人员数据
- 

# permission
## v13.3
-XMKFB-6046 省厅GA-门户增加后台配置功能
-XMKFB-6276 省厅-协同作战-添加参与人员时显示岗位和值班情况
- XMKFB-6219 省厅GA-四川公安指挥大屏左侧统计模块替换为布控监测模块
-XMKFB-6377[XMKFB-6333] 后-【省厅情指】风险警情卡片按照层级展示

## v13.2
-XMKFB-6187 后-【省厅情指】- 系统管理-用户组织管理，全省组织树展示顺序是乱的
-XMKFB-6201 后-【高新】- 人员档案按警种查询和按警署查数据不一致

## RC20240103
- XMKFB-6180 后-【自贡】- 系统管理报错
- 
## RC20241231
- XMKFB-6065 自贡-人员档案、事件档案优化

## v12.4
- XMKFB-5989 getRootDeptByPermission 增加usePermission参数

## v12.3
- XMKFB-5413 后-禁用的用户需要再前台的相关接口过滤掉
- XMKFB-5665 合 -【自贡】- 警务协作-组织列表弹窗-常用分组优化


## v12.1
- XMKFB-5540 后台管理-用户组织管理，组织类别数据清空无效  
- XMKFB-5583[XMKFB-5541] 需要后端接口返回部门简称  

## v11.4
- XMKFB-5398 后-编辑组织，删除组织子类别和警种无效
- XMKFB-5283 同警种、指定警种本单位及下级单位或全部单位数据权限用户查看用户组织无数据
- XMKFB-528 同警种、指定警种本单位及下级单位或全部单位数据权限用户发起常控报系统错误
- XMKFB-5288 同警种、指定警种本单位及下级单位或全部单位数据权限用户查看角色权限、数据权限报系统错误
- XMKFB-5339 后-【德阳】-北新机械厂-群体档案-上传附件bug
- XMKFB-5365[XMKFB-5319] 后-【省厅情指】- 门户首页系统应用接口提供
- XMKFB-5413 合-【自贡】- 合成、协作、后台问题

## v11.3
- XMKFB-5272 后-【德阳】- 档案新增需求

## v11.2
- XMKFB-5147 协作转办，返回的转办单位不正确
- XMKFB-5146 协作上报，接口异常

## RC20241115
- XMKFB-5188 合-【自贡】-系统首页增加系统集

## v11.1
- XMKFB-4778 省厅协作相关优化
- XMKFB-4728 广安-预案相关调整

## v10.5
- XMKFB-4755 德阳GA-大数据核查+后台配置数据权限支持按警种配置
- XMKFB-4894 【泸州】警务协作，从其他协作类别切换至技侦协作后firstApprovalUser接口报错

## v10.4
- XMKFB-4702[XMKFB-4669] 后-【德阳】-完成人员档案相关调整

## v10.3
- XMKFB-4344 后-【泸州】-审批表单支持单位/个人签章  

## v10.2
- XMKFB-4076 广安GA-预案功能

## v9.4
- 根据省份证号查询人员字段不全问题修复
- XMKFB-4094 后-【省厅】-删除掉不需要的组织类别
- XMKFB-4196[XMKFB-3836] 添加常用分组允许指定到人
- XMKFB-4168[XMKFB-4111] 后-【泸州】-个人/组织签章维护功能开发

# v9.3
- XMKFB-4009 后-【高新】-人员档案责任派出所筛选为空
- XMKFB-3513 后-【自贡】-单点登录对接  

## v9.2
- XMKFB-3923 【自贡】指令，接收单位按警种选择无效

## v9.1
- XMKFB-3631[XMKFB-2556] 要情、指令导出优化

## v8.5

- XMKFB-3424 部门管理员逻辑问题
- XMKFB-3633 后-【自贡】-系统首页优化
- XMKFB-3461 合-【高新】-人员档案增加归档流程

## v8.4

- 根据身份证查询用户的接口返回用户所属部门
- XMKFB-3174[XMKFB-3173] 后-【自贡】-首页后端优化
- 根据身份证查询用户的接口返回用户所属部门
- XMKFB-3184 后-接收单位查询接口筛选支持

## v8.3

- XMKFB-3091 后-【省厅】-发起协作流程优化
- XMKFB-3184 后-接收单位查询接口筛选支持
- XMKFB-3078 合-【省厅】-省厅系统安全漏洞处理

## RC20240812

- XMKFB-3076 后-【省厅】-角色支持排序

## v8.2

- 组织树权限优化
- 新增、编辑自定义角色时未填充createUserId、createDeptId bug修复
- XMKFB-3035[XMKFB-2918] 后-【自贡】-提供收藏功能接口

## v8.1

- XMKFB-2876 后-【省厅】-发送政务微信消息覆盖整个系统模块
- XMKFB-2837[XMKFB-2641] 增加常用分组接口

## v7.4

- XMKFB-2721 合-【省厅】-协作及相关审批流程修改

# v7.2

- XMKFB-2620[XMKFB-2579] 后 - 单位补充归属地域名称的返回

## RC20240710

- XMKFB-2505 合-【省厅】-迁移云哨后的一些功能调整

## v7.1

- XMKFB-2419[XMKFB-1961] 后-情指行消息改造
- XMKFB-2459 角色中有个“派出所角色”、给用户授予“情指民警”权限保存后，系统会自动将“派出所”角色授给用户

## v6.2 发版日志

- XMKFB-1864 部门管理员需求
- XMKFB-2031 人员列表检索增加只看活跃用户筛选
- XMKFB-2108[XMKFB-2106] 前 - 采集人选择异常
-

## v6.1 发版日志

- XMKFB-1929[XMKFB-1725] 后-登录首页模块接口提供
- XMKFB-1841 【自贡】水印中的IP未获取登录用户的IP

## v5.4 发版日志

- XMKFB-1797、组织接口需要是否有子节点的参数

## v5.3 发版日志

- XMKFB-1674、获取接口调用方的ip
- XMKFB-1687、【后】需要提供相关接口

## v5.1 发版日志

- XMKFB-1471、后-【达州】批量导入人员后，所属警种、责任派出所为同一单位

## v1.5 20240426 发版日志

- XMKFB-1167[XMKFB-983] 后-完成协作审批流程切换及审批服务改造

## v1.5 发版日志

# 2024-04-23

- XMKFB-1183、组织模板导入数据的校验问题

# 2024-04-22

- XMKFB-1115、组织导入优化
- XMKFB-1217、广安-后台导入人员优化

# 2024-04-18

- XMKFB-1140、组织导入模板的内容优化与调整
- XMKFB-1060、合-用户导入功能优化，组织导入功能开发
- XMKFB-1058 添加参与人列表中的人员卡片未显示对应人员存在的职务信息
- XMKFB-1063 用户导入优化

## v1.4 发版日志

- XMKFB-874 发起/编辑协作弹窗中协作人显示的数据未做限制

## v1.3 发版日志

- XMKFB-664[XMKFB-552] 后-完成用户权限相关接口对接

### nacos配置更新

```
```