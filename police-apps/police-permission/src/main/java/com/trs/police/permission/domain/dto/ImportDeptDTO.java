package com.trs.police.permission.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-04-10 15:02
 */
@Data
@EqualsAndHashCode
public class ImportDeptDTO {

    /**
     * 机构编号
     */
    @ExcelProperty("机构编号")
    private String code;

    /**
     * 机构名称
     */
    @ExcelProperty("机构名称")
    private String name;

    /**
     *  机构简称
     */
    @ExcelProperty("机构简称")
    private String shortName;

    /**
     *  上级单位
     */
    @ExcelProperty("上级单位")
    private String parentCode;

    @ExcelProperty("所属地区")
    private String districtCode;

    @ExcelProperty("组织类别")
    private String type;

    @ExcelProperty("警种")
    private String policeKind;
}
