package com.trs.police.permission.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.permission.domain.dto.DeptCommonSearchDTO;
import com.trs.police.permission.domain.entity.Dept;
import com.trs.police.permission.mapper.DeptMapper;
import com.trs.police.permission.service.DeptSearchService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 实现类
 */
@Service
public class DeptSearchServiceImpl implements DeptSearchService {

    @Autowired
    private DeptMapper deptMapper;

    @Override
    public RestfulResultsV2<DeptVO> deptCommonSearch(DeptCommonSearchDTO dto) {
        List<Dept> depts = deptMapper.selectList(Wrappers.lambdaQuery(Dept.class)
                .or(wp -> wp.like(Dept::getName, dto.getKeyword())
                        .like(Dept::getShortName, dto.getKeyword())
                ));
        List<DeptVO> vos = depts.stream().map(Dept::toDeptVO).collect(Collectors.toList());
        return RestfulResultsV2.ok(vos);
    }
}
