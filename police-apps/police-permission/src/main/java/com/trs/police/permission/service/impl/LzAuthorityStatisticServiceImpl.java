package com.trs.police.permission.service.impl;

import com.trs.police.permission.constant.LzAuthorityStatisticConstant;
import com.trs.police.permission.domain.dto.LzAuthorityStatisticDto;
import com.trs.police.permission.service.LzAuthorityStatistic.LzAuthStatisticService;
import com.trs.police.permission.service.LzAuthorityStatisticService;
import com.trs.police.statistic.domain.bean.CountItem;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 泸州权限中心大屏统计service
 */
@Service
public class LzAuthorityStatisticServiceImpl implements LzAuthorityStatisticService {

    @Autowired
    private List<LzAuthStatisticService> lzAuthStatisticServices;

    @Override
    public CountItem dataOverview(LzAuthorityStatisticDto dto)  throws Exception{
        LzAuthStatisticService lzAuthStatisticService = lzAuthStatisticServices.stream()
                .filter(e -> LzAuthorityStatisticConstant.DATA_OVERVIEW
                .equals(e.getServiceKey())).findFirst().orElse(null);
        if (lzAuthStatisticService == null) {
            return new CountItem();
        }
        List<CountItem> list = lzAuthStatisticService.doStatistic(dto);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new CountItem();
    }

    @Override
    public List<CountItem> platformAuthStatistic(LzAuthorityStatisticDto dto)  throws Exception{
        LzAuthStatisticService lzAuthStatisticService = lzAuthStatisticServices.stream()
                .filter(e -> LzAuthorityStatisticConstant.PLAT_FORM_AUTH_STATISTIC
                .equals(e.getServiceKey())).findFirst().orElse(null);
        if (lzAuthStatisticService == null) {
            return new ArrayList<>();
        }
        List<CountItem> list = lzAuthStatisticService.doStatistic(dto);
        return list;
    }

    @Override
    public CountItem applicationTrend(LzAuthorityStatisticDto dto)  throws Exception{
        LzAuthStatisticService lzAuthStatisticService = lzAuthStatisticServices.stream()
                .filter(e -> LzAuthorityStatisticConstant.APPLICATION_TREND
                        .equals(e.getServiceKey())).findFirst().orElse(null);
        if (lzAuthStatisticService == null) {
            return new CountItem();
        }
        List<CountItem> list = lzAuthStatisticService.doStatistic(dto);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new CountItem();
    }

    @Override
    public List<CountItem> platformAuthInfoStatistic(LzAuthorityStatisticDto dto)  throws Exception{
        LzAuthStatisticService lzAuthStatisticService = lzAuthStatisticServices.stream()
                .filter(e -> LzAuthorityStatisticConstant.PLAT_FORM_AUTH_INFO_STATISTIC
                        .equals(e.getServiceKey())).findFirst().orElse(null);
        if (lzAuthStatisticService == null) {
            return new ArrayList<>();
        }
        List<CountItem> list = lzAuthStatisticService.doStatistic(dto);
        return list;
    }

    @Override
    public CountItem detailAuthInfoStatistic(LzAuthorityStatisticDto dto)  throws Exception{
        LzAuthStatisticService lzAuthStatisticService = lzAuthStatisticServices.stream()
                .filter(e -> LzAuthorityStatisticConstant.DETAIL_AUTH_INFO_STATISTIC
                        .equals(e.getServiceKey())).findFirst().orElse(null);
        if (lzAuthStatisticService == null) {
            return new CountItem();
        }
        List<CountItem> list = lzAuthStatisticService.doStatistic(dto);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new CountItem();
    }

    @Override
    public CountItem userInfoStatistic(LzAuthorityStatisticDto dto) throws Exception {
        LzAuthStatisticService lzAuthStatisticService = lzAuthStatisticServices.stream()
                .filter(e -> LzAuthorityStatisticConstant.USER_INFO_STATISTIC
                        .equals(e.getServiceKey())).findFirst().orElse(null);
        if (lzAuthStatisticService == null) {
            return new CountItem();
        }
        List<CountItem> list = lzAuthStatisticService.doStatistic(dto);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new CountItem();
    }
}
