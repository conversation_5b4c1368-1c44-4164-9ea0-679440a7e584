package com.trs.police.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.params.DeptRequestParams;
import com.trs.police.permission.domain.entity.Dept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * t_dept 表查询接口
 *
 * <AUTHOR>
 */
@Mapper
public interface DeptMapper extends BaseMapper<Dept> {

    /**
     * 根据单位代码获取单位信息
     *
     * @param code 单位代码
     * @return 单位信息
     */
    @Select("select * from t_dept d where d.code = #{code} and d.deleted != 1")
    Dept findByCode(@NotEmpty @Param("code") String code);

    /**
     * 根据单位名获取单位信息
     *
     * @param name 单位名
     * @return 单位信息
     */
    @Select("select * from t_dept d where d.name = #{name} and d.deleted = 0 limit 1")
    Dept findByName(@NotEmpty @Param("name") String name);

    /**
     * 根据用户id查询用户的主要部门
     *
     * @param userId 用户id
     * @return 单位信息
     */
    @Select("select d.* from t_dept d join t_user_dept_relation tudr on d.id = tudr.dept_id "
        + "where tudr.user_id=#{userId} ")
    List<Dept> findAllByUserId(@NotEmpty @Param("userId") Long userId);

    /**
     * 根据pid查找
     *
     * @param pid  pid
     * @param page 分页信息
     * @return 单位分页信息
     */
    @Select("select * from t_dept d where d.pid = #{pid} and d.deleted != 1")
    Page<Dept> findByPid(@NotNull @Param("pid") Long pid, Page<Dept> page);

    /**
     * 根据父id查询，根据show_order排序
     *
     * @param id 父id
     * @return {@link Dept}
     */
    @Select("select * from t_dept d where d.pid = #{id} and d.deleted != 1 "
        + "order by (case WHEN show_order is null then 1000 WHEN show_order = 0 then 999 else show_order END) asc ")
    List<Dept> findAllByParentId(Long id);

    /**
     * 根据父id查询，根据show_order排序，并排除没有下属用户的部门
     *
     * @param id 父id
     * @return {@link Dept}
     */
    @Select("select * from t_dept d join t_user_dept_relation r on d.id = r.dept_id join t_user u on u.id = r.user_id "
            + "where d.pid = #{id} and d.deleted != 1 GROUP BY d.id "
            + "order by (case WHEN show_order is null then 1000 WHEN show_order = 0 then 999 else show_order END) asc ")
    List<Dept> findListByParentId(Long id);

    /**
     * 通过编号前缀获取部门id
     *
     * @param codePrefix 部门编号
     * @return java.util.List 部门id集合
     */
    @Select("select d.id from t_dept d where d.deleted != 1 and d.code like concat(#{codePrefix},'%')")
    List<Long> findIdByCodePrefix(@Param("codePrefix") String codePrefix);

    /**
     * 获取部门信息
     *
     * @param dept 部门信息
     * @return java.util.List
     */
    List<Dept> findByEntity(Dept dept);

    /**
     * 获取所有部门
     *
     * @return 部门
     */
    @Select("select * from t_dept where deleted != 1")
    List<Dept> getAll();

    /**
     * 通过pid和name查询部门
     *
     * @param pid  父id
     * @param name 名称
     * @return 部门
     */
    @Select("select * from t_dept where pid = #{pid} and name =#{name} and deleted != 1")
    Dept findByPidAndName(@Param("pid") Long pid, @Param("name") String name);

    /**
     * 通过pid和name查询部门简称
     *
     * @param pid       父id
     * @param shortName 部门简称
     * @return 部门
     */
    @Select("select * from t_dept where pid = #{pid} and short_name =#{shortName} and deleted != 1")
    Dept findByPidAndShortName(@Param("pid") Long pid, @Param("shortName") String shortName);

    /**
     * 根据区域查询
     *
     * @param district 区域代码
     * @param deptIds  部门id数组
     * @return 部门
     */
    List<Dept> selectByDistrict(
        @Param("districts") List<String> district, @Param("deptIds") List<Long> deptIds);

    /**
     * 根据警种查询
     *
     * @param policeKind 警种
     * @param deptIds    部门id数组
     * @return 部门
     */
    List<Dept> selectByPoliceKind(@Param("policeKinds") List<Long> policeKind,
        @Param("deptIds") List<Long> deptIds);

    /**
     * 根据code获取部门（不排除已删除部门）
     *
     * @param code 部门code
     * @return 部门
     */
    @Select("select * from t_dept d where d.code = #{code}")
    Dept getByCode(@NotEmpty @Param("code") String code);

    /**
     * 根据code获取部门
     *
     * @param code 部门code
     * @return 部门
     */
    @Select("select * from t_dept d where d.code = #{code} and deleted = 0")
    Dept getByCodeExcludeDelete(@NotEmpty @Param("code") String code);

    /**
     * 根据pid查询子部门
     *
     * @param pid 父部门id
     * @return 子部门
     */
    @Select("select * from t_dept where pid = #{pid} and deleted = 0 order by show_order")
    List<Dept> selectByPid(Long pid);

    /**
     * 根据部门code查询子部门
     *
     * @param pCode 父部门code
     * @return 子部门
     */
    @Select("select * from t_dept where pid = (select id from t_dept where code = #{pCode}) and deleted = 0 order by show_order")
    List<Dept> selectByPCode(String pCode);

    /**
     * 根据部门id查询所有子部门（包括本身）
     *
     * @param deptId 部门id
     * @return 结果
     */
    @Select("select * from t_dept where path like concat('%-', #{deptId}, '-%') or id = #{deptId}")
    List<Dept> selectChildrenByDept(@Param("deptId") Long deptId);

    /**
     * 通过参数查询部门
     *
     * @param params 查询参数
     * @return {@link DeptDto}
     */
    List<DeptDto> selectByParams(@Param("params")DeptRequestParams params);

    /**
     * 查询派出所
     *
     * @param pCode 上级部门编码
     * @param pid 上级部门id
     * @param areaCode 区域代码
     * @return {@link DeptDto}
     */
    List<DeptDto> selectPoliceStation(@Param("pid") Long pid, @Param("pCode") String pCode, @Param("areaCode") String areaCode);

    /**
     * 查询分局
     *
     * @return {@link DeptDto}
     */
    List<DeptDto> selectPoliceBureau();

    /**
     * 查询所有部门
     *
     * @return {@link DeptDto}
     */
    List<DeptDto> selectAllDept();

    /**
     * 通过全称name查询部门,只查匹配到的第一个
     *
     * @param name 名称
     * @return 部门
     */
    @Select("select * from t_dept where name =#{name} and deleted != 1 limit 1")
    Dept findOneByName(@Param("name") String name);

    /**
     *  部门排序
     *
     * @param deptId 部门id
     * @param showOrder 排序字段
     * @param pid pid
     * @return 执行成功条数
     */
    @Update("update t_dept set show_order=#{showOrder} where id=#{deptId} and pid=#{pid}")
    int orderDept(@Param("deptId") Long deptId, @Param("showOrder") Integer showOrder, @Param("pid") Long pid);

    /**
     * 更新组织签章
     *
     * @param code code
     * @param signet 组织签章
     */
    @Update("update t_dept "
            + "set signet=#{signet},"
            + "update_time = current_timestamp,"
            + "update_user_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id},"
            + "update_dept_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().dept.id} "
            + "where code=#{code}")
    void updateDeptSignet(@Param("code") String code, @Param("signet") String signet);
    /**
     * 更新组织签章
     *
     * @param deptId 部门id
     * @param path 部门path
     */
    @Update("update t_dept set `path` = #{path} where id = #{deptId}")
    void updateDeptPath(@Param("deptId") Long deptId, @Param("path") String path);

    /**
     * 根据区域编码查询部门
     *
     * @param prefixCode 区域编码
     * @return 部门
     */
    @Select("select * from t_dept where district_code like concat(#{pCode},'%') and deleted = 0 order by show_order")
    List<Dept> selectByPrefixCode(String prefixCode);

    /**
     * 根据区域编码和警种查询部门
     *
     * @param prefixCode 区域编码
     * @param policeKinds 警种
     * @return 部门
     */
    List<Dept> findByPoliceKinds(@Param("prefixCode") String prefixCode, @Param("policeKinds") List<Long> policeKinds);

    /**
     * 根据警种查询责任警种
     *
     * @param policeKind 警种
     * @param excludePoliceKinds 排除的警种
     * @return 部门
     */
    List<Dept> findControlPoliceByPoliceKind(@Param("policeKind") Long policeKind, @Param("excludePoliceKinds") List<Long> excludePoliceKinds);

    /**
     * 获取层级部门，仅包含省级、市级、区县级和派出所
     *
     * @return 部门
     */
    @Select("select * from t_dept where type in (0, 1, 2, 3)")
    List<Dept> getLevelDept();
}