package com.trs.police.permission.controller;

import com.trs.police.common.core.params.SearchParams;
import com.trs.police.permission.domain.vo.SystemConfigureVO;
import com.trs.police.permission.service.SystemConfigureService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/06/05
 * @description:
 */
@RestController
public class SystemConfigController {
    @Resource
    private SystemConfigureService systemConfigureService;

    /**
     * getSystemInfo
     *
     * @return {@link List}<{@link SystemConfigureVO}>
     */
    @GetMapping("/home-page")
    public List<SystemConfigureVO> getSystemInfo() {
        return systemConfigureService.getSystemInfo();
    }

    /**
     * getIndividualCenter
     *
     * @param postType postType
     * @return {@link List}<{@link SystemConfigureVO}>
     */
    @GetMapping("/home-page/getIndividualCenter")
    public List<SystemConfigureVO> getIndividualCenter(Integer postType) {
        return systemConfigureService.getIndividualCenter(postType);
    }

    /**
     * 收藏
     *
     * @param id 模块id
     * @param action 操作
     */
    @PostMapping("/home-page/favorite")
    public void handleFavoriteAction(Long id, Integer action) {
        systemConfigureService.handleFavoriteAction(id, action);
    }

    /**
     * 我的收藏
     *
     * @return {@link List}<{@link SystemConfigureVO}>
     */
    @GetMapping("/home-page/myFavorite")
    public List<SystemConfigureVO> handleFavoriteAction() {
        return systemConfigureService.myFavorite();
    }

    /**
     * 自贡新收藏 自贡环境使用
     *
     * @param ids 模块id
     */
    @PostMapping("/home-page/favoriteV2")
    public void handleFavoriteActionV2(String ids) {
        systemConfigureService.handleFavoriteActionV2(ids);
    }

    /**
     * 使用
     *
     * @param id 模块id
     */
    @PostMapping("/home-page/usage")
    public void usage(Long id) {
        systemConfigureService.usage(id);
    }

    /**
     * 最近使用
     *
     * @return {@link List}<{@link SystemConfigureVO}>
     */
    @GetMapping("/home-page/recentUsage")
    public List<SystemConfigureVO> recentUsage() {
        return systemConfigureService.recentUsage();
    }

    /**
     * 系统集
     *
     * @param searchParams searchParams
     * @return {@link List}<{@link SystemConfigureVO}>
     */
    @GetMapping("/home-page/systemCollections")
    public List<SystemConfigureVO> systemCollections(SearchParams searchParams) {
        return systemConfigureService.systemCollections(searchParams);
    }
}
