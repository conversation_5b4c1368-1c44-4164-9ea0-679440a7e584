package com.trs.police.permission.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.ExceptionMessageConstant;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.permission.domain.entity.SystemConfigureEntity;
import com.trs.police.permission.domain.entity.SystemConfigureFavoriteEntity;
import com.trs.police.permission.domain.entity.SystemConfigureUsageEntity;
import com.trs.police.permission.domain.vo.SystemConfigureVO;
import com.trs.police.permission.mapper.SystemConfigureFavoriteMapper;
import com.trs.police.permission.mapper.SystemConfigureMapper;
import com.trs.police.permission.mapper.SystemConfigureUsageMapper;
import com.trs.police.permission.service.SystemConfigureService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/12 13:50
 */
@Service
public class SystemConfigureServiceImpl implements SystemConfigureService {
    @Resource
    public SystemConfigureMapper systemConfigureMapper;

    @Resource
    private SystemConfigureFavoriteMapper systemConfigureFavoriteMapper;

    @Resource
    private SystemConfigureUsageMapper systemConfigureUsageMapper;

    @Resource
    private DictService dictService;

    List<SystemConfigureEntity> systemConfigureEntityList;


    @Override
    public List<SystemConfigureVO> getSystemInfo() {
        // 获取当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        // 查询当前用户已经收藏的模块
        QueryWrapper<SystemConfigureFavoriteEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("create_user_id", currentUser.getId());
        queryWrapper.eq("create_dept_id", currentUser.getDept().getId());
        List<SystemConfigureFavoriteEntity> systemConfigureFavoriteEntities = systemConfigureFavoriteMapper.selectList(queryWrapper);
        List<Long> moduleIds = systemConfigureFavoriteEntities.stream()
                .map(SystemConfigureFavoriteEntity::getModuleId)
                .collect(Collectors.toList());
        systemConfigureEntityList = systemConfigureMapper.selectList(new QueryWrapper<>());
        // 过滤出所有 module=0 的第三层数据
        List<SystemConfigureVO> thirdLevelModules = systemConfigureEntityList.stream()
                .filter(entity -> entity.getModule().equals(0))
                .map(entity -> recursion(entity, moduleIds))
                .collect(Collectors.toList());
        return systemConfigureEntityList
                .stream()
                .filter(entity -> entity.getModule().equals(1))
                .map(entity -> {
                    SystemConfigureVO vo = recursion(entity, moduleIds);
                    // 移除子节点中 module=0 的数据
                    if (vo.getChild() != null) {
                        vo.setChild(vo.getChild().stream()
                                .filter(child -> !child.getModule().equals(0))
                                .collect(Collectors.toList()));
                    }
                    // 动态生成第二层的 "系统集" 节点
                    // 只有当存在第三层数据时才添加系统集节点
                    if (!CollectionUtils.isEmpty(thirdLevelModules)) {
                        // 动态生成第二层的 "系统集" 节点
                        SystemConfigureVO systemSetNode = new SystemConfigureVO();
                        systemSetNode.setName("系统集");
                        systemSetNode.setChild(thirdLevelModules);
                        // 将 "系统集" 节点添加到第二层节点的子节点中
                        if (vo.getChild() != null) {
                            vo.getChild().add(systemSetNode);
                        } else {
                            vo.setChild(Collections.singletonList(systemSetNode));
                        }
                    }
                    return vo;
                })
                .sorted(Comparator.comparing(SystemConfigureVO::getShowNumber))
                .collect(Collectors.toList());
    }


    @Override
    public List<SystemConfigureVO> getIndividualCenter(Integer postType) {
        // 获取当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        // 查询的模块
        QueryWrapper<SystemConfigureFavoriteEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("create_user_id", currentUser.getId());
        queryWrapper.eq("create_dept_id", currentUser.getDept().getId());
        List<SystemConfigureFavoriteEntity> systemConfigureFavoriteEntities = systemConfigureFavoriteMapper.selectList(queryWrapper);
        List<Long> moduleIds = systemConfigureFavoriteEntities.stream()
                .map(SystemConfigureFavoriteEntity::getModuleId)
                .collect(Collectors.toList());
        String ids = BeanFactoryHolder.getEnv().getProperty("system.configure.judge.tool.id", "");
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        if (postType.equals(1)) {
            //查询研判工具相关功能
            systemConfigureEntityList = systemConfigureMapper.selectList(new QueryWrapper<SystemConfigureEntity>().in("id", idList));
        } else if (postType.equals(2)){
            //查询系统功能(除研判工具和系统集里面的功能)
            systemConfigureEntityList = systemConfigureMapper.selectList(new QueryWrapper<SystemConfigureEntity>()
                    .notIn("id", idList)
                    .ne("module", 0));
        }else {
            //查询系统集功能
            systemConfigureEntityList = systemConfigureMapper.selectList(new QueryWrapper<SystemConfigureEntity>()
                    .eq("module", 0));
        }

        return systemConfigureEntityList
                .stream()
                .filter(entity -> moduleIds.contains(entity.getId()))
                .map(entity -> recursion(entity, moduleIds))
                .sorted(Comparator.comparing(SystemConfigureVO::getShowNumber))
                .collect(Collectors.toList());
    }

    @Override
    public void handleFavoriteActionV2(String ids) {
        // 获取当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        //先清空收藏表里面的数据
        systemConfigureFavoriteMapper.delete(new QueryWrapper<>());
        if (StringUtils.isNotEmpty(ids)){
            List<Long> idsList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<SystemConfigureEntity> systemConfigureEntityList = systemConfigureMapper.selectBatchIds(idsList);
            PreConditionCheck.checkNotEmpty(systemConfigureEntityList, String.format("id=%s的模块不存在", ids));
            idsList.forEach(id -> {
                SystemConfigureFavoriteEntity entity = new SystemConfigureFavoriteEntity();
                entity.setModuleId(id);
                entity.fillAuditFields(currentUser);
                systemConfigureFavoriteMapper.insert(entity);
            });
        }
    }

    @Override
    public void handleFavoriteAction(Long id, Integer action) {
        SystemConfigureEntity systemConfigureEntityList = systemConfigureMapper.selectById(id);
        PreConditionCheck.checkNotNull(systemConfigureEntityList, String.format("id=%s的模块不存在", id));
        // 获取当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        // 收藏
        if (action == 1) {
            SystemConfigureFavoriteEntity entity = new SystemConfigureFavoriteEntity();
            entity.setModuleId(id);
            entity.fillAuditFields(currentUser);
            systemConfigureFavoriteMapper.insert(entity);
        } else {
            // 取消收藏
            QueryWrapper<SystemConfigureFavoriteEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("module_id", id);
            queryWrapper.eq("create_user_id", currentUser.getId());
            queryWrapper.eq("create_dept_id", currentUser.getDept().getId());
            systemConfigureFavoriteMapper.delete(queryWrapper);
        }
    }

    @Override
    public List<SystemConfigureVO> myFavorite() {
        // 获取当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        QueryWrapper<SystemConfigureFavoriteEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("create_user_id", currentUser.getId());
        queryWrapper.eq("create_dept_id", currentUser.getDept().getId());
        List<SystemConfigureFavoriteEntity> systemConfigureFavoriteEntities = systemConfigureFavoriteMapper.selectList(queryWrapper);
        List<SystemConfigureEntity> systemConfigureEntities = systemConfigureMapper.selectList(new QueryWrapper<>());
        Map<Long, SystemConfigureEntity> map = systemConfigureEntities.stream().collect(Collectors.toMap(SystemConfigureEntity::getId, Function.identity()));
        List<SystemConfigureVO> systemConfigureVOList = new ArrayList<>();
        systemConfigureFavoriteEntities.forEach(entity -> {
            SystemConfigureEntity systemConfigureEntity = map.get(entity.getModuleId());
            if (Objects.nonNull(systemConfigureEntity)) {
                SystemConfigureVO vo = SystemConfigureVO.of(systemConfigureEntity);
                systemConfigureVOList.add(vo);
            }
        });
        SystemConfigureVO myFavorite = new SystemConfigureVO();
        myFavorite.setName("我的收藏");
        myFavorite.setChild(systemConfigureVOList);
        return Arrays.asList(myFavorite);
    }

    @Override
    public void usage(Long id) {
        PreConditionCheck.checkNotNull(id, "id不能为空");
        // 获取当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        SystemConfigureUsageEntity entity = new SystemConfigureUsageEntity();
        entity.setModuleId(id);
        entity.fillAuditFields(currentUser);
        systemConfigureUsageMapper.insert(entity);
    }

    @Override
    public List<SystemConfigureVO> recentUsage() {
        // 获取当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        SystemConfigureVO recentUsage = new SystemConfigureVO();
        recentUsage.setName("最近使用");
        List<SystemConfigureVO> vos = systemConfigureUsageMapper.recentUsage(currentUser);
        if (CollectionUtils.isEmpty(vos)) {
            return Arrays.asList(recentUsage);
        }
        List<SystemConfigureEntity> systemConfigureEntities = systemConfigureMapper.selectList(new QueryWrapper<SystemConfigureEntity>()
                .in("id", vos.stream().map(SystemConfigureEntity::getId).collect(Collectors.toList())));
        Map<Long, SystemConfigureEntity> map = systemConfigureEntities.stream().collect(Collectors.toMap(SystemConfigureEntity::getId, Function.identity()));
        List<SystemConfigureVO> results = new ArrayList<>();
        vos.forEach(vo -> {
            SystemConfigureEntity entity = map.get(vo.getId());
            if (Objects.nonNull(entity)) {
                SystemConfigureVO of = SystemConfigureVO.of(entity);
                results.add(of);
            }
        });
        recentUsage.setChild(results);
        return Arrays.asList(recentUsage);
    }

    @Override
    public List<SystemConfigureVO> systemCollections(SearchParams searchParams) {
        // 获取当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        // 查询当前用户已经收藏的模块
        QueryWrapper<SystemConfigureFavoriteEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("create_user_id", currentUser.getId());
        queryWrapper.eq("create_dept_id", currentUser.getDept().getId());
        List<SystemConfigureFavoriteEntity> systemConfigureFavoriteEntities = systemConfigureFavoriteMapper.selectList(queryWrapper);
        List<Long> moduleIds = systemConfigureFavoriteEntities.stream()
                .map(SystemConfigureFavoriteEntity::getModuleId)
                .collect(Collectors.toList());
        // 查询警种码表
        List<Dict2VO> dict2Vos = dictService.commonSearch("police_kind", null, null, null);
        Map<Long, String> policeKindMap = dict2Vos.stream().collect(Collectors.toMap(Dict2VO::getCode, Dict2VO::getName, (v1, v2) -> v1));
        String ids = BeanFactoryHolder.getEnv().getProperty("com.trs.home-page.systemCollection.ids", "[]");
        List<Integer> idList = JSON.parseArray(ids, Integer.class);
        // module=0为第三方应用模块
        List<SystemConfigureEntity> systemConfigureEntities = systemConfigureMapper.selectList(new QueryWrapper<SystemConfigureEntity>()
                .and(e -> e.eq("module", 0).or().in(!CollectionUtils.isEmpty(idList), "id", idList))
                .like(StringUtils.isNotEmpty(searchParams.getSearchValue()), "name", searchParams.getSearchValue()));
        List<SystemConfigureVO> results = systemConfigureEntities.stream().map(entity -> {
            SystemConfigureVO vo = SystemConfigureVO.of(entity);
            if (moduleIds.contains(entity.getId())) {
                vo.setIsFavorite(true);
            }
            if (Objects.nonNull(entity.getPoliceKind())) {
                vo.setPoliceKindName(policeKindMap.get(entity.getPoliceKind().longValue()));
            }
            return vo;
        }).collect(Collectors.toList());
        return results;
    }

    // 处理entity (添加上子节点)
    private SystemConfigureVO recursion(SystemConfigureEntity entity, List<Long> ids) {
        SystemConfigureVO vo = SystemConfigureVO.of(entity);
        // 标注是否是用户收藏
        if (ids.contains(vo.getId())) {
            vo.setIsFavorite(true);
        }
        // 获取子节点
        final List<SystemConfigureEntity> child = systemConfigureEntityList
                .stream()
                .filter(item -> entity.getId().equals(item.getPid()))
                .collect(Collectors.toList());
        // 递归未子节点的子节点赋值
        if (!child.isEmpty()) {
            final List<SystemConfigureVO> collect = child
                    .stream()
                    .map(item -> {
                        SystemConfigureVO childVO = SystemConfigureVO.of(item);
                        return recursion(childVO, ids);
                    }).sorted(Comparator.comparing(SystemConfigureVO::getShowNumber))
                    .collect(Collectors.toList());
            vo.setChild(collect);
        }
        return vo;
    }
}
