package com.trs.police.permission.constant;

/**
 * 权限中心统计接口常量
 */
public class LzAuthorityStatisticConstant {

    public static final String DATA_OVERVIEW = "dataOverview";

    public static final String PLAT_FORM_AUTH_STATISTIC = "platformAuthStatistic";

    public static final String APPLICATION_TREND = "applicationTrend";

    public static final String USER_INFO_STATISTIC = "userInfoStatistic";

    public static final String PLAT_FORM_AUTH_INFO_STATISTIC = "platformAuthInfoStatistic";

    public static final String DETAIL_AUTH_INFO_STATISTIC = "detailAuthInfoStatistic";

    public static final String PLATFORM_ALL = "平台总数";

    public static final String SQJZRS = "授权警种人数";

    public static final String SQQXRS = "授权区县人数";

    public static final String JRSQS = "今日申请数";

    public static final String LSSQS = "历史申请总数";

    public static final String USER_ALL_COUNT = "用户总数";

    public static final String WSQ_COUNT = "未授权人数";

    public static final String YSQ_COUNT = "已授权人数";

    public static final String MJ = "民警";

    public static final String YSQ = "已授权民警";

    public static final String SYBZ = "事业编制人员";

    public static final String YSQ_SYBZ = "已授权事业编制人员";

    public static final Integer PLATFORM_TYPE_OR_SOURCE_LEVEL = 1;

    public static final Integer PLATFORM_LEVEL = 2;
}
