package com.trs.police.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.dto.SearchUserDTO;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.CodeNameTypeVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.core.vo.permission.UserVO;
import com.trs.police.permission.domain.entity.User;
import com.trs.police.permission.domain.vo.UserListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * t_user 表查询接口
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 通过用户名查找用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(@Param("username") String username);

    /**
     * 通过用户名查找用户信息
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    User findByMobile(@Param("mobile") String mobile);

    /**
     * 通过指定的字段名+用户名查找用户信息
     *
     * @param fieldName 查找的字段
     * @param usernames 用户名
     * @return 用户信息
     */
    List<User> findByFieldNameUsernames(
        @Param("fieldName") String fieldName,
        @Param("usernames") List<String> usernames
    );

    /**
     * 根据用户id查询用户信息
     *
     * @param userId 用户id
     * @return {@link User}
     */
    User findById(@Param("userId") Long userId);

    /**
     * 根据用户id查询用户信息
     *
     * @param userIds 用户id
     * @return {@link User}
     */
    List<User> findByBatchId(@Param("userIds") List<Long> userIds);

    /**
     * 根据用户id修改状态
     *
     * @param userId 用户id
     * @param status 用户状态 0: 停用 1: 启用
     */
    @Update("update t_user "
            + "set status=#{status},"
            + "update_time = current_timestamp,"
            + "update_user_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id},"
            + "update_dept_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().dept.id} "
            + "where id=#{userId}")
    void updateUserStatus(@Param("userId") Long userId, @Param("status") int status);

    /**
     * 根据身份证号计数
     *
     * @param idNumber 身份证号
     * @return 数量
     */
    @Select("select count(1) FROM t_user where idcard=#{idNumber,jdbcType=VARCHAR}")
    Long countByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 查询用户id
     *
     * @param searchParams 查询参数
     * @return java.util.List 用户id
     */
    List<Long> getIdBySearchParams(SearchParams searchParams);

    /**
     * 通过部门id查询用户id
     *
     * @param deptIds 部门id
     * @return java.util.List 用户id
     */
    List<Long> getIdByDeptIds(@Param("deptIds") List<Long> deptIds);

    /**
     * 获取用户的角色，数据权限，部门，职务
     *
     * @param id 用户id
     * @return java.util.List 角色，数据权限，部门，职务等名称
     */
    List<KeyValueVO> getUserRelationInfo(Long id);

    /**
     * 获取用户的角色id，部门编码
     *
     * @param id 用户id
     * @return java.util.List 角色，部门
     */
    List<KeyValueVO> getUserRelationInfoV2(Long id);

    /**
     * 根据部门和角色查询用户
     *
     * @param deptId 部门id
     * @param roleId 角色id
     * @return 用户
     */
    @Select("select t.* from "
            + "    (select u.* from t_user u left join t_user_dept_relation udr on u.id = udr.user_id where udr.dept_id = #{deptId}) t "
            + "    left join t_user_role_relation urr "
            + "    on t.id = urr.user_id and urr.dept_id = #{deptId} "
            + "where urr.role_id = #{roleId} group by id")
    List<User> getUsersByDeptAndRole(@Param("deptId") Long deptId, @Param("roleId") Long roleId);

    /**
     * 获取部门下的用户
     *
     * @param areaCode 地域码
     * @param roleId 角色
     * @param deptType 部门类型
     * @return 部门下的用户
     */
    List<UserDeptVO> getUsersByCodeAndRoleIdAndType(@Param("areaCode") String areaCode, @Param("roleId") Long roleId, @Param("deptType") Integer deptType);


    /**
     * 根据部门id获取该部门及其下级部门用户
     *
     * @param roleId         角色Id
     * @param deptCode       部门code
     * @param isContainChild 是否包含子组织
     * @param filterParams   数据权限
     * @return java.util.List 用户信息
     */
    List<SimpleUserVO> getUsersNeRole(
            @Param("roleId") Long roleId,
            @Param("deptCode") String deptCode,
            @Param("isContainChild") boolean isContainChild,
            @Param("filterParams") List<KeyValueTypeVO> filterParams);

    /**
     * 根据部门id获取该部门及其下级部门没有数据权限的用户
     *
     * @param deptCode       部门code
     * @param isContainChild 是否包含子组织
     * @param dataId         数据权限id
     * @param filterParams   数据权限
     * @return java.util.List 用户信息
     */
    List<SimpleUserVO> getUserHasNoDataPermission(
            @Param("deptCode") String deptCode,
            @Param("isContainChild") boolean isContainChild,
            @Param("dataId") Long dataId,
            @Param("filterParams") List<KeyValueTypeVO> filterParams);

    /**
     * 更新电话号码
     *
     * @param id     用户id
     * @param mobile 电话号码
     * @return {@link Integer}
     */
    @Select("update t_user "
            + "set mobile = #{mobile},"
            + "update_time = current_timestamp,"
            + "update_user_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id},"
            + "update_dept_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().dept.id} "
            + "where id = #{id}")
    Integer updateMobile(@Param("id") Long id, @Param("mobile") String mobile);


    /**
     * 根据身份证号计数
     *
     * @param idNumber 身份证号
     * @return 数量
     */
    @Select("select id as id, create_time as createTime, create_user_id as createUserId, create_dept_id as createDeptId, update_time as updateTime, update_user_id as updateUserId, update_dept_id as updateDeptId, username as username, real_name as realName, idcard as idNumber, birthday as birthday, gender as gender, telephone as telephone, mobile as mobile, email as email, police_code as policeCode, valid_date as validDate, status as status, signature as signature, avatar as avatar, password as password, duty as duty, zjhm as zjhm, mac as mac  FROM t_user where idcard=#{idNumber,jdbcType=VARCHAR}")
    User getUserByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 获取用户信息
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return SimpleUserVO
     */
    SimpleUserVO findSimpleUser(@Param("userId") Long userId, @Param("deptId") Long deptId);


    /**
     * 根据部门和角色获取用户信息
     *
     * @param roleId 角色id
     * @param deptId 部门id
     * @return 用户信息
     */
    List<SimpleUserVO> getByRoleAndDept(@Param("roleId") Long roleId, @Param("deptId") Long deptId);

    /**
     * 后台-用户列表
     *
     * @param paramsRequest 请求参数
     * @param page          分页参数
     * @return 用户信息
     */
    PageResult<UserListVO> getPage(@Param("params") ListParamsRequest paramsRequest, Page<UserListVO> page);

    /**
     * 获取当前用户权限下能看的所有人
     *
     * @param filterParams 数据权限
     * @return 用户信息
     */
    List<SimpleUserVO> getCurrentUserDataPermissionUsers(@Param("filterParams") List<KeyValueTypeVO> filterParams);

    /**
     * 根据部门code获取用户信息
     *
     * @param deptCode 部门code
     * @return 用户信息
     */
    List<SimpleUserVO> findByDeptCode(@Param("deptCode") String deptCode);

    /**
     * 分页获取用户
     *
     * @param buildParams 权限参数
     * @param page 分页参数
     * @param request 参数
     *
     * @return 用户
     */
    Page<SimpleUserVO> getCurrentUserDataPermissionUsersApp(@Param("filterParams") List<KeyValueTypeVO> buildParams,Page<SimpleUserVO> page,@Param("request") ListParamsRequest request);

    /**
     * 根据用户id获取app用户模块名称
     *
     * @param userIds 用户ids
     * @param deptIds 部门ids
     * @return 权限模块名
     */
    List<CodeNameTypeVO> selectAppApplicationByUserIds(@Param("userIds") List<Long> userIds, @Param("deptIds") List<Long> deptIds);

    /**
     * 根据用户id获取app用户模块名称
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return 权限模块名
     */
    CodeNameTypeVO selectAppApplicationByUserId(@Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 根据部门和角色获取用户信息
     *
     * @param roleIds 角色id
     * @param deptId 部门id
     * @return 用户信息
     */
    List<SimpleUserVO> getByRolesAndDept(@Param("roleIds") List<Long> roleIds, @Param("deptId") Long deptId);

    /**
     * 根据部门code获取用户信息
     *
     * @param searchUserDTO dto参数
     * @return 用户信息
     */
    List<SimpleUserVO> getUserListByDeptIdList(@Param("searchUserDTO") SearchUserDTO searchUserDTO);

    /**
     * 更新个人签章
     *
     * @param userName userName
     * @param signet 签章图片
     */
    @Update("update t_user "
            + "set signet=#{signet},"
            + "update_time = current_timestamp,"
            + "update_user_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id},"
            + "update_dept_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().dept.id} "
            + "where username=#{userName}")
    void updateUserSignet(@Param("userName") String userName, @Param("signet") String signet);

    /**
     * 根据部门id结合获取获取用户列表
     *
     * @param deptIds 部门id
     * @return 用户列表
     */
    List<UserVO> selectByDeptIds(@Param("deptIds") List<Long> deptIds);

    /**
     * 根据警号集合获取获取用户列表
     *
     * @param jhs 部门id
     * @return 用户列表
     */
    List<UserVO> selectByJhs(@Param("jhs") List<String> jhs);

    /**
     * 查询所有用户
     *
     * @param username 用户名
     * @param deptId  部门id
     * @param postCode  岗位代码
     * @param districtCode  地区代码
     * @return {@link List }<{@link User }>
     * <AUTHOR>
     * @since 1.0.0
     * @since 2024-11-21 14:19:36
     */
    List<User> findUserByUsername(@Param("username") String username,
                                  @Param("deptId") Long deptId,
                                  @Param("postCode") Integer postCode,
                                  @Param("districtCode") String districtCode);
}
