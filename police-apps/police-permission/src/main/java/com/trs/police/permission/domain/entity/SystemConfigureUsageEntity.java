package com.trs.police.permission.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;

/**
 * @author: dingkeyu
 * @date: 2024/08/08
 * @description: 系统配置使用实体类
 */
@Data
@TableName(value = "t_system_configure_usage", autoResultMap = true)
public class SystemConfigureUsageEntity extends AbstractBaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模块id
     */
    @TableField(value = "module_id")
    private Long moduleId;
}
