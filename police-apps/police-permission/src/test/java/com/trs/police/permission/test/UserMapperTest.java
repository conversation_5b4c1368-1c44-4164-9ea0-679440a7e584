package com.trs.police.permission.test;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.police.permission.domain.entity.User;
import com.trs.police.permission.mapper.UserMapper;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;

import javax.annotation.Resource;

import static org.assertj.core.api.Assertions.assertThat;

@MybatisPlusTest
@MapperScan(basePackages = "com.trs.police.permission.mapper")
class UserMapperTest {

    @Resource
    UserMapper userMapper;

    @Test
    void findById() {
        User user = userMapper.findById(1L);
        assertThat(user.getRealName()).isEqualTo("张放");
        assertThat(user.getDeptList()).isNotEmpty();
        assertThat(user.getDeptList().get(0).getName()).isEqualTo("四川省泸州市叙永县公安局观兴派出所");
    }

}