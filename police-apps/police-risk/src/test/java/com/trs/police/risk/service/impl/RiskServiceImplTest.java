package com.trs.police.risk.service.impl;

import com.trs.police.common.notice.starter.enums.SystemNoticeEnum;
import com.trs.police.risk.constant.enums.RiskRoleEnum;
import com.trs.police.risk.domain.entity.Risk;
import com.trs.police.risk.service.RiskService;
import feign.FeignException;
import feign.RetryableException;
import io.vavr.control.Try;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static io.vavr.API.Try;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class RiskServiceImplTest {

    @Autowired
    RiskService riskService;

    @Test
    void sendRiskMessage() {

        Risk risk = new Risk();
        risk.setContent("测试消息风险");
        //汇报异常在权限微服务哪里，如果是这样代码测试通过
        assertThrows(RetryableException.class, ()->riskService.sendRiskMessage(risk, SystemNoticeEnum.RISK_AUTO_DISPATCH_MESSAGE, RiskRoleEnum.RESPONSIBLE_PERSON,1L));

    }
}