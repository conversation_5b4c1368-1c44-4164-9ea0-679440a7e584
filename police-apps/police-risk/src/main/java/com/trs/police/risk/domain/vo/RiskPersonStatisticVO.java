package com.trs.police.risk.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskPersonStatisticVO {

    /**
     * 统计时间
     */
    private String statisticTime;

    /**
     * 风险预警次数
     */
    private Long riskWarnCount;

    /**
     * 民转刑人员数统计
     */
    private Long personCount;

    /**
     * 未处理人员数统计
     */
    private Long unHandlePersonCount;
}
