package com.trs.police.risk.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;

/**
 * 风险人员-关注表
 *
 * <AUTHOR>
 * @date 2024/9/24
 */
@Data
@TableName(value = "t_risk_person_focus",autoResultMap = true)
public class RiskPersonFocus extends AbstractBaseEntity {

    /**
     * 风险人员id
     */
    private Long riskPersonId;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 评论
     */
    private String comment;
}
