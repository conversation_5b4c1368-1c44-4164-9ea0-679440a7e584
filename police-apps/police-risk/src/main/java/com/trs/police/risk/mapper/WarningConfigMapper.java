package com.trs.police.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.risk.domain.entity.WarningConfig;
import com.trs.police.risk.domain.vo.RiskListVO;
import com.trs.police.risk.domain.vo.WarningConfigListVO;
import com.trs.police.risk.domain.vo.WarningConfigVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2023/5/29 16:51
 */
@Mapper
public interface WarningConfigMapper extends BaseMapper<WarningConfig> {

    /**
     * 获取启用的预警配置
     *
     * @return 配置
     */
    @ResultMap("mybatis-plus_WarningConfig")
    @Select("select * from t_risk_warning_config where enabled_status = 1 and deleted = 0 order by priority desc,risk_level asc ")
    List<WarningConfig> getEnabledConfig();


    /**
     * 根据id获取预警配置详情
     *
     * @param id 预警配置id
     * @return 预警配置
     */
    WarningConfigVO getById(@Param("id") Long id);

    /**
     * 列表查询
     *
     * @param listParamsRequest 筛选条件
     * @param page              分页
     * @return 列表
     */
    Page<WarningConfigListVO> getList(@Param("params") ListParamsRequest listParamsRequest,
        Page<WarningConfigListVO> page);


    /**
     * 启用
     *
     * @param configIds 配置id
     */
    void enable(@Param("configIds") List<Long> configIds);

    /**
     * 禁用
     *
     * @param configIds 配置id
     */
    void disable(@Param("configIds") List<Long> configIds);


    /**
     * 删除
     *
     * @param configIds 配置id
     */
    void delete(@Param("configIds") List<Long> configIds);

    /**
     * 风险列表
     *
     * @param id                配置id
     * @param listParamsRequest 筛选条件
     * @return 风险列表
     */
    List<RiskListVO> getRiskList(@Param("id") Long id, @Param("params") ListParamsRequest listParamsRequest);
}
