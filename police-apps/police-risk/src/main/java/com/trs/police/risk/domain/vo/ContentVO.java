package com.trs.police.risk.domain.vo;

import com.trs.police.common.core.vo.OperateVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  回复VO
 *
 * <AUTHOR>
 * @date 2023/3/21 11:06
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ContentVO extends OperateVO implements Serializable {

    private static final long serialVersionUID = 328583721196894698L;
    private String content;

    private List<SimpleDeptVO> atDept;

    private List<FileInfoVO> attachments;


}
