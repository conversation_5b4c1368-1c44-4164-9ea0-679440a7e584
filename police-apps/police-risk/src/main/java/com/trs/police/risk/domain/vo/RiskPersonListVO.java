package com.trs.police.risk.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.risk.typeHander.JsonToRiskLabelHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskPersonListVO {

    /**
     * 风险人员id
     */
    private Long id;

    /**
     * 姓名
     */
    private String name;
    /**
     * 风险等级
     */
    private Long riskLevel;

    /**
     * 风险等级名称
     */
    private String riskLevelName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 户籍地
     */
    private String registeredResidenceDetail;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 民族
     */
    private String nation;

    /**
     * 责任单位
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private SimpleDeptVO responsibleDept;

    /**
     * 风险分数
     */
    private Double score;

    /**
     * 风险标签
     */
    @TableField(typeHandler = JsonToRiskLabelHandler.class)
    private List<RiskLabelVO> riskLabels;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 风险预警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date warningTime;

    /**
     * 是否关注
     */
    private Integer isFocus;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 人员档案id，判断是不是重点人员
     */
    private Long zdPersonId;
}
