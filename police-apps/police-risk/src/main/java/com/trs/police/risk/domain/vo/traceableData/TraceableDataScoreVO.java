package com.trs.police.risk.domain.vo.traceableData;

import com.trs.police.risk.constant.enums.TraceableDataTypeEnum;
import com.trs.police.risk.domain.vo.push.JqMessageVO.Tag;
import com.trs.police.risk.domain.vo.push.ScoreVO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/9/12 14:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TraceableDataScoreVO {

    /**
     * 编号
     */
    private String code;
    /**
     * 得分
     */
    private Double score;
    /**
     * 得分详情
     */
    private ScoreVO scoreDetail;
    /**
     * 得分关键词
     */
    private List<KeyWordVO> scoreKeyWords;
    /**
     * 标签
     */
    private List<Tag> tags;
    /**
     * 类型
     */
    private TraceableDataTypeEnum sourceType;

    /**
     * 关联方式
     */
    private String relationType;
}
