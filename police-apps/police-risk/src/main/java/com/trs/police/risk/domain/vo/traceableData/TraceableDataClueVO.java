package com.trs.police.risk.domain.vo.traceableData;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/3 17:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TraceableDataClueVO extends TraceableDataBaseVO{

    private static final long serialVersionUID = 1567833566484747060L;

    /**
     * 上报单位
     */
    private String dept;
}
