package com.trs.police.risk.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/21 10:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_risk_copy")
public class RiskCopy extends AbstractBaseEntity {


    private static final long serialVersionUID = -5543895167305122409L;

    private Long deptId;

    private Long riskId;
}
