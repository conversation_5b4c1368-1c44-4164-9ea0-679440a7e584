package com.trs.police.risk.stragegy.createLogImpl;

import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.risk.domain.entity.OperationLogEntity;
import com.trs.police.risk.domain.vo.RiskVO;
import com.trs.police.risk.domain.vo.log.RiskLogVO;
import com.trs.police.risk.stragegy.OperationLogStrategyFactory;
import com.trs.police.risk.stragegy.OperateLogStrategy;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/25
 */
@Service
@Slf4j
public class CreateRiskOperateLogStrategy implements OperateLogStrategy, InitializingBean {

    @Override
    public <T>void createLog(T newObj, OperationLogEntity operationLogEntity, String desc) {
        RiskVO risk = JsonUtil.parseSpecificObject(newObj, RiskVO.class);
        RiskLogVO logVO = RiskLogVO.of(risk);
        operationLogEntity.setDetail(JsonUtil.toJsonString(logVO));
    }

    @Override
    public void afterPropertiesSet() {
        OperationLogStrategyFactory.register(Operation.CREATE_RISK, this);
    }
}
