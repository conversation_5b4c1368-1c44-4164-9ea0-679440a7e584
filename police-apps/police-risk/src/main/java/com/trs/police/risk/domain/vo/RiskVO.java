package com.trs.police.risk.domain.vo;

import com.trs.police.common.core.vo.OperateVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.risk.constant.enums.RiskLevelEnum;
import com.trs.police.risk.constant.enums.RiskStatusEnum;
import com.trs.police.risk.constant.enums.TimeLimitTypeEnum;
import com.trs.police.risk.domain.entity.Risk;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskVO implements Serializable {

    private static final long serialVersionUID = -5616911828519249941L;
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 风险级别
     */
    private RiskLevelEnum riskLevel;
    /**
     * 风险类型
     */
    private Long riskType;
    /**
     * 概述
     */
    private String content;
    /**
     * 责任单位
     */
    private SimpleDeptVO responsibleDept;
    /**
     * 处置单位
     */
    private SimpleDeptVO[] handleDept;
    /**
     * 风险状态
     */
    private RiskStatusEnum riskStatus;
    /**
     * 化解状态
     */
    private Boolean resolveStatus;
    /**
     * 风险编号
     */
    private String riskCode;
    /**
     * 签收
     */
    private OperateVO sign;
    /**
     * 研判
     */
    private JudgeVO judge;
    /**
     * 处置时限类型
     */
    private TimeLimitTypeEnum timeLimitType;
    /**
     * 处置时限
     */
    private LocalDateTime timeLimit;
    /**
     * 附件
     */
    private FileInfoVO[] attachments;
    /**
     * 取消处置
     */
    private CancelVO cancel;
    /**
     * 相关风险
     */
    private List<Long> relatedRisk;

    private List<String> relatedJq;

    private List<String> relatedClue;


    /**
     * 转实体类
     *
     * @return Risk
     */
    public Risk toEntity() {
        Risk risk = new Risk();
        risk.setTitle(title);
        risk.setRiskLevel(riskLevel);
        risk.setRiskType(riskType);
        risk.setContent(content);
        risk.setResponsibleDept(responsibleDept);
        return risk;
    }
}
