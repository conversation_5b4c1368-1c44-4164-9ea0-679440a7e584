package com.trs.police.risk.domain.vo;

import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.OperateVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.risk.constant.RiskConstant;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/3/21 10:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RejectVO extends OperateVO implements Serializable {

    private static final long serialVersionUID = -7655118063254752774L;

    private String reason;

    /**
     * 转contentVO
     *
     * @param atDept @单位
     * @return contentVO
     */
    public ContentVO toContentVO(SimpleDeptVO atDept) {
        ContentVO contentVO = new ContentVO();
        contentVO.setUser(new SimpleUserVO(AuthHelper.getNotNullUser()));
        contentVO.setOperateTime(LocalDateTime.now());
        contentVO.setAtDept(List.of(atDept));
        contentVO.setAttachments(List.of());
        contentVO.setContent(reason + RiskConstant.CONTENT_TEMPLATE
            .replace("${shortName}", atDept.getShortName())
            .replace("${deptName}", atDept.getDeptName())
            .replace("${deptId}", atDept.getDeptId().toString())
            .replace("${deptCode}", atDept.getDeptCode()));
        return contentVO;
    }
}
