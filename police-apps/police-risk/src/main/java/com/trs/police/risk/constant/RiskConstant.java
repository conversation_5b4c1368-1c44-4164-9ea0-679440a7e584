package com.trs.police.risk.constant;

import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.risk.constant.enums.RiskStatusEnum;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/22 20:02
 */
public class RiskConstant {

    private static final Logger LOGGER = LoggerFactory.getLogger(RiskConstant.class);

    private RiskConstant() {
    }

    /**
     * 风险信息类型——警情
     */
    public static final String RELATION_TYPE_JQ = "2";

    /**
     * 风险信息类型-老警情
     */
    public static final String RELATION_TYPE_OLD_JQ = "4";

    /**
     * 风险信息12345
     */
    public static final String RISK_12345 = "12345";

    /**
     * 风险信息线索
     */
    public static final String RISK_XS = "xs";

    /**
     * 风险信息类型——线索
     */
    public static final Integer RELATION_TYPE_CLUE = 3;

    /**
     * 签收时限
     */
    public static final Integer SIGN_OVERDUE_LENGTH = 8;
    /**
     * 研判时限
     */
    public static final Integer JUDGE_OVERDUE_LENGTH = 24;

    /**
     * 自动分派
     */
    public static final Integer DISPATCH_AUTO = 2;

    /**
     * 手动分派
     */
    public static final Integer DISPATCH_MANUAL = 1;
    /**
     * 主责人台账列表不能看的风险状态
     */
    public static final KeyValueTypeVO RESPONSIBLE_PERSON_CANT_SEE_STATUS = new KeyValueTypeVO(
            "responsiblePersonCantSeeStatus",
            List.of(RiskStatusEnum.WAITING_DISPATCH.getCode(), RiskStatusEnum.RETURN.getCode()), "list");

    /**
     * 风险列表参数前缀
     */
    public static final String LIST_PARAMS_PREFIX = "risk-list-";
    /**
     * 回复内容at模板
     */
    public static final String CONTENT_TEMPLATE = "<span style=\"color:#253c91;\" contenteditable=\"false\" shortName=\"${shortName}\" deptName=\"${deptName}\" deptId=\"${deptId}\" deptCode=\"${item.deptCode}\">@${shortName}</span>&nbsp;";
    /**
     * 列表类型-风险台账
     */
    public static final String LIST_TYPE_STANDING_BOOK = "standing-book";
    /**
     * 列表类型-风险处置
     */
    public static final String LIST_TYPE_HANDLE = "handle";
    /**
     * 列表类型-风险抄送
     */
    public static final String LIST_TYPE_COPY = "copy";

    /**
     * 风险编码前缀
     */
    public static final String RISK_CODE_PREFIX = "FX";

    /**
     * 操作按钮
     */
    public static final String DISPATCH = "分派";

    public static final String REOPEN = "重启";

    public static final String SIGN = "签收";

    public static final String JUDGE = "研判";

    public static final String URGE = "催办";

    public static final String CANCEL = "取消处置";

    public static final String FEEDBACK = "反馈";

    public static final String REPLY = "回复";

    public static final String EDIT_HANDLE_DEPT = "编辑处置单位";

    public static final String AGREE = "同意";

    public static final String REJECT = "驳回";

    public static final String EDIT_DONE = "编辑风险办结";

    public static final String EDIT_FEEDBACK = "编辑";

    public static final String DELETE_FEEDBACK = "删除";

    public static final String DONE = "提交办结";

    public static final String EDIT_REPLY = "编辑";

    public static final String DELETE_REPLY = "删除";

    public static final String TAG_SCORE_NAME = "风险信息标签得分";

    public static final String DENOISE_NAME = "溯源数据降噪得分";

    public static final String FREQUENCY_NAME = "溯源数据频率得分";

    public static final String WEIGHT_NAME = "溯源数据标签得分";

    public static final String RELATION_PERSON_NAME = "相关人员得分";

    public static final String RESPONSIBLE_DEPT = "主责单位";
    public static final String HANDLER_DEPT = "处置单位";
    public static final String COPY_DEPT = "抄送单位";

    public static final Long READ_DEPT_ID = 1000000L;


    /**
     * 分派人-根据状态获取按钮
     */
    public static final Map<RiskStatusEnum, List<String>> DISPATCHER_STATUS_OPERATIONS
            = Map.of(RiskStatusEnum.WAITING_DISPATCH, List.of(DISPATCH),
            RiskStatusEnum.RETURN, List.of(DISPATCH),
            RiskStatusEnum.DONE, List.of(REOPEN),
            RiskStatusEnum.NO_HANDLE, List.of(REOPEN)
    );
    /**
     * 主责人-根据状态获取按钮
     */
    public static final Map<RiskStatusEnum, List<String>> RESPONSIBLE_PERSON_STATUS_OPERATIONS
            = Map.of(RiskStatusEnum.WAITING_SIGN, List.of(SIGN),
            RiskStatusEnum.WAITING_JUDGE, List.of(JUDGE),
            RiskStatusEnum.DOING, List.of(FEEDBACK, URGE, CANCEL, EDIT_HANDLE_DEPT),
            RiskStatusEnum.WAITING_DONE, List.of(AGREE, REJECT, FEEDBACK, CANCEL),
            RiskStatusEnum.DONE, List.of(EDIT_DONE)
    );

    /**
     * 主责人- 列表根据状态获取按钮
     */
    public static final Map<RiskStatusEnum, List<String>> RESPONSIBLE_PERSON_STATUS_LIST_OPERATIONS
            = Map.of(RiskStatusEnum.WAITING_SIGN, List.of(SIGN),
            RiskStatusEnum.WAITING_JUDGE, List.of(JUDGE),
            RiskStatusEnum.DOING, List.of(URGE, CANCEL),
            RiskStatusEnum.WAITING_DONE, List.of(AGREE, REJECT, CANCEL),
            RiskStatusEnum.DONE, List.of(EDIT_DONE)
    );


    /**
     * 处置人-根据状态获取按钮
     */
    public static final Map<RiskStatusEnum, List<String>> HANDLER_STATUS_OPERATIONS
            = Map.of(RiskStatusEnum.WAITING_SIGN, List.of(SIGN),
            RiskStatusEnum.SIGN, List.of(FEEDBACK, DONE),
            RiskStatusEnum.FEEDBACK, List.of(FEEDBACK, DONE),
            RiskStatusEnum.WAITING_DONE, List.of(FEEDBACK)
    );
    /**
     * 抄送人-根据状态获取按钮
     */
    public static final Map<RiskStatusEnum, List<String>> COPY_ROLE_OPERATIONS
            = Map.of(RiskStatusEnum.WAITING_DISPATCH, List.of(FEEDBACK),
            RiskStatusEnum.RETURN, List.of(FEEDBACK),
            RiskStatusEnum.WAITING_SIGN, List.of(FEEDBACK),
            RiskStatusEnum.WAITING_JUDGE, List.of(FEEDBACK),
            RiskStatusEnum.DOING, List.of(FEEDBACK),
            RiskStatusEnum.WAITING_DONE, List.of(FEEDBACK)
    );

    /**
     * 获取签收超期时间长度
     *
     * @return 超期时间长度
     */
    public static Integer getSignOverdueLength() {
        try {
            return BeanFactoryHolder.getEnv().getProperty("com.trs.police.riskSign.signOverdueLength", Integer.class, SIGN_OVERDUE_LENGTH);
        } catch (Exception e) {
            LOGGER.error("获取签收超期时间长度发生异常");
            return SIGN_OVERDUE_LENGTH;
        }
    }
}
