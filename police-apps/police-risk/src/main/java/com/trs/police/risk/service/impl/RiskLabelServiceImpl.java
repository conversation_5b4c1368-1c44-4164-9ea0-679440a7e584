package com.trs.police.risk.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.redis.starter.service.RedisService;
import com.trs.police.risk.domain.entity.RiskLabel;
import com.trs.police.risk.domain.vo.RiskTagRuleTreeVO;
import com.trs.police.risk.mapper.RiskLabelMapper;
import com.trs.police.risk.service.RiskLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/30 17:12
 */
@Service
@Slf4j
public class RiskLabelServiceImpl implements RiskLabelService {

    private static final String TAG_NAME = "tagName";

    private static final String FILTER = "filter";

    private static final String SCORE = "score";

    private static final String ID = "id";

    private static final String PARENT_ID = "parentId";

    private static final String PRIORITY = "priority";

    private static final String IS_RELATION = "isRelation";

    @Resource
    private RiskLabelMapper riskLabelMapper;

    @Resource
    private RedisService redisService;

    private static final String DEFAULT_PATH = "-";


    @PostConstruct
    private void deleteRedisCache() {
        log.info("删除风险标签相关缓存");
        redisService.delByPrefix("riskLabel");
    }

    @Cacheable(value = "riskLabel", key = "'tree'")
    @Override
    public List<CodeNameVO> getTree() {
        return riskLabelMapper.getTree();
    }

    /**
     * 获取风险标签-树 用于配置展示
     *
     * @return 风险标签-树
     */
    @Override
    public List<RiskTagRuleTreeVO> getTreeConfig(String tagName) {
        try {
            List<RiskLabel> riskLabels = riskLabelMapper.getTreeConfig();

            // 处理筛选
            if (tagName != null) {
                ArrayList<RiskLabel> byName = riskLabels.stream().filter(label -> label.getName().contains(tagName)).collect(Collectors.toCollection(ArrayList::new));
                ArrayList<RiskLabel> tempList = new ArrayList<>();
                for (RiskLabel riskLabel : riskLabels) {
                    for (RiskLabel label : byName) {
                        if (label.getPid() != null && riskLabel.getId().equals(label.getPid())) {
                            tempList.add(riskLabel);
                        }
                    }
                }
                // riskLabels 最终为筛选后的结果
                byName.addAll(tempList);
                riskLabels = byName;
            }

            // 1. 创建ID到VO的映射
            Map<Long, RiskTagRuleTreeVO> voMap = new HashMap<>(16);

            // 2. 数据转换基础结构
            riskLabels.forEach(label -> {
                RiskTagRuleTreeVO vo = new RiskTagRuleTreeVO();
                vo.setId(label.getId());
                vo.setPid(label.getPid());
                vo.setName(label.getName());
                vo.setPriority(label.getPriority());
                vo.setScore(label.getScore());
                vo.setNeedRelation(label.getNeedRelation());
                vo.setConditionDesc(label.getConditionDesc());
                voMap.put(label.getId(), vo);
            });

            // 3. 构建树结构
            ArrayList<RiskTagRuleTreeVO> forest = new ArrayList<>();
            voMap.values().forEach(vo -> {
                Long pid = vo.getPid();
                if (pid == null || pid == 0 || !voMap.containsKey(pid)) {
                    // 根节点条件：父ID为空/0 或 父节点不存在
                    forest.add(vo);
                } else {
                    // 添加到父节点的子列表
                    RiskTagRuleTreeVO parent = voMap.get(pid);
                    if (parent != null) {
                        if (parent.getChildren() == null) {
                            // 初始化子节点列表
                            parent.setChildren(new ArrayList<>());
                        }
                        parent.getChildren().add(vo);
                    }
                }
            });


           return forest;
        } catch (Exception e) {
            log.error("获取风险标签-树失败, 错误信息为 {}", e.getMessage(), e);
            throw new RuntimeException("获取风险标签-树失败 , 错误信息为 " + e.getMessage(), e);
        }
    }

    @Override
    public void updatePath() {
        for (RiskLabel root : riskLabelMapper.getRoots()) {
            root.setPath(DEFAULT_PATH);
            riskLabelMapper.updateById(root);
            updateChildPath(root);
        }
    }

    /**
     * 保存标签规则
     *
     * @param rule 标签规则
     */
    @Override
    public String saveOrUpdateRiskLabel(String rule) {
        try {
            RiskLabel riskLabel = new RiskLabel();
            JSONObject ruleObj = JSONObject.parseObject(rule);
            riskLabel.setId(ruleObj.getLong(ID));
            riskLabel.setName(ruleObj.getString(TAG_NAME));
            riskLabel.setPid(ruleObj.getLong(PARENT_ID));
            riskLabel.setNeedRelation(ruleObj.getBoolean(IS_RELATION));
            riskLabel.setPriority(ruleObj.getInteger(PRIORITY));
            riskLabel.setScore(ruleObj.getFloat(SCORE));
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(rule);
            riskLabel.setSetting(jsonNode);
            if (riskLabel.getId() != null) {
                //修改规则
                riskLabelMapper.updateById(riskLabel);
                return rule;
            } else {
                // 新增规则
                riskLabelMapper.insert(riskLabel);
                if (jsonNode.isObject()) {
                    // 给setting 字段ID 赋值
                    ObjectNode objectNode = (ObjectNode) jsonNode;
                    objectNode.put(ID, riskLabel.getId());
                    riskLabel.setSetting(objectNode);
                    riskLabelMapper.updateById(riskLabel);
                }
                ruleObj.put(ID, riskLabel.getId());
                return ruleObj.toJSONString();
            }
        } catch (Exception e) {
            log.error("保存标签规则出错, 错误信息为: {}", e.getMessage(), e);
            throw new RuntimeException("保存标签规则出错, 错误信息为: " + e.getMessage(), e);
        }
    }

    /**
     * 根据id获取标签规则
     *
     * @param id 标签规则id
     * @return 标签规则
     */
    @Override
    public RiskLabel getRiskLabel(Integer id) {
        try {
            QueryWrapper<RiskLabel> wrapper = new QueryWrapper<>();
            wrapper.eq("id", id);
            return riskLabelMapper.selectOne(wrapper);
        } catch (RuntimeException e) {
            log.error("根据id获取标签规则出错, 错误信息为: {}", e.getMessage(), e);
            throw new RuntimeException("根据id获取标签规则出错, 错误信息为: " + e.getMessage(), e);
        }
    }

    private void updateChildPath(RiskLabel label) {
        List<RiskLabel> children = riskLabelMapper.getByPid(label.getId());
        if (children.isEmpty()) {
            return;
        }
        children.forEach(item -> {
            String path = label.getPath() + label.getId() + DEFAULT_PATH;
            item.setPath(path);
            riskLabelMapper.updateById(item);
            updateChildPath(item);
        });
    }
}
