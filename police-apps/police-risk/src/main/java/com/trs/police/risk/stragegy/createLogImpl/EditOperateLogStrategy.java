package com.trs.police.risk.stragegy.createLogImpl;

import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.mapper.CommonMapper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.risk.constant.enums.RiskLevelEnum;
import com.trs.police.risk.domain.entity.OperationLogEntity;
import com.trs.police.risk.domain.entity.Risk;
import com.trs.police.risk.mapper.RiskMapper;
import com.trs.police.risk.stragegy.OperationLogStrategyFactory;
import com.trs.police.risk.stragegy.OperateLogStrategy;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/25
 */
@Service
@Slf4j
public class EditOperateLogStrategy implements OperateLogStrategy, InitializingBean {

    @Resource
    RiskMapper riskMapper;

    @Resource
    CommonMapper commonMapper;

    @Override
    public <T>void createLog(T newObj, OperationLogEntity operationLogEntity, String desc) {
        Long riskId = operationLogEntity.getRelatedId();
        KeyValueVO keyValueVO = JsonUtil.parseSpecificObject(newObj, KeyValueVO.class);
        Risk risk = riskMapper.selectById(riskId);
        String key = keyValueVO.getKey();
        switch (key) {
            case "riskLevel":
                RiskLevelEnum riskLevel = risk.getRiskLevel();
                RiskLevelEnum newRiskLevel = RiskLevelEnum.codeOf(Integer.parseInt(keyValueVO.getValue()));
                if (newRiskLevel != null) {
                    operationLogEntity.setDetail(
                            "将风险等级：从 " + riskLevel.getName() + " 修改为 " + newRiskLevel.getName());
                }
                break;
            case "riskType":
                CodeNameVO riskType = commonMapper.getDict(risk.getRiskType(), "risk_type");
                CodeNameVO newRiskType = commonMapper.getDict(Long.parseLong(keyValueVO.getValue()), "risk_type");
                operationLogEntity.setDetail(
                        "将风险类型：从 " + riskType.getName() + " 修改为 " + newRiskType.getName());
                break;
            default:
                break;
        }
    }

    @Override
    public void afterPropertiesSet() {
        OperationLogStrategyFactory.register(Operation.EDIT, this);
    }
}
