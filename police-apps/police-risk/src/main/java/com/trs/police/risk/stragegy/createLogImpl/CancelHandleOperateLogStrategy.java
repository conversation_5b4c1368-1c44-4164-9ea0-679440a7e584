package com.trs.police.risk.stragegy.createLogImpl;

import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.risk.constant.enums.RiskOperateEnum;
import com.trs.police.risk.domain.entity.OperationLogEntity;
import com.trs.police.risk.domain.vo.CancelVO;
import com.trs.police.risk.stragegy.OperationLogStrategyFactory;
import com.trs.police.risk.stragegy.OperateLogStrategy;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/10/25
 */
@Service
@Slf4j
public class CancelHandleOperateLogStrategy implements OperateLogStrategy, InitializingBean {

    @Override
    public <T>void createLog(T newObj, OperationLogEntity operationLogEntity, String desc) {
        CancelVO cancelVO = JsonUtil.parseSpecificObject(newObj, CancelVO.class);
        if (cancelVO.getReasonType() == RiskOperateEnum.OTHER) {
            operationLogEntity.setDetail("取消原因：" + cancelVO.getReason());
        } else {
            operationLogEntity.setDetail("取消原因：" + cancelVO.getReasonType().getName());
        }
    }

    @Override
    public void afterPropertiesSet() {
        OperationLogStrategyFactory.register(Operation.CANCEL_HANDLE, this);
    }
}
