<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.risk.mapper.RiskPersonRiskScoreTendencyMapper">

    <resultMap id="riskScoreTendencyVO" type="com.trs.police.risk.domain.vo.RiskScoreTendencyVO">
        <result column="statisticsTime" property="statisticsTime"/>
        <result column="score" property="score"/>
        <result column="riskLabels" property="riskLabels" typeHandler="com.trs.police.risk.typeHander.JsonToRiskLabelHandler"/>
    </resultMap>

    <select id="riskScoreTendency" resultMap="riskScoreTendencyVO">
        select
            rpt.statistics_time as statisticsTime,
            rpt.score as score,
            rpt.risk_labels as riskLabels
        from t_risk_person_risk_score_tendency rpt
        where rpt.risk_person_id = #{id}
        group by rpt.statistics_time
        order by rpt.statistics_time asc
    </select>
</mapper>