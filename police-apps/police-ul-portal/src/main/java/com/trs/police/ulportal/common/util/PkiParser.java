package com.trs.police.ulportal.common.util;

import com.trs.police.ulportal.common.config.PKIGatewayProperties;
import com.trs.police.ulportal.domain.dto.PkiInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * pki信息解码器
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
public class PkiParser {

    @Resource(name = "unSSLRestTemplate")
    private RestTemplate restTemplate;

    @Resource
    private PKIGatewayProperties pkiProperties;
    /**
     * 前端加密的加密字符串
     */
    private String data;
    /**
     * 前端加密的随机字符
     */
    private String pwd;

    /**
     * 检查pli网关配置信息
     */
    @PostConstruct
    private void check() {
        if (StringUtils.isBlank(pkiProperties.getAttrType())) {
            log.error(
                    "从pki网关获取属性列表控制项为空,请确认 properties 文件中com.trs.ga.pki.gateway.attrType配置项是否正确");
            throw new RuntimeException(
                    "从pki网关获取属性列表控制项为空,请确认 properties 文件中com.trs.ga.pki.gateway.attrType配置项是否正确");

        }
    }

    /**
     * 解析pki
     *
     * @param signedData  签名数据
     * @param originalJsp 随机数
     * @param clientIp    签名客户端ip
     * @return pki信息
     * @throws PkiGatewayException   pki网关错误
     * @throws PkiAuthorityException pki错误
     */
    public PkiInfo parser(final String signedData, final String originalJsp, String clientIp)
            throws PkiGatewayException, PkiAuthorityException {

        // 1 根据密文 随机数 ip 三个信息组装 pki 网关请求参数
        String requestXml = createRequestXml(signedData, originalJsp, clientIp);
        log.info("向 pki 网关请求的报文内容:{}", requestXml);

        // 2 向网关发起请求
        String resXml = sendToPkiGateway(requestXml);
        log.info("pki 网关的响应内容:{}", resXml);

        // 3 解析网关响应结果
        return parserPkiGatewayResponse(resXml);

    }

    /**
     * 组装pki网关认证报文
     *
     * @param detach   证书认证信息,即前端 pki 插件解析后的 pki 加密字符串
     * @param original 认证原文,即秘钥信息
     * @param clientip 客户端 IP地址
     * @return 报文信息
     */
    private String createRequestXml(final String detach, final String original, final String clientip) {

        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement("message");
        Element requestHeadElement = root.addElement("head");
        Element requestBodyElement = root.addElement("body");
        requestHeadElement.addElement("version").setText("1.0");
        requestHeadElement.addElement("serviceType").setText("AuthenService");
        Element clientInfoElement = requestBodyElement.addElement("clientInfo");
        Element clientIpElement = clientInfoElement.addElement("clientIP");
        clientIpElement.setText(clientip);
        requestBodyElement.addElement("appId").setText(pkiProperties.getAppid());
        Element authenElement = requestBodyElement.addElement("authen");
        Element authCredentialElement = authenElement.addElement("authCredential");
        authCredentialElement.addAttribute("authMode", "cert");
        authCredentialElement.addElement("detach").setText(detach);
        authCredentialElement.addElement("original").setText(original);
        requestBodyElement.addElement("accessControl").setText("true");
        Element attributesElement = requestBodyElement.addElement("attributes");

        attributesElement.addAttribute("attributeType", pkiProperties.getAttrType());
        if ("portion".equals(pkiProperties.getAttrType())) {
            if (StringUtils.isNotBlank(pkiProperties.getAttributes())) {
                String[] attrs = pkiProperties.getAttributes().split(";");
                for (String att : attrs) {
                    if ((att.contains("X509")) || (att.contains("_saml"))) {
                        attributesElement.addAttribute(att, "http://www.jit.com.cn/cinas/ias/ns/saml/saml11/X.509");
                    } else {
                        attributesElement.addAttribute(att, "http://www.jit.com.cn/ums/ns/user");
                        attributesElement.addAttribute(att, "http://www.jit.com.cn/pmi/pms/ns/role");
                        attributesElement.addAttribute(att, "http://www.jit.com.cn/pmi/pms/ns/privilege");
                    }
                }
            }
        }

        return doc.asXML();
    }

    /**
     * 向 pki 网关发送解密请求
     *
     * @param requestXml 请求报文
     * @return 响应报文
     */
    private String sendToPkiGateway(String requestXml) throws PkiGatewayException {

        int statusCode;
        String result = null;
        HttpHeaders header = new HttpHeaders();
        header.add("Connection", "close");
        header.add("Content-Type", "text/xml;charset=UTF-8");

        HttpEntity<String> entity = new HttpEntity<>(requestXml, header);

        ResponseEntity<byte[]> response = restTemplate.postForEntity(pkiProperties.getAuthUrl(), entity, byte[].class);

        statusCode = response.getStatusCodeValue();

        if (statusCode == 200 || statusCode == 500) {

            if (statusCode == 200) {
                result = new String(Objects.requireNonNull(response.getBody()), StandardCharsets.UTF_8);
            } else {
                log.warn("网关响应状态码:{}", statusCode);
                throw new PkiGatewayException("pki网关错误,相应代码:" + statusCode);
            }
        }

        return result;

    }

    /**
     * 向 pki 网关发送解密请求
     *
     * @param requestXml 请求报文
     * @return 响应报文
     */
    private String sendToPkiGatewayByOkClient(String requestXml) throws PkiGatewayException {

        String result;
        Map<String, String> header = new HashMap<>();
        header.put("Connection", "close");
        header.put("Content-Type", "text/xml;charset=UTF-8");

        result = OkHttpUtil.postXmlParams(pkiProperties.getAuthUrl(), requestXml, header);

        return result;

    }

    /**
     * 提取 pki 网关解密结果
     *
     * @param responseXml 网关响应报文
     * @return pki信息
     */
    public PkiInfo parserPkiGatewayResponse(final String responseXml) throws PkiAuthorityException {

        final PkiInfo result = new PkiInfo();

        Document respDocument;
        Element headElement;
        Element bodyElement;
        try {
            respDocument = DocumentHelper.parseText(responseXml);

            headElement = respDocument.getRootElement().element("head");
            bodyElement = respDocument.getRootElement().element("body");
            if (headElement != null) {
                boolean state = Boolean.valueOf(headElement.elementTextTrim("messageState")).booleanValue();
                //认证失败
                if (state) {
                    log.error("pki 网关认证失败,错误代码:{},错误信息:{}", headElement.elementTextTrim("messageCode"),
                            headElement.elementTextTrim("messageDesc"));
                    throw new PkiAuthorityException(
                            "pki网关认证失败,网关错误代码:" + headElement.elementTextTrim("messageCode")
                                    + ",错误信息:" + headElement.elementTextTrim("messageDesc"));

                }
            }

            Element authResult = bodyElement.element("authResultSet").element("authResult");
            if (!Boolean.valueOf(authResult.attributeValue("success")).booleanValue()) {
                log.error("pki 网关认证失败,错误代码:{},错误信息:{}", headElement.elementTextTrim("authMessageCode"),
                        headElement.elementTextTrim("authMessageDesc"));
                throw new PkiAuthorityException(
                        "pki网关认证失败,网关错误代码:" + headElement.elementTextTrim("authMessageCode")
                                + ",错误信息:" + headElement.elementTextTrim("authMessageDesc"));
            }

            try {

                result.setNotBefore(DateUtils.parseDate(
                        bodyElement.selectSingleNode("attributes/attr[@name='X509Certificate.NotBefore']").getText(),
                        "yyyy年MM月dd日 HH:mm:ss"));
                result.setNotAfter(DateUtils.parseDate(
                        bodyElement.selectSingleNode("attributes/attr[@name='X509Certificate.NotAfter']").getText(),
                        "yyyy年MM月dd日 HH:mm:ss"));

            } catch (ParseException | NullPointerException e) {
                log.warn("pki 网关解密 pki 有效期错误", e);
            }

            String info = bodyElement.selectSingleNode("attributes/attr[@name='X509Certificate.SubjectDN']").getText();
            String[] infos = StringUtils.split(info, ",");

            Map<String, String> params = Arrays.stream(infos).map(i -> i.split("="))
                    .collect(Collectors.toMap(i -> i[0].strip(), i -> i[1].strip(), (ov, nv) -> nv + ov));

            String[] uinfo = params.get("CN").split(" ");
            String deptCode = params.get("ST") + params.get("L") + params.get("O") + params.get("OU");
            result.setName(uinfo[0]);
            result.setIdcard(StringUtils.upperCase(uinfo[1]));
            result.setDeptCode(deptCode);

            return result;
        } catch (Exception e) {
            log.error("pki网关错误", e);
            throw new PkiAuthorityException("pki网关错误", e);
        }

    }

    /**
     * pki 认证失败错误
     *
     * <AUTHOR>
     */
    public static class PkiAuthorityException extends Exception {

        /**
         * 构造方法
         *
         * @param message 异常描述
         */
        public PkiAuthorityException(String message) {
            super(message);
        }

        /**
         * 构造方法
         *
         * @param message 异常描述
         * @param cause   引发异常的异常
         */
        public PkiAuthorityException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * pki 认证网关通信错误
     */
    public static class PkiGatewayException extends Exception {

        /**
         * 构造方法
         *
         * @param message 异常描述
         */
        public PkiGatewayException(String message) {
            super(message);
        }

        /**
         * 构造方法
         *
         * @param message 异常描述
         * @param cause   引发异常的异常
         */
        public PkiGatewayException(String message, Throwable cause) {
            super(message, cause);
        }
    }

}
