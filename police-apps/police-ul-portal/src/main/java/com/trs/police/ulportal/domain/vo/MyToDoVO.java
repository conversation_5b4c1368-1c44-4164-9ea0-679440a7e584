package com.trs.police.ulportal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 反馈消息视图
 *
 * @date 2023/11/29 15:18
 */
@Data
@Builder
public class MyToDoVO implements Serializable {

    private static final long serialVersionUID = 986752329872943105L;

    /**
     * 主键
     */
    @ApiModelProperty("主键，任务id")
    private String id;

    @ApiModelProperty("任务编号")
    private String taskNum;

    @ApiModelProperty("任务标题")
    private String taskTitle;

    @ApiModelProperty("任务类型：01.每日五情 02.领导批示 03.工作任务")
    private Integer taskType;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("任务内容")
    private String taskContent;

    @ApiModelProperty("任务状态：01.暂存 02.待审核 03.已驳回 04.处置中 05.已完成 06.已中止")
    private Integer taskStatus;

    @ApiModelProperty("执行状态：01.待签收 02.处置中 03.已完成")
    private Integer execStatus;

    @ApiModelProperty("创建人所属部门")
    private String crUserDeptName;

    @ApiModelProperty("创建人所属部门代码")
    private String crUserDeptNameCode;

    @ApiModelProperty("创建人所属部门id")
    private String crUserDeptId;

    @ApiModelProperty("回复数量")
    private Integer replyCount;

    @ApiModelProperty("责任单位")
    private String responsibilityUnit;

    @ApiModelProperty("发布时间")
    private String pubTime;

    @ApiModelProperty("完成时间")
    private String completeTime;

    @ApiModelProperty("驳回原因")
    private String rejectReason;

    @ApiModelProperty("终止原因")
    private String terminationReason;

    @ApiModelProperty("任务总结")
    private String taskSummarize;

    @ApiModelProperty("是否结束,true/false")
    private Boolean isEnd;

    @ApiModelProperty("是否逾期, true/false")
    private Boolean isOverdue;

    @ApiModelProperty("是否删除：0、未删除，1、已删除")
    private String isDelete;
}
