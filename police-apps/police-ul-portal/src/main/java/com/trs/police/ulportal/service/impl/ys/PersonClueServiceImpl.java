package com.trs.police.ulportal.service.impl.ys;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.PersonClueConstant;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.PersonClueEntity;
import com.trs.police.common.core.entity.dwd.WarningPersonClueEntity;
import com.trs.police.common.core.params.DeptRequestParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.core.vo.search.KeyValueTypeVoForSearch;
import com.trs.police.common.openfeign.starter.DTO.AiGjDTO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.common.openfeign.starter.DTO.FindPersonByOtherArchivesDTO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.SearchService;
import com.trs.police.common.openfeign.starter.vo.MultiThemeGjxxVO;
import com.trs.police.common.openfeign.starter.vo.ThemeGjxxVO;
import com.trs.police.ulportal.common.config.PersonClueCareMonitorConfig;
import com.trs.police.ulportal.common.constants.PersonClueType;
import com.trs.police.ulportal.common.enums.PersonClueEnum;
import com.trs.police.ulportal.common.helper.TokenHelper;
import com.trs.police.ulportal.common.util.PersonClueUtil;
import com.trs.police.ulportal.common.util.SearchServiceDtoBuild;
import com.trs.police.ulportal.converter.PersonClueConvert;
import com.trs.police.ulportal.converter.WarningPersonClueConverter;
import com.trs.police.ulportal.domain.dto.ScoreDto;
import com.trs.police.ulportal.domain.dto.TrackDto;
import com.trs.police.ulportal.domain.dto.ys.EditPersonCluePersonInfoDTO;
import com.trs.police.ulportal.domain.dto.ys.PersonClueDTO;
import com.trs.police.ulportal.domain.dto.ys.PersonClueJudgeDTO;
import com.trs.police.ulportal.domain.dto.ys.PersonClueNavigationDTO;
import com.trs.police.ulportal.domain.entity.PersonCarRelation;
import com.trs.police.ulportal.domain.entity.PersonClueScoreRecord;
import com.trs.police.ulportal.domain.entity.ys.DbzlXsEntity;
import com.trs.police.ulportal.domain.entity.ys.PersonClueJudgeEntity;
import com.trs.police.ulportal.domain.vo.*;
import com.trs.police.ulportal.domain.vo.ys.*;
import com.trs.police.ulportal.mapper.PersonCarRelationMapper;
import com.trs.police.ulportal.mapper.PersonClueJudgeMapper;
import com.trs.police.ulportal.mapper.PersonClueScoreRecordMapper;
import com.trs.police.ulportal.mapper.ProfilePersonMapper;
import com.trs.police.ulportal.mapper.dwd.WarningPersonClueMapper;
import com.trs.police.ulportal.mapper.ys.DbzlXsMapper;
import com.trs.police.ulportal.mapper.ys.PersonClueMapper;
import com.trs.police.ulportal.service.LogService;
import com.trs.police.ulportal.service.impl.PersonClueRelatedInfoImpl;
import com.trs.police.ulportal.service.impl.label.PersonClueLabelSynchronous;
import com.trs.police.ulportal.service.impl.scoreCount.DqscModelScoreCountService;
import com.trs.police.ulportal.service.ys.PersonClueCountService;
import com.trs.police.ulportal.service.ys.PersonClueMpService;
import com.trs.police.ulportal.service.ys.PersonClueService;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.builder.util.KeyMgrFactory;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.TimeUtils.YYYYMMDD;

/**
 * 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PersonClueServiceImpl implements PersonClueService {

    @Autowired
    private PersonClueMpService personClueMpService;
    @Autowired
    private PersonClueRelatedInfoImpl personClueRelatedInfoImpl;
    @Autowired
    private SearchService searchService;
    @Autowired
    private PersonClueScoreRecordMapper scoreRecordMapper;
    @Autowired
    private PersonClueMapper personClueMapper;
    @Autowired
    private WarningPersonClueMapper warningPersonClueMapper;
    @Resource
    private WarningPersonClueConverter warningPersonClueConverter;
    @Resource
    private PersonClueConvert personClueConvert;

    @Autowired
    private PersonCarRelationMapper personCarRelationMapper;
    @Autowired
    private PersonClueLabelSynchronous synchronous;
    @Autowired
    private PersonClueCareMonitorConfig config;
    @Autowired
    private DictService dictService;
    @Autowired
    private DqscModelScoreCountService dqscService;
    @Autowired
    private DbzlXsMapper dbzlXsMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private TokenHelper tokenHelper;
    @Autowired
    private PersonClueJudgeMapper personClueJudgeMapper;
    @Autowired
    private LogService logService;
    @Autowired
    private ProfilePersonMapper profilePersonMapper;

    @Override
    public RestfulResultsV2<PersonClueVO> list(PersonClueDTO dto){
        List<String> types = getClueTypes(dto);
        // 没有模型则直接返回
        if (CollectionUtils.isEmpty(types)) {
            return RestfulResultsV2.ok(Collections.emptyList())
                    .addPageNum(dto.getPageNumber())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(0L);
        }
        // 查询数据
        LambdaQueryChainWrapper<PersonClueEntity> lambdaQuery = personClueMpService.lambdaQuery();
        buildByDto(lambdaQuery, dto);
        buildByScore(lambdaQuery, types);
        Page<PersonClueEntity> data = lambdaQuery.page(new Page<>(dto.getPageNumber(), dto.getPageSize()));
        List<PersonClueEntity> results = data.getRecords();
        results.forEach(e->e.setWarningReason(PersonClueUtil.getWarningReason(e)));
        List<Long> ids = CollectionUtils.isNotEmpty(results)
                ? results.stream().map(PersonClueEntity::getId).collect(Collectors.toList()) : new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ids)){
            List<DbzlXsVO> dbzlXsVoS = dbzlXsMapper.selectListAll(ids);
             map = CollectionUtils.isNotEmpty(dbzlXsVoS) ? dbzlXsVoS
                    .stream().collect(Collectors.toMap(DbzlXsVO::getLyid, DbzlXsVO::getId,(e1,e2)->e1)) : map;
        }
        final Map<String, String> wordIdMap = map;
        // 转换成为vo
        String wordInfoUrl = BeanFactoryHolder.getEnv().getProperty("com.trs.minio.wordInfoUrl");
        String token = tokenHelper.getToken();
        // 获取线索的所有研判信息
        Map<Long, List<PersonClueJudgeVO>> clueIdJudgeVoMap = getClueIdJudgeVoMap(ids);
        // 通过检索服务获取人员档案的证件号码和姓名对应关系
        Map<String, String> zjhmAndNameMap = getXmList(results);
        List<PersonClueVO> list = results
                .stream()
                .map(PersonClueConvert.CONVERTER::doToVo)
                .peek(vo -> {
                    vo.setDriverLicenseStatus(getDriverLicenseStatus(vo));
                    vo.setNumbering(StringUtils.isNotEmpty(vo.getNumbering()) ? vo.getNumbering() : vo.getClueCode());
                    vo.setName(PersonClueEnum.getNameByCode(vo.getClueType()));
                    vo.setWordId(wordIdMap.get(String.valueOf(vo.getId())));
                    String url = String.format("%s&token=%s&xsid=%s", wordInfoUrl, token, wordIdMap.get(String.valueOf(vo.getId())));
                    vo.setUrl(url);
                    vo.setJudges(clueIdJudgeVoMap.getOrDefault(vo.getId(), Collections.emptyList()));
                    vo.setJudgeCount(vo.getJudges().size());
                    if (CollectionUtils.isNotEmpty(vo.getZjhmList())) {
                        List<String> xmList = vo.getZjhmList().stream()
                                .map(zjhmAndNameMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        vo.setXmList(xmList);
                    }
                    vo.setNumber(CollectionUtils.isNotEmpty(vo.getXmList()) ? vo.getXmList().size() : 0);
                })
                .collect(Collectors.toList());
        RestfulResultsV2<PersonClueVO> ok = RestfulResultsV2.ok(list);
        ok.addTotalCount(data.getTotal());
        return ok;
    }

    /**
     * 通过检索服务获取人员档案的证件号码和姓名对应关系
     *
     * @param results 检索结果
     * @return map
     */
    private Map<String, String> getXmList(List<PersonClueEntity> results) {
        // 提取所有证件号码并去重
        List<String> allZjhm = extractAndDistinctZjhm(results);
        if (allZjhm.isEmpty()) {
            return Collections.emptyMap();
        }

        // 初始化 DTO
        FindPersonByOtherArchivesDTO otherArchivesDTO = createFindPersonByOtherArchivesDTO();

        // 分批处理证件号码
        List<List<String>> partitions = Lists.partition(allZjhm, 100);
        Map<String, String> resultMap = new HashMap<>(allZjhm.size());

        for (List<String> partitionZjhm : partitions) {
            otherArchivesDTO.setRecordIds(String.join(",", partitionZjhm));
            List<ArchivesVO> datas = searchService.findOtherArchivesByPerson(otherArchivesDTO).getDatas();
            if (!CollectionUtils.isEmpty(datas)) {
                datas.forEach(data -> resultMap.putIfAbsent(data.getRecordId(), handleFields(data.getFields(), "xm")));
            }
        }

        return resultMap;
    }

    private List<String> extractAndDistinctZjhm(List<PersonClueEntity> results) {
        return results.stream()
                .map(PersonClueEntity::getZjhmList)
                .filter(StringUtils::isNotEmpty)
                .flatMap(zjhmList -> Stream.of(zjhmList.split("[,|;]")))
                .distinct()
                .collect(Collectors.toList());
    }

    private FindPersonByOtherArchivesDTO createFindPersonByOtherArchivesDTO() {
        FindPersonByOtherArchivesDTO dto = new FindPersonByOtherArchivesDTO();
        dto.setArchivesType("person");
        dto.setExport(Boolean.TRUE);
        dto.setNeedAuth(Boolean.FALSE);
        dto.setPageNum(1);
        dto.setPageSize(100);
        return dto;
    }

    private Map<Long, List<PersonClueJudgeVO>> getClueIdJudgeVoMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Map.of();
        }
        QueryWrapper<PersonClueJudgeEntity> queryWrapper = new QueryWrapper<PersonClueJudgeEntity>().in("clue_id", ids);
        List<PersonClueJudgeEntity> judgeEntities = personClueJudgeMapper.selectList(queryWrapper);
        // 删除研判记录
        return judgeEntities.stream()
                .sorted(Comparator.comparing(PersonClueJudgeEntity::getUpdateTime).reversed())
                .map(personClueConvert::judgeEntityToJudgeVo)
                .collect(Collectors.groupingBy(PersonClueJudgeVO::getClueId));
    }

    private void buildByScore(LambdaQueryChainWrapper<PersonClueEntity> lambdaQuery, List<String> types) {
        //类型为6:0;100:0;101:0
        String scoreLimit = BeanFactoryHolder.getEnv().getProperty("com.trs.police.cluePool.clueListScoreLimit");
        if (StringUtils.isNotEmpty(scoreLimit)) {
            String[] clueTypeAndScore = scoreLimit.split(";");
            Map<String, String> map = Arrays.stream(clueTypeAndScore)
                    .filter(s -> types.contains(s.split(":")[0]))
                    .collect(Collectors.toMap(s -> s.split(":")[0], s -> s.split(":")[1]));
            types.removeAll(map.keySet());
            lambdaQuery.and(lqAnd -> {
                lqAnd.in(CollectionUtils.isNotEmpty(types),PersonClueEntity::getClueType, types);
                lqAnd.or(!map.isEmpty(), lqOr -> {
                    for (Map.Entry<String, String> entry : map.entrySet()) {
                        lqOr.or(lqOr1 -> {
                            lqOr1.eq(PersonClueEntity::getClueType, Integer.parseInt(entry.getKey()));
                            lqOr1.gt(PersonClueEntity::getRiskScore, Double.parseDouble(entry.getValue()));
                        });
                    }
                });
            });
        } else {
            lambdaQuery.in(CollectionUtils.isNotEmpty(types),PersonClueEntity::getClueType, types);
        }
    }

    private void buildByDto(LambdaQueryChainWrapper<PersonClueEntity> lambdaQuery, PersonClueDTO dto) {
        lambdaQuery.and(StringUtils.isNotEmpty(dto.getClueState()),wrapper ->{
                    switch (dto.getClueState()){
                        case "4":
                            wrapper.eq(PersonClueEntity::getClueState, dto.getClueState())
                                    .or()
                                    .eq(PersonClueEntity::getClueState, PersonClueConstant.PUSHED);
                            break;
                        default:
                            wrapper.eq(PersonClueEntity::getClueState, dto.getClueState());
                            break;
                    }
                })
                .ge(StringUtils.isNotEmpty(dto.getClueWarnTimeStart()), PersonClueEntity::getClueWarnTime, getTime(dto.getClueWarnTimeStart(), " 00:00:00"))
                .le(StringUtils.isNotEmpty(dto.getClueWarnTimeEnd()), PersonClueEntity::getClueWarnTime, getTime(dto.getClueWarnTimeEnd(), " 23:59:59"))
                .ge(StringUtils.isNotEmpty(dto.getAcceptTimeStart()), PersonClueEntity::getClueWarnTime, getTime(dto.getAcceptTimeStart(), " 00:00:00"))
                .le(StringUtils.isNotEmpty(dto.getAcceptTimeEnd()), PersonClueEntity::getClueWarnTime, getTime(dto.getAcceptTimeEnd(), " 23:59:59"))
                .eq(StringUtils.isNotEmpty(dto.getPersonLabel()), PersonClueEntity::getPersonLabel, dto.getPersonLabel())
                .in(CollectionUtils.isNotEmpty(dto.getSsbmdm()), PersonClueEntity::getSsbmdm, dto.getSsbmdm())
                .eq(Objects.nonNull(dto.getIsGx()), PersonClueEntity::getIsGx, dto.getIsGx())
                .eq(Objects.nonNull(dto.getIsGxGj()), PersonClueEntity::getIsGxGj, dto.getIsGxGj())
                .eq(Objects.nonNull(dto.getClueType()),PersonClueEntity::getClueType, dto.getClueType())
                .eq(Objects.nonNull(dto.getJudgeStatus()),PersonClueEntity::getJudgeStatus, dto.getJudgeStatus())
                .eq(StringUtils.isNotEmpty(dto.getGzylx()),PersonClueEntity::getGzylx, dto.getGzylx())
                .eq(Objects.nonNull(dto.getIsCd()),PersonClueEntity::getIsCdJz,dto.getIsCd())
                .and(StringUtils.isNotEmpty(dto.getSearchField()) && StringUtils.isNotEmpty(dto.getSearchValue()), wp -> {
                    switch (dto.getSearchField()) {
                        case "all":
                            wp.like(PersonClueEntity::getNumbering, dto.getSearchValue())
                                    .or()
                                    .like(PersonClueEntity::getProbablyPeopleName, dto.getSearchValue())
                                    .or()
                                    .like(PersonClueEntity::getAcceptPersonName, dto.getSearchValue())
                                    .or()
                                    .like(PersonClueEntity::getTel, dto.getSearchValue())
                                    .or()
                                    .like(PersonClueEntity::getProbablyPeopleId, dto.getSearchValue())
                                    .or()
                                    .like(PersonClueEntity::getSsbm,dto.getSearchValue())
                                    .or()
                                    .like(PersonClueEntity::getLicensePlate,dto.getSearchValue())
                                    .or()
                                    .like(PersonClueEntity::getJjdbh,dto.getSearchValue())
                                    .or().like(PersonClueEntity::getClueCode, dto.getSearchValue())
                                    .or().like(PersonClueEntity::getGzymc, dto.getSearchValue())
                                    .or().like(PersonClueEntity::getZjhmList, dto.getSearchValue());
                            break;
                        case "numbering":
                            wp.like(PersonClueEntity::getNumbering, dto.getSearchValue());
                            break;
                        case "probablyPeopleName":
                            wp.like(PersonClueEntity::getProbablyPeopleName, dto.getSearchValue());
                            break;
                        case "acceptPersonName":
                            wp.like(PersonClueEntity::getAcceptPersonName, dto.getSearchValue());
                            break;
                        case "tel":
                            wp.like(PersonClueEntity::getTel, dto.getSearchValue());
                            break;
                        case "probablyPeopleId":
                            wp.like(PersonClueEntity::getProbablyPeopleId, dto.getSearchValue());
                            break;
                        case "controlPolice":
                            wp.like(PersonClueEntity::getSsbm, dto.getSearchValue());
                            break;
                        case "carNumber":
                            wp.like(PersonClueEntity::getLicensePlate, dto.getSearchValue());
                            break;
                        case "ajNumber":
                            wp.like(PersonClueEntity::getJjdbh, dto.getSearchValue());
                            break;
                        case "clueCode":
                            wp.like(PersonClueEntity::getClueCode, dto.getSearchValue());
                            break;
                        case "gzymc":
                            wp.like(PersonClueEntity::getGzymc, dto.getSearchValue());
                            break;
                        case "relatePersonZjhm":
                            wp.like(PersonClueEntity::getZjhmList, dto.getSearchValue());
                            break;
                        default:
                            break;
                    }
                })
                .orderByDesc(PersonClueConstant.XD_DRIVE.equals(dto.getClueType()),PersonClueEntity::getCaptureTime)
                .orderByDesc(Arrays.asList(PersonClueConstant.MYPC_TYPE,PersonClueConstant.DQSC_TYPE,PersonClueConstant.PQ_TYPE)
                        .contains(dto.getClueType()),PersonClueEntity::getRiskScore)
                .orderByDesc(Arrays.asList(PersonClueConstant.CFBJ_RISK_PERSON,PersonClueConstant.MZX_RISK_PERSON)
                        .contains(dto.getClueType()),PersonClueEntity::getClueWarnTime)
                .orderByDesc(PersonClueEntity::getProbablyPeopleId)
                .orderBy("clueWarnTime".equals(dto.getSearchField()), "asc".equalsIgnoreCase(dto.getSortDirection()), PersonClueEntity::getClueWarnTime);
    }


    @NotNull
    private List<String> getClueTypes(PersonClueDTO dto) {
        List<DictDto> dictList = Objects.nonNull(dto.getType())
                ? dictService.getDictListByTypeList(Collections.singletonList(dto.getType()))
                : Collections.emptyList();
        if (StringUtils.isEmpty(dto.getDictDesc())){
            boolean allDept = StringUtils.isEmpty(dto.getPcode()) || dto.getPcode().equals("85");
            dto.setSsbmdm(allDept ? getAllDeptCode() : Collections.singletonList(dto.getPcode()));
            return dictList.stream()
                    .filter(dict -> dict.getPCode().equals(85L))
                    .map(DictDto::getFlag)
                    .collect(Collectors.toList());
        }
        return dictList.stream()
                .filter(dict -> dict.getPCode().equals(Long.parseLong(dto.getPcode())))
                .map(DictDto::getFlag)
                .collect(Collectors.toList());
    }

    private List<String> getAllDeptCode() {
        DeptRequestParams params = new DeptRequestParams();
        params.setType(3);
        return permissionService.getDeptByParams(params)
                .stream().map(DeptDto::getCode)
                .collect(Collectors.toList());
    }

    @Override
    public PersonClueOverviewVO overview() {
        PersonClueOverviewVO result = new PersonClueOverviewVO();
        result.setTotalCount(personClueMpService.lambdaQuery().count());
        String currentTime = TimeUtils.dateToString(new Date(), YYYYMMDD);
        Long count = personClueMpService.lambdaQuery()
                .ge(PersonClueEntity::getClueWarnTime, currentTime + " 00:00:00")
                .le(PersonClueEntity::getClueWarnTime, currentTime + " 23:59:59")
                .count();
        result.setTodayAddCount(count);
        result.setUseCount(0L);
        result.setTodayUseAddCount(0L);
        return result;
    }

    private String getTime(String time, String end) {
        if (StringUtils.isEmpty(time)) {
            return null;
        }
        return TimeUtils.stringToString(time, YYYYMMDD) + end;
    }

    @Override
    public List<KeyValueTypeVO> modelDesc() {
        final String config = BeanFactoryHolder.getEnv().getProperty("com.trs.model.nameAndDesc", String.class, "");
        if (StringUtils.isNotEmpty(config)) {
            return JSONArray.parseArray(config, KeyValueTypeVO.class).stream()
                    .filter(a -> StringUtils.isNotEmpty(a.getKey()))
                    .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public RestfulResultsV2<ScoreDetailVO> scoreDetail(ScoreDto dto) {
        ScoreDetailVO vo = new ScoreDetailVO();
        Page<PersonClueScoreRecord> page = new Page<PersonClueScoreRecord>()
                .setCurrent(dto.getPageNum()).setSize(dto.getPageSize());
        List<Long> scoreList = scoreRecordMapper.sumScore(dto);
        Long score = CollectionUtils.isEmpty(scoreList) ? 0L : scoreList.stream().mapToLong(e->e).sum();
        IPage<PersonClueScoreRecord> pageResult = scoreRecordMapper.selectListByItems(dto,page);
        vo.setScoreContent(pageResult.getRecords());
        vo.setTotalScore(score);
        return RestfulResultsV2.ok(vo)
                .addTotalCount(pageResult.getTotal())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize());
    }

    @Override
    public RestfulResultsV2<PersonTrackVO> personTrackList(TrackDto dto) {
        List<Long> areaIds = config.getIds(dto.getClueType());
        List<PersonTrackVO> resultList = new ArrayList<>();
        //若人员的轨迹信息来源非t_warning_person_clue表，则调用检索模块查询轨迹
        if (CollectionUtils.isEmpty(areaIds)){
            RestfulResultsV2<ThemeGjxxVO> result = getGjListBySearchService(dto);
            resultList = resultToVo(result.getDatas());
            resultList = resultList.stream().sorted(Comparator.comparing(PersonTrackVO::getOrderTime).reversed()).collect(Collectors.toList());
            return RestfulResultsV2.ok(resultList);
        }
        //若人员的轨迹信息来源t_warning_person_clue表，直接查表数据
        Page page = new Page(dto.getPageNum(), dto.getPageSize());
        QueryWrapper<WarningPersonClueEntity> wrapper = new QueryWrapper<WarningPersonClueEntity>()
                .in(StringUtils.isNotEmpty(dto.getIdCard()),"id_card", Arrays.asList(dto.getIdCard().split(",")))
                .ge(StringUtils.isNotEmpty(dto.getStartTime()),"activity_time",dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()),"activity_time",dto.getEndTime())
                .groupBy("activity_time")
                .orderByDesc("activity_time");
        Page<WarningPersonClueEntity> result = warningPersonClueMapper.selectPage(page, wrapper);
        List<WarningPersonClueEntity> warningPersonList = result.getRecords().stream().collect(Collectors.toList());
        warningPersonList.forEach(e->e.setActivityTime(Objects.nonNull(e.getActivityTime())
                ? e.getActivityTime() : e.getWarningTime()));
        for (WarningPersonClueEntity entity : warningPersonList) {
            PersonTrackVO vo = warningPersonClueConverter.toEntity(entity);
            vo.setAreaId((List<Long>) (CollectionUtils.retainAll(entity.getAreaId(), areaIds)));
            vo.setArchivesType("person");
            resultList.add(vo);
        }
        return RestfulResultsV2.ok(resultList)
                .addTotalCount(page.getTotal())
                .addPageSize(dto.getPageSize())
                .addPageNum(dto.getPageNum());
    }

    /**
     * 获取轨迹列表
     *
     * @param dto dto
     * @return 轨迹信息
     */
    private RestfulResultsV2<ThemeGjxxVO> getGjListBySearchService(TrackDto dto) {
        PersonClueEntity entity = Objects.nonNull(dto.getClueId()) ? personClueMapper.selectById(dto.getClueId())
                : new PersonClueEntity();
        if (Objects.isNull(entity)){
            return RestfulResultsV2.ok(new ArrayList<>());
        }
        AiGjDTO aiGjDTO = new AiGjDTO();
        //获取身份证-姓名对应
        Map<String, String> xmList = getXmList(Arrays.asList(entity));
        List<ThemeGjxxVO> gjList = new ArrayList<>();
        //如果模型为涉毒人员聚集风险，则调用多轨迹查询.
        if (dto.getClueType().equals(PersonClueType.SDRYJJFX) && StringUtils.isNotEmpty(dto.getIdCard())){
            if (StringUtils.isEmpty(dto.getIdCard())){
                return RestfulResultsV2.ok(new ArrayList<>());
            }
            List<MultiThemeGjxxVO> datas = getMutilGjList(dto, entity, xmList);
            String range = BeanFactoryHolder.getEnv().getProperty("com.trs.cluePool.range", "100");
            //gjList = filterRangeData(entity,gjList,Double.valueOf(range));
            for (MultiThemeGjxxVO data : datas) {
                if (CollectionUtils.isEmpty(data.getGjList())){
                    continue;
                }
                log.info(String.valueOf(data.getGjList().size()));
                gjList.addAll(filterRangeData(entity,data.getGjList(),Double.valueOf(range)));
            }
            //过滤窝点多少范围内的数据
        }else {
            aiGjDTO = SearchServiceDtoBuild.buildDto(dto,entity);
            gjList.addAll(searchService.aiAnalysisGjList(aiGjDTO).getDatas());
        }
        gjList = gjList.stream().filter(e->StringUtils.isNotEmpty(e.getHdsj())).collect(Collectors.toList());
        return RestfulResultsV2.ok(gjList);
    }

    @NotNull
    private List<MultiThemeGjxxVO> getMutilGjList(TrackDto dto, PersonClueEntity entity, Map<String, String> xmList) {
        AiGjDTO aiGjDTO;
        Map<String,String> personCarMap = new HashMap<>();
        switch (entity.getGzylx()){
            case "停车场":
                List<String> idCards =StringUtils.isNotEmpty(dto.getIdCard()) ? Arrays.asList(dto.getIdCard().split(",")) : new ArrayList<>();
                List<PersonCarRelation> personCarRelationList = personCarRelationMapper.selectList(new QueryWrapper<PersonCarRelation>()
                        .in("id_card", idCards));
                aiGjDTO = SearchServiceDtoBuild.buildSdRyJjCarFxDto(dto, CollectionUtils.isNotEmpty(personCarRelationList)
                                ? personCarRelationList.stream().map(PersonCarRelation::getCarNumber).collect(Collectors.joining(",")) : ""
                        , entity);
                personCarMap = CollectionUtils.isNotEmpty(personCarRelationList) ? personCarRelationList.stream()
                        .collect(Collectors.toMap(PersonCarRelation::getCarNumber,PersonCarRelation::getIdCard))
                        : personCarMap;
                break;
            case "智慧小区":
            default:
                aiGjDTO = SearchServiceDtoBuild.buildSdRyJjPersonFxDto(dto, dto.getIdCard(), entity);
                break;
        }
        List<MultiThemeGjxxVO> datas = searchService.multiGjList(aiGjDTO).getDatas();
        switch (entity.getGzylx()){
            case "停车场":
                Map<String,String> personCarRelationMap = personCarMap;
                datas.forEach(e->{
                    if (CollectionUtils.isNotEmpty(e.getGjList())){
                        e.getGjList().forEach(gj->{
                            gj.setBz(xmList.get(StringUtils.isNotEmpty(personCarRelationMap.get(e.getRecord()))
                                    ? personCarRelationMap.get(e.getRecord()) : ""));
                            gj.setRecordId(personCarRelationMap.get(e.getRecord()));
                        });
                    }
                });
                break;
            case "智慧小区":
            default:
                datas.forEach(e->{
                    if (CollectionUtils.isNotEmpty(e.getGjList())){
                        e.getGjList().forEach(gj->{
                            gj.setBz(xmList.get(e.getRecord()));
                            gj.setRecordId(e.getRecord());
                        });
                    }
                });
                break;
        }
        return datas;
    }

    /**
     * 过滤范围内的数据
     *
     * @param entity 实体类
     * @param gjList 轨迹集合
     * @param range 范围
     * @return 轨迹信息
     */
    private List<ThemeGjxxVO> filterRangeData(PersonClueEntity entity, List<ThemeGjxxVO> gjList, Double range) {
        Double earthAverageR = 6371393d;
        if (StringUtils.isEmpty(entity.getLatitude()) || StringUtils.isEmpty(entity.getLongitude())){
            return new ArrayList<>();
        }
        //线索的地点的经度弧度
        double longitude = Math.toRadians(Double.valueOf(entity.getLongitude()));
        //线索的地点的纬度弧度
        double latitude = Math.toRadians(Double.valueOf(entity.getLatitude()));
        gjList = gjList.stream().filter(e->StringUtils.isNotEmpty(e.getJdwgs84()) && StringUtils.isNotEmpty(e.getWdwgs84()))
                .collect(Collectors.toList());
        gjList = gjList.stream().filter(l->{
            if (l.getJdwgs84().equals(entity.getLongitude()) && l.getWdwgs84().equals(entity.getLatitude())) {
                return true;
            }
            // 轨迹信息经度弧度
            double radiansBx = Math.toRadians(Double.valueOf(l.getJdwgs84()));
            // 轨迹信息维度弧度
            double radiansBy = Math.toRadians(Double.valueOf(l.getWdwgs84()));
            double cos = Math.cos(latitude) * Math.cos(radiansBy) * Math.cos(longitude - radiansBx) + Math.sin(latitude) * Math.sin(radiansBy);
            if (cos < -1.0 || cos > 1.0) {
                // 处理异常值，可以记录日志或抛出异常
                return false;
            }
            double acos = Math.acos(cos);
            return (earthAverageR * acos) < range;
        }).collect(Collectors.toList());
        return CollectionUtils.isEmpty(gjList) ? new ArrayList<>() : gjList;
    }


    /**
     * 转vo
     *
     * @param datas 参数
     * @return 结果
     */
    private List<PersonTrackVO> resultToVo(List<ThemeGjxxVO> datas) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<PersonTrackVO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(datas)){
            return list;
        }
        for (ThemeGjxxVO data : datas) {
            PersonTrackVO vo = new PersonTrackVO();
            vo.setRecordId(data.getRecordId());
            vo.setPersonName(data.getBz());
            vo.setType(data.getTzlx());
            vo.setCarNumber(data.getTzlx().equals("车牌号") ? data.getTzzhm() : null);
            vo.setArchivesType("person");
            vo.setZjlid(data.getZjlid());
            vo.setSjlyxtfldm(data.getSjlyxtfldm());
            vo.setJdwgs84(data.getJdwgs84());
            vo.setWdwgs84(data.getWdwgs84());
            vo.setActivityAddress(data.getHddz());
            vo.setGjlx(data.getGjlx());
            vo.setGzylx(data.getGzylx());
            vo.setGjxt(data.getGjxt());
            vo.setGjdt(data.getGjdt());
            vo.setActivityTime(data.getHdsj());
            vo.setOrderTime(LocalDateTime.parse(data.getHdsj(), formatter));
            list.add(vo);
        }
        return list;
    }

    /**
     * 门户新线索池左侧导航栏
     *
     * @param dto dto
     * @return 导航列表
     */
    @Override
    public RestfulResultsV2<PersonClueNavigationVO> navigation(PersonClueNavigationDTO dto) throws ServiceException {
        // 目前还差部门下的模型不支持统计，部门下是全部的模型，可以通过配置获取。判断是统计部门还是部门下的模型可以通过判断dto.pcode是否是数值来，数值是码表，值57是部门的二级树，非数值是部门下的模型，
        // 或者由增加参数交由前端控制
        PersonClueCountService service = KeyMgrFactory.findMgrByKey(PersonClueCountService.class, buildKey(dto));
        PreConditionCheck.checkNotNull(service, "该类型的服务暂不支持");
        List<PersonClueNavigationVO> count = service.countStatistic(dto);
        return RestfulResultsV2.ok(count);
    }

    private String buildKey(PersonClueNavigationDTO dto) {
        if (Objects.nonNull(dto.getTopStatistics()) && dto.getTopStatistics().equals("topStatistics")){
            return "topStatistics";
        }
        return dto.getPcode().equals("57") ? "deptCode" : "clueType";
    }

    @Override
    public RestfulResultsV2 push(Integer id) {
        PersonClueEntity personClue = personClueMpService.lambdaQuery()
                .eq(Objects.nonNull(id), PersonClueEntity::getId, id).one();
        if (personClue.getClueState().equals(PersonClueConstant.PENDING)){
            personClue.setPushTime(LocalDateTime.now());
            personClue.setClueState(PersonClueConstant.PUSHED);
        }else {
            return RestfulResultsV2.error("当前消息不可推送");
        }
        personClueMpService.updateById(personClue);
        return RestfulResultsV2.ok("推送成功");
    }

    //还没测试
    @Override
    public RestfulResultsV2<PersonClueVO> clueDetail(Integer id){
        PersonClueEntity entity = personClueMapper.selectOne(new QueryWrapper<PersonClueEntity>().eq("id", id));
        if (Objects.isNull(entity)){
            return RestfulResultsV2.ok("此线索不存在");
        }
        PersonClueVO personClueVO = personClueConvert.doToVo(entity);
        personClueVO.setDriverLicenseStatus(getDriverLicenseStatus(personClueVO));
        personClueVO.setName(PersonClueEnum.getNameByCode(entity.getClueType()));
        personClueVO.setPushTime(Objects.nonNull(entity.getPushTime())
                ? entity.getPushTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
        personClueVO.setWarningReason(PersonClueUtil.getWarningReason(entity));
        //personClueVO.setUrl(BeanFactoryHolder.getEnv().getProperty("com.trs.minio.wordInfoUrl"));
        personClueVO.setNumbering(StringUtils.isNotEmpty(personClueVO.getNumbering()) ? personClueVO.getNumbering() : personClueVO.getClueCode());
        // 研判信息
        Map<Long, List<PersonClueJudgeVO>> judgeVoMap = getClueIdJudgeVoMap(Collections.singletonList(personClueVO.getId()));
        personClueVO.setJudges(judgeVoMap.getOrDefault(personClueVO.getId(), Collections.emptyList()));
        personClueVO.setJudgeCount(personClueVO.getJudges().size());
        //是否调用请求第三方接口
        boolean clueProcessSwitch = BeanFactoryHolder.getEnv().containsProperty("com.trs.personclue.clueprocess");
        if (clueProcessSwitch){
            try{
                getThirdInfo(id, personClueVO);
            }catch (Exception e){
                log.error("调用第三方接口获取线索进度失败");
            }
        }
        String url = String.format("%s&token=%s&xsid=%s", BeanFactoryHolder.getEnv().getProperty("com.trs.minio.wordInfoUrl"),
                tokenHelper.getToken(), personClueVO.getWordId());
        personClueVO.setUrl(url);
        return RestfulResultsV2.ok(personClueVO);
    }

    /**
     * 获取驾驶证状态信息
     *
     * @param personClueVO vo
     * @return jieguo
     */
    private List<String> getDriverLicenseStatus(PersonClueVO personClueVO) {
        if (StringUtils.isEmpty(personClueVO.getDriverInfo()) || "null".equals(personClueVO.getDriverInfo())){
            return new ArrayList<>();
        }
        List<JSONObject> jsonObjects = JSONObject.parseArray(personClueVO.getDriverInfo(), JSONObject.class);
        if (CollectionUtils.isEmpty(jsonObjects)){
            return new ArrayList<>();
        }
        return JSONObject.parseArray(personClueVO.getDriverInfo(),JSONObject.class)
                .stream().map(e->e.getString("zt"))
                .collect(Collectors.toList());
    }

    /**
     * 获取第三方信息
     *
     * @param id id
     * @param personClueVO personClueVO
     * @throws JsonProcessingException 异常信息
     */
    private void getThirdInfo(Integer id, PersonClueVO personClueVO) throws JsonProcessingException {
        List<DbzlXsEntity> dbzlXsEntitys = dbzlXsMapper.selectList(new QueryWrapper<DbzlXsEntity>().eq("lyid", id));
        if (CollectionUtils.isEmpty(dbzlXsEntitys)){
            return;
        }
        DbzlXsEntity dbzlXsEntity = dbzlXsEntitys.get(0);
        personClueVO.setWordId(dbzlXsEntity.getId());
        String internetAddress = BeanFactoryHolder.getEnv().getProperty("com.trs.minio.processurl"
                ,"http://***********:81/mosty-api/mosty-rwzx/tbDbzl/lcxz?id={0}}");
        String url = MessageFormat.format(internetAddress,dbzlXsEntity.getId());
        Request casRequest = new Request.Builder().get()
                .url(url)
                .build();
        OkHttpClient client = new OkHttpClient();
        String clueRecords = "";
        try (Response execute = client.newCall(casRequest).execute()) {
            log.info("cas request response: " + execute.body().toString());
            clueRecords = execute.body().string();
        } catch (IOException exception) {
            log.error("cas request failed", exception);
        }
        //clueRecords = OkHttpUtil.getInstance().getData(url);
        log.info("请求结果为：" + clueRecords);
        if (StringUtils.isNotEmpty(clueRecords)){
            JsonNode jsonNode = new ObjectMapper().readTree(clueRecords);
            String data = Objects.nonNull(jsonNode.get("data")) ? jsonNode.get("data").toString() : "[]";
            List<JSONObject> jsonObjects = JSONObject.parseArray(data, JSONObject.class);
            personClueVO.setClueRecord(jsonObjects);
            log.info("请求地址为：" + url);
        }
    }

    //未测试
    @Override
    public void thirdDataSynchronous() {
        log.info("同步第三方视图数据：" + System.currentTimeMillis());
        try {
            personClueRelatedInfoImpl.personClueThirdInfoSynchronous();
            log.info("同步第三方视图数据成功");
        }catch (Throwable throwable){
            log.error("同步第三方视图数据失败！", throwable);
        }
        log.info("同步第三方视图数据结束：" + System.currentTimeMillis());
    }

    /**
     * 线索添加研判
     *
     * @param dto dto
     * @return 是否成功
     */
    @Override
    @DSTransactional
    public RestfulResultsV2<Boolean> clueJudge(PersonClueJudgeDTO dto) {
        // 若研判id不为空，做编辑权限校验，与删除通用
        PersonClueJudgeEntity judgeEntity = null;
        if (Objects.nonNull(dto.getJudgeId())) {
            judgeEntity = personClueJudgeMapper.selectById(dto.getJudgeId());
            checkIsLoginUser(judgeEntity);
        }
        judgeEntity = buildPersonClueJudgeEntity(judgeEntity, dto);
        if (Objects.nonNull(judgeEntity.getId())) {
            personClueJudgeMapper.updateById(judgeEntity);
        } else {
            personClueJudgeMapper.insert(judgeEntity);
        }
        // 更新研判状态
        UpdateWrapper<PersonClueEntity> updateWrapper = new UpdateWrapper<PersonClueEntity>()
                .eq("id", dto.getClueId())
                .set("judge_status", 1);
        personClueMapper.update(null, updateWrapper);
        // 赋值数据并更新或新增
        return RestfulResultsV2.ok(Boolean.TRUE);
    }

    private PersonClueJudgeEntity buildPersonClueJudgeEntity(PersonClueJudgeEntity judgeEntity, PersonClueJudgeDTO dto) {
        judgeEntity = personClueConvert.judgeDtoToJudgeEntity(judgeEntity, dto);
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        judgeEntity.setJudgeIdCard(currentUser.getIdNumber());
        judgeEntity.setJudgeUserName(currentUser.getRealName());
        judgeEntity.setCreateDeptId(currentUser.getDept().getId());
        judgeEntity.setUpdateDeptId(currentUser.getDept().getId());
        judgeEntity.setClueId(Objects.nonNull(dto.getClueId()) ? dto.getClueId() : judgeEntity.getClueId());
        return judgeEntity;
    }

    private void checkIsLoginUser(PersonClueJudgeEntity judgeEntity) {
        PreConditionCheck.checkNotNull(judgeEntity, "研判记录不存在");
        CurrentUser user = AuthHelper.getNotNullUser();
        PreConditionCheck.checkArgument(user.getId().equals(judgeEntity.getCreateUserId()),
                "只能由本人编辑、删除自己的研判意见");
    }

    /**
     * 线索删除研判
     *
     * @param judgeId 研判id
     * @return 是否成功
     */
    @Override
    @DSTransactional
    public RestfulResultsV2<Boolean> deleteJudge(Long judgeId) {
        // 若研判id不为空，做编辑权限校验，与删除通用
        PersonClueJudgeEntity judgeEntity = personClueJudgeMapper.selectById(judgeId);
        checkIsLoginUser(judgeEntity);
        personClueJudgeMapper.deleteById(judgeId);
        QueryWrapper<PersonClueJudgeEntity> wrapper = new QueryWrapper<PersonClueJudgeEntity>()
                .eq("clue_id", judgeEntity.getClueId());
        Long judgeCount = personClueJudgeMapper.selectCount(wrapper);
        if (judgeCount <= 0) {
            // 更新研判状态
            UpdateWrapper<PersonClueEntity> updateWrapper = new UpdateWrapper<PersonClueEntity>()
                    .eq("id", judgeEntity.getClueId())
                    .set("judge_status", 0);
            personClueMapper.update(null, updateWrapper);
        }
        // 删除研判记录
        return RestfulResultsV2.ok(Boolean.TRUE);
    }

    /**
     * 线索删除研判
     *
     * @param clueId 线索id
     * @param pageParams 分页参数
     * @return 是否成功
     */
    @Override
    public RestfulResultsV2<PersonClueJudgeVO> clueJudgeList(Long clueId, PageParams pageParams) {
        // 若研判id不为空，做编辑权限校验，与删除通用
        PreConditionCheck.checkNotNull(clueId, "线索id不能为空");
        QueryWrapper<PersonClueJudgeEntity> queryWrapper = new QueryWrapper<PersonClueJudgeEntity>()
                .eq("clue_id", clueId)
                .orderByDesc("update_time");
        List<PersonClueJudgeEntity> judgeEntities = personClueJudgeMapper.selectList(queryWrapper);
        // 删除研判记录
        return RestfulResultsV2.ok(judgeEntities.stream()
                .sorted(Comparator.comparing(PersonClueJudgeEntity::getUpdateTime).reversed())
                .map(personClueConvert::judgeEntityToJudgeVo)
                .collect(Collectors.toList()));
    }

    /**
     * 线索编辑人员信息
     *
     * @param dto dto
     * @return 是否成功
     */
    @Override
    public RestfulResultsV2<Boolean> editPersonInfo(EditPersonCluePersonInfoDTO dto) {
        try {
            dto.isValid();
            List<ArchivesVO> operateArchivesList = new ArrayList<>();
            ArchivesVO archivesVO = dto.convertToArchivesPersonVO();
            searchService.pushArchivesMessage(Collections.singletonList(archivesVO));
            logService.createOperatorLog(JSONObject.toJSONString(archivesVO), "线索池", "云哨", "线索池更新人员档案");
            operateArchivesList.add(archivesVO);
            long personSleep = BeanFactoryHolder.getEnv().getProperty("com.trs.police.personClue.editPersonSleep", Long.class, 100000L);
            Thread.sleep(personSleep);
            Tuple2<List<ArchivesVO>, List<ArchivesVO>> tuple2 = dto.convertToArchivesPhoneVO();
            List<ArchivesVO> vo1List = tuple2._1;
            long phoneSleep = BeanFactoryHolder.getEnv().getProperty("com.trs.police.personClue.editPhoneSleep", Long.class, 0L);
            if (!vo1List.isEmpty()) {
                searchService.pushArchivesMessage(vo1List);
                logService.createOperatorLog(JSONObject.toJSONString(dto), "线索池", "云哨", "线索池删除手机档案");
                Thread.sleep(phoneSleep * vo1List.size());
                operateArchivesList.addAll(vo1List);
            }
            List<ArchivesVO> voList = tuple2._2;
            if (!voList.isEmpty()) {
                searchService.pushArchivesMessage(voList);
                logService.createOperatorLog(JSONObject.toJSONString(dto), "线索池", "云哨", "线索池新增手机档案");
                Thread.sleep(phoneSleep * voList.size());
                operateArchivesList.addAll(voList);
            }
            // 添加操作了的档案数据记录
            operateArchivesList.forEach(searchService::addArchivesOperate);
        } catch (Exception e) {
            log.error("线索池更新异常", e);
            return RestfulResultsV2.error(String.format("线索池更新异常:%s", e.getMessage()));
        }
        return RestfulResultsV2.ok(Boolean.TRUE);
    }

    @Override
    public RestfulResultsV2<PersonClueRelatePersonVO> relatePersonList(Long clueId, PageParams pageParams) {
        try {
            PersonClueEntity clueEntity = personClueMapper.selectById(clueId);
            if (StringUtils.isEmpty(clueEntity.getZjhmList())) {
                return RestfulResultsV2.ok(Collections.emptyList())
                        .addPageNum(0)
                        .addPageSize(1);
            }
            // 获取分页查询的数据
            RestfulResultsV2<PersonClueRelatePersonVO> resultsV2 = getPersonPageList(clueEntity.getZjhmList(), pageParams);
            // 填充手机号码
            buildPersonPhoneList(resultsV2.getDatas());
            return resultsV2;
        } catch (Exception e) {
            log.error("涉及人员列表查询失败:", e);
            return RestfulResultsV2.error("涉及人员列表查询失败:" + e.getMessage());
        }
    }

    private void buildPersonPhoneList(List<PersonClueRelatePersonVO> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return;
        }
        List<String> zjhmList = datas.stream().map(PersonClueRelatePersonVO::getZjhm).collect(Collectors.toList());
        String zjhmStr = String.join(",", zjhmList);
        FindPersonByOtherArchivesDTO otherArchivesDTO = new FindPersonByOtherArchivesDTO();
        otherArchivesDTO.setArchivesType("phone");
        otherArchivesDTO.setRecordIds(zjhmStr);
        otherArchivesDTO.setExport(Boolean.TRUE);
        otherArchivesDTO.setNeedAuth(Boolean.FALSE);
        otherArchivesDTO.setPageNum(1);
        otherArchivesDTO.setPageSize(9999);
        RestfulResultsV2<ArchivesVO> archivesByPerson = searchService.findOtherArchivesByPerson(otherArchivesDTO);
        Map<String, List<String>> jzzjhmAndPhoneMap = archivesByPerson.getDatas().stream()
                .collect(Collectors.groupingBy(archives -> handleFields(archives.getFields(), "jzzjhm"),
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream().map(ArchivesVO::getRecordId)
                                        .collect(Collectors.toList()))));
        datas.forEach(vo -> vo.setSjhm(jzzjhmAndPhoneMap.getOrDefault(vo.getZjhm(), Collections.emptyList())));
    }

    private String handleFields(List<KeyValueTypeVoForSearch> fields, String fieldName) {
        Optional<KeyValueTypeVoForSearch> first = fields.stream().filter(f -> f.getFieldName().equals(fieldName)).findFirst();
        return first.isPresent() ? first.get().getValue().toString() : "";
    };

    private RestfulResultsV2<PersonClueRelatePersonVO> getPersonPageList(String zjhmList, PageParams pageParams) {
        FindPersonByOtherArchivesDTO otherArchivesDTO = new FindPersonByOtherArchivesDTO();
        otherArchivesDTO.setArchivesType("person");
        otherArchivesDTO.setRecordIds(zjhmList);
        otherArchivesDTO.setExport(Boolean.TRUE);
        otherArchivesDTO.setPageNum(pageParams.getPageNumber());
        otherArchivesDTO.setPageSize(pageParams.getPageSize());
        otherArchivesDTO.setNeedAuth(Boolean.FALSE);
        RestfulResultsV2<ArchivesVO> archivesByPerson = searchService.findOtherArchivesByPerson(otherArchivesDTO);
        List<PersonClueRelatePersonVO> personClueRelatePersonVOList = archivesByPerson.getDatas().stream()
                .map(archivesVO -> {
                    PersonClueRelatePersonVO personVO = new PersonClueRelatePersonVO();
                    personVO.setZjhm(archivesVO.getRecordId());
                    for (KeyValueTypeVoForSearch field : archivesVO.getFields()) {
                        switch (field.getFieldName()) {
                            case "xm":
                                personVO.setXm(Objects.nonNull(field.getValue()) ? field.getValue().toString() : "");
                                break;
                            case "xzdz":
                                personVO.setXzdz(Objects.nonNull(field.getValue()) ? field.getValue().toString() : "");
                                break;
                            case "tags":
                                personVO.setTags(Objects.nonNull(field.getValue())
                                        ? JSONObject.parseArray(JSONObject.toJSONString(field.getValue())).stream()
                                        .map(o -> JSONObject.parseObject(o.toString()).getString("tag_name"))
                                        .collect(Collectors.toList())
                                        : Collections.emptyList());
                                break;
                            case "xb":
                                personVO.setXb(Objects.nonNull(field.getValue()) ? field.getValue().toString() : "");
                                break;
                            default:
                                break;
                        }
                    }
                    return personVO;
                }).collect(Collectors.toList());
        RestfulResultsV2<PersonClueRelatePersonVO> resultsV2 = RestfulResultsV2.ok(personClueRelatePersonVOList);
        resultsV2.setSummary(archivesByPerson.getSummary());
        return resultsV2;
    }

    @Override
    public RestfulResultsV2<PersonClueIdCards> getSdJjRelated(Long id) {
        PersonClueIdCards personClueIdCards = new PersonClueIdCards();
        PersonClueEntity personClueEntity = personClueMapper.selectById(id);
        if (StringUtils.isEmpty(personClueEntity.getZjhmList())){
            return RestfulResultsV2.ok("此线索无相关人员");
        }
        List<PersonClueEntity> personClueEntities = Arrays.asList(personClueEntity);
        Map<String, String> xmList = getXmList(personClueEntities);
        personClueIdCards.setIdCards(personClueEntity.getZjhmList());
        List<String> idCards = Arrays.asList(personClueEntity.getZjhmList().split(","));
        List<PeopleUseInfoVO> collect = new ArrayList<>();
        for (String idCard : idCards) {
            PeopleUseInfoVO vo = new PeopleUseInfoVO();
            vo.setUserIdCard(idCard);
            vo.setTrueName(xmList.get(idCard));
            collect.add(vo);
        }
        personClueIdCards.setPersons(collect);
        return RestfulResultsV2.ok(personClueIdCards);
    }

    @Override
    public void run() {
        log.info("线索池扒窃、涉黄人员、盗窃三车积分计算：" + System.currentTimeMillis());
        try {
            dqscService.countAllItemScore(new ArrayList<>(), PersonClueConstant.DQSC_TYPE);
            dqscService.countAllItemScore(new ArrayList<>(), PersonClueConstant.PQ_TYPE);
            dqscService.countAllItemScore(new ArrayList<>(), PersonClueConstant.MYPC_TYPE);
            log.info("积分计算成功");
        }catch (Throwable throwable){
            log.error("积分计算失败！", throwable);
        }
        log.info("积分计算结束：" + System.currentTimeMillis());
    }

    @Override
    public void personLabelSynchronous() {
        log.info("门户线索池人员标签信息开始同步：" + System.currentTimeMillis());
        try{
            synchronous.personClueLabelSynchronous();
        }catch (Throwable throwable){
            log.error("门户线索池人员标签信息同步失败！", throwable);
        }
        log.info("门户线索池人员标签信息开始结束：" + System.currentTimeMillis());
    }

    /**
     * 构造dto
     *
     * @param personClueList 人员list
     * @return 结果
     */
    private BaseArchivesSearchDTO buildDto(PersonClueEntity personClueList) {
        BaseArchivesSearchDTO dto = new BaseArchivesSearchDTO();
        String uuid = searchService.uuid();
        JSONObject object = JSONObject.parseObject(uuid);
        dto.setUuid((String) object.getJSONArray("data").get(0));
        String idCards = Arrays.asList(personClueList.getZjhmList().split(",")).stream()
                .collect(Collectors.joining("|"));
        dto.setArchivesType("person");
        dto.setIdCard(idCards);
        dto.setLabelsType("ALL");
        dto.setPageNum(1);
        dto.setPageSize(9999);
        dto.setSearchValue("证件号码：" + idCards);
        return dto;
    }
}
