package com.trs.police.ulportal.adapter.controller.policesituationtopic;

import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceTimeCategoryDTO;
import com.trs.police.ulportal.domain.vo.policesituationtopic.CommonResultVo;
import com.trs.police.ulportal.service.policesituationtopic.PoliceSituationTimeCategoryService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-05-16 20:21
 */
@RestController
@Api(value = "警情时间段", tags = "警情时间段")
@RequiredArgsConstructor
@RequestMapping("/timeCategory")
@Slf4j
public class PoliceSituationTimeCategoryController {

    private final PoliceSituationTimeCategoryService policeSituationTimeCategoryService;

    /**
     *  获取警情时间段一级类别统计
     *
     * @param dto dto
     * @return 统计信息
     */
    @PostMapping("/getCategoryCount")
    @ApiOperation(value = "获取警情时间段一级类别统计", notes = "获取警情时间段一级类别统计")
    public RestfulResultsV2<CommonResultVo> getTotalCategoryCount(@RequestBody @Validated PoliceTimeCategoryDTO dto) {
        return policeSituationTimeCategoryService.getTotalCategoryCount(dto);
    }

}
