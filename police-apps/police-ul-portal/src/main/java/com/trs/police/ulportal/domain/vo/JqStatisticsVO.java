package com.trs.police.ulportal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@Data
public class JqStatisticsVO implements Serializable {
    /**
     * 重复报警次数
     */
    private Long bjRepeatCount;

    /**
     * 重复报警比例
     */
    private String bjRepeatRation;

    /**
     * 重复报警人数
     */
    private Long bjRepeatPersonCount;

    /**
     * 民警考核人数
     */
    private Long cjPersonCount;

    /**
     * 警情总数量
     */
    private Long jqTotalCount;

    /**
     * 无效警情数量
     */
    private Long jqInvalidCount;

    /**
     * 有效警情数量
     */
    private Long jqValidCount;

    /**
     * 警情分类名称
     */
    private String jqClassName;

    /**
     * 警情各分类数量
     */
    private String jqClassCount;

    /**
     * 统计时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="MM-dd")
    private LocalDateTime jqTime;

}
