package com.trs.police.ulportal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 反馈消息视图
 * @date 2023/11/13 15:18
 */
@Data
public class AppVO implements Serializable {

    private static final long serialVersionUID = 986752123222943105L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 应用名
     */
    @ApiModelProperty("应用名")
    private String appName;

    /**
     * 应用类型
     */
    @ApiModelProperty("应用类型:1-人,2-车,3-电,4-网")
    private String appType;

    /**
     * 图标
     */
    @ApiModelProperty("图标")
    private String logo;

    /**
     * 应用分类
     */
    @ApiModelProperty("应用分类")
    private Integer type;

    /**
     * 应用介绍
     */
    @ApiModelProperty("应用介绍")
    private String appDesc;

    @ApiModelProperty("跳转地址")
    private String url;

    /**
     * 应用状态:0-正常，-1-废弃
     */
    @ApiModelProperty("应用状态:0-正常，-1-废弃")
    private Integer appStatus;
}
