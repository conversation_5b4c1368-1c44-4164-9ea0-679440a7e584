package com.trs.police.ulportal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-20 15:09
 */
@Data
public class ExtUserDeptVO implements Serializable {

    private static final long serialVersionUID = -5703715942832526599L;

    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long deptId;
}
