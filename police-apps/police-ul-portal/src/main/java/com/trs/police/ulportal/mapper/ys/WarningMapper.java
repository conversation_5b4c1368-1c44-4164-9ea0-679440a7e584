package com.trs.police.ulportal.mapper.ys;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.ulportal.domain.entity.ys.WarningEntity;
import com.trs.police.ulportal.domain.vo.ys.CluelInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 预警表(Warning)持久层
 *
 * <AUTHOR>
 * @date 2022-08-11 14:04:37
 */
@Mapper
@DS("ys-mysqldb")
public interface WarningMapper extends BaseMapper<WarningEntity> {

    /**
     * 根据模型id获取线索详情
     *
     * @param page page
     * @param id id
     * @return 线索信息
     */
    IPage<CluelInfoVO> getCluelInfo(Page<CluelInfoVO> page, @Param("id") Long id);

}