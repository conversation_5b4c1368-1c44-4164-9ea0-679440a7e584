package com.trs.police.ulportal.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 角色表
 * @date 2023/12/4 11:32
 */

@Data
@TableName(value = "t_portal_role")
@Slf4j
public class Role implements Serializable {

    private static final long serialVersionUID = 4358928200943105L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色名
     */
    @TableField(value = "role_name")
    private String roleName;

    /**
     * 角色描述
     */
    @TableField(value = "role_desc")
    private String roleDesc;

    /**
     * 是否为系统默认角色：1-是，0-否
     */
    @TableField(value = "sys_defined")
    private Integer sysDefined;

    /**
     * 状态：0-正常，-1-删除
     */
    @TableField(value = "role_status")
    @TableLogic
    private Integer roleStatus;

    @TableField(value = "mac")
    private String mac;

    /**
     *  toMacString 方法
     *
     * @return mac
     */
    public String toMacString() {
        return "Role{" +
                "id=" + id +
                ", roleName='" + roleName + '\'' +
                ", roleDesc='" + roleDesc + '\'' +
                ", sysDefined=" + sysDefined +
                ", roleStatus=" + roleStatus +
                '}';
    }
}
