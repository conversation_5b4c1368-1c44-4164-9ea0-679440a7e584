package com.trs.police.ulportal.service.ys;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.ulportal.domain.dto.ScoreDto;
import com.trs.police.ulportal.domain.dto.TrackDto;
import com.trs.police.ulportal.domain.dto.ys.EditPersonCluePersonInfoDTO;
import com.trs.police.ulportal.domain.dto.ys.PersonClueDTO;
import com.trs.police.ulportal.domain.dto.ys.PersonClueJudgeDTO;
import com.trs.police.ulportal.domain.dto.ys.PersonClueNavigationDTO;
import com.trs.police.ulportal.domain.vo.PersonClueIdCards;
import com.trs.police.ulportal.domain.vo.PersonTrackVO;
import com.trs.police.ulportal.domain.vo.ScoreDetailVO;
import com.trs.police.ulportal.domain.vo.ys.*;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 * 人员线索服务
 *
 * <AUTHOR>
 */
public interface PersonClueService {

    /**
     * 列表服务
     *
     * @param dto xx
     * @return xx
     */
    RestfulResultsV2<PersonClueVO> list(PersonClueDTO dto);

    /**
     * 请求总览
     *
     * @return 总览数据
     */
    PersonClueOverviewVO overview();

    /**
     * 模型描述
     *
     * @return 结果
     */
    List<KeyValueTypeVO> modelDesc();

    /**
     * 获取积分明细
     *
     * @param dto 参数
     * @return 结果
     */
    RestfulResultsV2<ScoreDetailVO> scoreDetail(ScoreDto dto);

    /**
     * 手动计算分数
     */
    void run();

    /**
     * 手动同步标签
     */
    void personLabelSynchronous();

    /**
     * 获取人员轨迹信息
     *
     * @param dto 参数
     * @return 结果
     */
    RestfulResultsV2<PersonTrackVO> personTrackList(TrackDto dto);

    /**
     * 门户新线索池左侧导航栏
     *
     * @param dto dto
     * @return 导航列表
     */
    RestfulResultsV2<PersonClueNavigationVO> navigation(PersonClueNavigationDTO dto) throws ServiceException;

    /**
     * 推送
     *
     * @param id 线索id
     * @return 结果
     */
    RestfulResultsV2 push(Integer id);

    /**
     * 获取线索详情
     *
     * @param id 线索id
     * @return 结果
     */
    RestfulResultsV2<PersonClueVO> clueDetail(Integer id);

    /**
     * 手动执行同步第三方数据
     */
    void thirdDataSynchronous();

    /**
     * 线索添加研判
     *
     * @param dto dto
     * @return 是否成功
     */
    RestfulResultsV2<Boolean> clueJudge(PersonClueJudgeDTO dto);

    /**
     * 线索删除研判
     *
     * @param judgeId 研判id
     * @return 是否成功
     */
    RestfulResultsV2<Boolean> deleteJudge(Long judgeId);

    /**
     * 线索研判列表
     *
     * @param clueId 线索id
     * @param pageParams 分页参数
     * @return 是否成功
     */
    RestfulResultsV2<PersonClueJudgeVO> clueJudgeList(Long clueId, PageParams pageParams);

    /**
     * 线索编辑人员信息
     *
     * @param dto dto
     * @return 是否成功
     */
    RestfulResultsV2<Boolean> editPersonInfo(EditPersonCluePersonInfoDTO dto);

    /**
     * 线索编辑人员信息
     *
     * @param clueId 线索id
     * @param pageParams 分页参数
     * @return 是否成功
     */
    RestfulResultsV2<PersonClueRelatePersonVO> relatePersonList(Long clueId, PageParams pageParams);

    /**
     * 获取涉毒聚集相关人员
     *
     * @param id id
     * @return 结果
     */
    RestfulResultsV2<PersonClueIdCards> getSdJjRelated(Long id);
}
