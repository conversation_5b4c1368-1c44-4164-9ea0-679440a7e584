package com.trs.police.ulportal.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.trs.police.ulportal.common.util.DecimalUtil;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/18 18:38
 */
public class SimpleDoubleSerializer extends JsonSerializer<Double> {

    @Override
    public void serialize(Double value, JsonGenerator gen, SerializerProvider serializers)
        throws IOException {
        gen.writeString(Objects.isNull(value)? "- -": DecimalUtil.keepFourDecimals(value));
    }

}
