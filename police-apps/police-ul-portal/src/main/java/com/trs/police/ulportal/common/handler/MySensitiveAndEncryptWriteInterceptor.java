package com.trs.police.ulportal.common.handler;

import com.chenhaiyang.plugin.mybatis.sensitive.annotation.*;
import com.chenhaiyang.plugin.mybatis.sensitive.type.SensitiveType;
import com.chenhaiyang.plugin.mybatis.sensitive.type.SensitiveTypeRegisty;
import com.chenhaiyang.plugin.mybatis.sensitive.utils.JsonUtils;
import com.chenhaiyang.plugin.mybatis.sensitive.utils.PluginUtils;
import com.trs.police.ulportal.common.annotation.EncryptParam;
import com.trs.police.ulportal.common.encrypt.MutliEncrypt;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.Configuration;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.util.*;

/**
 * 拦截写请求的插件。插件生效仅支持预编译的sql
 *
 * <AUTHOR>
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class}),
})
@Slf4j
public class MySensitiveAndEncryptWriteInterceptor implements Interceptor {

    private static final String MAPPEDSTATEMENT="delegate.mappedStatement";
    private static final String BOUND_SQL="delegate.boundSql";

    private MutliEncrypt encrypt;

    public MySensitiveAndEncryptWriteInterceptor(MutliEncrypt encrypt) {
        Objects.requireNonNull(encrypt,"encrypt should not be null!");
        this.encrypt = encrypt;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        StatementHandler statementHandler = PluginUtils.realTarget(invocation.getTarget());
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement)metaObject.getValue(MAPPEDSTATEMENT);
        SqlCommandType commandType = mappedStatement.getSqlCommandType();

        BoundSql boundSql = (BoundSql)metaObject.getValue(BOUND_SQL);
        Object params = boundSql.getParameterObject();

        // 支持直接加密参数
        Method method = getMethod(mappedStatement.getId());
        if (method != null) {
            Annotation[][] parameterAnnotations = method.getParameterAnnotations();
            for (int i = 0; i < parameterAnnotations.length; i++) {
                String paramName = "param" + (i + 1);
                Boolean needEncrypt = false;
                for (Annotation annotation : parameterAnnotations[i]) {
                    if(annotation instanceof EncryptParam){
                        needEncrypt = true;
                    }else if(annotation instanceof Param){
                        paramName = ((Param) annotation).value();
                    }
                }
                if(needEncrypt){
                    Object value = params instanceof Map ? ((Map) params).get(paramName) : params;
                    if(value != null && value instanceof CharSequence){
                        String newValue = encrypt.encrypt(value.toString());
                        boundSql.setAdditionalParameter(paramName, newValue);
                    }
                }
            }
        }

        // 兼容使用@Param注解的情况
        if(params instanceof Map) {
            Set<Map.Entry<String, Object>> set = ((Map) params).entrySet();
            for (Map.Entry<String, Object> entry : set) {
                // 排除默认的param1等参数
                if(entry.getKey().matches("param\\d+")){
                    continue;
                }
                Object value = entry.getValue();
                SensitiveEncryptEnabled sensitiveEncryptEnabled = value != null ? value.getClass().getAnnotation(SensitiveEncryptEnabled.class) : null;
                if(sensitiveEncryptEnabled != null && sensitiveEncryptEnabled.value()){
                    handleParameters(mappedStatement.getConfiguration(), boundSql,value,commandType, entry.getKey());
                }
            }
        }else {
            SensitiveEncryptEnabled sensitiveEncryptEnabled = params != null ? params.getClass().getAnnotation(SensitiveEncryptEnabled.class) : null;
            if(sensitiveEncryptEnabled != null && sensitiveEncryptEnabled.value()){
                handleParameters(mappedStatement.getConfiguration(), boundSql,params,commandType, null);
            }
        }
        return invocation.proceed();
    }

    private Method getMethod(String statementId) throws ClassNotFoundException {
        String className = statementId.substring(0, statementId.lastIndexOf('.'));
        String methodName = statementId.substring(statementId.lastIndexOf('.') + 1);
        Class<?> clazz = Class.forName(className);
        for (Method method : clazz.getDeclaredMethods()) {
            if (method.getName().equals(methodName)) {
                return method;
            }
        }
        return null;
    }

    private void handleParameters(Configuration configuration, BoundSql boundSql,Object param,SqlCommandType commandType, String prefix) throws Exception {

        Map<String, Object> newValues = new HashMap<>(16);
        MetaObject metaObject = configuration.newMetaObject(param);

        for (Field field : getAllFields(param.getClass())) {
            // 排除非数据库字段
            Object value = Try.of(()->metaObject.getValue(field.getName())).getOrNull();
            if(value == null){
                continue;
            }
            Object newValue = value;
            if(value instanceof CharSequence || value instanceof List){
                newValue = handleEncryptField(field,newValue);
                if(isWriteCommand(commandType) && !SensitiveTypeRegisty.alreadyBeSentisived(newValue)) {
                    newValue = handleSensitiveField(field, newValue);
                    newValue = handleSensitiveJsonField(field, newValue);
                }
            }
            if(value!=null && newValue!=null && !value.equals(newValue)) {
                newValues.put(field.getName(), newValue);
            }else {
                newValues.put(field.getName(), value);
            }
        }
        for (Map.Entry<String, Object> entry: newValues.entrySet()) {
            if(StringUtils.isEmpty(prefix)){
                boundSql.setAdditionalParameter(entry.getKey(), entry.getValue());
            }else {
                boundSql.setAdditionalParameter(prefix + "." + entry.getKey(), entry.getValue());
            }
        }
    }

    private static Field[] getAllFields(Class<?> clazz) {
        if (clazz == null || clazz.equals(Object.class)) {
            return new Field[0];
        }
        Field[] fields = clazz.getDeclaredFields();
        Field[] parentFields = getAllFields(clazz.getSuperclass());
        Field[] allFields = new Field[fields.length + parentFields.length];
        System.arraycopy(fields, 0, allFields, 0, fields.length);
        System.arraycopy(parentFields, 0, allFields, fields.length, parentFields.length);
        return allFields;
    }

    private boolean isWriteCommand(SqlCommandType commandType) {
        return SqlCommandType.UPDATE.equals(commandType) || SqlCommandType.INSERT.equals(commandType);
    }

    private Object handleEncryptField(Field field, Object value) {

        EncryptField encryptField = field.getAnnotation(EncryptField.class);
        Object newValue = value;
        if (encryptField != null && value != null) {
            // 支持list
            if(value instanceof List){
                encryptListField((List)value);
            }else {
                newValue = encrypt.encrypt(value.toString());
            }
        }
        return newValue;
    }

    private void encryptListField(List list){
        for (int i = 0; i < list.size(); i++) {
            if(list.get(i)==null){
                continue;
            }
            if(list.get(i) instanceof CharSequence){
                String encrypt = this.encrypt.encrypt(list.get(i).toString());
                list.set(i, encrypt);
            }
        }
    }

    private Object handleSensitiveField(Field field, Object value) {
        SensitiveField sensitiveField = field.getAnnotation(SensitiveField.class);
        Object newValue = value;
        if (sensitiveField != null && value != null) {
            newValue = SensitiveTypeRegisty.get(sensitiveField.value()).handle(value);
        }
        return newValue;
    }
    private Object handleSensitiveJsonField(Field field, Object value) {
        SensitiveJSONField sensitiveJsonField = field.getAnnotation(SensitiveJSONField.class);
        Object newValue = value;
        if (sensitiveJsonField != null && value != null) {
            newValue = processJsonField(newValue,sensitiveJsonField);
        }
        return newValue;
    }

    /**
     * 在json中进行脱敏
     *
     * @param newValue new
     * @param sensitiveJsonField 脱敏的字段
     * @return json
     */
    private Object processJsonField(Object newValue,SensitiveJSONField sensitiveJsonField) {

        try{
            Map<String,Object> map = JsonUtils.parseToObjectMap(newValue.toString());
            SensitiveJSONFieldKey[] keys =sensitiveJsonField.sensitivelist();
            for(SensitiveJSONFieldKey jsonFieldKey :keys){
                String key = jsonFieldKey.key();
                SensitiveType sensitiveType = jsonFieldKey.type();
                Object oldData = map.get(key);
                if(oldData!=null){
                    String newData = SensitiveTypeRegisty.get(sensitiveType).handle(oldData);
                    map.put(key,newData);
                }
            }
            return JsonUtils.parseMaptoJSONString(map);
        }catch (Throwable e){
            //失败以后返回默认值
            log.error("脱敏json串时失败，cause : {}",e.getMessage(),e);
            return newValue;
        }
    }

    @Override
    public Object plugin(Object o) {
        return Plugin.wrap(o, this);
    }

    @Override
    public void setProperties(Properties properties) {
        //do nothing
    }
}
