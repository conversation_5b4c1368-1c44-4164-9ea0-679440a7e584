package com.trs.police.ulportal.domain.vo.ys;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预警模型
 *
 * <AUTHOR>
 * @date 2024/3/25 16:26
 */
@Data
public class MonitorWarningModelVO implements Serializable {

    private static final long serialVersionUID = -3965865104311227911L;

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 模型标题
     */
    @ApiModelProperty("模型标题")
    private String title;
    /**
     * 图标
     */
    @ApiModelProperty("图标")
    private String iconUrl;
    /**
     * 详情
     */
    @ApiModelProperty("详情")
    private String detail;
    /**
     * 模型类型 control_model_type
     */
    @ApiModelProperty("模型类型")
    private Long type;
    /**
     * 区域id
     */
    @ApiModelProperty("区域id")
    private Long importantAreaId;
    /**
     * 区域类型 control_warning_source_category
     */
    @ApiModelProperty("区域类型")
    private Long placeCode;

    @ApiModelProperty("状态")
    private Boolean enableStatus;
    /**
     * 推荐标签
     */
    @ApiModelProperty("推荐标签")
    private List<Integer> recommendLabel;

    /**
     * 推荐户籍地
     */
    @ApiModelProperty("推荐户籍地")
    private List<String> recommendDistrict;

    /**
     * 警种
     */
    @ApiModelProperty("警种")
    private String policeCategory;

    /**
     * 已推送线索数
     */
    @ApiModelProperty("已推送线索数")
    private Long clueNum;

    /**
     * 精确度
     */
    private Double accuracyRating = 1.00;
}
