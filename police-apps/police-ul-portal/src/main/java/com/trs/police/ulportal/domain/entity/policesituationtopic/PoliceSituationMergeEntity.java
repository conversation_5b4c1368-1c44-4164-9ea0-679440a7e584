package com.trs.police.ulportal.domain.entity.policesituationtopic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 接警处警关联合并表
 *
 * <AUTHOR>
 * @date 2024/8/30 11:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "dwd_cjdbhb", autoResultMap = true)
public class PoliceSituationMergeEntity {
    /**
     * cjdbh
     */
    @TableField(value = "cjdbh")
    private String cjdbh;
    /**
     * jjdbh
     */
    @TableField(value = "jjdbh")
    private String jjdbh;
    /**
     * xzqh
     */
    @TableField(value = "xzqh")
    private String xzqh;
    /**
     * cjdwbm
     */
    @TableField(value = "cjdwbm")
    private String cjdwbm;
    /**
     * cjdwmc
     */
    @TableField(value = "cjdwmc")
    private String cjdwmc;
    /**
     * cjybh
     */
    @TableField(value = "cjybh")
    private String cjybh;

    /**
     * cjyxm
     */
    @TableField(value = "cjyxm")
    private String cjyxm;

    /**
     * cjsj
     */
    @TableField(value = "cjsj")
    private String cjsj;
    /**
     * cjyj
     */
    @TableField(value = "cjyj")
    private String cjyj;
    /**
     * sjdwdm
     */
    @TableField(value = "sjdwdm")
    private Integer sjdwdm;
    /**
     * sjybh
     */
    @TableField(value = "sjybh")
    private Integer sjybh;
    /**
     * sjyxm
     */
    @TableField(value = "sjyxm")
    private Integer sjyxm;
    /**
     * pdddsj
     */
    @TableField(value = "pdddsj")
    private Integer pdddsj;
    /**
     * pdjssj
     */
    @TableField(value = "pdjssj")
    private Integer pdjssj;
    /**
     * ddxcsj
     */
    @TableField(value = "ddxcsj")
    private Integer ddxcsj;
    /**
     * sjybh
     */
    @TableField(value = "cjwbsj")
    private Integer cjwbsj;

    /**
     * czlbmc
     */
    @TableField(value = "czlbmc")
    private Integer czlbmc;

    /**
     * czlbbh
     */
    @TableField(value = "czlbbh")
    private Integer czlbbh;

    /**
     * id
     */
    @TableField(value = "id")
    private String id;

    /**
     * bjsj
     */
    @TableField(value = "bjsj")
    private String bjsj;
    /**
     * jjwcsj
     */
    @TableField(value = "jjwcsj")
    private Integer jjwcsj;

    /**
     * jjdwdm
     */
    @TableField(value = "jjdwdm")
    private Integer jjdwdm;

    /**
     * jjdwmc
     */
    @TableField(value = "jjdwmc")
    private Integer jjdwmc;


    /**
     * jjybh
     */
    @TableField(value = "jjybh")
    private Integer jjybh;

    /**
     * jjyxm
     */
    @TableField(value = "jjyxm")
    private Integer jjyxm;

    /**
     * lhlx
     */
    @TableField(value = "lhlx")
    private Integer lhlx;

    /**
     * jjlx
     */
    @TableField(value = "jjlx")
    private Integer jjlx;
    /**
     * bjrmc
     */
    @TableField(value = "bjrmc")
    private Integer bjrmc;
    /**
     * bjrxbdm
     */
    @TableField(value = "bjrxbdm")
    private Integer bjrxbdm;
    /**
     * bjdh
     */
    @TableField(value = "bjdh")
    private Integer bjdh;
    /**
     * bjnr
     */
    @TableField(value = "bjnr")
    private Integer bjnr;
    /**
     * gxdwdm
     */
    @TableField(value = "gxdwdm")
    private Integer gxdwdm;
    /**
     * jqjb
     */
    @TableField(value = "jqjb")
    private Integer jqjb;
    /**
     * bjdz
     */
    @TableField(value = "bjdz")
    private Integer bjdz;
    /**
     * cjlbbh
     */
    @TableField(value = "cjlbbh")
    private Integer cjlbbh;

    /**
     * jqlbdm
     */
    @TableField(value = "jqlbdm")
    private Integer jqlbdm;

    /**
     * jqlbmc
     */
    @TableField(value = "jqlbmc")
    private String jqlbmc;
    /**
     * jqlxdm
     */
    @TableField(value = "jqlxdm")
    private Integer jqlxdm;
    /**
     * jqlxmc
     */
    @TableField(value = "jqlxmc")
    private String jqlxmc;
    /**
     * jqxldm
     */
    @TableField(value = "jqxldm")
    private Integer jqxldm;
    /**
     * jqxlmc
     */
    @TableField(value = "jqxlmc")
    private String jqxlmc;
    /**
     * jqdz
     */
    @TableField(value = "jqdz")
    private Integer jqdz;

    /**
     * bjrxzb
     */
    @TableField(value = "bjrxzb")
    private Integer bjrxzb;

    /**
     * bjryzb
     */
    @TableField(value = "bjryzb")
    private Integer bjryzb;
    /**
     * xt_xzsj
     */
    @TableField(value = "xt_xzsj")
    private Integer xtXzsj;
    /**
     * xt_xgsj
     */
    @TableField(value = "xt_xgsj")
    private Integer xtXgsj;
    /**
     * cjyage
     */
    @TableField(value = "cjyage")
    private Integer cjyage;
}
