package com.trs.police.ulportal.domain.entity.policesituationtopic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *  12345数据事件工单列表
 *
 * <AUTHOR>
 * @date 2024-08-12 10:33:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "dwd_12345bjxqb", autoResultMap = true)
public class PoliceHotLineEntity implements Serializable {
    private static final long serialVersionUID = -4016820670657614793L;
    /**
     * 联系人电话
     */
    @TableField(value = "phone_num")
    private String phoneNum;

    /**
     * 问题标题
     */
    @TableField(value = "questiontitle")
    private String questionTitle;

    /**
     * 内容描述
     */
    @TableField(value = "desc_")
    private String desc;

    /**
     * 地址
     */
    @TableField(value = "hadress")
    private String hadress;

    /**
     * 报道日期
     */
    @TableField(value = "reportdate")
    private LocalDateTime reportDate;

    /**
     * 分类1
     */
    @TableField(value = "class_1")
    private String class1;

    /**
     * 分类2
     */
    @TableField(value = "class_2")
    private String class2;
    /**
     * 分类3
     */
    @TableField(value = "class_3")
    private String class3;

    /**
     * 分类4
     */
    @TableField(value = "class_4")
    private String class4;

}
