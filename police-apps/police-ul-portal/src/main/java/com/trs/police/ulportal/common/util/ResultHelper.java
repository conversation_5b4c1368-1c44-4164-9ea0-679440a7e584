package com.trs.police.ulportal.common.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.base.converter.Converter;

/**
 * 返回结果处理类
 *
 * <AUTHOR>
 * @date 2023/11/16
 */
public class ResultHelper {

    /**
     * IPage转换实现
     *
     * @param <T> 输入
     * @return 转换结果
     */
    public static <T> Converter<IPage> getIPageConverter() {
        return from -> {

            RestfulResultsV2<T> resultsV2 = RestfulResultsV2
                    .ok(from.getRecords());

            resultsV2.addPageNum(Math.toIntExact(from.getCurrent()));
            resultsV2.addPageSize(Math.toIntExact(from.getSize()));
            resultsV2.addTotalCount(from.getTotal());

            return resultsV2;
        };
    }

}
