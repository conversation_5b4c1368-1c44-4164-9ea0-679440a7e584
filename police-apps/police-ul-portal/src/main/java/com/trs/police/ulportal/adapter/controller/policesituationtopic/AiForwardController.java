package com.trs.police.ulportal.adapter.controller.policesituationtopic;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.ulportal.common.handler.PortalException;
import com.trs.police.ulportal.domain.dto.policesituationtopic.ConversationDTO;
import com.trs.police.ulportal.service.policesituationtopic.AiForwardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * Description: ai对话
 *
 * @author: lv.bo
 * @create: 2024-06-12 09:55
 */
@RestController
@Api(value = "ai对话相关接口", tags = "ai对话相关接口")
@RequiredArgsConstructor
@RequestMapping("/aiForward")
@Slf4j
public class AiForwardController {

    private final AiForwardService aiForwardService;

    /**
     *  提问接口转发
     *
     * @param dto dto
     * @return 问答接口返回值
     * @throws PortalException 异常
     */
    @PostMapping(value = "/conversation")
    @ApiOperation(value = "问答接口转发", notes = "问答接口转发")
    public JSONObject conversationForward(@RequestBody @Validated ConversationDTO dto) throws PortalException {
        return aiForwardService.conversationForward(dto);
    }

    /**
     *  获取答案接口转发
     *
     * @param response response
     * @param conversationId 对话id
     * @param qaId 问答id
     * @param chatLogId 问答接口主键id
     * @throws Exception 异常
     */
    @GetMapping(value = "/answer")
    @ApiOperation(value = "获取答案接口转发", notes = "获取答案接口转发")
    public void answerForward(HttpServletResponse response, String conversationId, String qaId, String chatLogId) throws Exception {
        aiForwardService.answerForward(response, conversationId, qaId, chatLogId);
    }
}
