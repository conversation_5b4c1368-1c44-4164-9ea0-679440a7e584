package com.trs.police.ulportal.service.impl.scoreCount;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trs.police.ulportal.domain.entity.PersonClueScoreRecord;
import com.trs.police.ulportal.mapper.PersonClueScoreRecordMapper;
import org.springframework.stereotype.Service;

/**
 * 线索池人员积分明细记录
 */
@Service
@DS("ys-mysqldb")
public class PersonClueScoreRecordMpService extends ServiceImpl<PersonClueScoreRecordMapper, PersonClueScoreRecord> {
}
