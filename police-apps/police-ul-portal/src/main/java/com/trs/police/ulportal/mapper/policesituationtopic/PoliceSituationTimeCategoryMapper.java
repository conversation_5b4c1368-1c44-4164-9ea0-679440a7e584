package com.trs.police.ulportal.mapper.policesituationtopic;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceTimeCategoryDTO;
import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceTimeRangeDTO;
import com.trs.police.ulportal.domain.entity.policesituationtopic.PoliceSituationTimeCategoryEntity;
import com.trs.police.ulportal.domain.vo.policesituationtopic.PoliceCountVO;
import com.trs.police.ulportal.domain.vo.policesituationtopic.PoliceSituationCategoryCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-05-16 20:19
 */
@Mapper
@DS("jqfx-mysqldb")
public interface PoliceSituationTimeCategoryMapper extends BaseMapper<PoliceSituationTimeCategoryEntity> {

    /**
     *  获取警情时间段一级类别统计
     *
     * @param dto dto
     * @param codes codes
     * @return 统计
     */
    List<PoliceSituationCategoryCountVO> getTotalCategoryCount(@Param("dto") PoliceTimeCategoryDTO dto, @Param("codes") List<String> codes);

    /**
     *  获取子级别类别统计
     *
     * @param dto dto
     * @param codes codes
     * @return 统计
     */
    List<PoliceSituationCategoryCountVO> getChildCategoryCount(@Param("dto") PoliceTimeCategoryDTO dto, @Param("codes") List<String> codes);

    /**
     * 获取部门警情统计
     *
     * @param dto dto
     * @param codeList cods
     * @return 统计
     */
    List<PoliceCountVO> getDeptPoliceCount(@Param("dto") PoliceTimeRangeDTO dto,@Param("codes")  List<String> codeList);
}
