package com.trs.police.ulportal.service.impl.policesituationtopic;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.grt.condify.exception.CondifyException;
import com.grt.condify.parser.MybatisSearchParser;
import com.trs.police.ulportal.common.util.ResultHelper;
import com.trs.police.ulportal.converter.CommunityConverter;
import com.trs.police.ulportal.domain.dto.policesituationtopic.CommunityListDTO;
import com.trs.police.ulportal.domain.entity.policesituationtopic.CommunityEntity;
import com.trs.police.ulportal.domain.vo.policesituationtopic.CommunityVo;
import com.trs.police.ulportal.mapper.policesituationtopic.CommunityMapper;
import com.trs.police.ulportal.service.policesituationtopic.CommunityService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-05-07 9:43
 */
@Service
@RequiredArgsConstructor
public class CommunityServiceImpl extends ServiceImpl<CommunityMapper, CommunityEntity> implements CommunityService {

    private final CommunityMapper communityMapper;

    private final CommunityConverter communityConverter;

    @Override
    public RestfulResultsV2<CommunityVo> queryForPage(IPage<CommunityEntity> page, QueryWrapper<CommunityEntity> queryWrapper) {
        IPage<CommunityEntity> iPage = communityMapper.selectPage(page, queryWrapper);
        return ResultHelper.getIPageConverter().convert(communityConverter.toPageVo(iPage));
    }

    @Override
    public RestfulResultsV2<CommunityVo> getCommunityList(CommunityListDTO dto) throws CondifyException {

        QueryWrapper<CommunityEntity> queryWrapper =
                MybatisSearchParser.buildQueryWrapper(dto);

        return RestfulResultsV2.ok(communityConverter.toVoList(communityMapper.selectList(queryWrapper)));
    }

    @Override
    public RestfulResultsV2<CommunityVo> getCommunityPage(CommunityListDTO dto) throws CondifyException {

        IPage<CommunityEntity> page = MybatisSearchParser.buildPage(dto);

        QueryWrapper<CommunityEntity> queryWrapper =
                MybatisSearchParser.buildQueryWrapper(dto);

        return queryForPage(page, queryWrapper);
    }
}
