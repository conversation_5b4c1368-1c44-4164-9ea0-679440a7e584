package com.trs.police.ulportal.domain.entity.policesituationtopic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 出警年龄段
 *
 * <AUTHOR>
 * @date 2022/6/23 16:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "dwd_cjnltjb", autoResultMap = true)
public class PoliceAgeGroupEntity implements Serializable {

    private static final long serialVersionUID = -3965865104319207917L;

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 日期
     */
    @TableField(value = "rq")
    private String rq;
    /**
     * 部门组织机构编号
     */
    @TableField(value = "cjdwbm")
    private String cjdwbm;
    /**
     * 部门名称
     */
    @TableField(value = "cjdwmc")
    private String cjdwmc;
    /**
     * 21-25
     */
    @TableField(value = "t21_25")
    private Integer t21To25;
    /**
     * t26To30
     */
    @TableField(value = "t26_30")
    private Integer t26To30;
    /**
     * t31To35
     */
    @TableField(value = "t31_35")
    private Integer t31To35;
    /**
     * t36To40
     */
    @TableField(value = "t36_40")
    private Integer t36To40;
    /**
     * t41To45
     */
    @TableField(value = "t41_45")
    private Integer t41To45;
    /**
     * t46To50
     */
    @TableField(value = "t46_50")
    private Integer t46To50;
    /**
     * t51To55
     */
    @TableField(value = "t51_55")
    private Integer t51To55;
    /**
     * t56To60
     */
    @TableField(value = "t56_60")
    private Integer t56To60;

}
