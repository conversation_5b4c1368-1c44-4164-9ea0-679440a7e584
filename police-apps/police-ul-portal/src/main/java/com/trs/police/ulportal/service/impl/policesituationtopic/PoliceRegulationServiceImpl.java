package com.trs.police.ulportal.service.impl.policesituationtopic;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.grt.condify.exception.CondifyException;
import com.trs.police.ulportal.common.config.JqfxDeptConfig;
import com.trs.police.ulportal.common.config.WebSecurityConfig;
import com.trs.police.ulportal.common.constants.JqConstants;
import com.trs.police.ulportal.common.util.TimeUtil;
import com.trs.police.ulportal.converter.PoliceSituationCategoryConverter;
import com.trs.police.ulportal.domain.dto.policesituationtopic.JqfxDeptDTO;
import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceRegulationsDTO;
import com.trs.police.ulportal.domain.entity.policesituationtopic.PoliceRegulationsEntity;
import com.trs.police.ulportal.domain.vo.policesituationtopic.*;
import com.trs.police.ulportal.mapper.ExtDeptMapper;
import com.trs.police.ulportal.mapper.policesituationtopic.PoliceRegulationMapper;
import com.trs.police.ulportal.mapper.policesituationtopic.PoliceSituationCategoryMapper;
import com.trs.police.ulportal.mapper.ys.YsUserMapper;
import com.trs.police.ulportal.service.policesituationtopic.PoliceRegulationsService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-05-07 9:43
 */
@Service
@RequiredArgsConstructor
public class PoliceRegulationServiceImpl extends ServiceImpl<PoliceRegulationMapper, PoliceRegulationsEntity> implements PoliceRegulationsService {

    private final PoliceRegulationMapper policeRegulationMapper;

    private final JqfxDeptConfig jqfxDeptConfig;

    private final ExtDeptMapper extDeptMapper;

    private final PoliceSituationCategoryMapper policeSituationCategoryMapper;

    private final PoliceSituationCategoryConverter pscConverter;

    private final WebSecurityConfig webSecurityConfig;

    private final YsUserMapper ysUserMapper;
    @Override
    public RestfulResultsV2<PoliceRegulationsVO> queryForPage(IPage<PoliceRegulationsEntity> page, QueryWrapper<PoliceRegulationsEntity> queryWrapper) {
        return null;
    }

    @Override
    public RestfulResultsV2<CommonResultVo> statisticsCjLess60(PoliceRegulationsDTO dto) throws CondifyException {
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        CommonResultVo commonResultVo = new CommonResultVo();
        commonResultVo.setDeptList(jqfxDeptConfig.getDeptList(dto.getRegionCode()));
        //需要本期，上期，去年同期的数据一并返回
        List<PoliceRegulationsCountVO> curData = policeRegulationMapper.statisticsCjLess60(dto);
        commonResultVo.setCurData(curData);
        //上期
        Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime, endTime);
        dto.setStartTime(lastMonthTimeRange._1);
        dto.setEndTime(lastMonthTimeRange._2);
        List<PoliceRegulationsCountVO> lastMonthData = policeRegulationMapper.statisticsCjLess60(dto);
        commonResultVo.setLastMonthData(lastMonthData);
        //去年同期
        Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime, endTime);
        dto.setStartTime(lastYearTimeRange._1);
        dto.setEndTime(lastYearTimeRange._2);
        List<PoliceRegulationsCountVO> lastYearData = policeRegulationMapper.statisticsCjLess60(dto);
        commonResultVo.setLastYearData(lastYearData);

        return RestfulResultsV2.ok(commonResultVo);
    }

    @Override
    public RestfulResultsV2<CommonResultVo> statisticsNoSigned(PoliceRegulationsDTO dto) throws CondifyException {
        //需要本期，上期，去年同期的数据一并返回
        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        CommonResultVo commonResultVo = new CommonResultVo();
        commonResultVo.setDeptList(jqfxDeptConfig.getDeptList(dto.getRegionCode()));
        List<PoliceRegulationsCountVO> curData = policeRegulationMapper.statisticsNoSigned(dto);
        commonResultVo.setCurData(curData);

        //上期
        Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(startTime, endTime);
        dto.setStartTime(lastMonthTimeRange._1);
        dto.setEndTime(lastMonthTimeRange._2);
        List<PoliceRegulationsCountVO> lastMonthData = policeRegulationMapper.statisticsNoSigned(dto);
        commonResultVo.setLastMonthData(lastMonthData);

        //去年同期
        Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(startTime, endTime);
        dto.setStartTime(lastYearTimeRange._1);
        dto.setEndTime(lastYearTimeRange._2);
        List<PoliceRegulationsCountVO> lastYearData = policeRegulationMapper.statisticsNoSigned(dto);
        commonResultVo.setLastYearData(lastYearData);


        return RestfulResultsV2.ok(commonResultVo);
    }

    @Override
    public RestfulResultsV2<PerCjAnalysisResultVO> analysisPerCj(PoliceRegulationsDTO dto) throws CondifyException {
        //需要查询多个维度的数据，然后进行整合：1.民警数量 2.平均出警数 3.警情数量
        //获取部门民警数量
        List<JqfxDeptDTO> jqfxDeptDtos = jqfxDeptConfig.getJqfxDeptList();
        List<PerCjAnalysisResultVO> perCjAnalysisResultVO = null;
        //判断是查ys的用户表还是门户的用户表
        if("false".equalsIgnoreCase(webSecurityConfig.securitySkip)){
            perCjAnalysisResultVO = ysUserMapper.getDeptMjNumber(jqfxDeptDtos);
        }else{
            perCjAnalysisResultVO = extDeptMapper.getDeptMjNumber(jqfxDeptDtos);
        }
        //获取部门出警数，然后除以部门民警数量得到人均出警数
        List<DeptCjCountVO> deptCjCountVoS = policeRegulationMapper.statisticsDeptCjCount(dto, jqfxDeptDtos);;


        //警情数量
        List<String> codes = jqfxDeptDtos.stream().map(JqfxDeptDTO::getCode).collect(Collectors.toList());
        List<PoliceCountVO> jqCountResult
                = policeSituationCategoryMapper.getPoliceCountVO(pscConverter.toDTO(dto), JqConstants.JQZS, codes);

        for (PerCjAnalysisResultVO curPerCj : perCjAnalysisResultVO) {
            //人均出警数
            for (DeptCjCountVO curDeptCjCount : deptCjCountVoS) {
                if (!curPerCj.getBmbh().equalsIgnoreCase(curDeptCjCount.getBmbh())) {
                    continue;
                }
                double perCjNumber = 0d;
                int mjNumber = curPerCj.getMjNumber();
                if (mjNumber > 0) {
                    perCjNumber = (double) curDeptCjCount.getCjNumber() / curPerCj.getMjNumber();
                }

                curPerCj.setPerCjNumber(round(perCjNumber, 2));
            }

            //警情数量
            for (PoliceCountVO curPoliceCountVO : jqCountResult) {
                if (!curPerCj.getBmbh().equalsIgnoreCase(curPoliceCountVO.getBmbh())) {
                    continue;
                }

                curPerCj.setJqCount(curPoliceCountVO.getCount());
            }
        }

        return RestfulResultsV2.ok(perCjAnalysisResultVO);
    }

    /**
     * 保留两位小数
     *
     * @param value  值
     * @param places 保留位数
     * @return 处理结果
     */
    public static double round(double value, int places) {
        if (places < 0) throw new IllegalArgumentException();

        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(places, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
}
