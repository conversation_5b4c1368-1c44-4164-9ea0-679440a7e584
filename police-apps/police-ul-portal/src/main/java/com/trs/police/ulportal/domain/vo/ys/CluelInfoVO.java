package com.trs.police.ulportal.domain.vo.ys;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @date: 2024/4/1 22:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CluelInfoVO {

    private String title;

    private Integer warningStatus;


    /**
     * 预警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime warningTime;
}
