package com.trs.police.ulportal.domain.entity.policesituationtopic;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:
 *
 * @author: yang.pengfei
 * @create: 2024-08-13 11:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "dwd_jzjqxq", autoResultMap = true)
public class PoliceComprehensiveSituationEntity implements Serializable {
    private static final long serialVersionUID = -6551210314012952527L;

    /**
     * 接警单编号
     */
    @TableField(value = "jjdbh")
    private String jjdbh;
}
