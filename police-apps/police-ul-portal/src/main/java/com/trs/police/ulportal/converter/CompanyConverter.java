package com.trs.police.ulportal.converter;


import com.trs.police.ulportal.domain.dto.policesituationtopic.CompanyDTO;
import com.trs.police.ulportal.domain.entity.policesituationtopic.CompanyEntity;

import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @description 公司库转换器
 * @date 2024/10/14 10:36
 */
@Mapper(componentModel = "spring")
public interface CompanyConverter {
    /**
     * dto转实体
     *
     * @param dto dto
     * @return CompanyEntity
     */
    CompanyEntity toEntity(CompanyDTO dto);
}
