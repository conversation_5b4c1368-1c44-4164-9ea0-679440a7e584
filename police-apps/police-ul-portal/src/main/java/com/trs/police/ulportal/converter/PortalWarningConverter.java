package com.trs.police.ulportal.converter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.ulportal.common.util.ConverterHelper;
import com.trs.police.ulportal.domain.entity.PortalWarning;
import com.trs.police.ulportal.domain.vo.WarningVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 门户预警转换器
 * @date 2023/12/04
 */
@Mapper(componentModel = "spring", imports = ConverterHelper.class)
public interface PortalWarningConverter {

    /**
     * 实体vo
     *
     * @param entity 实体
     * @return vo
     */
    @Mappings({
            @Mapping(target = "typeName", expression = "java(ConverterHelper.dealTypeName(entity.getType()))"),
            @Mapping(source = "createTime", target = "createTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    })
    WarningVO toVo(PortalWarning entity);

    /**
     * 实体集合转vo集合
     *
     * @param entities 实体集合
     * @return vo集合
     */
    List<WarningVO> toVoList(List<PortalWarning> entities);

    /**
     * 实体分页转vo分页
     *
     * @param page page
     * @return vo
     */
    Page<WarningVO> toPageVo(IPage<PortalWarning> page);

}
