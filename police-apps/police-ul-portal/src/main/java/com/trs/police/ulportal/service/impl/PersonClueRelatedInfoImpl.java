package com.trs.police.ulportal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.constant.PersonClueConstant;
import com.trs.police.common.core.entity.PersonClueEntity;
import com.trs.police.common.core.utils.LoopControlUtils;
import com.trs.police.ulportal.common.config.PersonClueLabelConfig;
import com.trs.police.ulportal.common.constants.PersonClueType;
import com.trs.police.ulportal.common.enums.PersonClueWarningReasonEnum;
import com.trs.police.ulportal.domain.vo.DbzlXsVO;
import com.trs.police.ulportal.mapper.ys.DbzlXsMapper;
import com.trs.police.ulportal.mapper.ys.PersonClueMapper;
import com.trs.police.ulportal.service.ys.PersonClueMpService;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.entity.PageInfo;
import com.trs.web.entity.PageList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 线索池预警原因
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PersonClueRelatedInfoImpl {

    private final PersonClueMapper personClueMapper;

    private final PersonClueMpService personClueMpService;

    private final PersonClueLabelConfig config;

    private final DbzlXsMapper dbzlXsMapper;

    /**
     * 同步方法
     */
    public void personClueThirdInfoSynchronous() {
        LoopControlUtils.doWhile(pageListFunction(), action());
    }


    private Function<PageInfo, PageList<PersonClueEntity>> pageListFunction() {
        Function<PageInfo, PageList<PersonClueEntity>> pageListFunction = (pageInfo) -> {
            log.info("当前正在同步第{}页数据", pageInfo.getPageNum());
            PageList<PersonClueEntity> pageList = new PageList<>();
            pageList.setPageSize(pageInfo.getPageSize());
            pageList.setPageNum(pageInfo.getPageNum());
            pageList.setTotal(0L);
            try {
                Page<PersonClueEntity> result = personClueMapper.selectPage(new Page<>(pageInfo.getPageNum(), pageInfo.getPageSize())
                        ,new QueryWrapper<PersonClueEntity>().ne("clue_state",PersonClueConstant.FINISHED));
                pageList.setTotal(result.getTotal());
                pageList.setContents(result.getRecords());
                return pageList;
            } catch (Exception e) {
                log.error("同步隐性人员发生异常", e);
            }
            return pageList;
        };
        return pageListFunction;
    }

    protected Consumer<List<PersonClueEntity>> action() {
        // 执行分批次捞取并入库
        Consumer<List<PersonClueEntity>> action = (personClueEntities) -> {
            List<Long> ids = personClueEntities.stream().map(e -> e.getId()).collect(Collectors.toList());
            List<DbzlXsVO> resultList = dbzlXsMapper.selectListAll(ids);
            Map<Long, DbzlXsVO> map =CollectionUtils.isEmpty(resultList) ? new HashMap<>()
                    : resultList.stream().collect(Collectors.toMap(e -> Long.valueOf(e.getLyid()), e -> e,(o1,o2)->o1));
                for (PersonClueEntity personClueEntity : personClueEntities) {
                    /*personClueEntity.setWarningReason(config.clueTypeReasonTypes.contains(personClueEntity.getClueType())
                            ? getWarningReason(personClueEntity) : null);*/
                    DbzlXsVO dbzlXsVO = map.get(personClueEntity.getId());
                    if (Objects.nonNull(dbzlXsVO)){
                        personClueEntity.setClueState(dbzlXsVO.getRwzt().startsWith("0")
                                ? dbzlXsVO.getRwzt().replaceFirst("0","") : dbzlXsVO.getRwzt());
                        personClueEntity.setIsTb("已同步");
                        personClueEntity.setSsbmdm(dbzlXsVO.getSsbmdm());
                        personClueEntity.setSsbm(dbzlXsVO.getSsbm());
                        personClueEntity.setWordInfo(dbzlXsVO.getRwzt().equals(PersonClueConstant.FINISHED)
                                ? dbzlXsVO.getId() : null);
                    }
                }
                personClueMpService.updateBatchById(personClueEntities);
        };
        return action;
    }
    /**
     * 拼接预警原因
     *
     * @param e 参数
     * @return 预警原因
     */
    private String getWarningReason(PersonClueEntity e) {
        String format = PersonClueWarningReasonEnum.getNameByCode(e.getClueType());
        switch (e.getClueType()){
            case PersonClueType.DQ:
                //[人员名称]、[身份证号]、 [时间YYYY-DD-DD HH:MM:SS] 、 [案件名称] 、 [案件编号]、[感知源地址]
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getCaseOccurTime());
            case PersonClueType.DRUG_DRIVER:
            case PersonClueType.DRIVER_WITHOUT_LICENCE:
                //[人员名称]、[身份证号]、 [时间YYYY-DD-DD HH:MM:SS] 、 [驾车状态] 、 [车牌号] 、 [车辆型号]、 [感知源地址]
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getClueWarnTime()
                        ,e.getLicensePlate(),e.getLicensePlate(),e.getLicensePlate(),e.getLicensePlate());
            case PersonClueType.SX_PERSON:
                //[人员名称]、[身份证号]、[涉邪APP名称] 。
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getReason()
                        .replace("安装",""));
            case PersonClueType.SJ_PERSON:
                //[人员名称]、[身份证号]。
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId());
            case PersonClueType.BAT_CHECK_SH:
                //[人员名称]、[身份证号]、 [时间YYYY-DD-DD HH:MM:SS] 、 [群聊名称] 、[群聊编号]、 [聊天软件名称]。
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getClueWarnTime()
                        ,e.getGroupChatId(),e.getGroupChatId(),e.getGroupChatApp());
            case PersonClueType.SH_PERSON:
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getRiskScore()
                        , BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.mypcScore"));
            case PersonClueType.DQSC_PERSON:
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getRiskScore()
                        , BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.dqscScore"));
            case PersonClueType.PQ_PERSON:
                //[人员名称]、[身份证号]、 [人员风险分数] 、[模型风险分数阈值] 。分值阈值没写
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getRiskScore()
                        , BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.pqScore"));
            default:
                return "";
        }
    }

}
