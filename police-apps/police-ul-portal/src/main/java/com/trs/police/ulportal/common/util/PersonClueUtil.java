package com.trs.police.ulportal.common.util;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.PersonClueEntity;
import com.trs.police.ulportal.common.constants.PersonClueType;
import com.trs.police.ulportal.common.enums.PersonClueWarningReasonEnum;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.apache.commons.collections4.CollectionUtils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-08-23 11:31
 */
public class PersonClueUtil {

    /**
     * 方法
     *
     * @param entity 实体类
     * @return 结果
     */
    public static List<String> dealPersonLabels(PersonClueEntity entity){
        return StringUtils.isNotEmpty(entity.getPersonLabel())
                ? Arrays.asList(entity.getPersonLabel().split(",")) : new ArrayList<>();
    }

    /**
     * 方法
     *
     * @param entity 实体类
     * @return 结果
     */
    public static List<String> dealAjLabels(PersonClueEntity entity){
        return StringUtils.isNotEmpty(entity.getAjLabel())
                ? Arrays.asList(entity.getAjLabel().split(",")) : new ArrayList<>();
    }

    /**
     * 编制预警原因
     *
     * @param e 参数
     * @return 结果
     */
    public static String getWarningReason(PersonClueEntity e) {
        String format = PersonClueWarningReasonEnum.getNameByCode(e.getClueType());
        switch (e.getClueType()){
            case PersonClueType.DQ:
                //[人员名称]、[身份证号]、 [时间YYYY-DD-DD HH:MM:SS] 、 [案件名称] 、 [案件编号]、[感知源地址]
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId()
                        , Objects.nonNull(e.getCaptureTime()) ? e.getCaptureTime() : e.getClueWarnTime()
                        ,"案件");
            case PersonClueType.DRUG_DRIVER:
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getCaptureTime()
                        , CollectionUtils.isNotEmpty(JSONObject.parseArray(e.getDriverInfo(),JSONObject.class))
                                ? JSONObject.parseArray(e.getDriverInfo(),JSONObject.class).get(0).getString("zt") : "未知"
                        ,e.getLicensePlate(),e.getGzymc());
            case PersonClueType.DRIVER_WITHOUT_LICENCE:
                //[人员名称]、[身份证号]、 [时间YYYY-DD-DD HH:MM:SS] 、 [驾车状态] 、 [车牌号] 、 [车辆型号(暂时不管)]、 [感知源地址]
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getClueWarnTime()
                        , CollectionUtils.isNotEmpty(JSONObject.parseArray(e.getDriverInfo(),JSONObject.class))
                        ? JSONObject.parseArray(e.getDriverInfo(),JSONObject.class).get(0).getString("zt") : "未知"
                        ,e.getLicensePlate(),e.getGzymc());
            case PersonClueType.SX_PERSON:
                //[人员名称]、[身份证号]、[涉邪APP名称] 。
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getReason());
            case PersonClueType.SJ_PERSON:
                //[人员名称]、[身份证号]。
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId());
            case PersonClueType.YSLD_PERSON:
                //[人员名称]、[身份证号]。
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId());
            case PersonClueType.BAT_CHECK_SH:
                //[人员名称]、[身份证号]、 [时间YYYY-DD-DD HH:MM:SS] 、 [群聊名称] 、[群聊编号]、 [聊天软件名称]。
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getCaptureTime()
                        ,e.getGroupChatName(),e.getGroupChatApp(),e.getGroupChatId());
            case PersonClueType.SH_PERSON:
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getRiskScore()
                        ,e.getRiskScore().compareTo(Double.valueOf(BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.mypcScore"))) > 0
                                ? "超过":"未超过"
                        , BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.mypcScore"));
            case PersonClueType.DQSC_PERSON:
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getRiskScore()
                        ,e.getRiskScore().compareTo(Double.valueOf(BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.mypcScore"))) > 0
                                ? "超过":"未超过"
                        , BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.dqscScore"));
            case PersonClueType.PQ_PERSON:
                //[人员名称]、[身份证号]、 [人员风险分数] 、[模型风险分数阈值] 。分值阈值没写
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId(),e.getRiskScore()
                        ,e.getRiskScore().compareTo(Double.valueOf(BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.mypcScore"))) > 0
                                ? "超过":"未超过"
                        , BeanFactoryHolder.getEnv().getProperty("com.trs.police.modelScore.pqScore"));
            case PersonClueType.SSSQ_PERSON:
                return MessageFormat.format(format, e.getProbablyPeopleName(), e.getProbablyPeopleId(),
                        e.getCaptureTime(), e.getLicensePlate(), e.getGzymc());
            case PersonClueType.SDRYJJFX:
                return MessageFormat.format(format,StringUtils.isNotEmpty(e.getGzymc()) ? e.getGzymc() : "某地点");
            case PersonClueType.WSYP_RISK_PERSON:
                return MessageFormat.format(format,e.getProbablyPeopleName(),e.getProbablyPeopleId());
            case PersonClueType.CFBJ_RISK_PERSON:
                return MessageFormat.format(format,e.getClueWarnTime(),e.getProbablyPeopleName(),e.getProbablyPeopleId());
            case PersonClueType.MZX_RISK_PERSON:
                return MessageFormat.format(format,e.getClueWarnTime(),e.getProbablyPeopleName(),e.getProbablyPeopleId());
            default:
                return "";
        }
    }

    /**
     * 获取recordId
     *
     * @param e 线索entity
     * @return recordId
     */
    public static String getRecordId(PersonClueEntity e) {
        if (Objects.isNull(e)){
            return "";
        }
        if (Objects.isNull(e.getClueType())){
            return "";
        }
        switch (e.getClueType()){
            case PersonClueType.SSSQ_PERSON:
            case PersonClueType.DRUG_DRIVER:
                return e.getLicensePlate();
            default:
                return e.getProbablyPeopleId();
        }
    }


    /**
     * 获取recordId
     *
     * @param e 线索entity
     * @return recordId
     */
    public static String getArchivesType(PersonClueEntity e) {
        if (Objects.isNull(e)){
            return "";
        }
        if (Objects.isNull(e.getClueType())){
            return "";
        }
        switch (e.getClueType()){
            case PersonClueType.SSSQ_PERSON:
            case PersonClueType.DRUG_DRIVER:
                return "car";
            default:
                return "person";
        }
    }
}
