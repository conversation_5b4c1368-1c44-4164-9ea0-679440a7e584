<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.ulportal.mapper.ys.YsUserMapper">

    <select id="getDeptMjNumber" resultType="com.trs.police.ulportal.domain.vo.policesituationtopic.PerCjAnalysisResultVO">
        select
        d.name as bmmc,
        d.code as bmbh,
        sum(1) as mjNumber
        from t_user as u
        left join t_user_dept_relation as ud on u.id = ud.user_id
        left join t_dept as d on ud.dept_id = d.id
        where 1 = 1
        <if test="deptList != null and deptList.size > 0">
            and d.code in
            <foreach collection="deptList" item="item" open="(" separator="," close=")">
                #{item.code}
            </foreach>
        </if>
        group by d.code
    </select>
</mapper>