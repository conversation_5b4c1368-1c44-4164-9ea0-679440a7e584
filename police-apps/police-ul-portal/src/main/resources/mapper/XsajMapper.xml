<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.ulportal.mapper.XsajMapper">


    <select id="queryXsajListByTime" resultType="com.trs.police.ulportal.domain.vo.XsajVO" parameterType="com.trs.police.ulportal.domain.dto.CommonStatisticsDto">
        SELECT
            asjbh,ajywztdm
        FROM
            `ods_hik_jwzh_xsaj_aj_gx`
        <where>
            <if test="statisticsDto.createTimeStart != null">
                and STR_TO_DATE(asjfssj_asjfsjssj,'%Y-%m-%d %H:%i:%s') >= #{statisticsDto.createTimeStart}
            </if>
            <if test="statisticsDto.createTimeEnd != null">
                and STR_TO_DATE(asjfssj_asjfsjssj,'%Y-%m-%d %H:%i:%s') &lt;= #{statisticsDto.createTimeEnd}
            </if>
            <if test="ajStatusList != null and ajStatusList.size > 0">
                and ajywztdm in
                <foreach collection="ajStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>