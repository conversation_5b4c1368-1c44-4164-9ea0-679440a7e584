package com.trs.police.statistic.domain.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 带名称的指标
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class NamedIndexVO extends IndexVO {

    private static final long serialVersionUID = 9042665716031399596L;

    /**
     * 统计对象名称
     */
    private String name;

    public NamedIndexVO(String name, IndexVO indexVO) {
        super(indexVO.getCount(), indexVO.getLast());
        this.name = name;
    }

    public NamedIndexVO(String name, Integer count) {
        super(count, 0);
        this.name = name;
    }
}

