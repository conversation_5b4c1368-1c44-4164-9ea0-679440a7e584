package com.trs.police.statistic.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/5/22 16:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_statistic_analysis_report_user_relation", autoResultMap = true)
public class AnalysisReportUserRelation {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private LocalDateTime createTime;
    /**
     * 创建用户
     */
    @TableField(fill = FieldFill.INSERT, value = "create_user_id")
    private Long createUserId;
    /**
     * 创建时部门
     */
    @TableField(fill = FieldFill.INSERT, value = "create_dept_id")
    private Long createDeptId;
    /**
     * 报告id
     */
    private Long reportId;

    public AnalysisReportUserRelation(Long reportId) {
        this.reportId = reportId;
    }

}
