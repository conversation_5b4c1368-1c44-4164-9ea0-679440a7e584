package com.trs.police.statistic.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.statistic.domain.vo.HeatMapVO;
import com.trs.police.statistic.domain.vo.page.PageListVO;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 超类
 *
 * <AUTHOR>
 */
public interface AbstractMapper {

    /**
     * 计数查询
     *
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return 计数
     */
    Integer selectCount(@Param("subCaseTypes") List<String> subCaseTypes,
        @Param("deptCode") String deptCode,
        @Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询
     *
     * @param page         分页参数
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return {@link PageListVO}
     */
    Page<PageListVO> selectPage(Page<PageListVO> page,
        @Param("subCaseTypes") List<String> subCaseTypes,
        @Param("deptCode") String deptCode,
        @Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 不分页查询列表
     *
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划前缀
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return {@link PageListVO}
     */
    List<PageListVO> selectList(
        @Param("subCaseTypes") List<String> subCaseTypes,
        @Param("deptCode") String deptCode,
        @Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);


    /**
     * 热力图查询
     *
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划前缀
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return {@link HeatMapVO}
     */
    List<HeatMapVO> selectHeatList(
        @Param("subCaseTypes") List<String> subCaseTypes,
        @Param("deptCode") String deptCode,
        @Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 计数查询
     *
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @return 计数
     */
    Integer selectCountThisYear(@Param("subCaseTypes") List<String> subCaseTypes,
                                @Param("deptCode") String deptCode,
                                @Param("beginTime") LocalDateTime beginTime,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * 计数查询
     *
     * @param subCaseTypes 案件类别
     * @param deptCode     行政区划
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @param lastYearBeginTime      去年开始时间
     * @param lastYearEndTime      去年结束时间
     * @return 计数
     */
    Integer selectCountLastYear(@Param("subCaseTypes") List<String> subCaseTypes,
                                @Param("deptCode") String deptCode,
                                @Param("beginTime") LocalDateTime beginTime,
                                @Param("endTime") LocalDateTime endTime,
                                @Param("lastYearBeginTime") LocalDateTime lastYearBeginTime,
                                @Param("lastYearEndTime") LocalDateTime lastYearEndTime);
}
