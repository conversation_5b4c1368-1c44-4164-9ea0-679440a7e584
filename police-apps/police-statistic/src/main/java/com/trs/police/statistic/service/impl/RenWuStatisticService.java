package com.trs.police.statistic.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.police.statistic.mapper.RenWuStatisticMapper;
import com.trs.police.statistic.service.ICommonSqlStatisticService;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：<a href="http://10.18.20.137:8082/browse/XMKFB-8682">XMKFB-8682</a>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/6 11:44
 * @since 1.0
 */
@Service
@AllArgsConstructor
public class RenWuStatisticService implements ICommonSqlStatisticService {

    private final RenWuStatisticMapper mapper;

    @Override
    public List<List<Tuple2<String, String>>> querySql(String startTime, String endTime) throws ServiceException {
        PreConditionCheck.checkNotEmpty(startTime, new ServiceException("参数startTime不能为空！"));
        PreConditionCheck.checkNotEmpty(endTime, new ServiceException("参数endTime不能为空！"));
        return mapper.statisticSql(startTime, endTime)
                .stream()
                .map(it -> {
                    long allCount = Optional.ofNullable(it.getAllCount()).orElse(0L);
                    long sign = Optional.ofNullable(it.getSign()).orElse(0L);
                    long assignPerson = Optional.ofNullable(it.getAssignPerson()).orElse(0L);
                    long feedbackPerson = Optional.ofNullable(it.getFeedbackPerson()).orElse(0L);
                    String signRate = allCount > 0L ? String.format("%.2f", sign * 100.0 / allCount) : "--";
                    String assignPersonRate = allCount > 0L ? String.format("%.2f", assignPerson * 100.0 / allCount) : "--";
                    String feedbackPersonRate = allCount > 0L ? String.format("%.2f", feedbackPerson * 100.0 / allCount) : "--";
                    return List.of(
                            Tuple.of("单位", it.getDymc()),
                            Tuple.of("情报线索下发数量", Long.toString(allCount)),
                            Tuple.of("签收数量", Long.toString(sign)),
                            Tuple.of("签收率", signRate + "%"),
                            Tuple.of("流转数量", Long.toString(assignPerson)),
                            Tuple.of("流转率", assignPersonRate + "%"),
                            Tuple.of("反馈数量", Long.toString(feedbackPerson)),
                            Tuple.of("反馈率", feedbackPersonRate + "%")
                    );
                }).collect(Collectors.toList());
    }

    @Override
    public String key() {
        return "renwu";
    }

    @Override
    public String desc() {
        return "任务数据统计";
    }
}
