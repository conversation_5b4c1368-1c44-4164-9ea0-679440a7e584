package com.trs.police.statistic.controller;

import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.statistic.domain.request.CountRequest;
import com.trs.police.statistic.domain.vo.model.AjCountVO;
import com.trs.police.statistic.domain.vo.model.JqCountVO;
import com.trs.police.statistic.domain.vo.model.JqDetailVO;
import com.trs.police.statistic.service.ModelService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 大模型查询接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/public/model")
public class ModelController {

    @Resource
    private ModelService modelService;

    /**
     * 统计警情
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/jq/count")
    public JqCountVO countJq(@RequestBody CountRequest request) {
        return modelService.countJq(request);
    }

    /**
     * 统计子类警情
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/jq/count/detail")
    public List<JqDetailVO> countJqSub(@RequestBody CountRequest request) {
        return modelService.countJqSub(request);
    }

    /**
     * 统计app
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/jq/count/app")
    public List<JqDetailVO> countJqApp(@RequestBody CountRequest request) {
        return modelService.countJqApp(request);
    }

    /**
     * 统计案件
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/aj/count")
    public List<AjCountVO> countAj(@RequestBody CountRequest request) {
        return modelService.countAj(request);
    }

    /**
     * 统计警情
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/aj/detail")
    public List<JqDetailVO> getCaseDetail(@RequestBody CountRequest request) {
        return modelService.getCaseDetail(request);
    }

    /**
     * 下载报告
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/report")
    public FileInfoVO getReport(@RequestBody CountRequest request) {
        return modelService.getReport(request);
    }
}
