package com.trs.police.subject.test;

import com.trs.police.subject.FxSubjectApp;
import com.trs.police.subject.fx.service.hideout.IHideoutSync;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * *@author:wen.wen
 * *@create 2024-06-06 23:57
 **/
@SpringBootTest(classes = FxSubjectApp.class)
public class HideoutSyncServiceTests {

    @Resource
    private IHideoutSync hideoutSyncService;

    @Test
    public void testSync() {
        hideoutSyncService.syncFxHideout();
        assert true;
    }
}
