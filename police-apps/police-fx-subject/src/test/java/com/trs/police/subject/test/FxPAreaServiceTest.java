package com.trs.police.subject.test;

import com.trs.police.subject.FxSubjectApp;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.fx.service.FxAreaService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = FxSubjectApp.class)
public class FxPAreaServiceTest {

    @Resource
    private FxAreaService fxAreaService;

//    @Test
//    public void controlAreaTest() {
//        StatisticsDTO dto = new StatisticsDTO();
//        dto.setStartTime("2024-04-25 00:00:00");
//        dto.setEndTime("2024-04-25 23:59:59");
//        List<ControlAreaVO> datas = fxAreaService.controlArea(dto, new PageParams(1, 10)).getDatas();
//        System.out.println();
//    }

    @Test
    public void getSensitiveTimesTest() {
        PersonDTO dto = new PersonDTO();
        dto.setStartTime("2024-05-01");
        dto.setEndTime("2024-05-31");
        List<String> datas = fxAreaService.getSensitiveTimes(dto).getDatas();
        System.out.println();
    }

    @Test
    public void getSensitiveTimeNodeTitleTest() {
        List<String> datas = fxAreaService.getSensitiveTimeNodeTitle("2024-05-15", "jz").getDatas();
        System.out.println();
    }

}
