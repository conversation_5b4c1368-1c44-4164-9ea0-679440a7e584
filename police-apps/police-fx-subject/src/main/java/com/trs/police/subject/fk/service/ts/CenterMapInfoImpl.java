package com.trs.police.subject.fk.service.ts;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.subject.common.mapper.DataOverviewMapper;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.WarningFkrxyjMapper;
import com.trs.police.subject.domain.dto.FkTsDto;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.subject.domain.vo.fk.CenterMapStatisticVO;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 反恐态势-中央地图
 */
@Service
public class CenterMapInfoImpl extends AbstractClass<CenterMapStatisticVO, FkTsDto> {

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private DataOverviewMapper dataOverviewMapper;

    @Autowired
    private WarningFkrxyjMapper fkrxyjMapper;

    @Override
    public List<CenterMapStatisticVO> search(SubjectSceneContext<FkTsDto> context) {
        FkTsDto dto = context.getDto();
        String areaCode = StringUtils.isNotEmpty(dto.getAreaCode()) ? dto.getAreaCode()
                : BeanFactoryHolder.getEnv().getProperty("com.trs.fk.tsBigScreen.areaCode", "510500");
        dto.setAreaCode(AreaUtils.areaPrefix(areaCode));
        List<KeyValueVO> keyValueVOList = getDistrictList(areaCode);
        //建档统计map，key为区域code
        Map<String, Long> jdMap = getJdCountMap(dto);
        //入区统计map，key为区域code
        Map<String, Long> rqMap = getRqMap(dto);
        //预警统计
        Map<String, Long> rxyjMap = getRxyjMap(dto);
        //警情统计
        Map<String, Long> jqMap = getJqMap(dto);
        List<String> areaCodeList = getDistrictList(areaCode).stream().map(e -> e.getKey()).collect(Collectors.toList());
        List<CenterMapStatisticVO> list = new ArrayList<>();
        Map<String, String> districtMap = CollectionUtils.isEmpty(keyValueVOList)
                ? new HashMap<>() : keyValueVOList.stream().collect(Collectors.toMap(KeyValueVO::getKey, KeyValueVO::getValue));
        for (String code : areaCodeList) {
            CenterMapStatisticVO vo = new CenterMapStatisticVO();
            vo.setKey(code);
            vo.setName(districtMap.get(code));
            vo.setJdCount(jdMap.getOrDefault(code,0L));
            vo.setRqCount(rqMap.getOrDefault(code,0L));
            vo.setWarningCount(rxyjMap.getOrDefault(code,0L));
            vo.setJqCount(jqMap.getOrDefault(code,0L));
            list.add(vo);
        }
        return list;
    }

    @Override
    public String key() {
        return "centerMapInfo";
    }

    @Override
    public String desc() {
        return "中央地图";
    }

    /**
     * 获取警情统计数量
     *
     * @param dto dto
     * @return 结果
     */
    private Map<String, Long> getJqMap(FkTsDto dto) {
        List<CountItem> jqList = fkPersonMapper.getRelatedJqCount(dto,1);
        Map<String, Long> jqMap = CollectionUtils.isEmpty(jqList)
                ? new HashMap<>() : jqList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        return jqMap;
    }

    /**
     * 获取预警map
     *
     * @param dto dto
     * @return 结果
     */
    private Map<String, Long> getRxyjMap(FkTsDto dto) {
        List<WarningFkrxyjEntity> rxyjList = fkrxyjMapper.selectList(new QueryWrapper<WarningFkrxyjEntity>()
                .ge(StringUtils.isNotEmpty(dto.getStartTime()),"capture_time", dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()),"capture_time", dto.getEndTime()));
        Map<String, Long> idCardCountMap = rxyjList.stream().collect(Collectors.groupingBy(WarningFkrxyjEntity::getIdCard,Collectors.counting()));
        List<String> idCards = CollectionUtils.isEmpty(rxyjList) ? new ArrayList<>()
                : rxyjList.stream().map(WarningFkrxyjEntity::getIdCard).distinct().collect(Collectors.toList());
        List<CountItem> yjList = fkPersonMapper.selectFkPerson(dto,idCards);
        //根据区域计算预警数量
        Map<String, Long> rxyjMap = yjList.stream().collect(Collectors.groupingBy(CountItem::getKey,
                Collectors.summingLong(item -> idCardCountMap.getOrDefault(item.getName(), 0L))));
        return rxyjMap;
    }

    /**
     * 获取入区map
     *
     * @param dto dto
     * @return 结果
     */
    private Map<String, Long> getRqMap(FkTsDto dto) {
        String modelName = BeanFactoryHolder.getEnv().getProperty("com.trs.fk.tsBigScreen.hitModelName", "首次入区");
        List<WarningFkrxyjEntity> rxyjList = fkrxyjMapper.selectList(new QueryWrapper<WarningFkrxyjEntity>()
                .like("warning_model", modelName)
                .ge(StringUtils.isNotEmpty(dto.getStartTime()),"capture_time", dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()),"capture_time", dto.getEndTime()));
        List<String> idCards = CollectionUtils.isEmpty(rxyjList) ? new ArrayList<>()
                : rxyjList.stream().map(WarningFkrxyjEntity::getIdCard).distinct().collect(Collectors.toList());
        List<CountItem> rqList = fkPersonMapper.getModelHitCount(dto,idCards);
        Map<String, Long> rqMap = CollectionUtils.isEmpty(rqList)
                ? new HashMap<>() : rqList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        return rqMap;
    }


    /**
     * 获取建档map
     *
     * @param dto dto
     * @return 结果
     */
    private Map<String, Long> getJdCountMap(FkTsDto dto) {
        //建档统计
        List<CountItem> jdList = dataOverviewMapper.getBdgkItemMap(1, dto);
        Map<String, Long> jdMap = CollectionUtils.isEmpty(jdList)
                ? new HashMap<>() : jdList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        return jdMap;
    }
}
