package com.trs.police.subject.fk.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.ImportantAreaMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.fk.entity.WarningFkrxyjEsEntity;
import com.trs.police.subject.fk.repository.FkWarningRepository;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 风险防控-区域人员轨迹
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class FkAreaPersonTrackListServiceImpl extends AbstractPersonControlAnalysisImpl<PersonTrackVO> {

    @Autowired
    private ImportantAreaMapper importantAreaMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private FkTrackListServiceImpl fkTrackListServiceImpl;

    @Autowired
    private FkWarningRepository fkWarningRepository;

    @Autowired
    private DictService dictService;


    @Override
    protected RestfulResultsV2<PersonTrackVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            if (StringUtils.isNullOrEmpty(dto.getAreaId())){
                return RestfulResultsV2.ok(new ArrayList<>());
            }
            //默认固定返回100条 因为这个场景只需要展示最新的轨迹并撒点，不需要进行分页，分页的话执行count操作耗时太多
            Integer limit = BeanFactoryHolder.getEnv().getProperty("subject.fk.track.limit", Integer.class, 100);
            Page<PersonTrackVO> page = new Page<>(1, limit);
            page.setSearchCount(false);
            Page<PersonTrackVO> voList = fkPersonMapper.fkPersonTrackList(dto,page);
            // 设置总数
            if (CollectionUtils.isEmpty(voList.getRecords())) {
                return RestfulResultsV2.ok(new ArrayList<>())
                        .addTotalCount(voList.getTotal())
                        .addPageNum(context.getPageNum())
                        .addPageSize(context.getPageSize());
            }
            //  获取人员标签名称,已建档的使用档案中的人员标签,未建档的使用最新的一次研判标签
            voList.getRecords().forEach(personVO -> {
                if (personVO.getOnRecord().equals(0)){
                    fkTrackListServiceImpl.findLastJudgedLabel(personVO);
                }
                fkTrackListServiceImpl.getPersonLabels(personVO);
            });
            List<String> areaPointList = getAreaPointList(Long.valueOf(dto.getAreaId()));
            List<String> idCardList = voList.getRecords().stream().map(PersonVO::getIdCard).distinct().collect(Collectors.toList());
            Expression expression = buildEsSearchExpression(idCardList, areaPointList);
            //检索es表获取轨迹
            List<WarningFkrxyjEsEntity> datas = fkWarningRepository.findList(expression);
            Map<String, WarningFkrxyjEsEntity> entityMap = datas.stream()
                    .collect(Collectors.groupingBy(
                            WarningFkrxyjEsEntity::getIdCard,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(WarningFkrxyjEsEntity::getCreateTime)),
                                    Optional::get
                            )
                    ));
            Map<Long, String> dictMap = dictService.commonSearch("control_warning_source_type", null, null, null)
                    .stream().collect(Collectors.toMap(Dict2VO::getCode, Dict2VO::getName));
            voList.getRecords().forEach(personVO -> {
                WarningFkrxyjEsEntity entity = entityMap.getOrDefault(personVO.getIdCard(),  null);
                if (Objects.nonNull(entity)){
                    Long sourceType = JSONObject.parseObject(entity.getWarningSource()).getLong("sourceType");
                    personVO.setJdwgs84(entity.getLongitude());
                    personVO.setWdwgs84(entity.getLatitude());
                    personVO.setGjlx(dictMap.getOrDefault(sourceType,null));
                    personVO.setActivityAddress(entity.getCaptureAddress());
                }
            });
            return RestfulResultsV2.ok(voList.getRecords())
                    .addTotalCount(Long.valueOf(limit))
                    .addPageNum(context.getPageNum())
                    .addPageSize(context.getPageSize());
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }

    private List<String> getAreaPointList(Long areaId) {
        ImportantAreaEntity areaEntities = importantAreaMapper.selectById(areaId);
        return handleGeo(areaEntities.getGeometries());
    }

    private List<String> handleGeo(GeometryVO[] geometries) {
        return Arrays.stream(geometries).map(geometryVO -> {
                    if (geometryVO.getType().equals("circle")) {
                        String replace = geometryVO.getGeometry().replace("POINT(", "").replace(")", "");
                        return (String.format("circle (%s %s)", replace, geometryVO.getProperties().getRadius()));
                    }
                    if (geometryVO.getType().equals("point")) {
                        return null;
                    }
                    return geometryVO.getGeometry();
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Expression buildEsSearchExpression(List<String> idCardList,List<String> geo) {
        return ExpressionBuilder.And(
                ExpressionBuilder.Condition("id_card", Operator.In, idCardList),
                ExpressionBuilder.Condition("location", Operator.WKT, geo));
    }
    @Override
    public String key() {
        return "fk-areaPersonTrackList";
    }

    @Override
    public String desc() {
        return "区域人员轨迹";
    }
}
