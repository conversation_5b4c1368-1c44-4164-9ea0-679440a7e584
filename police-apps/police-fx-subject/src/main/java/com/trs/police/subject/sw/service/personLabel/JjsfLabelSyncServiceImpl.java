package com.trs.police.subject.sw.service.personLabel;

import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.fx.entity.Person;
import com.trs.police.subject.fx.manager.FxActionExcavateManager;
import com.trs.police.subject.fx.manager.FxLabelSourceCalculationManager;
import com.trs.police.subject.common.mapper.FxWarningMapper;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.police.subject.common.repository.ThemeGjxxbRepository;
import com.trs.police.subject.fx.service.impl.FxWarningServiceImpl;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/07/24
 * @description: 进京上访标签同步
 */
@Service
@Slf4j
public class JjsfLabelSyncServiceImpl extends AbstractLabelSyncServiceImpl {

    @Resource
    private PersonMapper personMapper;
    @Resource
    private ThemeGjxxbRepository themeGjxxbRepository;
    @Resource
    private FxWarningMapper fxWarningMapper;
    @Resource
    private FxActionExcavateManager fxActionExcavateManager;
    @Resource
    private FxLabelSourceCalculationManager fxLabelSourceCalculationManager;
    @Resource
    private FxWarningServiceImpl fxWarningService;

    @Override
    protected Consumer<List<Person>> action() {
        String jjry = BeanFactoryHolder.getEnv().getProperty("fx.subject.label.jjry", "进京人员");
        // 执行分批次捞取并入库
        Consumer<List<Person>> action = (personList) -> {
            if (!CollectionUtils.isEmpty(personList)) {
                // 检索轨迹表(上京、上省、本市上访轨迹)
                List<Person> results = personList.stream().filter(person -> {
                    // 上京
                    Expression sjExp = ExpressionBuilder.And(
                            ExpressionBuilder.Condition("tzlx", Operator.FullTextEqualWholeWord, "身份证"),
                            ExpressionBuilder.Condition("tzzhm", Operator.FullTextEqualWholeWord, person.getIdCard()),
                            ExpressionBuilder.Condition("province", Operator.Equal, "11")
                    );
                    Long sjCount = themeGjxxbRepository.count(sjExp);
                    // 上省、上成都
                    Expression ssExp = ExpressionBuilder.And(
                            ExpressionBuilder.Condition("tzlx", Operator.FullTextEqualWholeWord, "身份证"),
                            ExpressionBuilder.Condition("tzzhm", Operator.FullTextEqualWholeWord, person.getIdCard()),
                            ExpressionBuilder.Condition("province", Operator.Equal, "51"),
                            ExpressionBuilder.Condition("city", Operator.Equal, "01")
                    );
                    Long ssCount = themeGjxxbRepository.count(ssExp);
                    // 去过北京或者成都都算 进京人员
                    return (sjCount > 0L || ssCount > 0L);
                }).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(results)) {
                    List<String> idCards = results.stream().map(Person::getIdCard).collect(Collectors.toList());
                    save2FxWarning(idCards, jjry, true);
                }
            }
        };
        return action;
    }

    @Override
    protected String getSubjectType() {
        return "jz";
    }

}
