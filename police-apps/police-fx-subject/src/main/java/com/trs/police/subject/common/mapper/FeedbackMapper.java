package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.subject.domain.dto.ClueExcavateDTO;
import com.trs.police.subject.domain.entity.task.FxFeedback;
import com.trs.police.subject.domain.vo.FeedbackRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: dingkeyu
 * @date: 2024/10/22
 * @description:
 */
@Mapper
public interface FeedbackMapper extends BaseMapper<FxFeedback> {

    /**
     * 反馈记录
     *
     * @param dto dto
     * @param page page
     * @return {@link Page}<{@link FeedbackRecordVO}>
     */
    Page<FeedbackRecordVO> feedbackRecords(@Param("dto") ClueExcavateDTO dto, Page<FeedbackRecordVO> page);
}
