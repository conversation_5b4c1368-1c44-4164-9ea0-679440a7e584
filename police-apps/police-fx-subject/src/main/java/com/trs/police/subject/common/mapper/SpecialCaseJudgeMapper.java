package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.subject.domain.dto.SpecialCaseJudgeDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.entity.SpecialCaseJudge;
import com.trs.police.subject.domain.vo.SpecialCaseJudgeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: tang.shuai
 * @date: 2024/04/23
 * @description: tb_fx_special_case_judge表查询接口
 */
@Mapper
public interface SpecialCaseJudgeMapper extends BaseMapper<SpecialCaseJudge> {

    /**
     * 专案研判列表
     *
     * @param dto dto
     * @param page            page
     * @return {@link PageResult}<{@link SpecialCaseJudgeVO}>
     */
    Page<SpecialCaseJudgeVO> specialCaseJudgeList(@Param("dto") SpecialCaseJudgeDTO dto, Page<Object> page);


    /**
     * 专案数统计
     *
     * @param dto dto
     * @return {@link Long}
     */
    Long specialCaseStatistics(@Param("dto") StatisticsDTO dto);
}
