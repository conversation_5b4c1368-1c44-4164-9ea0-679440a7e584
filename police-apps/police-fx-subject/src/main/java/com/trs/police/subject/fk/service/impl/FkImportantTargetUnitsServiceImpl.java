package com.trs.police.subject.fk.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.ImportantAreaMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.ImportantAreaVO;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;

/**
 * 风险防控-重点目标单位
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class FkImportantTargetUnitsServiceImpl extends AbstractPersonControlAnalysisImpl<ImportantAreaVO> {

    @Autowired
    private ImportantAreaMapper importantAreaMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;


    @Override
    protected RestfulResultsV2<ImportantAreaVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            Page<Object> page = new Page<>(context.getPageNum(), context.getPageSize());
            Page<ImportantAreaVO> pageList = importantAreaMapper.fkImportantTargetUnits(dto, page);
            if (CollectionUtils.isEmpty(pageList.getRecords())) {
                return RestfulResultsV2.ok(new ArrayList<>())
                        .addTotalCount(pageList.getTotal())
                        .addPageNum(context.getPageNum())
                        .addPageSize(context.getPageSize());
            }
            return RestfulResultsV2.ok(pageList.getRecords())
                    .addTotalCount(pageList.getTotal())
                    .addPageNum(context.getPageNum())
                    .addPageSize(context.getPageSize());
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }

    @Override
    public String key() {
        return "fk-importantTargetUnits";
    }

    @Override
    public String desc() {
        return "重点目标单位";
    }
}
