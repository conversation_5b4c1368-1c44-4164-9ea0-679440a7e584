package com.trs.police.subject.common.mapper;

import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.subject.domain.dto.FkTsDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据总览mapper
 */
@Mapper
public interface DataOverviewMapper {

    /**
     * 获取本地库数量
     *
     * @param status 状态，是否建档：0为未建档，1为已建档
     * @param dto dto
     * @return 本地库数量
     */
    List<CountItem> getBdgkkItem(@Param("status") Integer status,@Param("dto") FkTsDto dto);

    /**
     * 获取本地库数量
     *
     * @param status 状态，是否建档：0为未建档，1为已建档
     * @param dto dto
     * @return 本地库数量
     */
    List<CountItem> getBdgkItemMap(@Param("status") Integer status,@Param("dto") FkTsDto dto);

    /**
     * 获取重点单位数量
     *
     * @param dto dto
     * @return 重点单位数量
     */
    CountItem getZddwItem(@Param("dto") FkTsDto dto);

    /**
     * 获取群体数量
     *
     * @param dto dto
     * @return 群体数量
     */
    List<Long> getQtIds(@Param("dto") FkTsDto dto);

    /**
     * 获取线索id
     *
     * @param groupIds 群体id
     * @param dto dto
     * @return 线索id
     */
    List<Long> getXsId(@Param("groupIds")List<Long> groupIds,@Param("dto") FkTsDto dto);

    /**
     * 获取反恐人员预警
     *
     * @param dto dto
     * @return 预警
     */
    List<CountItem> selectWarning(@Param("dto") FkTsDto dto);

    /**
     * 获取涉疆人员count
     *
     * @return countItem
     */
    @Select("select count(1) as `count` from FK_P_60")
    CountItem getSjItem();
}
