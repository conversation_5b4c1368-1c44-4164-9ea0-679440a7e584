package com.trs.police.subject.jz.service.scene;


import com.trs.police.subject.domain.dto.SubjectJqtsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PoliceSubjectStatisticsVO;
import com.trs.police.subject.common.mapper.JqtsAnalyseMapper;
import com.trs.police.subject.common.service.scene.ISubjectStatisticScene;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.List;


/**
 * 警综警情类别service实现类
 *
 * <AUTHOR>
 * @date 2024/07/10
 */
@Service
public class JzJqTypeServiceImpl implements ISubjectStatisticScene<PoliceSubjectStatisticsVO, SubjectJqtsDTO> {
    @Autowired
    private JqtsAnalyseMapper jqtsAnalyseMapper;

    @Override
    public List<PoliceSubjectStatisticsVO> search(SubjectSceneContext<SubjectJqtsDTO> context) {
        //获取警综警情指定时间段数量前十的警情
        List<PoliceSubjectStatisticsVO> list =  jqtsAnalyseMapper.selectJzjqCount(context,context.getTopCondition());
        for (PoliceSubjectStatisticsVO vo : list) {
            if (vo.getName()==null || vo.equals("")){
                vo.setName("其他");
            }
        }
        return list;
    }

    @Override
    public String key() {
        return "jq-type-analysis";
    }

    @Override
    public String desc() {
        return "警情类别分析";
    }
}
