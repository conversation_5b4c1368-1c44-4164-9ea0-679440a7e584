package com.trs.police.subject.sa.service.gaTsBigscreen;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.subject.gats.constant.GaTsConstant;
import com.trs.police.subject.domain.dto.SituationStatisticDto;
import com.trs.police.subject.common.mapper.ProfileCaseEntityMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * impl
 */
@Service
public class CaseTsStatisticsImpl extends AbstractService<CountItem> {

    @Autowired
    private ProfileCaseEntityMapper profileCaseMapper;


    @Override
    public List<CountItem> doStatistic(SituationStatisticDto dto){

        // 2. 统计t_profile_case表数据
        String caseType = BeanFactoryHolder.getEnv().getProperty("com.trs.fxSubject.gaTsBigScreen.caseTypeKey","");
        List<String> caseTypeList = Arrays.asList(caseType.split(","));
        String districtCodePrefix = getCurrentUserDistrict(AuthHelper.getNotNullUser());
        List<KeyValueVO> caseStats = profileCaseMapper.countGroupByDate(dto,new ArrayList<>(),
                districtCodePrefix,caseTypeList);
        Map<String, Long> caseCountMap = caseStats.stream()
                .collect(Collectors.toMap(
                        KeyValueVO::getKey,
                        kv -> Long.parseLong(kv.getValue()),
                        (v1, v2) -> v1
                ));
        // 3. 生成近5日的日期列表
        List<String> dateList = TimeUtils.getDateList(dto.getStartTime(), dto.getEndTime(),
                null, "MM-dd", true, false);

        // 4. 计算每日总数并创建CountItem列表
        List<CountItem> children = dateList.stream().map(date -> {
            CountItem item = new CountItem();
            item.setKey(date);
            item.setName(date);
            item.setShowName(date);
            // 获取两个表的数量并相加
            long caseCount = caseCountMap.getOrDefault(date, 0L);
            item.setCount(caseCount);
            return item;
        }).collect(Collectors.toList());

        // 5. 计算近5日均值
        double avgCount = children.stream()
                .mapToLong(CountItem::getCount)
                .average()
                .orElse(0.0);
        avgCount = Double.valueOf(String.format("%.2f", avgCount));
        // 6. 统计近24小时数据
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.minusHours(24);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS);
        long recentCaseCount = profileCaseMapper.countRecent24Hours(yesterday.format(dateTimeFormatter),
                now.format(dateTimeFormatter),districtCodePrefix,new ArrayList<>(),caseTypeList);

        // 创建返回结果
        CountItem result = new CountItem();
        result.setChildren(children);

        // 设置其他属性
        JSONObject otherProperties = new JSONObject();
        otherProperties.put("avgCount", avgCount);
        otherProperties.put("recentCount", recentCaseCount);
        otherProperties.put("blblCount",caseTypeCount(dto,districtCodePrefix,"blbl"));
        otherProperties.put("slseCount",caseTypeCount(dto,districtCodePrefix,"slse"));
        otherProperties.put("qincaiCount",caseTypeCount(dto,districtCodePrefix,"qincai"));
        otherProperties.put("szCount",caseTypeCount(dto,districtCodePrefix,"sz"));
        result.addOtherProperties(otherProperties);

        return Arrays.asList(result);
    }

    /**
     * 根据类型获取数量
     *
     * @param dto dto
     * @param districtCodePrefix 区域代码前缀
     * @param typeKind 类型
     * @return 数量
     */
    public Integer caseTypeCount(SituationStatisticDto dto, String districtCodePrefix, String typeKind){
        String caseType = BeanFactoryHolder.getEnv().getProperty("com.trs.fxSubject.gaTsBigScreen." + typeKind,"");
        List<String> caseTypeList = Arrays.asList(caseType.split(","));
        List<KeyValueVO> resultList = profileCaseMapper.countGroupByDate(dto,new ArrayList<>(),
                districtCodePrefix,caseTypeList);
        if (CollectionUtils.isEmpty(resultList)){
            return 0;
        }
        return resultList.stream().mapToInt(e -> Integer.valueOf(e.getValue())).sum();
    }

    @Override
    public String getServiceKey() {
        return GaTsConstant.CASE_TS_STATISTICS;
    }
}
