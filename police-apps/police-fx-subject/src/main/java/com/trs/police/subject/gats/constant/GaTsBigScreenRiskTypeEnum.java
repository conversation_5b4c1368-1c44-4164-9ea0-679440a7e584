package com.trs.police.subject.gats.constant;


import lombok.Getter;

/**
 * fk模型名称
 */
public enum GaTsBigScreenRiskTypeEnum {

    PW("平稳", 1),
    LOW_RISK("低风险", 2),
    MID_RISK("中风险", 3),
    HEIGH_RISK("高风险", 4);

    /**
     * name
     */
    private String name;

    /**
     * type
     */
    @Getter
    private Integer type;

    /**
     * 构造器
     *
     * @param name   name
     * @param type type
     */
    GaTsBigScreenRiskTypeEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    /**
     * 获取db名称
     *
     * @return 结果
     */
    public String getDbModalName() {
        return name;
    }

    /**
     * 根据db名获取显示名
     *
     * @param type type
     * @return name
     */
    public static String getNameByType(Integer type) {
        for (GaTsBigScreenRiskTypeEnum fkModalNameEnum : GaTsBigScreenRiskTypeEnum.values()) {
            if (type.equals(fkModalNameEnum.getType())) {
                return fkModalNameEnum.name;
            }
        }
        return "";
    }

}
