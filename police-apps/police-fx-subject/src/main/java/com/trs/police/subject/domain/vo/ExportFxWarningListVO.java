package com.trs.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 模型预警列表导出VO
 *
 * <AUTHOR>
 * @date 2024/04/23
 */
@Data
public class ExportFxWarningListVO {

    /**
     * 预警内容
     */
    @ExcelProperty("预警内容")
    private String content;

    /**
     * 预警类型
     */
    @ExcelProperty("预警类型")
    private String model;

    /**
     * 预警标签
     */
    @ExcelProperty("预警标签")
    private String warningTags;

    /**
     * 预警状态
     */
    @ExcelProperty("预警状态")
    private String warningStatus;

    /**
     * 预警时间
     */
    @ExcelProperty("预警时间")
    private String warningTime;

    /**
     * 预警等级
     */
    @ExcelProperty("预警级别")
    private String warningLevel;

}
