package com.trs.police.subject.sw.service.scene;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.subject.domain.dto.ClueExcavateDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.GroupClueExcavateListVO;
import com.trs.police.subject.helper.DistrictHelper;
import com.trs.police.subject.common.mapper.FxGroupExcavateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * sw专题-线索挖掘-群体线索挖掘实现类
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class SwClueGroupExcavateImpl extends AbstractSwClueExcavateAnalysisImpl<GroupClueExcavateListVO> {

    @Autowired
    private FxGroupExcavateMapper fxGroupExcavateMapper;

    @Autowired
    private DistrictHelper districtHelper;

    @Autowired
    private SwSituationAnalysisMidAreaStatisticsImpl swSituationAnalysisMidAreaStatistics;

    @Override
    protected List<GroupClueExcavateListVO> doSearch(SubjectSceneContext<ClueExcavateDTO> context) {
        try {
            ClueExcavateDTO dto = context.getDto();
            dto.setSubjectType(districtHelper.configSubjectType(dto.getSubjectType()));
            dto.setPageNum(context.getPageNum());
            dto.setPageSize(context.getPageSize());
            // 查询群体线索挖掘
            Page<GroupClueExcavateListVO> pageList = fxGroupExcavateMapper.swGroupClueList(dto,
                    new Page<>(context.getPageNum(), context.getPageSize()));
            // 设置总数
            this.total = pageList.getTotal();
            //获取群体名称和群体分数
            getGroupName(pageList.getRecords());
            return pageList.getRecords();
        } catch (Exception e) {
            throw new RuntimeException("查询群体线索挖掘异常", e);
        }
    }

    /**
     * 查询真实姓名
     *
     * @param voList voList
     */
    private void getGroupName(List<GroupClueExcavateListVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        List<Long> groupIds = voList.stream().map(GroupClueExcavateListVO::getGroupId).collect(Collectors.toList());
        Map<Long, String> map = getNameMap(groupIds);
        Map<Long, Double> grupScoreMap = getGroupScore(groupIds);
        voList.forEach(vo -> {
            vo.setGroupName(map.get(vo.getGroupId()));
            vo.setRiskScore(grupScoreMap.get(vo.getGroupId()));
        });
    }

    private Map<Long, Double> getGroupScore(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        List<GroupClueExcavateListVO> personList = fxGroupExcavateMapper.selectGroupNameByGroupIds(groupIds);
        return personList.stream().collect(Collectors.toMap(GroupClueExcavateListVO::getGroupId,
                GroupClueExcavateListVO::getRiskScore, (v1, v2) -> v1));
    }

    private Map<Long, String> getNameMap(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        List<GroupClueExcavateListVO> personList = fxGroupExcavateMapper.selectGroupNameByGroupIds(groupIds);
        return personList.stream().collect(Collectors.toMap(GroupClueExcavateListVO::getGroupId,
                GroupClueExcavateListVO::getGroupName, (v1, v2) -> v1));
    }


    @Override
    public String key() {
        return "sw-clueGroupExcavate";
    }

    @Override
    public String desc() {
        return "群体线索挖掘";
    }
}
