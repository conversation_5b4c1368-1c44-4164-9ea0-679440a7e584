package com.trs.police.subject.sw.task;

import com.trs.police.subject.sw.service.personLabel.SwBskqLabelSyncServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @author: dingkeyu
 * @date: 2024/07/05
 * @description: 同步sw标签定时任务
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "com.trs.schedule.bskq.task", name = "enable", havingValue = "true")
public class SyncSwBskqTask {

    @Autowired
    private SwBskqLabelSyncServiceImpl swBskqLabelSyncService;

    /**
     *  sw人员标签同步
     */
    @Scheduled(cron = "${com.trs.schedule.bskq.task.cron:0 0 0 * * ?}")
    public void run() {
        try {
            log.info("开始同步sw人员本市跨区标签");
            swBskqLabelSyncService.syncPersonLabel();
        } catch (Exception e) {
            log.error("sw人员本市跨区同步失败", e);
        }
    }
}
