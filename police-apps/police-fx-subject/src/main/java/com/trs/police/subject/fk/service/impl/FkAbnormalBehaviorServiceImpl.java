package com.trs.police.subject.fk.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.FkWarningMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 风险防控-人员异常行为
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class FkAbnormalBehaviorServiceImpl extends AbstractPersonControlAnalysisImpl<PersonTrackVO> {

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private FkWarningMapper fkWarningMapper;

    @Autowired
    private FkTrackListServiceImpl fkTrackListServiceImpl;

    @Autowired
    private DictService dictService;

    @Override
    protected RestfulResultsV2<PersonTrackVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            Page<PersonTrackVO> page = new Page<>(context.getPageNum(), context.getPageSize());
            Page<PersonTrackVO> voList = fkPersonMapper.fkPersonTrackListV2(dto,page);
            if (CollectionUtils.isEmpty(voList.getRecords())) {
                return RestfulResultsV2.ok(new ArrayList<>())
                        .addTotalCount(voList.getTotal())
                        .addPageNum(context.getPageNum())
                        .addPageSize(context.getPageSize());
            }
            //  获取人员标签名称,已建档的使用档案中的人员标签,未建档的使用最新的一次研判标签
            voList.getRecords().forEach(personVO -> {
                if (personVO.getOnRecord().equals(0)){
                    fkTrackListServiceImpl.findLastJudgedLabel(personVO);
                }
                fkTrackListServiceImpl.getPersonLabels(personVO);
                //判断是否研判
                buildPersonJudge(personVO);
            });
            List<String> idCardList = voList.getRecords().stream().map(PersonVO::getIdCard).distinct().collect(Collectors.toList());
            Map<String, PersonTrackVO> voMap = fkWarningMapper.findWarningInfoByIdCard(idCardList)
                    .stream()
                    .collect(Collectors.groupingBy(
                            PersonTrackVO::getIdCard,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(PersonTrackVO::getActivityTime)),
                                    Optional::get
                            )
                    ));
            voList.getRecords().forEach(personTrackVO -> {
                PersonTrackVO vo = voMap.getOrDefault(personTrackVO.getIdCard(),  personTrackVO);
                personTrackVO.setJdwgs84(vo.getJdwgs84());
                personTrackVO.setWdwgs84(vo.getWdwgs84());
                personTrackVO.setActivityAddress(vo.getActivityAddress());
                personTrackVO.setActivityTime(vo.getActivityTime());
                personTrackVO.setGjlx(vo.getGjlx());
            });
            return RestfulResultsV2.ok(voList.getRecords())
                    .addTotalCount(voList.getTotal())
                    .addPageNum(context.getPageNum())
                    .addPageSize(context.getPageSize());
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }

    private void buildPersonJudge(PersonTrackVO personVO) {
        Integer judgeCount = fkWarningMapper.getPersonJudge(personVO.getId());
        if (judgeCount > 0){
            personVO.setPersonStatus("1");
        }else {
            personVO.setPersonStatus("0");
        }
    }

    @Override
    public String key() {
        return "fk-abnormalBehavior";
    }

    @Override
    public String desc() {
        return "fk-人员异常行为";
    }
}
