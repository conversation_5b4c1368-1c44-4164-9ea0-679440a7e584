package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.vo.PersonVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: dingkeyu
 * @date: 2024/08/13
 * @description:
 */
@Mapper
public interface SubjectPersonMapper {

    /**
     * jz人员列表
     *
     * @param personDTO personDTO
     * @param page      page
     * @return {@link IPage}<{@link PersonVO}>
     */
    IPage<PersonVO> jzPersonList(@Param("dto") PersonDTO personDTO, @Param("page") IPage page);

    /**
     * fx人员列表
     *
     * @param personDTO personDTO
     * @param page      page
     * @return {@link IPage}<{@link PersonVO}>
     */
    IPage<PersonVO> fxPersonList(@Param("dto") PersonDTO personDTO, @Param("page") IPage page);

    /**
     * sa人员列表
     *
     * @param dto dto
     * @param page page
     * @return {@link IPage}<{@link PersonVO}>
     */
    IPage<PersonVO> saPersonList(@Param("dto") PersonDTO dto, Page page);
}
