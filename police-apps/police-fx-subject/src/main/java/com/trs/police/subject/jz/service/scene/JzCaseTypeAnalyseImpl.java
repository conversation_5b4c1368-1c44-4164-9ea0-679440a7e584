package com.trs.police.subject.jz.service.scene;

import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.subject.domain.dto.SubjectJqtsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PoliceSubjectStatisticsVO;
import com.trs.police.subject.common.mapper.JqtsAnalyseMapper;
import com.trs.police.subject.common.service.scene.ISubjectStatisticScene;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 刑事案件service实现类
 *
 * <AUTHOR>
 * @date 2024/07/10
 */
@Service
public class JzCaseTypeAnalyseImpl implements ISubjectStatisticScene<PoliceSubjectStatisticsVO, SubjectJqtsDTO> {

    @Autowired
    private DictService dictService;

    @Autowired
    private JqtsAnalyseMapper jqtsAnalyseMapper;

    @Override
    public List<PoliceSubjectStatisticsVO> search(SubjectSceneContext<SubjectJqtsDTO> context) {
        //获取所有形式案件类别
        List<DictDto> listDto = dictService.getDictListByType("jz_ajlbdm");
        List<PoliceSubjectStatisticsVO> listVO = jqtsAnalyseMapper.selectCaseType(context,context.getTopCondition());
        //格数化案件类别名称为空得值
        PoliceSubjectStatisticsVO policeVO = findNull(listVO);
        Map<String, Long> collect = listVO.stream().collect(Collectors.
                toMap(PoliceSubjectStatisticsVO::getName, PoliceSubjectStatisticsVO::getCount));
        //将listDto转换List<PoliceSubjectStatisticsVO>
        List<PoliceSubjectStatisticsVO> voList = listToVo(collect,listDto);
        if (policeVO!=null){
            voList.add(policeVO);
        }
        //返回count和value不为0的值
        voList = voList.stream().filter(vo -> !vo.getCount().equals(0L) && !vo.getValue().equals(0L)).collect(Collectors.toList());
        return voList;
    }

    private PoliceSubjectStatisticsVO findNull(List<PoliceSubjectStatisticsVO> listVO) {
        PoliceSubjectStatisticsVO policeSubjectStatisticsVO = null;
        for (PoliceSubjectStatisticsVO subjectStatisticsVO : listVO) {
            if (subjectStatisticsVO.getKey()==null || subjectStatisticsVO.getKey().equals("")){
                policeSubjectStatisticsVO = subjectStatisticsVO;
                policeSubjectStatisticsVO.setKey("空值");
            }
        }
        return policeSubjectStatisticsVO;
    }

    private List<PoliceSubjectStatisticsVO> listToVo(Map<String, Long> collect, List<DictDto> dictDtos) {
        List<PoliceSubjectStatisticsVO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(dictDtos)){
            return list;
        }
        for (DictDto dictDto : dictDtos) {
            PoliceSubjectStatisticsVO policeSubjectStatisticsVO = new PoliceSubjectStatisticsVO();
            policeSubjectStatisticsVO.setKey(dictDto.getDictDesc());
            policeSubjectStatisticsVO.setName(dictDto.getName());
            //List<String> codes = handleCodes(dictDto.getChildren());
            List<PoliceSubjectStatisticsVO> children = listToVo(collect,dictDto.getChildren());
            if (CollectionUtils.isEmpty(children)){
                //若子集为空，直接设置当前数量,设置count值和value值相等
                Long count = collect.get(policeSubjectStatisticsVO.getName());
                policeSubjectStatisticsVO.setCount(count==null?0L:count);
                policeSubjectStatisticsVO.setValue(count==null?0L:count);
            }else {
                //若子集不为空，则将子集的数量相加，且过滤掉children中count为0L的元素
                children = children.stream().filter(vo -> !vo.getCount().equals(0L) && !vo.getValue().equals(0L)).collect(Collectors.toList());
                Long value = 0L;
                for (PoliceSubjectStatisticsVO child : children) {
                    value += child.getCount();
                }
                policeSubjectStatisticsVO.setCount(value);
                policeSubjectStatisticsVO.setValue(value);
            }
            policeSubjectStatisticsVO.setChildren(children);
            list.add(policeSubjectStatisticsVO);
        }
        return list;
    }



    private List<PoliceSubjectStatisticsVO> getDict(String ajlbdm,List<DictDto> list,Map<String, Long> collect) {
        List<PoliceSubjectStatisticsVO> listVO = listToVo(collect, list);
        //1.如果传递过来的代码为空，则返回第一层
        if (ajlbdm==null || ajlbdm.equals("")){
            for (PoliceSubjectStatisticsVO vo : listVO) {
                vo.setChildren(null);
            }
            return listVO;
        }
        //如果传递的代码不为空，应该取该代码的下级去计算
        //找到ajlbdm所在根节点，并设置其children字段值有属性
        DictDto rootDto = findRoot(ajlbdm, list);
        for (PoliceSubjectStatisticsVO vo : listVO) {
            if (vo.getName().equals(rootDto.getName())){
                continue;
            }
            vo.setChildren(null);
        }
        return listVO;
    }

    private DictDto findRoot(String ajlbdm, List<DictDto> list) {
        for (DictDto dictDto : list) {
            if (ajlbdm.equals(dictDto.getDictDesc())){
                return dictDto;
            }
            DictDto dto = null;
            if (dictDto.getChildren().isEmpty()){
                continue;
            }
            dto = findRoot(ajlbdm, dictDto.getChildren());
            if (dto.getId()!=null){
                return dictDto;
            }
        }
        //没找到，返回空
        return new DictDto();
    }


    @Override
    public String key() {
        return "jz-case-type-analysis";
    }

    @Override
    public String desc() {
        return "刑事案件类别分析";
    }


}
