package com.trs.police.subject.domain.vo;

import com.trs.police.common.core.vo.Tree;
import com.trs.police.subject.domain.entity.FxActionExcavateDict;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: zhang.wenquan
 * @description:
 * @date: 2022/6/20 12:59
 * @version: 1.0
 */
@Data
@NoArgsConstructor
public class FxActionExcavateDictVO implements Tree {

    private Integer id;

    private String name;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 父节点id
     */
    private Long parentId;

    /**
     * 检索线索
     */
    @Getter(value = AccessLevel.NONE)
    private String searchKey;

    /**
     * 深度
     */
    private String treeLevel;

    /**
     * 启用
     */
    private Integer enable;

    /**
     * 标签类型
     */
    private String tagType;

    private String tagTypeName;

    /**
     * 分值
     */
    private Integer score = 0;

    /**
     * 计算公式
     */
    private String formula;

    /**
     * 系数
     */
    private Double coefficient;

    /**
     * 命中
     */
    private String hit;

    private Long count = 0L;

    /**
     * 判断积分是否爆点
     */
     private boolean isHot;

    private Integer nodeType;

    /**
     * 子节点
     */
    private List<FxActionExcavateDictVO> children;


    @Override
    public String key() {
        return searchKey;
    }

    @Override
    public Optional<Integer> sort() {
        return Optional.of(getSort());
    }

    @Override
    public void setChildren(List<? extends Tree> children) {
        this.children = (List<FxActionExcavateDictVO>) children;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FxActionExcavateDictVO that = (FxActionExcavateDictVO) o;
        return name.equals(that.name) && treeLevel.equals(that.treeLevel);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, treeLevel);
    }

    /**
     * 数据转换
     *
     * @param dict dict
     * @return {@link FxActionExcavateDictVO}
     */
    public static FxActionExcavateDictVO of(FxActionExcavateDict dict) {
        FxActionExcavateDictVO vo = new FxActionExcavateDictVO();
        vo.setId(dict.getId());
        vo.setName(dict.getName());
        vo.setParentId(dict.getParentId());
        vo.setSearchKey(dict.getSearchKey());
        vo.setTreeLevel(dict.getTreeLevel());
        vo.setSort(dict.getSort());
        vo.setEnable(dict.getEnable());
        vo.setTagType(dict.getTagType());
        return vo;
    }
}
