package com.trs.police.subject.sw.service.scene;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.entity.ThemeGjxxbEntity;
import com.trs.police.common.core.utils.GeoHashUtils;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.PersonMapper;
import com.trs.police.subject.common.repository.ThemeGjxxbRepository;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.entity.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员管控-人员轨迹列表实现类
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
@Slf4j
public class SwPersonTrackListImpl extends AbstractPersonControlAnalysisImpl<PersonTrackVO> {

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private SwSituationAnalysisMidAreaStatisticsImpl swSituationAnalysisMidAreaStatistics;

    @Autowired
    private ThemeGjxxbRepository themeGjxxbRepository;

    @Override
    protected RestfulResultsV2<PersonTrackVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            final List<Long> personLabels = swSituationAnalysisMidAreaStatistics.getSwPersonLabels();
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());

            //人员基本信息
            List<PersonVO> personList = personMapper.swBkPersonListV2(dto, personLabels);
            if (CollectionUtils.isEmpty(personList)) {
                return RestfulResultsV2.ok(new ArrayList<>());
            }
            //获取预警模型id
            getWarningModel(personList);
            List<String> personIdList = personList.stream().map(PersonVO::getIdCard).collect(Collectors.toList());
            Map<String, PersonVO> personMap = personList.stream().collect(Collectors.toMap(PersonVO::getIdCard, vo -> vo));
            // 获取预警模型名称
            Set<Long> modelIds = personList.stream().flatMap(o -> o.getModelId().stream()).collect(Collectors.toSet());
            Map<String, String> modelMap = fkPersonMapper.getModelName(modelIds).stream()
                    .collect(Collectors.toMap(KeyValueVO::getKey, KeyValueVO::getValue));
            // 获取人员标签名称
            Set<Long> personLabelIds = personList.stream()
                    .flatMap(o -> o.getPersonLabelIds().stream()).collect(Collectors.toSet());
            Map<String, String> personLabelMap = fkPersonMapper.getPersonLabelName(personLabelIds).stream()
                    .collect(Collectors.toMap(KeyValueVO::getKey, KeyValueVO::getValue));
            //默认固定返回100条 因为这个场景只需要展示最新的轨迹并撒点，不需要进行分页，分页的话执行count操作耗时太多
            Integer limit = BeanFactoryHolder.getEnv().getProperty("subject.sw.track.limit", Integer.class, 100);
            List<PersonTrackVO> voList = new ArrayList<>();
            buildPersonTrackVO(personIdList, dto, limit, voList);
            voList.forEach(vo -> {
                PersonVO personVO = personMap.get(vo.getIdCard());
                if (personVO != null) {
                    if (!CollectionUtils.isEmpty(personVO.getPhoto())) {
                        vo.setAvatar(personVO.getPhoto().get(0).getUrl());
                    }
                    vo.setRealName(personVO.getRealName());
                    vo.setIdCard(personVO.getIdCard());
                    vo.setPhoto(personVO.getPhoto());
                    vo.setId(personVO.getId());
                    if (!CollectionUtils.isEmpty(personVO.getPersonLabelIds())) {
                        vo.setPersonCategory(personVO.getPersonLabelIds().stream()
                                .map(v -> personLabelMap.getOrDefault(v.toString(), null))
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(",")));
                    }
                    if (!CollectionUtils.isEmpty(vo.getModelId())) {
                        vo.setPersonType(vo.getModelId().stream()
                                .map(v -> modelMap.getOrDefault(v.toString(), null))
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(",")));
                    }
                }
            });
            setFocusFlag(voList);
            List<PersonTrackVO> results = voList.stream()
                    .sorted(Comparator.comparing(PersonTrackVO::getActivityTime).reversed())
                    .limit(limit)
                    .collect(Collectors.toList());
            return RestfulResultsV2.ok(results)
                    .addTotalCount(Long.valueOf(limit))
                    .addPageNum(context.getPageNum())
                    .addPageSize(context.getPageSize());
        } catch (Exception e) {
            throw new RuntimeException("sw人员轨迹列表查询异常", e);
        }
    }

    private void buildPersonTrackVO(List<String> personIdList, PersonDTO dto, Integer limit, List<PersonTrackVO> voList) {
        Expression expression = getExpression(personIdList, dto);
        try {
            PageInfo pageInfo = PageInfo.newPage(1, limit);
            pageInfo.desc("hdsj");
            List<ThemeGjxxbEntity> datas = themeGjxxbRepository.findPageList(expression, pageInfo).getContents();
            log.info("查询的轨迹数：{}", datas.size());
            datas.forEach(e -> {
                PersonTrackVO vo = new PersonTrackVO();
                vo.setJdwgs84(e.getJdwgs84());
                vo.setWdwgs84(e.getWdwgs84());
                vo.setActivityAddress(e.getHddz());
                vo.setActivityTime(e.getHdsj());
                vo.setGjlx(e.getGjlx());
                vo.setIdCard(e.getTzzhm());
                voList.add(vo);
            });
        } catch (Exception e) {
            log.error("查询轨迹信息接口失败：", e);
        }
    }

    private static Expression getExpression(List<String> idCardList, PersonDTO dto) {
        return ExpressionBuilder.And(
                ExpressionBuilder.Condition("tzlx", Operator.FullTextEqualWholeWord, "身份证"),
                ExpressionBuilder.Condition("tzzhm", Operator.In, idCardList),
                ExpressionBuilder.Condition(StringUtils.isNotEmpty(dto.getStartTime()), "hdsj", Operator.GreaterThanOrEqual, dto.getStartTime()),
                ExpressionBuilder.Condition(StringUtils.isNotEmpty(dto.getEndTime()), "hdsj", Operator.LessThanOrEqual, dto.getEndTime()),
                ExpressionBuilder.Condition(StringUtils.isNotEmpty(dto.getGjlx()), "gjlx", Operator.FullTextEqualWholeWord, dto.getGjlx())
        );
    }


    private List<PersonTrackVO> setFocusFlag(List<PersonTrackVO> personTrackVos) {
        if (!CollectionUtils.isEmpty(personTrackVos)) {
            // 设置GeoHash
            Integer precise = BeanFactoryHolder.getEnv().getProperty("fx.subject.geoHash.precise", int.class, 6);
            for (PersonTrackVO vo : personTrackVos) {
                // 根据经纬度获取geoHash
                if (StringUtils.isNotEmpty(vo.getWdwgs84()) && StringUtils.isNotEmpty(vo.getJdwgs84())) {
                    vo.setGeoHash(GeoHashUtils.getGeoHash(Double.parseDouble(vo.getWdwgs84()), Double.parseDouble(vo.getJdwgs84()), precise));
                }
            }
            // 根据geohash相同最多的位置,取其中一个位置将focus_flag设置为1
            Map<String, Long> map = personTrackVos.stream()
                    .filter(vo -> StringUtils.isNotEmpty(vo.getGeoHash()))
                    .collect(Collectors.groupingBy(PersonTrackVO::getGeoHash, Collectors.counting()));
            if (!map.isEmpty()) {
                String geoHash = map.entrySet().stream()
                        .max(Map.Entry.comparingByValue())
                        .map(Map.Entry::getKey)
                        .orElse("");
                // 找到第一个匹配的记录并设置focusFlag
                for (PersonTrackVO vo : personTrackVos) {
                    if (geoHash.equals(vo.getGeoHash())) {
                        vo.setFocusFlag(1);
                        break;
                    }
                }
            }
        }
        return personTrackVos;
    }

    private void getWarningModel(List<PersonVO> records) {
        List<Long> warningIdList = records.stream()
                .map(PersonVO::getWarningIds)
                .filter(Objects::nonNull)
                .flatMap(ids -> Arrays.stream(ids.split(",")))
                .map(String::trim)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<Long>> warningModelMap = personMapper.getWarningModelId(warningIdList)
                .stream().collect(Collectors.toMap(PersonTrackVO::getWarningId, PersonTrackVO::getModelId));
        records.forEach(vo -> {
            if (vo.getWarningIds() != null) {
                List<Long> modelIds = Arrays.stream(vo.getWarningIds().split(","))
                        .map(String::trim)
                        .flatMap(warningId -> Optional.ofNullable(warningModelMap.get(Long.parseLong(warningId)))
                                .stream()
                                .flatMap(List::stream))
                        .distinct()
                        .collect(Collectors.toList());
                vo.setModelId(modelIds.isEmpty() ? null : modelIds);
            }
        });
    }

    @Override
    public String key() {
        return "sw-personTrackList";
    }

    @Override
    public String desc() {
        return "sw人员轨迹列表";
    }
}
