package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.subject.domain.dto.ClueExcavateDTO;
import com.trs.police.subject.domain.entity.FxHiddenPersonEntity;
import com.trs.police.subject.domain.vo.HiddenPerosnExcavateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: dingkeyu
 * @date: 2024/07/04
 * @description:
 */
@Mapper
public interface FxHiddenPersonMapper extends BaseMapper<FxHiddenPersonEntity> {

    /**
     * 隐性人员挖掘列表
     *
     * @param dto dto
     * @param page page
     * @return {@link Page}<{@link HiddenPerosnExcavateVO}>
     */
    Page<HiddenPerosnExcavateVO> hiddenPersonList(@Param("dto")ClueExcavateDTO dto, Page page);
}
