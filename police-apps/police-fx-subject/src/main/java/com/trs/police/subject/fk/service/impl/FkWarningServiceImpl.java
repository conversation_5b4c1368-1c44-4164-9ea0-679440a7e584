package com.trs.police.subject.fk.service.impl;

import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.fk.manager.FkWarningManager;
import com.trs.police.subject.fk.service.FkWarningService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 高新反恐预警服务
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Slf4j
@Service
public class FkWarningServiceImpl implements FkWarningService {

    @Resource
    private FkWarningManager fkWarningManager;

    @Override
    public RestfulResultsV2<PersonalStatisticsVO> warningStatistics(StatisticsDTO dto) {
        try {
            return fkWarningManager.warningStatistics(dto);
        } catch (Exception e) {
            log.error("预警统计异常", e);
            return RestfulResultsV2.error("预警统计异常");
        }
    }

    @Override
    public RestfulResultsV2<PersonalStatisticsVO> topStatistics(StatisticsDTO dto) {
        try {
            return fkWarningManager.topStatistics(dto);
        } catch (Exception e) {
            log.error("顶部统计异常", e);
            return RestfulResultsV2.error("顶部统计异常");
        }
    }
}
