package com.trs.police.subject.fk.service.ts;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.WarningFkrxyjMapper;
import com.trs.police.subject.domain.dto.FkTsDto;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.fk.FkWarningStatisticVO;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 反恐态势-预警统计
 */
@Service
public class WarningStatisticImpl extends AbstractClass<FkWarningStatisticVO, FkTsDto> {

    @Autowired
    private WarningFkrxyjMapper warningFkrxyjMapper;

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Override
    public List<FkWarningStatisticVO> search(SubjectSceneContext<FkTsDto> context) {
        FkTsDto dto = context.getDto();
        String areaCode = StringUtils.isNotEmpty(dto.getAreaCode()) ? dto.getAreaCode()
                : BeanFactoryHolder.getEnv().getProperty("com.trs.fk.tsBigScreen.areaCode", "510109");
        Boolean isPcs = BeanFactoryHolder.getEnv().getProperty("com.trs.fk.tsBigScreen.isPcs", Boolean.class,false);
        dto.setAreaCode(AreaUtils.areaPrefix(areaCode));
        List<KeyValueVO> keyValueList;
        if (isPcs){
            keyValueList = gePcstList(areaCode,false);
        }else {
            keyValueList = getDistrictList(areaCode);
        }
        //获取需要的身份证信息
        //预警数
        List<CountItem> yjList = warningFkrxyjMapper.selectFkWarningList(dto, null,false,
                isPcs);
        //无需研判统计
        List<CountItem> wxypList = yjList.stream().filter(e-> MonitorLevelEnum.BLUE.getCode().equals(e.getLevel()))
                        .collect(Collectors.toList());
        //已研判统计
        List<CountItem> ypList = yjList.stream().filter(e-> StringUtils.isNotEmpty(e.getId()))
                .collect(Collectors.toList());
        List<String> areaCodeList = keyValueList.stream().map(KeyValueVO::getKey)
                .distinct().collect(Collectors.toList());
        List<FkWarningStatisticVO> list = new ArrayList<>();
        Map<String, Long> wxypMap = CollectionUtils.isEmpty(wxypList)
                ? new HashMap<>() : wxypList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        Map<String, Long> ypMap = CollectionUtils.isEmpty(ypList)
                ? new HashMap<>() : ypList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        Map<String, Long> yjMap = CollectionUtils.isEmpty(yjList)
                ? new HashMap<>() : yjList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        Map<String, String> districtMap = CollectionUtils.isEmpty(keyValueList)
                ? new HashMap<>() : keyValueList.stream().collect(Collectors.toMap(KeyValueVO::getKey, KeyValueVO::getValue));
        for (String code : areaCodeList) {
            FkWarningStatisticVO vo = new FkWarningStatisticVO();
            vo.setKey(code);
            vo.setName(Objects.nonNull(districtMap.get(code)) ? districtMap.get(code) : "");
            vo.setWarningCount(yjMap.getOrDefault(code,0L));
            vo.setJudgeCount(ypMap.getOrDefault(code,0L));
            vo.setNoJudgeCount(wxypMap.getOrDefault(code,0L));
            list.add(vo);
        }
        return list;
    }

    @Override
    public String key() {
        return "warningStatistic";
    }

    @Override
    public String desc() {
        return "预警统计";
    }
}
