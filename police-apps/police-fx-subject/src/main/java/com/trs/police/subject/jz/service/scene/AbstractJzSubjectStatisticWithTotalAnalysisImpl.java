package com.trs.police.subject.jz.service.scene;

import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PoliceSubjectStatisticsVO;
import com.trs.police.common.core.utils.RatioCalculationUtils;

import java.util.Collections;
import java.util.List;

/**
 * 态势分析-经侦主题带总数统计抽象类
 *
 * <AUTHOR>
 * @date 2024/7/10
 *
 * @param <DTO> DTO
 */
public abstract class AbstractJzSubjectStatisticWithTotalAnalysisImpl<DTO> extends AbstractJzSubjectStatisticDistributionAnalysisImpl<DTO> {
    @Override
    public List<PoliceSubjectStatisticsVO> search(SubjectSceneContext<DTO> context) {
        List<PoliceSubjectStatisticsVO> search = super.search(context);
        PoliceSubjectStatisticsVO total = new PoliceSubjectStatisticsVO();
        total.setChildren(search);
        total.setCount(search.stream().mapToLong(PoliceSubjectStatisticsVO::getCount).sum());
        total.setRecentCount(search.stream().filter(vo->vo.getRecentCount()!=null).mapToLong(PoliceSubjectStatisticsVO::getRecentCount).sum());
        total.setLastYearCount(search.stream().filter(vo->vo.getLastYearCount()!=null).mapToLong(PoliceSubjectStatisticsVO::getLastYearCount).sum());
        total.setYoy(RatioCalculationUtils.computeRatio(total.getCount(), total.getRecentCount()));
        total.setRatio(RatioCalculationUtils.computeRatio(total.getCount(), total.getLastYearCount()));

        return Collections.singletonList(total);
    }
}
