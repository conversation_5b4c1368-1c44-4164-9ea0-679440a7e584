package com.trs.police.subject.fk.service.ts;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.vo.profile.LabelVO;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.domain.dto.FkTsDto;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.entity.fkrxyj.ProfilePerson;
import com.trs.police.subject.domain.vo.fk.FkPersonTypeVO;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 反恐态势-人员类别统计
 */
@Service
public class PersonTypeAnalysisImpl extends AbstractClass<FkPersonTypeVO, FkTsDto> {

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private ProfileService profileService;

    @Override
    public List<FkPersonTypeVO> search(SubjectSceneContext<FkTsDto> context) {
        FkTsDto dto = context.getDto();
        String areaCode = StringUtils.isNotEmpty(dto.getAreaCode()) ? dto.getAreaCode()
                : BeanFactoryHolder.getEnv().getProperty("com.trs.fk.tsBigScreen.areaCode", "510109");
        dto.setAreaCode(AreaUtils.areaPrefix(areaCode));
        //获取反恐人员
        List<ProfilePerson> personList = fkPersonMapper.selectCountByPersonType(dto);
        if (CollectionUtils.isEmpty(personList)){
            return new ArrayList<>();
        }
        String personTypeLabel = BeanFactoryHolder.getEnv().getProperty("com.trs.fk.statistic.personTypeLabel", "45");
        List<Long> personTypeLabelList = Arrays.asList(personTypeLabel.split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
        List<LabelVO> labelList = CollectionUtils.isEmpty(personTypeLabelList) ? new ArrayList<>()
                : profileService.getLabelByIds(personTypeLabelList);
        List<FkPersonTypeVO> resultList = new ArrayList<>();
        for (LabelVO labelEntity : labelList) {
            FkPersonTypeVO vo = new FkPersonTypeVO();
            vo.setPersonTypeName(labelEntity.getName());
            List<ProfilePerson> collect = personList.stream()
                    .filter(e -> !CollectionUtils.isEmpty(e.getPersonLabel()) && e.getPersonLabel()
                            .contains(labelEntity.getId())).collect(Collectors.toList());
            vo.setTotalCount(CollectionUtils.isEmpty(collect) ? 0L : Long.valueOf(collect.size()));
            resultList.add(vo);
        }
        return resultList;
    }

    @Override
    public String key() {
        return "personTypeAnalysis";
    }

    @Override
    public String desc() {
        return "人员类别分析";
    }
}
