package com.trs.police.subject.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.police.common.core.vo.profile.JqCommonVO;
import com.trs.police.subject.domain.dto.SituationStatisticDto;
import com.trs.police.subject.domain.vo.GaTsCommonVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 广安态势大屏统计
 */
@Mapper
public interface GaTsBigScreenMapper extends BaseMapper<GaTsCommonVO> {


    /**
     * 统计要情sql
     *
     * @param pageResult page
     * @param dto dto
     * @param groupGatherType 要情类别
     * @param districtPrefix districtPrefix
     * @return 结果
     */
    IPage<GaTsCommonVO> selectYaoQingPage(@Param("page") IPage<GaTsCommonVO> pageResult, @Param("dto") SituationStatisticDto dto,
                                          @Param("groupGatherType") Integer groupGatherType,
                                          @Param("districtPrefix") String districtPrefix);

    /**
     * 获取警情信息
     *
     * @param dto dto
     * @param districtCode 区域代码
     * @return 结果
     */
    List<JqCommonVO> selectJqListByDto(@Param("dto") SituationStatisticDto dto, @Param("districtCode") String districtCode);
}
