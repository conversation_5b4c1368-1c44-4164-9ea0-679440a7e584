package com.trs.police.subject.domain.entity.sa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: dingkeyu
 * @date: 2024/11/08
 * @description: 可疑暗娼场所
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "tb_sa_kyaccs")
public class KyaccsEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 证件号码
     */
    private String idCard;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 现住址（区/县）
     */
    private String qxAddress;

    /**
     * 现住址（乡镇）
     */
    private String xzAddress;

    /**
     * 具体接触地址
     */
    private String jtjcdz;
}
