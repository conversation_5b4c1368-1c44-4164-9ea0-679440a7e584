package com.trs.police.subject.jz.controller;

import com.trs.common.base.PreConditionCheck;
import com.trs.police.subject.jz.constant.JzSubjectJqtsTopConditonEnum;
import com.trs.police.subject.domain.dto.SubjectJqtsDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.AjVO;
import com.trs.police.subject.domain.vo.JzjqVO;
import com.trs.police.subject.domain.vo.PoliceSubjectStatisticsVO;
import com.trs.police.subject.domain.vo.XryVO;
import com.trs.police.subject.jz.service.SubjectService;
import com.trs.police.subject.common.service.scene.ISubjectStatisticScene;
import com.trs.police.subject.common.service.scene.SubjectSceneSearchFactory;
import com.trs.police.subject.common.util.FileUtils;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


/**
 * 警侦专题-警情态势controller
 *
 * <AUTHOR>
 * @date 2024/6/27
 */
@Slf4j
@RestController
@RequestMapping("/subject/jqts")
public class SubjectJqtsController {
    @Autowired
    private SubjectService subjectService;

    /**
     * 顶部统计
     *
     * @param context 参数
     * @return 结果
     */
    @PostMapping("/topStatistics")
    public RestfulResultsV2<PoliceSubjectStatisticsVO> topStatistics(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("jqts-topStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 区域统计
     *
     * @param context 参数
     * @return 结果
     */
    @PostMapping("/areaStatistics")
    public RestfulResultsV2<PoliceSubjectStatisticsVO> areaStatistics(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("jqts-areaStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 趋势统计
     *
     * @param context 参数
     * @return 结果
     */
    @PostMapping("/timeStatistics")
    public RestfulResultsV2<PoliceSubjectStatisticsVO> timeStatistics(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("jqts-timeStatistic");
        return RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 警情列表
     *
     * @param context 参数
     * @return 结果
     */
    @PostMapping("/jzjqList")
    public RestfulResultsV2<JzjqVO> jzjqList(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        return subjectService.jzjqList(context);
    }

    /**
     * 警情列表-导出
     *
     * @param response response
     * @param context 参数
     */
    @PostMapping("/exportJzjqList")
    public void exportjJzjqList(HttpServletResponse response, @RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        context.setPageSize(Integer.MAX_VALUE);
        String excelName = getExcelName(context, "警情列表");
        FileUtils.downloadExcel(response, JzjqVO.class, excelName, "sheet1", subjectService.jzjqList(context).getDatas(), context.getExportField(), true);
    }

    /**
     * 嫌疑人列表
     *
     * @param context 参数
     * @return 结果
     */
    @PostMapping("/xyrList")
    public RestfulResultsV2<XryVO> xyrList(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        return subjectService.xyrList(context);
    }

    /**
     * 嫌疑人列表-导出
     *
     * @param response response
     * @param context 参数
     */
    @PostMapping("/exportXyrList")
    public void exportXyrList(HttpServletResponse response, @RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        context.setPageSize(Integer.MAX_VALUE);
        String excelName = getExcelName(context, "嫌疑人列表");
        FileUtils.downloadExcel(response, XryVO.class, excelName, "sheet1", subjectService.xyrList(context).getDatas(), context.getExportField(), true);
    }

    private String getExcelName(SubjectSceneContext<SubjectJqtsDTO> context, String listName){
        String name = JzSubjectJqtsTopConditonEnum.getByCode(context.getTopCondition()).getName();
        PreConditionCheck.checkNotNull(name, "未知场景");
        return name + "--" + listName + ".xlsx";
    }

    /**
     * 案件详情列表
     *
     * @param context 参数
     * @return 结果
     */
    @PostMapping("/ajList")
    public RestfulResultsV2<AjVO> ajList(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        return subjectService.ajList(context);
    }

    /**
     * 案件详情列表-导出
     *
     * @param response response
     * @param context 参数
     */
    @PostMapping("/exportAjList")
    public void ajList(HttpServletResponse response, @RequestBody SubjectSceneContext<SubjectJqtsDTO> context) {
        context.setPageSize(Integer.MAX_VALUE);
        String excelName = getExcelName(context,"案件列表");
        FileUtils.downloadExcel(response, AjVO.class, excelName, "sheet1", subjectService.ajList(context).getDatas(), context.getExportField(), true);
    }


    /**
     * 警综警情类别分析
     *
     * @param context 参数
     * @return 警综警情类别分析
     */
    @PostMapping("/typeAnalyse")
    public RestfulResultsV2<PoliceSubjectStatisticsVO> kindAnalyse(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context){
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("jq-type-analysis");
        return  RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 形式案件类别分析
     *
     * @param context 参数
     * @return 刑事案件类别分析
     */
    @PostMapping("/caseKindAnalyse")
    public RestfulResultsV2<PoliceSubjectStatisticsVO> caseKindAnalyse(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context){
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("jz-case-type-analysis");
        return  RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 抓获人员状态信息
     *
     * @param context 参数
     * @return 刑事案件类别分析
     */
    @PostMapping("/catchPeopleStatus")
    public RestfulResultsV2<PoliceSubjectStatisticsVO> catchPeopleStatus(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context){
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("jz-catch-people-status");
        return  RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }

    /**
     * 强制措施分析
     *
     * @param context 参数
     * @return 刑事案件类别分析
     */
    @PostMapping("/compulsionAnalyse")
    public RestfulResultsV2<PoliceSubjectStatisticsVO> compulsionAnalyse(@RequestBody SubjectSceneContext<SubjectJqtsDTO> context){
        ISubjectStatisticScene subjectSceneSearchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl("jz-compulsion-analyse");
        return  RestfulResultsV2.ok(subjectSceneSearchImpl.search(context));
    }
}
