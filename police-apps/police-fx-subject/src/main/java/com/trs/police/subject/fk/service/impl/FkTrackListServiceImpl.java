package com.trs.police.subject.fk.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.subject.common.mapper.FkPersonMapper;
import com.trs.police.subject.common.mapper.FkWarningMapper;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.fk.entity.WarningFkrxyjEsEntity;
import com.trs.police.subject.fk.repository.FkWarningRepository;
import com.trs.police.subject.sw.service.scene.AbstractPersonControlAnalysisImpl;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 风险防控-所有人员轨迹信息
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Component
public class FkTrackListServiceImpl extends AbstractPersonControlAnalysisImpl<PersonTrackVO> {

    @Autowired
    private FkPersonMapper fkPersonMapper;

    @Autowired
    private FkWarningMapper fkWarningMapper;

    @Autowired
    private FkWarningRepository fkWarningRepository;

    @Autowired
    private DictService dictService;

    @Override
    protected RestfulResultsV2<PersonTrackVO> doSearch(SubjectSceneContext<PersonDTO> context) {
        try {
            PersonDTO dto = context.getDto();
            dto.setStartTime(context.getStartTime());
            dto.setEndTime(context.getEndTime());
            //默认固定返回100条 因为这个场景只需要展示最新的轨迹并撒点，不需要进行分页，分页的话执行count操作耗时太多
            Integer limit = BeanFactoryHolder.getEnv().getProperty("subject.fk.track.limit", Integer.class, 100);
            Page<PersonTrackVO> page = new Page<>(1, limit);
            page.setSearchCount(false);
            Page<PersonTrackVO> voList = fkPersonMapper.fkPersonTrackList(dto, page);
            // 设置总数
            if (CollectionUtils.isEmpty(voList.getRecords())) {
                return RestfulResultsV2.ok(new ArrayList<>())
                        .addTotalCount(voList.getTotal())
                        .addPageNum(context.getPageNum())
                        .addPageSize(context.getPageSize());
            }
            //  获取人员标签名称,已建档的使用档案中的人员标签,未建档的使用最新的一次研判标签
            voList.getRecords().stream().forEach(personVO -> {
                if (personVO.getOnRecord().equals(0)) {
                    findLastJudgedLabel(personVO);
                }
                getPersonLabels(personVO);
            });
            List<String> idCardList = voList.getRecords().stream().map(PersonVO::getIdCard).distinct().collect(Collectors.toList());
            Expression expression = buildEsSearchExpression(idCardList);
            //检索es表获取轨迹
            List<WarningFkrxyjEsEntity> datas = fkWarningRepository.findList(expression);
            Map<String, WarningFkrxyjEsEntity> entityMap = datas.stream()
                    .collect(Collectors.groupingBy(
                            WarningFkrxyjEsEntity::getIdCard,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(WarningFkrxyjEsEntity::getCreateTime)),
                                    Optional::get
                            )
                    ));
            Map<Long, String> dictMap = dictService.commonSearch("control_warning_source_type", null, null, null)
                    .stream().collect(Collectors.toMap(Dict2VO::getCode, Dict2VO::getName));
            voList.getRecords().forEach(personVO -> {
                WarningFkrxyjEsEntity entity = entityMap.getOrDefault(personVO.getIdCard(),  null);
                if (Objects.nonNull(entity)){
                    Long sourceType = JSONObject.parseObject(entity.getWarningSource()).getLong("sourceType");
                    personVO.setJdwgs84(entity.getLongitude());
                    personVO.setWdwgs84(entity.getLatitude());
                    personVO.setGjlx(dictMap.getOrDefault(sourceType,null));
                    personVO.setActivityAddress(entity.getCaptureAddress());
                }
            });
            return RestfulResultsV2.ok(voList.getRecords())
                    .addTotalCount(Long.valueOf(limit))
                    .addPageNum(context.getPageNum())
                    .addPageSize(context.getPageSize());
        } catch (Exception e) {
            throw new RuntimeException("人员动向查询异常", e);
        }
    }



    private Expression buildEsSearchExpression(List<String> idCardList) {
        return ExpressionBuilder.And(
                ExpressionBuilder.Condition("id_card", Operator.In, idCardList));
    }

    /**
     * 根据人员id查询人员最后研判的标签
     *
     * @param personVO personVO
     */
    public void findLastJudgedLabel(PersonVO personVO) {
        String lastJudgedLabel = fkPersonMapper.getLastJudgedLabel(personVO.getId());
        personVO.setPersonType(lastJudgedLabel);
    }

    /**
     * 获取人员标签名称
     *
     * @param personVO personVO
     */
    public void getPersonLabels(PersonVO personVO) {
        if (personVO.getPersonType() != null && personVO.getPersonType().startsWith("[") && personVO.getPersonType().endsWith("]")) {
            // 移除方括号并分割字符串
            String[] labelIds = personVO.getPersonType().substring(1, personVO.getPersonType().length() - 1).split(",");
            List<Long> labelIdList = Arrays.stream(labelIds)
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            // 如果personLabelIds为空，则使用从personType解析出的值
            if (CollectionUtils.isEmpty(labelIdList)) {
                return;
            }
            personVO.setPersonLabelIds(labelIdList);
        }
        // 获取人员标签名称
        Map<String, String> personLabelMap = fkPersonMapper.getPersonLabelName(personVO.getPersonLabelIds()).stream()
                .collect(Collectors.toMap(KeyValueVO::getKey, KeyValueVO::getValue));
        // 设置人员标签名称
        if (CollectionUtils.isEmpty(personVO.getPersonLabelIds())){
            return;
        }
        personVO.setPersonType(personVO.getPersonLabelIds().stream()
                .map(v -> personLabelMap.getOrDefault(v.toString(),null))
                .filter(Objects::nonNull)
                .collect(Collectors.joining(",")));
    }


    @Override
    public String key() {
        return "fk-trackList";
    }

    @Override
    public String desc() {
        return "fk-所有人员轨迹";
    }
}
