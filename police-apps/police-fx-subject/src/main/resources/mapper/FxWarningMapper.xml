<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.FxWarningMapper">

    <resultMap id="fxModelWarningList" type="com.trs.police.subject.domain.entity.FxWarningEntity">
        <result column="id" property="id"/>
        <result column="model_id" property="modelId" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="person_label" property="personLabel" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="area_id" property="areaId" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="place_code" property="placeCode" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="warning_tags" property="warningTags" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToStringListHandler"/>
        <result column="fx_tags" property="fxTags" typeHandler="com.trs.police.subject.common.handler.JsonToFxTagsHandler"/>
    </resultMap>

    <select id="selectPageList" resultMap="fxModelWarningList">
        SELECT
            *
        FROM t_warning_fxryyj
        <where>
            <if test="dto.warningBeginTime != null">
                AND warning_time &gt;= #{dto.warningBeginTime}
            </if>
            <if test="dto.warningEndTime != null">
                AND warning_time &lt;= #{dto.warningEndTime}
            </if>
            <if test="dto.warningStatus != null">
                AND warning_status = #{dto.warningStatus}
            </if>
            <if test="dto.warningLevel != null">
                AND warning_level = #{dto.warningLevel}
            </if>
            <if test="modelIds != null">
                AND JSON_OVERLAPS((ifnull(model_id,'[]')),
                (select JSON_ARRAYAGG(id) from t_control_monitor_warning_model where id in
                <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                    ${modelId}
                </foreach>
                ))>0
            </if>
            <if test="dto.minScore != null">
                AND warning_score &gt;= #{dto.minScore}
            </if>
            <if test="dto.maxScore != null">
                AND warning_score &lt;= #{dto.maxScore}
            </if>
            <if test="dto.searchField != null and dto.searchField != '' and dto.searchValue != null and dto.searchValue != ''">
                <choose>
                    <when test="dto.searchField == '全部' or dto.searchField == 'fullText'">
                        AND content LIKE CONCAT('%',#{dto.searchValue},'%')
                    </when>
                    <otherwise>
                        AND ${dto.searchField} LIKE CONCAT('%',#{dto.searchValue},'%')
                    </otherwise>
                </choose>
            </if>
            <if test="dto.ids != null and dto.ids != ''">
                AND id IN
                <foreach collection="dto.ids.split(',')" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        <if test="dto.orderField != null">
            ORDER BY ${dto.orderField} ${dto.orderValue}
        </if>
    </select>

    <select id="exceptionBehaviorStatistics" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            warning_tag_element AS tag,
            COUNT(*) AS warningCount
        FROM
            t_warning_fxryyj w
            LEFT JOIN tb_fx_person p ON w.id_card = p.id_card,
            JSON_TABLE ( w.warning_tags, '$[*]' COLUMNS ( warning_tag_element VARCHAR(255) PATH '$' )) AS jt
        <where>
            <if test="dto.subjectType != null and dto.subjectType != ''">
                AND p.subject_type = #{dto.subjectType}
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND w.warning_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND w.warning_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.personType != null and dto.personType != ''">
                AND p.person_type IN
                <foreach collection="dto.personType.split(',')" item="type" separator="," open="(" close=")">
                    #{type}
                </foreach>
            </if>
            <if test="dto.personStatus != null and dto.personStatus != ''">
                AND p.person_status IN
                <foreach collection="dto.personStatus.split(',')" item="status" separator="," open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                AND p.control_area_code IN
                <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
        GROUP BY warning_tag_element
    </select>

    <select id="getAreaWarningCount" resultType="com.trs.police.common.core.vo.control.AreaListVO">
        SELECT
            area_id_element AS id,
            COUNT(1) AS warningCount
        FROM
            t_warning w,
            JSON_TABLE ( w.area_id, '$[*]' COLUMNS ( area_id_element INTEGER PATH '$' )) AS jt
        GROUP BY area_id_element
    </select>

    <select id="selectByWarningTag" resultMap="fxModelWarningList">
        select * from t_warning_fxryyj where JSON_CONTAINS(warning_tags, CONCAT('\"', #{item}, '\"'))
    </select>
</mapper>