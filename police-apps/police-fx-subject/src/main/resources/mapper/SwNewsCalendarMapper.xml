<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.SwNewsCalendarMapper">

    <select id="selectPageByDto" resultType="com.trs.police.subject.domain.entity.SwRiskCalendarEntity">
        select * from sw_risk_calendar sw
        <where>
            sw.deleted = 0
            <choose>
                <when test="dto.searchKey == 'content'">
                    and sw.`content` like concat('%',#{dto.searchValue},'%')
                </when>
            </choose>
        </where>
        <if test="dto.startTime != null and dto.startTime != ''">
            and sw.date >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            and sw.date &lt;= #{dto.endTime}
        </if>
        order by date desc
    </select>
</mapper>