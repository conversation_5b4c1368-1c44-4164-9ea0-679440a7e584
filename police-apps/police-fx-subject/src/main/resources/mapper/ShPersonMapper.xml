<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.ShPersonMapper">

    <select id="arealDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            twpc.control_area_code AS areaCode,
            COUNT(DISTINCT p.id_card) AS totalCount
        FROM tb_fx_person p
        <include refid="leftOrRight"/> JOIN t_warning_person_clue twpc ON p.id_card = twpc.id_card
        <where>
            <include refid="commonCondition"></include>
        </where>
        GROUP BY twpc.control_area_code
    </select>

    <select id="getOtherCount" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            twpc.control_area_code AS areaCode,
            COUNT(1) AS warningCount,
            COUNT(DISTINCT p.id_card) AS activeCount
        FROM tb_fx_person p
        RIGHT JOIN t_warning_person_clue twpc ON p.id_card = twpc.id_card
        <where>
            <include refid="commonCondition"></include>
        </where>
        GROUP BY twpc.control_area_code
    </select>

    <select id="getClueCount" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            twpc.control_area_code AS areaCode,
            COUNT(DISTINCT pc.probably_people_id) AS clueCount
        FROM t_person_clue pc
        LEFT JOIN tb_fx_person p ON pc.probably_people_id = p.id_card
        LEFT JOIN t_warning_person_clue twpc ON pc.probably_people_id = twpc.id_card
        <where>
            <include refid="commonCondition"></include>
            AND pc.clue_type = 6
        </where>
        GROUP BY twpc.control_area_code
    </select>

    <select id="categoricalDistribution" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            p.person_type AS personTypeName,
            COUNT(DISTINCT p.id_card) AS totalCount
        FROM tb_fx_person p
        <include refid="leftOrRight"/> JOIN t_warning_person_clue twpc ON p.id_card = twpc.id_card
        <where>
            <include refid="commonCondition"></include>
        </where>
        GROUP BY p.person_type
        ORDER BY totalCount DESC
    </select>

    <select id="topStatistics" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            (SELECT COUNT(1) FROM tb_fx_person WHERE subject_type = 'sh') AS totalCount,
            COUNT(1) AS warningCount,
            COUNT(DISTINCT p.id_card) AS activeCount
        FROM tb_fx_person p
        RIGHT JOIN t_warning_person_clue twpc ON p.id_card = twpc.id_card
        <where>
            <include refid="commonCondition"></include>
        </where>
    </select>

    <select id="activeStatistic" resultType="com.trs.police.common.core.vo.AreaStatisticsVO">
        SELECT
            COUNT(DISTINCT p.id_card) AS activeCount,
            DATE(twpc.warning_time) AS time
        FROM t_warning_person_clue twpc
        <include refid="leftOrRight"/> JOIN tb_fx_person p ON twpc.id_card = p.id_card
        <where>
            <include refid="commonCondition"></include>
        </where>
        GROUP BY time
    </select>

    <select id="personList" resultType="com.trs.police.subject.domain.vo.PersonVO">
        SELECT
            DISTINCT
            p.id_card AS idCard,
            p.real_name AS realName,
            p.avatar,
            p.person_number AS personNumber,
            p.birthday,
            p.person_type AS personType,
            p.person_category AS personCategory,
            p.lk_status AS lkStatus,
            p.care_status AS careStatus
        FROM tb_fx_person p
        <include refid="leftOrRight"/> JOIN t_warning_person_clue twpc ON p.id_card = twpc.id_card
        <where>
            <include refid="commonCondition"></include>
            <if test="dto.searchValue != null and dto.searchValue != ''">
                AND (
                p.real_name like CONCAT('%',#{dto.searchValue},'%') OR
                p.id_card like CONCAT('%',#{dto.searchValue},'%')
                )
            </if>
        </where>
    </select>

    <sql id="commonCondition">
        AND p.subject_type = 'sh'
        <if test="dto.startTime != null and dto.startTime != ''">
            AND twpc.warning_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND twpc.warning_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.personType != null and dto.personType != ''">
            AND p.person_type IN
            <foreach collection="dto.personType.split(',')" item="type" separator="," open="(" close=")">
                #{type}
            </foreach>
        </if>
        <if test="dto.areaCode != null and dto.areaCode != ''">
            AND twpc.control_area_code IN
            <foreach collection="dto.areaCode.split(',')" item="code" separator="," open="(" close=")">
                #{code}
            </foreach>
        </if>
    </sql>

    <sql id="leftOrRight">
        <choose>
            <when test="'totalCount'.equals(dto.topCondition) or dto.topCondition.isEmpty()">
                LEFT
            </when>
            <otherwise>
                RIGHT
            </otherwise>
        </choose>
    </sql>
</mapper>
