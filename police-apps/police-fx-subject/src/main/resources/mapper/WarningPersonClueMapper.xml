<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.WarningPersonClueMapper">

    <select id="warningPersonClueByCode" resultType="com.trs.police.common.core.vo.control.AreaListVO">
        SELECT
            COUNT(1) AS warningCount,
            twpc.control_area_code AS districtCode
        FROM tb_fx_person p
        LEFT JOIN t_warning_person_clue twpc ON p.id_card = twpc.id_card
        <where>
            AND p.subject_type = 'sh'
            <if test="codes != null and !codes.isEmpty()">
                AND twpc.control_area_code IN
                <foreach collection="codes" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND twpc.warning_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND twpc.warning_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY twpc.control_area_code
        ORDER BY warningCount DESC
    </select>

    <select id="warningPersonClueById" resultType="com.trs.police.common.core.vo.control.AreaListVO">
        SELECT
            area_id_element AS id,
            count(1) AS warningCount
        FROM tb_fx_person p
        LEFT JOIN t_warning_person_clue twpc ON p.id_card = twpc.id_card,
        JSON_TABLE(twpc.area_id, '$[*]' COLUMNS (area_id_element INTEGER PATH '$')) AS jt
        <where>
            AND p.subject_type = 'sh'
            <if test="dto.startTime != null and dto.startTime != ''">
                AND twpc.warning_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND twpc.warning_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY area_id_element
    </select>
</mapper>