package com.trs.police.incident.analysis.controller;

import com.trs.police.incident.analysis.domain.request.DispatchingAnalysisGetAgeGroupRequest;
import com.trs.police.incident.analysis.domain.request.DispatchingAnalysisSubDeptAverageRequest;
import com.trs.police.incident.analysis.domain.response.DispatchingAnalysisGetAgeGroupResponse;
import com.trs.police.incident.analysis.domain.response.DispatchingAnalysisSubDeptAverageResponse;
import com.trs.police.incident.analysis.service.DispatchingAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 出警分析控制器
 */

@Api(value = "出警分析", tags = "出警分析")
@RestController
@RequestMapping(value = "/dispatching-analysis", produces = "application/json")
public class DispatchingAnalysisController {

    final DispatchingAnalysisService service;

    public DispatchingAnalysisController(DispatchingAnalysisService service) {
        this.service = service;
    }

    /**
     * 各年龄段警情比对
     *
     * @param request 请求参数
     * @return {@link DispatchingAnalysisGetAgeGroupResponse}
     */
    @ApiOperation(value = "各年龄段警情比对")
    @PostMapping("/age-group")
    List<DispatchingAnalysisGetAgeGroupResponse> getAgeGroup(@RequestBody DispatchingAnalysisGetAgeGroupRequest request) {
        return service.getAgeGroup(request);
    }

    /**
     * 下属单位人均出警数
     *
     * @param request 请求参数
     * @return {@link DispatchingAnalysisSubDeptAverageResponse}
     */
    @ApiOperation(value = "下属单位人均出警数")
    @PostMapping("/sub-dept-average-dispatch")
    DispatchingAnalysisSubDeptAverageResponse subDeptAverageDispatch(@RequestBody DispatchingAnalysisSubDeptAverageRequest request) {
        return service.subDeptAverageDispatch(request);
    }

}
