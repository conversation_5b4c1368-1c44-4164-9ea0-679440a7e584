package com.trs.police.incident.analysis.domain.request;

import com.trs.police.common.core.params.PageParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 重复报警获取满足条件的人员列表请求参数
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("重复报警获取人员列表请求参数")
public class DuplicateIncidentGetPersonListRequest extends TopRequest {

    @ApiModelProperty("分页参数")
    PageParams pageParams;

}
