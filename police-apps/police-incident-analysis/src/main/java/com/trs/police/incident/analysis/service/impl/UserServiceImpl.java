package com.trs.police.incident.analysis.service.impl;

import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.incident.analysis.domain.response.UserInfoResponse;
import com.trs.police.incident.analysis.service.UserService;
import org.springframework.stereotype.Service;


/**
 * 用户相关服务实现
 */
@Service
public class UserServiceImpl implements UserService {

    @Override
    public UserInfoResponse getCurUser() {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        UserInfoResponse userInfo = new UserInfoResponse();
        if (currentUser != null) {
            userInfo.setId(currentUser.getId());
            userInfo.setLoginName(currentUser.getUsername());
            userInfo.setUserName(currentUser.getRealName());
            userInfo.setSex(currentUser.getGender());
            userInfo.setIdEntityCard(currentUser.getIdNumber());
            userInfo.setOrgCode(currentUser.getDept().getCode());
            userInfo.setOrgName(currentUser.getDept().getName());
            userInfo.setPositionName(currentUser.getDuty());
            userInfo.setMobile(currentUser.getMobile());
        }
        return userInfo;
    }
}
