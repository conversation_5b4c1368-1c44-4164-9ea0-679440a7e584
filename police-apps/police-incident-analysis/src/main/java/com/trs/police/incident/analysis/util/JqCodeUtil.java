package com.trs.police.incident.analysis.util;

import com.google.common.collect.Lists;
import com.trs.police.incident.analysis.conf.properties.VirtualIncidentProperties;
import com.trs.police.incident.analysis.domain.dto.VirtualIncidentPropertyDTO;
import io.vavr.Tuple2;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 警情类别相关工具类
 */
public class JqCodeUtil {

    /**
     * 将虚拟警情类别代号转换为警情类别代码，
     * 和其他的警情类别代码统一后按照等级分类
     *
     * @param incidentTypeOrJqCodes 虚拟警情类别代号或警情类别代码集合
     * @param properties 虚拟警情参数
     * @return 按照等级拆分的警情列表，key为等级数，从1到4，value为该等级的警情类别代码
     */
    public static Map<Integer, List<String>> classifyJqCodes(List<String> incidentTypeOrJqCodes, VirtualIncidentProperties properties) {
        Map<String, VirtualIncidentPropertyDTO> incidentMap = properties.getVirtualIncidents();

        //待查询的虚拟警情id关联的警情类别代码集合
        List<String> jqCodes = incidentTypeOrJqCodes.stream()
                .flatMap(incidentTypeOrJqCode -> translateVirtualIncident(incidentTypeOrJqCode, incidentMap).stream())
                .collect(Collectors.toList());
        //将所有警情类别代码分类为四个等级
        return classifyJqCodes(jqCodes);
    }

    /**
     * 将警情类别归类为四个等级
     *
     * @param incidentType 警情类别代号
     * @param properties 虚拟警情参数
     * @return 按照等级拆分的警情列表，key为等级数，从1到4，value为该等级的警情类别代码
     */
    public static Map<Integer, List<String>> classifyJqCodes(String incidentType, VirtualIncidentProperties properties) {
        return classifyJqCodes(List.of(incidentType), properties);
    }

    /**
     * 将警情类别归类为四个等级
     * 针对每个独立的警情类别代码都调用distinguishJqCodes后将结果合并
     *
     * @param jqCodes 警情类别代码集合
     * @return 按照等级拆分的警情列表，key为等级数，从1到4，value为该等级的警情类别代码
     */
    public static Map<Integer, List<String>> classifyJqCodes(List<String> jqCodes) {
        return jqCodes.stream().map(JqCodeUtil::distinguishJqCodes).collect(Collectors.toMap(
                Tuple2::_1,
                (tuple) -> Lists.newArrayList(tuple._2()),
                (v1, v2) -> {
                    v1.addAll(v2);
                    return v1;
                }));
    }

    private static final Pattern NUMBER_CHECK_PATTERN = Pattern.compile("^\\d+$");

    /**
     * 是否是虚拟警情代码
     *
     * @param code 虚拟警情代码&警情类别代码
     * @return true：是虚拟警情代码 false：不是虚拟警情，即警情类别代码，或者为空
     */
    public static boolean isVirtual(String code) {
        return !StringUtils.isBlank(code) && !NUMBER_CHECK_PATTERN.matcher(code).matches();
    }

    /**
     * 翻译虚拟警情，将指定的虚拟警情翻译为警情类别代码
     *
     * @param virtualIncidentType 虚拟警情类型
     * @param incidentMap 分类后的警情类型映射
     * @return 警情类别代码集合
     */
    public static List<String> translateVirtualIncident(String virtualIncidentType, Map<String, VirtualIncidentPropertyDTO> incidentMap) {
        //根据虚拟警情ID获取到对应配置的值集合
        return isVirtual(virtualIncidentType) ? incidentMap.get(virtualIncidentType).getSubCode().stream().flatMap(value -> {
            //值有可能是另一个虚拟警情的ID，有可能是警情类别代码
            //如果是警情类别代码（值是0-9的纯数字），则直接返回
            //如果是另一个虚拟警情ID，就递归翻译，直到找到所有的值都是警情类别代码为止
            return translateVirtualIncident(value, incidentMap).stream();
        }).collect(Collectors.toList()) : List.of(virtualIncidentType);
    }

    /**
     * 区分警情类别代码
     *
     * @param jqCode 警情类别代码
     * @return {@link Tuple2}，左数据为警情类别级别，从1到4，右侧为警情类别原值
     */
    public static Tuple2<Integer, String> distinguishJqCodes(String jqCode) {
        String effectiveCode = HierarchicalUtil.getEffectiveCode(jqCode);
        return new Tuple2<>(effectiveCode.length() / 2, jqCode);
    }

}
