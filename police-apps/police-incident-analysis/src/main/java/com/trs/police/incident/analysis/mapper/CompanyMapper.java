package com.trs.police.incident.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.incident.analysis.domain.entity.CompanyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 公司库表mapper
 */

@Mapper
public interface CompanyMapper extends BaseMapper<CompanyEntity> {

    /**
     * 根据公司别名获取标准名称
     *
     * @param companyName 公司别名
     * @return 公司标准名称
     */
    @Select("select a.company_name from t_company as a where a.company_name = #{companyName} or FIND_IN_SET(#{companyName}, a.company_aliases) > 0")
    List<String> getStandardName(@Param("companyName") String companyName);
}
