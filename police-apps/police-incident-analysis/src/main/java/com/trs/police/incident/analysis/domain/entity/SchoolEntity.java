package com.trs.police.incident.analysis.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学校库表实体
 */

@Data
@TableName(value = "t_school", autoResultMap = true)
public class SchoolEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 学校名称
     */
    @TableField()
    private String schoolName;

    /**
     * 学校别名(多个逗号分割)
     */
    @TableField()
    private String schoolAliases;

    /**
     * 创建时间
     */
    @TableField()
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField()
    private LocalDateTime updateTime;

}
