package com.trs.police.incident.analysis.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.police.common.core.excpetion.SystemException;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.incident.analysis.conf.properties.AiConversationProperties;
import com.trs.police.incident.analysis.domain.dto.ConversationDTO;
import com.trs.police.incident.analysis.domain.request.ConversationAskIncidentRequest;
import com.trs.police.incident.analysis.domain.request.PoliceSituationListRequest;
import com.trs.police.incident.analysis.domain.response.PoliceSituationListResponse;
import com.trs.police.incident.analysis.rest.domain.AiConversationAskRequest;
import com.trs.police.incident.analysis.rest.service.AiConversationRestService;
import com.trs.police.incident.analysis.service.AiConversationService;
import com.trs.police.incident.analysis.service.CommonPublicService;
import com.trs.police.incident.analysis.util.HierarchicalUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.commons.httpclient.DefaultOkHttpClientConnectionPoolFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * AI对话服务实现
 */

@Slf4j
@Service
public class AiConversationServiceImpl implements AiConversationService {

    private final AiConversationProperties properties;

    private final AiConversationRestService restService;

    private final CommonPublicService commonService;

    private final ObjectMapper objectMapper;

    public AiConversationServiceImpl(AiConversationProperties properties,
                                     CommonPublicService commonService) {
        this.properties = properties;
        this.commonService = commonService;
        //设置超时时间  避免因过长导致连接中断
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                //设置连接池 最大空闲数200,表示理想需要的TPS
                .connectionPool(new DefaultOkHttpClientConnectionPoolFactory().create(200, 15, TimeUnit.MINUTES))
                .connectTimeout(15, TimeUnit.MINUTES)
                .readTimeout(15, TimeUnit.MINUTES)
                .writeTimeout(15, TimeUnit.MINUTES)
                .callTimeout(20, TimeUnit.MINUTES)
                .build();
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(properties.getDomain())
                .addConverterFactory(JacksonConverterFactory.create())
                .client(okHttpClient)
                .build();
        restService = retrofit.create(AiConversationRestService.class);
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public JsonNode ask(ConversationDTO dto) {
        AiConversationAskRequest askRequest = new AiConversationAskRequest(
                dto.getConversationId(),
                properties.getTenantId(),
                dto.getQuestion());
        log.info("问题打印{}",dto.getQuestion());
        Call<JsonNode> askCall = restService.ask(
                properties.getUserName(),
                properties.getAccessToken(),
                properties.getAppId(),
                askRequest);
        Response<JsonNode> response;
        try {
            response = askCall.execute();
        } catch (Exception e) {
            log.error("请求" + askCall.request().url() + "发生错误：", e);
            throw new SystemException("请求AI服务发生错误");
        }
        if (!response.isSuccessful() || response.body() == null || !"200".equals(response.body().get("code").asText())) {
            log.error("接口" + askCall.request().url() + "返回错误：[" + response.code() + "]" + response.message());
        }
        return response.body().get("data");
    }

    @Override
    public void answerForward(HttpServletResponse response, String conversationId, String qaId, String chatLogId) {
        response.setHeader("Content-Type", "text/event-stream;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        try (PrintWriter pw = response.getWriter()) {
            long startTimeMillis = System.currentTimeMillis();
            long endTimeMillis = startTimeMillis;

            String lastResult = "";
            try {
                // 最大读取时间暂时定为60s
                while ((endTimeMillis - startTimeMillis) / 1000 <= 60) {
                    Call<JsonNode> answerCall = restService.answer(
                            properties.getUserName(),
                            properties.getAccessToken(),
                            properties.getAppId(),
                            conversationId,
                            qaId,
                            chatLogId,
                            objectMapper.createObjectNode()
                    );

                    Response<JsonNode> response1 = answerCall.execute();
                    if (Objects.isNull(response1.body()) || !response1.isSuccessful()) {
                        endTimeMillis = System.currentTimeMillis();
                        continue;
                    }
                    JsonNode body = response1.body();
                    if (lastResult.hashCode() == body.toString().hashCode()) {
                        Thread.sleep(300);
                        endTimeMillis = System.currentTimeMillis();
                        continue;
                    }
                    lastResult = body.toString();
                    if (body.has("data") && body.get("data").isArray()) {
                        JsonNode data = body.get("data").get(0);
                        if (data.has("answer") && StringUtils.isEmpty(data.get("answer").asText())) {
                            endTimeMillis = System.currentTimeMillis();
                            Thread.sleep(300);
                            continue;
                        }
                        pw.write("event:text\n");
                        pw.write("data:" + body + "\n\n");
                        pw.flush();
                        if (data.has("answer") && data.get("answer").asText().endsWith("$$END$$")) {
                            break;
                        }
                        Thread.sleep(300);
                        endTimeMillis = System.currentTimeMillis();
                    } else {
                        throw new SystemException("请求出错，请重试！");
                    }
                }
            } catch (Exception e) {
                log.error("event模式获取答案错误：", e);
                log.error("最近的一次返回值：" + lastResult);
            }
        } catch (IOException e) {
            log.error("answer服务response.getWriter()发生异常：", e);
            throw new SystemException(e);
        }
    }

    private final static Integer DEFAULT_AI_DATA_PAGE_SIZE = 200;
    private final static Integer DEFAULT_AI_DATA_MAX_LENGTH = 5000;

    @Override
    public JsonNode conversationAskIncident(ConversationAskIncidentRequest request) {
        PoliceSituationListRequest dataParams = request.getDataParams();
        dataParams.setAreaCode(HierarchicalUtil.getEffectiveCode(dataParams.getDeptCode()));
        String question = CollectionUtils.isEmpty(dataParams.getLbCodes())
                ? properties.getDefaultQuestion()
                : properties.getQuestion(dataParams.getLbCodes().get(0)).orElse("");

        dataParams.setPageParams(new PageParams(1, DEFAULT_AI_DATA_PAGE_SIZE));
        final PageResult<PoliceSituationListResponse> data = commonService.getPoliceSituationList(request.getDataParams());
        StringBuilder sb = new StringBuilder();
        for (PoliceSituationListResponse item : data.getItems()) {
            StringBuilder sbSub = new StringBuilder();
            sbSub.append("报警内容：").append(item.getBjnr())
                    .append("，警情类别：").append(item.getJqlb())
                    .append("，报警时间：").append(item.getBjsj())
                    .append("，报警人：").append(item.getBjr())
                    .append("，报警电话：").append(item.getBjdh())
                    .append("，报警地址：").append(item.getBjdz())
                    .append("。");
            if(DEFAULT_AI_DATA_MAX_LENGTH < (sb + sbSub.toString() + question).length()){
                break;
            }
            sb.append(sbSub);
        }
        sb.append(question);
        request.setQuestion(sb.toString());
        ConversationDTO dto = new ConversationDTO();
        BeanUtils.copyProperties(request, dto);
        return ask(dto);
    }

    @Override
    public JsonNode conversationAskCompany(ConversationAskIncidentRequest request) {
        PoliceSituationListRequest dataParams = request.getDataParams();
        dataParams.setAreaCode(HierarchicalUtil.getEffectiveCode(dataParams.getDeptCode()));
        String question = properties.getCompanyQuestion();

        dataParams.setPageParams(new PageParams(1, DEFAULT_AI_DATA_PAGE_SIZE));
        final PageResult<PoliceSituationListResponse> data = commonService.getPoliceSituationList(request.getDataParams());
        StringBuilder sb = new StringBuilder()
                .append("公司名称：")
                .append(dataParams.getCompanyName()).append("。")
                .append(question);
        for (PoliceSituationListResponse item : data.getItems()) {
            StringBuilder sbSub = new StringBuilder();
            sbSub.append("报警内容：").append(item.getBjnr())
                    .append("，警情类别：").append(item.getJqlb())
                    .append("，报警时间：").append(item.getBjsj())
                    .append("，报警人：").append(item.getBjr())
                    .append("，报警电话：").append(item.getBjdh())
                    .append("。");
            if(DEFAULT_AI_DATA_MAX_LENGTH < (sb + sbSub.toString() + question).length()){
                break;
            }
            sb.append(sbSub);
        }
        sb.append(question);
        request.setQuestion(sb.toString());
        ConversationDTO dto = new ConversationDTO();
        BeanUtils.copyProperties(request, dto);
        return ask(dto);
    }
}
