package com.trs.police.incident.analysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.incident.analysis.domain.entity.DeptDetailEntity;
import com.trs.police.incident.analysis.domain.response.PoliceStationInfoResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Description: 派出所详情统计接口
 *
 * <AUTHOR>
 * @create 2024-11-25 13:59
 */
@Mapper
public interface DeptDetailMapper extends BaseMapper<DeptDetailEntity> {

    /**
     * 获取派出所信息
     *
     * @param deptCode 派出所编码
     * @return 派出所信息
     */
    @Select("select a.dz,d.name as gxdwmc,c.name as pcsmc,c.police_count as dwrs,a.zdmj as xqmj,count(b.gxdwdm) as sqsl, a.rksl as xqrksl " +
            "from t_jgdw_xq as a " +
            "left join t_sqb as b on a.bmbh = b.gxdwdm " +
            "left join t_dept as c on c.code = a.bmbh " +
            "left join t_dept as d on d.code = c.parent_code " +
            "where a.bmbh = #{deptCode} " +
            "group by a.bmbh,c.code")
    PoliceStationInfoResponse getPoliceStationInfo(@Param("deptCode")String deptCode);
}
