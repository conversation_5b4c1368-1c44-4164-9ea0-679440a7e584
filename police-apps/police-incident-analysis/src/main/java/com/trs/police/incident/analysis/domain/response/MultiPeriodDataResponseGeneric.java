package com.trs.police.incident.analysis.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 多期数据范型类
 *
 * @param <T> 数据范型
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MultiPeriodDataResponseGeneric<T> {

    /**
     * 本期数据
     */
    @ApiModelProperty(value = "本期数据")
    private T thisPeriodData;

    /**
     * 上期数据
     */
    @ApiModelProperty(value = "上期数据")
    private T previousPeriodData;

    /**
     * 去年同期数据
     */
    @ApiModelProperty(value = "去年同期数据")
    private T lastYearSamePeriodData;

}
