package com.trs.police.incident.analysis.service.impl;

import com.trs.police.incident.analysis.converter.DispatchingAnalysisConverter;
import com.trs.police.incident.analysis.domain.dto.DeptDTO;
import com.trs.police.incident.analysis.domain.dto.TopQueryDTO;
import com.trs.police.incident.analysis.domain.response.MultiPeriodDataResponseGeneric;
import com.trs.police.incident.analysis.mapper.DeptMapper;
import com.trs.police.incident.analysis.service.CodeSnippetService;
import com.trs.police.incident.analysis.util.TimeUtil;
import io.vavr.Tuple2;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 代码公共片段服务实现
 */

@Service
public class CodeSnippetServiceImpl implements CodeSnippetService {

    final DeptMapper deptMapper;

    final DispatchingAnalysisConverter converter;

    public CodeSnippetServiceImpl(DeptMapper deptMapper, DispatchingAnalysisConverter converter) {
        this.deptMapper = deptMapper;
        this.converter = converter;
    }

    @Override
    public <T extends MultiPeriodDataResponseGeneric<R>, O extends TopQueryDTO, R> T getMultiPeriodData(
            O queryDTO,
            Function<O, R> mapperCallFunction,
            Class<T> returnClass) {
        String startDate = queryDTO.getStartDate();
        String endDate = queryDTO.getEndDate();
        T t;
        try {
            t = returnClass.getConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
        //本期
        R thisPeriodData = mapperCallFunction.apply(queryDTO);
        t.setThisPeriodData(thisPeriodData);

        //上期
        Tuple2<String, String> lastMonthTimeRange = TimeUtil.getLastMonthTimeRange(
                startDate,
                endDate);
        queryDTO.setStartDate(lastMonthTimeRange._1);
        queryDTO.setEndDate(lastMonthTimeRange._2);
        R previousPeriodData = mapperCallFunction.apply(queryDTO);
        t.setPreviousPeriodData(previousPeriodData);

        //去年同期
        Tuple2<String, String> lastYearTimeRange = TimeUtil.getLastYearTimeRange(
                startDate,
                endDate);
        queryDTO.setStartDate(lastYearTimeRange._1);
        queryDTO.setEndDate(lastYearTimeRange._2);
        R lastYearPeriodData = mapperCallFunction.apply(queryDTO);
        t.setLastYearSamePeriodData(lastYearPeriodData);

        return t;
    }

    @Override
    public List<DeptDTO> getSubDeptList(String areaCode) {
        return deptMapper.getSubDeptList(areaCode)
                .stream()
                .map(converter::toDeptDTO)
                .collect(Collectors.toList());
    }
}
