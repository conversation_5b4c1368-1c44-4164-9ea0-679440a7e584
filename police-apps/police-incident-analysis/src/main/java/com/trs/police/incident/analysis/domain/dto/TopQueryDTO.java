package com.trs.police.incident.analysis.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 顶层查询DTO
 */

@Data
@AllArgsConstructor
public class TopQueryDTO {

    private String areaCode;

    private String startDate;

    private String endDate;

    /**
     * 是否只过滤有效警情
     */
    private Boolean effectiveJq;

    public TopQueryDTO() {
        this(null, null, null, true);
    }

    public TopQueryDTO(String areaCode, String startDate, String endDate) {
        this(areaCode, startDate, endDate, true);
    }

}
