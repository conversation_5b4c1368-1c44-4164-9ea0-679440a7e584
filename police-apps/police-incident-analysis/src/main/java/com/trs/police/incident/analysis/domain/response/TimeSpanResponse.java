package com.trs.police.incident.analysis.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * Description: 时间段分布返回
 *
 * <AUTHOR>
 * @create 2024-11-11 15:02
 */
@Data
@ApiModel("时间段分布返回")
public class TimeSpanResponse implements Serializable {

    private static final long serialVersionUID = 4844817191547445835L;

    /**
     * 小时时段 0-23
     */
    @ApiModelProperty(value = "小时时段")
    private String hour;

    /**
     * 警情数量
     */
    @ApiModelProperty(value = "警情数量")
    private String count;
}
