package com.trs.police.incident.analysis.converter;

import com.trs.police.incident.analysis.domain.dto.CommunityAnalysisQueryDTO;
import com.trs.police.incident.analysis.domain.dto.CommunityDTO;
import com.trs.police.incident.analysis.domain.dto.CommunityDetailQueryDTO;
import com.trs.police.incident.analysis.domain.entity.CommunityEntity;
import com.trs.police.incident.analysis.domain.request.CommunityAnalysisRequest;
import com.trs.police.incident.analysis.domain.request.CommunityDetailRequest;
import com.trs.police.incident.analysis.domain.request.IncidentTypeRequest;
import com.trs.police.incident.analysis.domain.response.CommunityDetailResponse;
import com.trs.police.incident.analysis.domain.response.IncidentTypeResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 按社区分析参数转换类
 */

@Mapper(componentModel = "spring")
public interface CommunityAnalysisConverter {

    /**
     * {@link CommunityAnalysisRequest}转换为{@link CommunityAnalysisQueryDTO}
     *
     * @param request {@link CommunityAnalysisRequest}
     * @return {@link CommunityAnalysisQueryDTO}
     */
    @Mappings({
            @Mapping(target = "lbCodes", expression = "java(com.trs.police.incident.analysis.util.HierarchicalUtil.getEffectiveListAndFilterInvalid(request.getLbCodes()))"),
            @Mapping(target = "effectiveJq", expression = "java(request.getSfyxjq() == null)"),
    })
    CommunityAnalysisQueryDTO toDTO(CommunityAnalysisRequest request);

    /**
     * {@link CommunityDetailRequest}转换为{@link CommunityDetailQueryDTO}
     *
     * @param request {@link CommunityDetailRequest}
     * @return {@link CommunityDetailQueryDTO}
     */
    @Mappings({
            @Mapping(target = "lbCodes", expression = "java(com.trs.police.incident.analysis.util.HierarchicalUtil.getEffectiveListAndFilterInvalid(request.getLbCodes()))"),
            @Mapping(target = "effectiveJq", expression = "java(request.getSfyxjq() == null)"),
    })
    CommunityDetailQueryDTO toDTO(CommunityDetailRequest request);

    /**
     * {@link CommunityDetailRequest}转换为{@link IncidentTypeRequest}
     *
     * @param request {@link CommunityDetailRequest}
     * @return {@link IncidentTypeRequest}
     */
    IncidentTypeRequest toIncidentTypeRequest(CommunityDetailRequest request);

    /**
     * {@link IncidentTypeResponse}转换为{@link CommunityDetailResponse}
     *
     * @param response {@link IncidentTypeResponse}
     * @return {@link CommunityDetailResponse}
     */
    CommunityDetailResponse toCommunityDetailResponse(IncidentTypeResponse response);

    /**
     * {@link CommunityEntity}转换为{@link CommunityDTO}
     *
     * @param entity {@link CommunityEntity}
     * @return {@link CommunityDTO}
     */
    @Mappings({
            @Mapping(target = "communityCode", source = "sqdm"),
            @Mapping(target = "communityName", source = "sqmc"),
    })
    CommunityDTO toCommunityDTO(CommunityEntity entity);
}
