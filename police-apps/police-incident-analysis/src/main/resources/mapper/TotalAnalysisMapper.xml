<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.incident.analysis.mapper.TotalAnalysisMapper">
    <sql id="statisticRelation">
        JOIN t_jjdbtj AS b ON a.jjdbh = b.jjdbh
    </sql>

    <select id="getTotalCount" resultType="java.lang.Integer">
        SELECT
        COUNT(1) AS jqNum
        FROM t_jjdb AS a
        <include refid="statisticRelation" />
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        <if test="dto.sfyxjq != null">
            and
            <if test="dto.sfyxjq == 1">
                b.sfyxjq = 1
            </if>
            <if test="dto.sfyxjq == 0">
                b.sfyxjq = 0
            </if>
        </if>
    </select>
    <select id="getDateCount" resultType="com.trs.police.incident.analysis.domain.response.DateCountResponse">
        SELECT
        a.bjrq as rq,
        COUNT(1) AS count
        FROM t_jjdb AS a
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        GROUP BY a.bjrq;
    </select>
    <select id="getDeptCount" resultType="com.trs.police.incident.analysis.domain.response.PoliceSituationDeptCountResponse">
        SELECT
        <choose>
            <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                b.sssjdm as bmbh,
                b.sssjmc as bmmc,
            </when>
            <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                b.ssfjdm as bmbh,
                b.ssfjmc as bmmc,
            </when>
            <otherwise>
                a.gxdwdm as bmbh,
                b.gxdwmc as bmmc,
            </otherwise>
        </choose>
        count(1) as count
        FROM t_jjdb as a
        <include refid="statisticRelation" />
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
            group by
            <choose>
                <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                    b.sssjdm,
                    b.sssjmc
                </when>
                <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                    b.ssfjdm,
                    b.ssfjmc
                </when>
                <otherwise>
                    a.gxdwdm,
                    b.gxdwmc
                </otherwise>
            </choose>
            order by
        <choose>
            <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                b.sssjdm
            </when>
            <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                b.ssfjdm
            </when>
            <otherwise>
                a.gxdwdm
            </otherwise>
        </choose>
    </select>

    <select id="getTimeSpanCount" resultType="com.trs.police.incident.analysis.domain.response.TimeSpanResponse">
        SELECT
        b.hour as hour,
        count(1) as count
        FROM t_jjdb AS a
        <include refid="statisticRelation" />
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        group by b.hour
        order by count desc
    </select>
    <select id="getPoliceSituationCommunityCount" resultType="com.trs.police.incident.analysis.domain.response.PoliceSituationCommunityCountResponse">
        SELECT
        b.sqbh,
        b.sqmc,
        count(1) as count
        FROM t_jjdb AS a
        <include refid="statisticRelation" />
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        group by b.sqbh
        order by count desc
    </select>
</mapper>