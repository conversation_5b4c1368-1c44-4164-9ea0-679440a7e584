<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.incident.analysis.mapper.PoliceStationAnalysisMapper">
    <select id="getTotalCount" resultType="java.lang.Integer">
        select
        count(1)
        FROM t_jjdb as a
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.PoliceSituationCommonQueryCondition" />
    </select>
</mapper>