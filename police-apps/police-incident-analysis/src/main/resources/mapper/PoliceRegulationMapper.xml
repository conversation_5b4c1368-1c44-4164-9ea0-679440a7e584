<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.incident.analysis.mapper.PoliceRegulationMapper">

    <select id="getViolationCount" resultType="com.trs.police.incident.analysis.domain.response.PoliceSituationDeptCountResponse">
        SELECT
        <choose>
            <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                b.sssjdm as bmbh,
                b.sssjmc as bmmc,
            </when>
            <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                b.ssfjdm as bmbh,
                b.ssfjmc as bmmc,
            </when>
            <otherwise>
                a.gxdwdm as bmbh,
                b.gxdwmc as bmmc,
            </otherwise>
        </choose>
        count(1) as count
        FROM  t_jjdb as a
        JOIN t_jjdbtj as b ON a.jjdbh = b.jjdbh
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        and b.cjzs60 = 1
        group by
        <choose>
            <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                b.sssjdm,
                b.sssjmc
            </when>
            <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                b.ssfjdm,
                b.ssfjmc
            </when>
            <otherwise>
                a.gxdwdm,
                b.gxdwmc
            </otherwise>
        </choose>
        order by count desc
    </select>
    <select id="getUnSignedCount" resultType="com.trs.police.incident.analysis.domain.response.PoliceSituationDeptCountResponse">
        SELECT
        <choose>
            <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                b.sssjdm as bmbh,
                b.sssjmc as bmmc,
            </when>
            <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                b.ssfjdm as bmbh,
                b.ssfjmc as bmmc,
            </when>
            <otherwise>
                a.gxdwdm as bmbh,
                b.gxdwmc as bmmc,
            </otherwise>
        </choose>
        count(1) as count
        FROM  t_jjdb as a
        JOIN t_jjdbtj as b ON a.jjdbh = b.jjdbh
        <include refid="com.trs.police.incident.analysis.mapper.SqlSnippetMapper.deptJoinAndBjrqCondition">
            <property name="mainTableAlias" value="a"/>
        </include>
        and b.wqs = 1
        group by
        <choose>
            <when test="dto.areaCode != null and dto.areaCode.length() == 2">
                b.sssjdm,
                b.sssjmc
            </when>
            <when test="dto.areaCode != null and dto.areaCode.length() == 4">
                b.ssfjdm,
                b.ssfjmc
            </when>
            <otherwise>
                a.gxdwdm,
                b.gxdwmc
            </otherwise>
        </choose>
        order by count desc
    </select>
</mapper>