package com.trs.police.incident.analysis.controller;


import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@SpringBootTest
public class ReportsControllerTest {

    private MockMvc mockMvc;

    @Resource
    private ReportsController reportsController;

    private AutoCloseable autoCloseable;

    @BeforeEach
    public void setup() {
        autoCloseable = MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(reportsController).build();
    }

    @AfterEach
    public void destroy() throws Exception {
        autoCloseable.close();
    }

    @Test
    public void repostCityTest() throws Exception {
        // 准备
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2025, 12, 31);
        MockHttpServletResponse response = mockMvc.perform(MockMvcRequestBuilders.get("/report/city")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED) //设置内容格式
                        .param("startDate", startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                        .param("endDate", endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                )
                .andDo(MockMvcResultHandlers.print())//返回打印结果
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn().getResponse();

        response.setCharacterEncoding("UTF-8"); //解决中文乱码问题

        byte[] contentAsByteArray = response.getContentAsByteArray();
        Assertions.assertNotNull(contentAsByteArray);
        FileUtils.writeByteArrayToFile(new File("data.xlsx"), contentAsByteArray);

    }

}
