package com.trs.police.spacetime.collision.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.police.spacetime.collision.domain.dto.ResultDto;
import com.trs.police.spacetime.collision.domain.dto.TrackDto;
import com.trs.police.spacetime.collision.domain.vo.CollisionTrailDotVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 轨迹信息表
 *
 * <AUTHOR>
 */
@DS("trino")
@Mapper
public interface GjxxbMapper {

    /**
     * 根据主记录id查询轨迹
     *
     * @param zjlid 主记录id
     * @return 轨迹
     */
    TrackDto selectTrackById(@Param("zjlid") String zjlid);

    /**
     * 根据任务id查询碰撞结果
     *
     * @param jobId 任务id
     * @return 结果
     */
    List<ResultDto> selectResultByJobId(@Param("jobId") String jobId);

    /**
     * 统计轨迹数量（多边形）
     *
     * @param wkt       wkt字符串
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    Long countTrackByAreaAndTime(@Param("wkt") String wkt, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 统计轨迹数量（圆）
     *
     * @param coordinateX 圆心x
     * @param coordinateY y
     * @param radius      半径
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @return 结果
     */
    Long countTrackByCircleAndTime(
            @Param("coordinateX") Double coordinateX,
            @Param("coordinateY") Double coordinateY,
            @Param("radius") Double radius,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime
    );

    /**
     * 查询原始轨迹
     *
     * @param tableName 表名
     * @param recordId  id
     * @return 结果
     */
    Map<String, Object> selectPhotoByRecordId(@Param("tableName") String tableName, @Param("recordId") String recordId);

    /**
     * 根据证件号码检索轨迹点位，活动时间倒叙，限制50条
     *
     * @param idNumber  证件号码
     * @param startTime 证件号码
     * @param endTime   证件号码
     * @return 轨迹点位结果
     */
    List<CollisionTrailDotVO> selectTrailDot(
            @Param("idNumber") String idNumber,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );
}
