package com.trs.police.spacetime.collision.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.vo.KeyValueVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 碰撞任务
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_spacetime_collision_job", autoResultMap = true)
public class CollisionJobEntity {

    /**
     * 任务id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 碰撞id
     */
    private Long collisionId;
    /**
     * 任务名称
     */
    private String jobName;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 任务状态
     */
    private Boolean success;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 碰撞结果
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private KeyValueVO[] result;

    public CollisionJobEntity(Long collisionId, String jobName) {
        this.collisionId = collisionId;
        this.jobName = jobName;
        this.startTime = LocalDateTime.now();
    }

}
