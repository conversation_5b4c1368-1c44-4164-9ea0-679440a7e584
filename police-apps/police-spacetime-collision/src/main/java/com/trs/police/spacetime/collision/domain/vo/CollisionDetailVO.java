package com.trs.police.spacetime.collision.domain.vo;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/9/12 15:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CollisionDetailVO extends CollisionCreateVO {

    /**
     * 状态
     */
    private Integer status;
    /**
     * 感知源类型名称
     */
    private List<String> sourceTypeName;
    /**
     * 碰撞结果
     */
    private List<CollisionResultVO> collisionResult;
    /**
     * 通知电话
     */
    private String notifyTel;
    /**
     * 创建用户
     */
    private String createUser;
    /**
     * 创建部门
     */
    private String createDept;
    /**
     * 创建时间
     */
    private String time;
    /**
     * 运行时长
     */
    private String runTime;
    /**
     * 任务开始运行时间
     */
    private LocalDateTime runStartTime;

    /**
     * 碰撞结果列表tab
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CollisionResultVO {
        private String displayType;
        private Integer count;
    }
}
