<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.spacetime.collision.mapper.CollisionMapper">

    <resultMap id="collisionListMap" type="com.trs.police.spacetime.collision.domain.vo.CollisionListVO">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="createUser" property="createUser"/>
        <result column="createDept" property="createDept"/>
        <result column="time" property="time"/>
        <result column="unRead" property="unRead"/>
        <result column="result" property="resultCount"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <sql id="selectWhere">
        <bind name="searchParams" value="request.searchParams"/>
        <bind name="filterParams" value="request.filterParams"/>
        <bind name="userId" value="@com.trs.police.common.core.utils.AuthHelper@getNotNullUser().id"/>
        <bind name="deptId" value="@com.trs.police.common.core.utils.AuthHelper@getNotNullUser().dept.id"/>
        <foreach collection="filterParams" item="param">
            <choose>
                <when test="param.key == 'createDept'">
                    <bind name="prefixCode"
                          value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value) + '%'"/>
                    and create_dept_id in (SELECT d.id
                    FROM t_dept d
                    WHERE
                    d.code LIKE #{prefixCode})
                </when>
                <when test="param.key == 'status'">
                    and status= #{param.value}
                </when>
                <when test="param.key == 'mine'">
                    and s.create_user_id= #{userId}
                </when>
            </choose>
        </foreach>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
            AND (name like #{pattern,jdbcType=VARCHAR})
        </if>
    </sql>

    <select id="collisionList" resultMap="collisionListMap">
        select
        s.id,
        s.name,
        s.status,
        s.notify_tel as notifyTel,
        (select u.real_name from t_user u where u.id=s.create_user_id ) as createUser,
        (select d.short_name from t_dept d where d.id=s.create_dept_id) as createDept,
        s.create_time as time,
        ifnull(s.un_read, 0) as unRead,
        j.result as result
        from t_spacetime_collision s left join t_spacetime_collision_job j on j.id = s.last_job_id
        <where>
            <include refid="selectWhere"/>
        </where>
        order by s.update_time desc
    </select>
    <select id="countCollision" resultType="com.trs.police.common.core.vo.IdNameCountVO">
        <bind name="searchParams" value="request.searchParams"/>
        <bind name="filterParams" value="request.filterParams"/>
        select
        count(1) as `count`,
        s.`status` as `name`
        from t_spacetime_collision s
        <where>
            <include refid="selectWhere"/>
        </where>
        group by s.`status`
    </select>

</mapper>