package com.trs.police.feedback.service;

import com.grt.condify.exception.CondifyException;
import com.trs.police.feedback.domain.dto.FeedbackMessageDTO;
import com.trs.police.feedback.domain.dto.FeedbackMessageListDTO;
import com.trs.police.feedback.domain.entity.FeedbackMessage;
import com.trs.police.feedback.domain.vo.FeedbackMessageVO;
import com.trs.web.builder.base.RestfulResultsV2;

/**
 * 反馈信息服务层
 *
 * <AUTHOR>
 * @since 2023/11/15 17:24
 **/

public interface FeedbackService extends BaseService<FeedbackMessage, FeedbackMessageVO> {

    /**
     * 保存反馈信息
     *
     * @param feedbackMessageDTO 反馈信息DTO
     * @return 保存后的反馈信息id
     */
    RestfulResultsV2<Long> save(FeedbackMessageDTO feedbackMessageDTO);

    /**
     * 查询反馈列表
     *
     * @param feedbackMessageListDTO 检索条件
     * @return 反馈列表-带分页
     */
    RestfulResultsV2<FeedbackMessageVO> queryList(FeedbackMessageListDTO feedbackMessageListDTO) throws CondifyException;

    /**
     * 查询我的历史反馈
     *
     * @param feedbackMessageListDTO 检索条件
     * @return 反馈列表-带分页
     */
    RestfulResultsV2<FeedbackMessageVO> queryMyFeedbackList(FeedbackMessageListDTO feedbackMessageListDTO) throws CondifyException;

    /**
     * 获取反馈详情
     *
     * @param id 主键
     * @return 反馈详情
     */
    RestfulResultsV2<FeedbackMessageVO> getFeedbackDetail(Long id);

    /**
     * 回复反馈
     *
     * @param id       主键
     * @param replyMsg 回复内容
     * @return 反馈详情
     */
    RestfulResultsV2<String> reply(Long id, String replyMsg);

    /**
     * 设置已读
     *
     * @param id       主键
     * @return 设置已读
     */
    RestfulResultsV2<String> read(Long id);
}
