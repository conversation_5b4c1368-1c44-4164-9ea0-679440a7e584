package com.trs.police.feedback.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description 前端入参-反馈消息(用于保存等form表单提交)
 * @date 2023/11/13 15:41
 */
@Data
public class FeedbackMessageDTO {

    @ApiModelProperty(value = "反馈信息id，0为新增，大于0为编辑")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @NotEmpty
    private String title;

    /**
     * 系统模块:1-工作台，2-风险，3-管控，4-作战，5-档案，6-挂账，7-审批，8-统计，9-应用，10-fx专题，11-jz专题
     */
    @ApiModelProperty(value = "系统模块:1-工作台，2-风险，3-管控，4-作战，5-档案，6-挂账，7-审批，8-统计，9-应用，10-fx专题，11-jz专题")
    private Integer module;

    /**
     * 反馈类型：1-优化建议，2-系统故障
     */
    @ApiModelProperty(value = "反馈类型：1-优化建议，2-系统故障")
    private Integer feedbackType;

    /**
     * 问题描述
     */
    @ApiModelProperty(value = "问题描述")
    private String content;

    /**
     * 文件id列表
     */
    @ApiModelProperty(value = "文件id列表")
    private List<Long> fileListId;

    /**
     * 模块类型：0-ys,1-门户
     */
    @ApiModelProperty(value = "模块类型：0-ys,1-门户")
    private Integer moduleType;

}
