package com.trs.police.feedback.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.trs.web.builder.base.RestfulResultsV2;

/**
 * service基类
 *
 * @param <T> 实体
 * @param <V> VO
 */
public interface BaseService<T, V> extends IService<T> {
    /**
     * 分页查询
     *
     * @param page         分页信息
     * @param queryWrapper 查询条件
     * @return 查询结果
     */
    RestfulResultsV2<V> queryForPage(IPage<T> page, QueryWrapper<T> queryWrapper);

}
