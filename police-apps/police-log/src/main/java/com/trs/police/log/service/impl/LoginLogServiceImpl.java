package com.trs.police.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.WcspsdkUtils;
import com.trs.police.common.core.vo.log.LoginLogVO;
import com.trs.police.log.domain.dto.LoginLogDTO;
import com.trs.police.log.domain.entity.LoginLogEntity;
import com.trs.police.log.mapper.LoginLogMapper;
import com.trs.police.log.service.LoginLogService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @since 2022/11/9 18:25
 **/
@Service
public class LoginLogServiceImpl implements LoginLogService {

    @Resource
    private LoginLogMapper loginLogMapper;

    @Override
    @Transactional(rollbackFor = TRSException.class)
    public void createLoginLog(LoginLogVO loginLogVO) {
        // 原始的deptIdPath只到上级，不包含本级，这里添加本级
        loginLogVO.setDeptIdPath(buildDeptIdPath(loginLogVO.getDeptIdPath(), loginLogVO.getDeptId()));
        LoginLogEntity loginLogEntity = new LoginLogEntity();
        BeanUtil.copyPropertiesIgnoreNull(loginLogVO, loginLogEntity);
        loginLogEntity.setMac(WcspsdkUtils.mac(loginLogEntity.toMacString()));
        loginLogMapper.insert(loginLogEntity);
    }

    private String buildDeptIdPath(String idPath, Long deptId) {
        if (StringUtils.isNullOrEmpty(idPath)) {
            return "1";
        }
        if (idPath.startsWith("-") && !idPath.startsWith("-1-")) {
            return String.format("1%s%s", idPath, deptId);
        } else {
            return idPath + deptId;
        }
    }

    @Override
    public RestfulResultsV2<LoginLogVO> loginLogList(LoginLogDTO dto) {

        String searchField = null;
        if ("user".equals(dto.getKeywordType())) {
            searchField = "user_name";
        }
        if ("ip".equals(dto.getKeywordType())) {
            searchField = "ip_address";
        }

        QueryWrapper<LoginLogEntity> queryWrapper = new QueryWrapper<LoginLogEntity>()
                .ge(StringUtils.isNotEmpty(dto.getStartTime()), "create_time", dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()), "create_time", dto.getEndTime());
                if (StringUtils.isNotEmpty(dto.getDeptInfo())) {
                    queryWrapper.and(wrapper -> wrapper
                            // 从父级部门检索
                            .likeRight("dept_id_path", dto.getDeptInfo() + "-")
                            .or()
                            // 直接就是本部门
                            .eq("dept_id_path", dto.getDeptInfo())
                    );
                }
                // 按组织搜索

        if("all".equals(dto.getKeywordType()) && StringUtils.isNotEmpty(dto.getKeyword())) {
            // 构造查询条件 检索两个字段
            queryWrapper.and(wrapper -> wrapper
                    .like("user_name", dto.getKeyword())
                    .or()
                    .like("ip_address", dto.getKeyword())
            );
        } else if (searchField != null && StringUtils.isNotEmpty(dto.getKeyword())){
            // 构造检索条件 检索指定字段
            queryWrapper.like(searchField, dto.getKeyword());
        }
        if ("asc".equals(dto.getOrderType())) {
            queryWrapper.orderByAsc("create_time");
        } else {
            queryWrapper.orderByDesc("create_time");
        }
        Page<LoginLogEntity> page = Page.of(dto.getPageNum(), dto.getPageSize());
        Page<LoginLogEntity> loginLogEntityPage = loginLogMapper.selectPage(page, queryWrapper);
        loginLogEntityPage.getRecords().forEach(loginLogEntity -> PreConditionCheck.checkArgument(WcspsdkUtils.verifyMac(loginLogEntity.getMac(), loginLogEntity.toMacString()), loginLogEntity.getId()+"日志被篡改！"));
        List<LoginLogVO> loginLogVOList =  loginLogEntityPage.getRecords().stream().map(loginLogEntity -> {
            LoginLogVO loginLogVO = new LoginLogVO();
            BeanUtil.copyPropertiesIgnoreNull(loginLogEntity, loginLogVO);
            return loginLogVO;
        }).collect(Collectors.toList());
        return RestfulResultsV2.ok(loginLogVOList)
                .addTotalCount(loginLogEntityPage.getTotal())
                .addPageSize((int) loginLogEntityPage.getSize())
                .addPageNum((int) loginLogEntityPage.getCurrent());
    }

    @Override
    public RestfulResultsV2 encryptHistory(LoginLogDTO dto) {
        QueryWrapper<LoginLogEntity> queryWrapper = new QueryWrapper<LoginLogEntity>()
                .eq(StringUtils.isNotEmpty(dto.getUserName()), "user_name", dto.getUserName())
                .ge(StringUtils.isNotEmpty(dto.getStartTime()), "create_time", dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()), "create_time", dto.getEndTime())
                .orderByDesc("create_time");
        List<LoginLogEntity> loginLogList = loginLogMapper.selectList(queryWrapper);
        loginLogList.forEach(loginLogMapper::updateById);
        return RestfulResultsV2.ok();
    }

    @Override
    public RestfulResultsV2 encryptHistoryMac(LoginLogDTO dto) {
        QueryWrapper<LoginLogEntity> queryWrapper = new QueryWrapper<LoginLogEntity>()
                .eq(StringUtils.isNotEmpty(dto.getUserName()), "user_name", dto.getUserName())
                .ge(StringUtils.isNotEmpty(dto.getStartTime()), "create_time", dto.getStartTime())
                .le(StringUtils.isNotEmpty(dto.getEndTime()), "create_time", dto.getEndTime())
                .orderByDesc("create_time");
        List<LoginLogEntity> loginLogList = loginLogMapper.selectList(queryWrapper);
        loginLogList.forEach(loginLogEntity -> {
            loginLogEntity.setMac(WcspsdkUtils.mac(loginLogEntity.toMacString()));
            loginLogMapper.updateById(loginLogEntity);
        });
        return RestfulResultsV2.ok();
    }
}
