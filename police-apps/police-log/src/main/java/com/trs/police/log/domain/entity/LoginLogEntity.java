package com.trs.police.log.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.chenhaiyang.plugin.mybatis.sensitive.annotation.EncryptField;
import com.chenhaiyang.plugin.mybatis.sensitive.annotation.SensitiveEncryptEnabled;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @since 2022/11/9 18:17
 **/
@SensitiveEncryptEnabled
@Data
@AllArgsConstructor
@TableName("t_login_log")
@NoArgsConstructor
public class LoginLogEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long deptId;

    private String deptName;

    /**
     * 部门id路径，包含本级
     */
    private String deptIdPath;
    /**
     * 登录方式 pwd pki
     */
    private String type;

    private String userName;

    private String detail;

    private String ipAddress;

    private LocalDateTime createTime;

    @EncryptField
    private String idCard;

    private String trueName;

    private String mac;

    /**
     * 生成mac字符串
     *
     * @return 结果
     */
    public String toMacString() {
        return "LoginLogEntity{" +
                "id=" + id +
                ", deptId=" + deptId +
                ", deptName='" + deptName + '\'' +
                ", type='" + type + '\'' +
                ", userName='" + userName + '\'' +
                ", detail='" + detail + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", createTime=" + createTime +
                ", idCard='" + idCard + '\'' +
                ", trueName='" + trueName + '\'' +
                '}';
    }
}
