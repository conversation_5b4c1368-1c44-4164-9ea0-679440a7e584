package com.trs.police.log.manager.impl;

import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.log.domain.entity.OperationLogEntity;
import com.trs.police.log.domain.vo.RiskLogVO;
import com.trs.police.log.manager.AbstractLogManager;
import com.trs.police.log.util.LogUtil;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 常控预警配置日志
 *
 * <AUTHOR>
 * @since 2023/1/10 16:28
 **/
@Component
public class RiskManagerImpl implements AbstractLogManager<RiskLogVO> {

    @Resource
    private PermissionService permissionService;

    @Override
    public RiskLogVO searchLog(OperationLogEntity operationLogEntity) {
        CurrentUser currentUser = permissionService
            .findCurrentUser(operationLogEntity.getCreateUserId(), operationLogEntity.getCreateDeptId());
        String detail = operationLogEntity.getDetail();
        RiskLogVO vo = LogUtil.of(new RiskLogVO(), operationLogEntity, currentUser);

        switch (operationLogEntity.getOperation()) {
            case ADD_RELATION_TRACEABLE_DATA:
            case DELETE_RELATION_TRACEABLE_DATA:
                vo.setRelation(JsonUtil.parseJsonNode(detail));
                break;
            case ADD_PERSON:
            case DELETE_PERSON:
                vo.setPerson(JsonUtil.parseJsonNode(detail));
                break;
            case FEEDBACK:
                vo.setFeedback(JsonUtil.parseJsonNode(detail));
                break;
            case CREATE_RISK:
                vo.setCreate(JsonUtil.parseJsonNode(detail));
                break;
            case JUDGE:
                vo.setJudge(JsonUtil.parseJsonNode(detail));
                break;
            default:
                vo.setContent(detail);
                break;
        }
        return vo;
    }

}
