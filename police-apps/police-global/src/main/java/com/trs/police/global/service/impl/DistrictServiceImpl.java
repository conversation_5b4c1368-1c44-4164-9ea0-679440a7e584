package com.trs.police.global.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.entity.District;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.global.config.GlobalAppConfig;
import com.trs.police.global.mapper.DistrictMapper;
import com.trs.police.global.service.DistrictService;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 行政区划服务实现类
 *
 * <AUTHOR>
 * @date 2022/09/08
 */
@Service
@RequiredArgsConstructor
public class DistrictServiceImpl implements DistrictService {

    private final DistrictMapper districtMapper;

    private final GlobalAppConfig globalAppConfig;


    /**
     * 检索线索连接符
     */
    private static final String JOIN_CHARACTER = "-";

    @Override
    @Cacheable(value = {"districtTree"}, key = "#code")
    public List<DistrictDto> getDistrictTree(String code) {
        String all = "all";
        if (all.equals(code)) {
            code = globalAppConfig.getTopLevelDistrictCode();
        }
        District district = districtMapper.getByCodeRecursive(code);
        return district == null ? Collections.emptyList() : List.of(district.toDto());
    }

    @Override
    public String getNameByCode(String code) {
        return districtMapper.getNameByCode(code);
    }

    @Override
    public List<DistrictListDto> getByLevel(Integer level) {
        return districtMapper.getByLevel(level);
    }

    @Override
    public List<String> getPathByCode(String code) {
        District district = districtMapper.getByCode(code);
        LinkedList<String> path = new LinkedList<>();
        String topLevelCode = globalAppConfig.getTopLevelDistrictCode();
        while (!district.getCode().equals(topLevelCode)) {
            path.addFirst(district.getCode());
            district = districtMapper.getByCode(district.getPCode());
        }
        path.addFirst(district.getCode());
        return path;
    }

    /**
     * 通过父节点的线索构造当前节点的path
     *
     * @param parent 父节点
     * @return 当前节点线索 从根节点id到父节点id的路径
     */
    public static String buildPath(District parent) {
        if (Objects.isNull(parent)) {
            return buildPath();
        }
        return parent.getPath()
                + parent.getCode()
                + JOIN_CHARACTER;
    }

    /**
     * 构造path
     *
     * @return path
     */
    public static String buildPath() {
        return JOIN_CHARACTER;
    }

    @Override
    public List<DistrictDto> getOneLayerDistrictList(String areaCode) {
        List<District> districts;
        if (StringUtils.isEmpty(areaCode)) {
            districts = districtMapper.selectList(new QueryWrapper<District>().eq("level", "1"));
        } else {
            districts = districtMapper.selectList(new QueryWrapper<District>().eq("p_code", areaCode));
        }
        if (CollectionUtils.isEmpty(districts)) {
            return new ArrayList<>();
        }
        List<String> pCodes = districts.stream().map(District::getCode).collect(Collectors.toList());
        //获取当前待返回区域的子节点区域，以便前端判断当前节点是否有下一级
        List<District> districtList = districtMapper.selectList(new QueryWrapper<District>().in("p_code", pCodes));
        List<DistrictDto> list = new ArrayList<>();
        for (District district : districts) {
            DistrictDto dto = district.toDto();
            dto.setIsLeaf(CollectionUtils.isEmpty(districtList.stream().filter(e -> e.getPCode().equals(district.getCode()))
                    .collect(Collectors.toList())));
            list.add(dto);
        }
        return list;
    }

    /**
     * 获取默认路径
     *
     * @param province 省份code
     * @param city     城市code
     * @param area     区县code
     * @return 默认路径
     */
    private List<DistrictDto> getDefaultDistrictPath(String province, String city, String area) {
        List<District> cities = districtMapper.selectList(new QueryWrapper<District>().eq("p_code", province));
        List<District> areas = districtMapper.selectList(new QueryWrapper<District>().eq("p_code", city));
        District cityDistrict = cities.stream().filter(e -> e.getCode().equals(city)).findFirst().orElse(null);
        if (Objects.isNull(cityDistrict)) {
            return new ArrayList<>();
        }
        DistrictDto cityDto = cityDistrict.toDto();
        District areaDistrict = areas.stream().filter(e -> e.getCode().equals(area)).findFirst().orElse(null);
        if (Objects.isNull(areaDistrict)) {
            return new ArrayList<>();
        }
        DistrictDto areaDto = areaDistrict.toDto();
        cityDto.setChildren(Collections.singletonList(areaDto));
        cityDto.setIsLeaf(false);
        return List.of(cityDto);
    }

    @Override
    public DistrictListDto getByCode(String code) {
        if (Objects.equals("all", code)) {
            code = globalAppConfig.getTopLevelDistrictCode();
        }
        return districtMapper.getDistrictByCode(code);
    }


    private void updateChildPath(District parent) {
        final List<District> child = districtMapper.getByParentCode(parent.getCode());
        if (child.isEmpty()) {
            return;
        }
        child.forEach(item -> {
            item.setPath(buildPath(parent));
            districtMapper.updatePathById(item.getId(), item.getPath());
            updateChildPath(item);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePath() {
        final District parent = districtMapper.getByCode(globalAppConfig.getTopLevelDistrictCode());
        parent.setPath(buildPath());
        districtMapper.updatePathById(parent.getId(), parent.getPath());
        updateChildPath(parent);
    }

    @Override
    public List<DistrictDto> getByDistrictCodes(List<String> codes) {
        return districtMapper.getByDistrictCodes(codes);
    }

    @Override
    public DistrictListDto getCurrentUserDistrict() {
        return districtMapper.getDistrictByCode(Objects.requireNonNull(AuthHelper.getCurrentUser()).getDept().getDistrictCode());
    }

    @Override
    public List<DistrictDto> getByDistrictNames(List<String> names) {
        return districtMapper.getByDistrictNames(names);
    }

    @Override
    public List<District> findByDistrictNames(List<String> names) {
        return districtMapper.findByDistrictNames(names);
    }


}
