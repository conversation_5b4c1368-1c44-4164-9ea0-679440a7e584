package com.trs.police.global.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.constant.log.OperateModule;
import lombok.Data;

import java.io.Serializable;

/**
 * 模块表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "t_operate_module", autoResultMap = true)
public class OperateModuleEntity implements Serializable {

    private static final long serialVersionUID = 3388615014322987006L;
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 中文名
     */
    private String cnName;
    /**
     * 英文名
     */
    private String enName;
    /**
     * 父id
     */
    private Long pid;
    /**
     * 路径
     */
    private String path;
    /**
     * 是否待办
     */
    private Long isTodo;

    /**
     * 是否待办（APP）
     */
    private Long isTodoForApp;

    private Boolean enable;

    private String description;

    private String url;

    private String pathName;

    /**
     * 返回枚举
     *
     * @return OperateModule
     */
    public OperateModule toEnum() {
        return OperateModule.codeOf(this.id);
    }
}
