package com.trs.police.global.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> yanghy
 * @date : 2022/9/15 19:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoiImportVO {

    @ExcelProperty("name")
    private String name;


    @ExcelProperty("address")
    private String address;


    @ExcelProperty("pname")
    private String pname;


    @ExcelProperty("cityname")
    private String cityName;


    @ExcelProperty("adcode")
    private String adcode;


    @ExcelProperty("adname")
    private String adname;


    @ExcelProperty("type")
    private String type;


    @ExcelProperty("typename")
    private String typename;


    @ExcelProperty("lon")
    private Double lon;

    @ExcelProperty("lat")
    private Double lat;

    /**
     * 转dto
     *
     * @return {@link PoiDto}
     */
    public PoiDto toPoiDto() {
        PoiDto locationVO = new PoiDto();
        BeanUtils.copyProperties(this, locationVO);
        locationVO.setLocation(new CoordinateVO(lon, lat));
        String s = type.split("\\|")[0];
        String[] split = s.split(";");
        if (split.length > 0) {
            locationVO.setLType(split[0]);
        }
        if (split.length > 1) {
            locationVO.setMType(split[1]);
        }
        if (split.length > 2) {
            locationVO.setSType(split[2]);
        }
        return locationVO;
    }
}
