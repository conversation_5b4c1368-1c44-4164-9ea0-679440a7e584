DELIMITER $$
DROP PROCEDURE IF EXISTS `update_case_tag_records` $$
CREATE PROCEDURE update_case_tag_records()
BEGIN
    -- 查询每个 id 对应的 p_id
    SELECT DISTINCT id, p_id FROM t_dict WHERE type = 'case_tag';

    -- 更新 t_dict 表中 type=case_tag 记录的 code、p_code 和 p_id 字段
    UPDATE t_dict t1
    JOIN (
        SELECT DISTINCT id, p_id FROM t_dict WHERE type = 'case_tag'
    ) t2 ON t1.id = t2.id
    SET t1.p_code = t2.p_id
    WHERE t1.type = 'case_tag';
END $$
DELIMITER ;
CALL update_case_tag_records;
DROP PROCEDURE IF EXISTS `update_case_tag_records`;
