DELIMITER $$
DROP PROCEDURE IF EXISTS `modify_column` $$
CREATE PROCEDURE modify_column()
BEGIN
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='t_profile_event' AND column_name='event_code'
    )
    THEN
ALTER TABLE t_profile_event ADD event_code varchar(100) NULL COMMENT '事件编号';
END IF;
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='t_profile_event' AND column_name='source_time'
    )
    THEN
ALTER TABLE t_profile_event ADD source_time datetime NULL COMMENT '来源时间';
END IF;
END $$
DELIMITER ;
CALL modify_column;
DROP PROCEDURE IF EXISTS `modify_column`;