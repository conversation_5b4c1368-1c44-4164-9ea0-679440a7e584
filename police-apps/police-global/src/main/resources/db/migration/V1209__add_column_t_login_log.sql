DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_login_log' AND column_name='dept_id_path')
THEN
alter table t_login_log add column `dept_id_path` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '部门id路径，包含本级';
END IF;


END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;