DELIMITER $$
DROP PROCEDURE IF EXISTS `update_records` $$
CREATE PROCEDURE update_records()
BEGIN
    DECLARE ccode,ccode2 INT;
    -- 更新p_code
    SET ccode = (select code from t_dict where `type`='collaboration_status' and `name`='协作状态' and `code`=0);
    update t_dict set p_code = code where `type` = 'collaboration_status' and `code` IN(1,2,3,4,5,6);

    SET ccode2 = (select code from t_dict where `type`='fight_result_instruct_type' and `name`='战果填报批示状态' and `code`=0);
    update t_dict set p_code = ccode2 where `type` = 'fight_result_instruct_type' and `code` IN(1,2,3);
END $$
DELIMITER ;
CALL update_records;
DROP PROCEDURE IF EXISTS `update_records`;