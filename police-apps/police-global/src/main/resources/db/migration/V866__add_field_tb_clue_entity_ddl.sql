DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_clue_entity' AND column_name='part_police_kinds'
    )
    THEN
    ALTER TABLE tb_clue_entity ADD part_police_kinds VARCHAR(1000) DEFAULT NULL COMMENT '参与警种代码，多个逗号分割';
    END IF;
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_clue_entity' AND column_name='part_police_types'
    )
    THEN
    ALTER TABLE tb_clue_entity ADD part_police_types VARCHAR(1000) DEFAULT NULL COMMENT '参与警种名称，多个逗号分割';
    END IF;
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_clue_entity' AND column_name='sign_flag'
    )
    THEN
    ALTER TABLE tb_clue_entity ADD sign_flag INT(2) DEFAULT 0 NULL COMMENT '是否保密\n0：不保密\n1：保密';
    END IF;
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_clue_entity' AND column_name='jjcd'
    )
    THEN
    ALTER TABLE tb_clue_entity ADD jjcd VARCHAR(100) DEFAULT NULL COMMENT '紧急程度';
    END IF;
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_clue_entity' AND column_name='cjfs'
    )
    THEN
    ALTER TABLE tb_clue_entity ADD cjfs VARCHAR(100) DEFAULT NULL COMMENT '采集方式';
    END IF;
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_clue_entity' AND column_name='source'
    )
    THEN
    ALTER TABLE tb_clue_entity ADD `source` VARCHAR(100) DEFAULT NULL COMMENT '来源';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;
