DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_intelligence_renwu_related_person' AND column_name='work_method')
    THEN
        ALTER TABLE `tb_intelligence_renwu_related_person` ADD COLUMN `work_method` VARCHAR(100) NULL COMMENT '工作方式' AFTER `work_status`;
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;

