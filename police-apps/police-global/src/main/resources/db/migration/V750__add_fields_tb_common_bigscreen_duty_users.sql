-- 补充值班日期字段
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_common_bigscreen_duty_users' AND column_name='duty_time')
    THEN
ALTER TABLE tb_common_bigscreen_duty_users ADD duty_time DATETIME NULL COMMENT '值班日期(yyyy-MM-dd)';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;