DROP PROCEDURE IF EXISTS AddColumnIfNotExists;
DELIMITER //
CREATE PROCEDURE AddColumnIfNotExists(IN tableName VARCHAR(64), IN columnName VARCHAR(64), IN columnDetails VARCHAR(64))
BEGIN
    DECLARE existing INT DEFAULT 0;
    SELECT COUNT(*)
    INTO existing
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = tableName AND COLUMN_NAME = columnName;
    IF existing = 0 THEN
        SET @s = CONCAT('ALTER TABLE ', tableName, ' ADD ', columnName, ' ', columnDetails);
        PREPARE stmt FROM @s;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //
DELIMITER ;
CALL AddColumnIfNotExists('t_warning_fk_person', 'last_warning_id', 'BIGINT NULL COMMENT \'最后预警ID\'');
CALL AddColumnIfNotExists('t_warning_fkrxyj', 'source_type', 'INT NULL COMMENT \'轨迹类型\'');
CALL AddColumnIfNotExists('t_warning_fkrxyj', 'location', 'POINT NULL COMMENT \'轨迹\'');
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;

DROP PROCEDURE IF EXISTS AddColumnIfNotExists;
DELIMITER //
CREATE PROCEDURE AddColumnIfNotExists(IN tableName VARCHAR(64), IN columnName VARCHAR(64), IN columnDetails VARCHAR(64))
BEGIN
    DECLARE existing INT DEFAULT 0;
    SELECT COUNT(*)
    INTO existing
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = tableName AND COLUMN_NAME = columnName;
    IF existing = 1 THEN
        SET @s = CONCAT('ALTER TABLE ', tableName, ' MODIFY COLUMN ', columnName, ' ', columnDetails);
        PREPARE stmt FROM @s;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //
DELIMITER ;
CALL AddColumnIfNotExists('t_monograph_dept_relation', 'dept_content', 'LONGTEXT COMMENT \'填写内容\'');
CALL AddColumnIfNotExists('t_monograph_directory_dept_relation', 'directory_content', 'LONGTEXT COMMENT \'填写内容\'');
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;


