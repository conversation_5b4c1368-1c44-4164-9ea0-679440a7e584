delete from t_profile_module where id in (1650,1649,1648,1647,1646,1645,1644,1643,1642,1640,1638,1637,1633,1632);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1650, '群体成员分布', 'relatedPersonArea', 'fkGroup', 1647, 1, 4, NULL, NULL, '{}', 0, '{}', 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1649, '关联杆体', 'relatedGt', 'fkGroup', 1647, 1, 2, NULL, NULL, '{}', 0, '{}', 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1648, '档案管理', 'archive', 'fkGroup', NULL, 0, 1, '{}', '{}', '{"name": "群体信息", "type": "LIST_SCHEMA", "table": "(select  a.id as id,  a.name as groupName,  a.business_category as businessCategory,  p.name as leaderName,  p.id_number as leaderIdNumber,  p.registered_residence as leaderRegisteredResidence,  p.basic_investigation_address as leaderBasicInvestigationAddress,  p.work_address as leaderWorkAddress, p.work_address_detail as leaderWorkAddressDetail, p.occupation_category as leaderOccupationCategory,  p.inflow_time as leaderInflowTime,  (select count(*) from t_profile_person_group_relation pgr where pgr.group_id = a.id) as personCount from  t_profile_group as a left join (  SELECT  *,  ROW_NUMBER() OVER (PARTITION BY group_id  ORDER BY  create_time DESC) AS rn  FROM  t_profile_person_group_relation  where  JSON_OVERLAPS(`identity`, ''1'')  ) AS r ON  a.id = r.group_id  AND r.rn = 1 left join t_profile_person p on  p.id = r.person_id)", "fields": [{"db": {"column": "groupName", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center", "width": 100, "ellipsis": true}, "schema": {"type": "string", "title": "名称"}, "properties": {"href": "/ys-app/archives/group/details?id={value}", "isName": true, "copyable": false, "editable": false, "required": false, "sortable": false, "isRelatedShow": true}}}, {"db": {"column": "leaderWorkAddressDetail", "jdbcType": "string"}, "name": "leaderWorkAddressDetail", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "地址"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"column": "leaderName", "jdbcType": "string"}, "name": "leaderName", "listSchema": {"style": {"align": "center", "width": 100, "ellipsis": true}, "schema": {"type": "string", "title": "领头人"}, "properties": {"isName": false, "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"column": "leaderRegisteredResidence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "leaderRegisteredResidence", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "户籍地址"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"column": "leaderBasicInvestigationAddress", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "leaderBasicInvestigationAddress", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "基础摸排地址"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"column": "leaderWorkAddress", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "leaderWorkAddress", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "务工地址"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"column": "businessCategory", "jdbcType": "string"}, "name": "businessCategory", "listSchema": {"style": {"align": "center", "width": 100, "ellipsis": true}, "schema": {"type": "string", "title": "经营类别"}, "properties": {"isName": false, "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"column": "leaderOccupationCategory", "jdbcType": "string"}, "name": "leaderOccupationCategory", "listSchema": {"style": {"align": "center", "width": 100, "ellipsis": true}, "schema": {"type": "string", "title": "职业"}, "properties": {"isName": false, "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"column": "leaderInflowTime", "mapping": "date_to_text", "jdbcType": "string"}, "name": "leaderInflowTime", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "流入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"column": "personCount", "jdbcType": "number"}, "name": "personCount", "listSchema": {"style": {"align": "center", "width": 100, "ellipsis": true}, "schema": {"type": "string", "title": "关联人数"}, "properties": {"href": "/ys-app/archives/group/details?id={value}#relatedPerson", "copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": true, "searchFields": [{"key": "name", "name": "群体名称"}]}', 0, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1647, '基本信息', 'basicInfo', 'fkGroup', NULL, 1, 2, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1646, '群体信息', 'fkGroup', 'fkGroup', 1647, 1, 1, '{"name": "群体信息", "type": "TABLE_SCHEMA", "table": "t_profile_group", "fields": [{"db": {"table": "t_profile_group", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "群体名称", "copyable": false}}, {"db": {"table": "t_profile_group", "column": "group_label", "mapping": "label_id_array_to_name", "jdbcType": "json_id_array"}, "name": "group_label", "tableSchema": {"span": 1, "type": "label", "title": "群体类别", "copyable": false}}, {"db": {"table": "t_profile_group", "column": "control_level", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_control_level"}, "name": "control_level", "tableSchema": {"span": 1, "type": "string", "title": "群体级别", "copyable": false}}, {"db": {"table": "t_profile_person_group_relation", "column": "count(1)", "jdbcType": "number", "databaseRelation": {"type": "FOREIGN_KEY", "table": "t_profile_person_group_relation", "column": "group_id"}}, "name": "personCount", "tableSchema": {"span": 1, "type": "string", "title": "关联人数", "copyable": false}}, {"db": {"table": "t_profile_group", "column": "business_category", "jdbcType": "string"}, "name": "business_category", "tableSchema": {"span": 1, "type": "string", "title": "经营类别", "copyable": false}}, {"db": {"table": "t_profile_group", "column": "basic_info", "jdbcType": "string"}, "name": "basic_info", "tableSchema": {"span": 4, "type": "string", "title": "基本情况", "copyable": false}}], "moduleUi": {"column": 4, "bordered": true}}', '{"name": "群体信息", "type": "FORM_SCHEMA", "table": "t_profile_group", "fields": [{"db": {"table": "t_profile_group", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "群体名称"}}}, {"db": {"table": "t_profile_group", "column": "control_level", "jdbcType": "string"}, "dict": {"type": "profile_person_control_level"}, "name": "control_level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "群体级别"}}}, {"db": {"table": "t_profile_group", "column": "group_label", "jdbcType": "json_id_array"}, "name": "groupLabel", "tree": {"root": "group", "type": "label"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array"}, "title": "群体类别"}}}, {"db": {"table": "t_profile_group", "column": "business_category", "jdbcType": "string"}, "name": "business_category", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "经营类别"}}}, {"db": {"table": "t_profile_group", "column": "basic_info", "jdbcType": "string"}, "name": "basic_info", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "基本情况"}}}], "required": ["name"]}', NULL, 1, '{"type": "PRIMARY_KEY", "table": "t_profile_group", "column": "id"}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1645, '警种信息', 'control', 'fkGroup', NULL, 1, 3, NULL, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1644, '政府管控信息', 'govControl', 'fkGroup', 1645, 1, 1, '{"name": "政府管控信息", "table": "t_profile_group_government_control", "fields": [{"db": {"table": "t_profile_group_government_control", "column": "control_government", "jdbcType": "string"}, "name": "control_government", "tableSchema": {"span": 1, "type": "string", "title": "党政责任部门", "copyable": false}}, {"db": {"table": "t_profile_group_government_control", "column": "control_government_person", "jdbcType": "string"}, "name": "control_government_person", "tableSchema": {"span": 1, "type": "string", "title": "党政责任人", "copyable": false}}, {"db": {"table": "t_profile_group_government_control", "column": "control_government_person_duty", "jdbcType": "string"}, "name": "control_government_person_duty", "tableSchema": {"span": 1, "type": "string", "title": "党政责任人职务", "copyable": false}}, {"db": {"table": "t_profile_group_government_control", "column": "control_government_contact", "jdbcType": "string"}, "name": "control_government_contact", "tableSchema": {"span": 1, "type": "string", "title": "联系方式", "copyable": true}}, {"db": {"table": "t_profile_group_government_control", "column": "control_community", "jdbcType": "string"}, "name": "control_community", "tableSchema": {"span": 1, "type": "string", "title": "责任街道社区", "copyable": false}}, {"db": {"table": "t_profile_group_government_control", "column": "control_community_person", "jdbcType": "string"}, "name": "control_community_person", "tableSchema": {"span": 1, "type": "string", "title": "社区责任人", "config": {"fieldName": "control_community_person", "processType": "string", "processConfig": null}, "copyable": false}}, {"db": {"table": "t_profile_group_government_control", "column": "control_community_person_duty", "jdbcType": "string"}, "name": "control_community_person_duty", "tableSchema": {"span": 1, "type": "string", "title": "社区责任人职务", "copyable": false}}, {"db": {"table": "t_profile_group_government_control", "column": "control_community_contact", "jdbcType": "string"}, "name": "control_community_contact", "tableSchema": {"span": 1, "type": "string", "title": "联系方式", "copyable": true}}], "moduleUi": {"column": 3, "bordered": true}}', '{"name": "政府管控信息", "type": "FORM_SCHEMA", "table": "t_profile_group_government_control", "fields": [{"db": {"table": "t_profile_group_government_control", "column": "control_government", "jdbcType": "string"}, "name": "controlGovernment", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "党政责任部门"}}}, {"db": {"table": "t_profile_group_government_control", "column": "control_government_person", "jdbcType": "string"}, "name": "controlGovernmentPerson", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "党政责任人"}}}, {"db": {"table": "t_profile_group_government_control", "column": "control_government_person_duty", "jdbcType": "string"}, "name": "controlGovernmentPersonDuty", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "党政责任人职务"}}}, {"db": {"table": "t_profile_group_government_control", "column": "control_government_contact", "jdbcType": "string"}, "name": "controlGovernmentContact", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "联系方式"}}}, {"db": {"table": "t_profile_group_government_control", "column": "control_community", "jdbcType": "string"}, "name": "controlCommunity", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "责任街道社区"}}}, {"db": {"table": "t_profile_group_government_control", "column": "control_community_person", "jdbcType": "string"}, "name": "controlCommunityPerson", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "社区责任人"}}}, {"db": {"table": "t_profile_group_government_control", "column": "control_community_person_duty", "jdbcType": "string"}, "name": "controlCommunityPersonDuty", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "社区责任人职务"}}}, {"db": {"table": "t_profile_group_government_control", "column": "control_community_contact", "jdbcType": "string"}, "name": "controlCommunityContact", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "联系方式"}}}], "required": []}', NULL, 1, '{"type": "FOREIGN_KEY", "table": "t_profile_group_government_control", "column": "group_id", "extendCondition": [{"value": 12, "column": "police_kind"}]}', 'TABLE_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1643, '公安管控信息', 'policeControl', 'fkGroup', 1645, 1, 2, '{"name": "公安管控信息", "type": "TABLE_SCHEMA", "table": "t_profile_group_police_control", "fields": [{"db": {"table": "t_profile_group_police_control", "column": "control_bureau", "mapping": "dept_code_to_dept_name", "jdbcType": "string"}, "name": "control_bureau", "tree": {"type": "dept"}, "tableSchema": {"span": 1, "type": "string", "title": "责任分局", "copyable": false}}, {"db": {"table": "t_profile_group_police_control", "column": "control_bureau_leader", "mapping": "user_id_array_to_single_user_card", "jdbcType": "string"}, "name": "control_bureau_leader", "tableSchema": {"span": 1, "type": "user", "title": "责任领导", "copyable": false}}, {"db": {"table": "t_profile_group_police_control", "column": "control_police", "mapping": "dept_code_to_dept_name", "jdbcType": "string"}, "name": "control_police", "tree": {"type": "dept"}, "tableSchema": {"span": 1, "type": "string", "title": "责任警种", "copyable": false}}, {"db": {"table": "t_profile_group_police_control", "column": "control_police_leader", "mapping": "user_id_array_to_single_user_card", "jdbcType": "string"}, "name": "control_police_leader", "tableSchema": {"span": 1, "type": "user", "title": "责任领导", "copyable": false}}, {"db": {"table": "t_profile_group_police_control", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "string"}, "name": "control_station", "tree": {"type": "dept"}, "tableSchema": {"span": 1, "type": "string", "title": "责任派出所", "copyable": false}}, {"db": {"table": "t_profile_group_police_control", "column": "control_station_leader", "mapping": "user_id_array_to_single_user_card", "jdbcType": "string"}, "name": "control_station_leader", "tableSchema": {"span": 1, "type": "user", "title": "责任领导", "copyable": false}}, {"db": {"table": "t_profile_group_police_control", "column": "control_person", "mapping": "user_id_array_to_user_card", "jdbcType": "string"}, "name": "control_person", "tableSchema": {"span": 2, "type": "multiUser", "title": "责任民警", "copyable": false}}], "moduleUi": {"column": 2, "bordered": true}}', '{"name": "公安管控信息", "type": "FORM_SCHEMA", "table": "t_profile_group_police_control", "fields": [{"db": {"table": "t_profile_group_police_control", "column": "control_bureau", "jdbcType": "string"}, "name": "controlBureau", "tree": {"root": "510500000000", "type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "责任分局"}}}, {"db": {"table": "t_profile_group_police_control", "column": "control_bureau_leader", "jdbcType": "number"}, "name": "controlBureauLeader", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "options": "/permission/user/user-dept/list/no-page?deptId=$.controlBureau", "fieldNames": {"label": "name", "value": "userId"}, "titleLocation": "left"}}, "schema": {"type": "number", "title": "责任分局领导"}}}, {"db": {"table": "t_profile_group_police_control", "column": "control_police", "jdbcType": "string"}, "name": "controlPolice", "tree": {"root": "510500000000", "type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "责任警种"}}}, {"db": {"table": "t_profile_group_police_control", "column": "control_police_leader", "jdbcType": "number"}, "name": "controlPoliceLeader", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "options": "/permission/user/user-dept/list/no-page?deptId=$.controlPolice", "fieldNames": {"label": "name", "value": "userId"}, "titleLocation": "left"}}, "schema": {"type": "number", "title": "责任警种领导"}}}, {"db": {"table": "t_profile_group_police_control", "column": "control_station", "jdbcType": "string"}, "name": "controlStation", "tree": {"type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "责任派出所"}}}, {"db": {"table": "t_profile_group_police_control", "column": "control_station_leader", "jdbcType": "number"}, "name": "controlStationLeader", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "options": "/permission/user/user-dept/list/no-page?deptId=$.controlStation", "fieldNames": {"label": "name", "value": "userId"}, "titleLocation": "left"}}, "schema": {"type": "number", "title": "责任派出所领导"}}}, {"db": {"table": "t_profile_group_police_control", "column": "control_person", "jdbcType": "json_id_array"}, "name": "controlPerson", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "select", "options": "/permission/user/user-dept/list/no-page?deptId=$.controlStation", "fieldNames": {"label": "name", "value": "userId"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "number"}, "title": "责任民警", "minItems": 1}}}], "required": ["controlPerson", "controlStationLeader", "controlStation"]}', NULL, 1, '{"type": "FOREIGN_KEY", "table": "t_profile_group_control", "column": "group_id", "extendCondition": [{"value": 12, "column": "police_kind"}]}', 'NO_SCHEMA', 'FORM_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1642, '敏感时间节点', 'sensitiveTime', 'fkGroup', 1645, 1, 3, NULL, NULL, '{"name": "敏感时间节点", "type": "LIST_SCHEMA", "table": "t_profile_sensitive_time", "fields": [{"db": {"table": "t_profile_sensitive_time", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "节点名称"}, "properties": {"copyable": true, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_sensitive_time", "column": "start_time", "mapping": "date_time_to_general_string", "jdbcType": "timestamp"}, "name": "start_time", "listSchema": {"style": {"align": "center"}, "schema": {"type": "datetime", "title": "起始时间"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_sensitive_time", "column": "end_time", "mapping": "date_time_to_general_string", "jdbcType": "timestamp"}, "name": "end_time", "listSchema": {"style": {"align": "center"}, "schema": {"type": "datetime", "title": "结束时间"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_sensitive_time", "column": "remark", "jdbcType": "string"}, "name": "remark", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "备注"}, "properties": {"copyable": true, "editable": true, "required": false, "sortable": false}}}], "selectable": false, "searchFields": []}', 1, '{"type": "JSON_ID_ARRAY", "table": "t_profile_group", "column": "sensitive_time_ids"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1640, '群体人员', 'relatedPerson', 'fkGroup', 1647, 1, 3, NULL, NULL, '{"name": "相关人员", "type": "LIST_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "人员姓名"}, "properties": {"href": "/ys-app/archives/person/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person", "column": "gender", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "gender"}, "name": "gender", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "性别"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "id_number", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "居民身份证号"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person", "column": "tel", "mapping": "json_array_to_string", "jdbcType": "json_id_array"}, "name": "tel", "listSchema": {"style": {"align": "left"}, "schema": {"type": "array", "title": "通联号码"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person_group_relation", "column": "identity", "mapping": "dict_code_to_name", "jdbcType": "json_id_array", "databaseRelation": {"type": "RELATION_TABLE", "table": "t_profile_person_group_relation", "joinTo": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}, "joinFrom": {"table": "t_profile_group", "column": "id", "joinColumn": "group_id"}}}, "dict": {"type": "person_group_relation_identity"}, "name": "identity", "listSchema": {"style": {"align": "center"}, "schema": {"type": "multiSelect", "title": "身份"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person", "column": "inflow_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "inflowTime", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "流入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person", "column": "is_slrylb", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "yes_or_not"}, "name": "is_slrylb", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "十类人员类别"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_person", "column": "other_illegal_activities", "jdbcType": "string"}, "name": "other_illegal_activities", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "其他犯罪情况"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person", "column": "specific_analysis", "jdbcType": "string"}, "name": "specific_analysis", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "具体研判情况"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person", "column": "update_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "udpateTime", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "更新时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "searchFields": [{"key": "name", "name": "人员名称"}], "defaultSortParams": {"sortField": "(case when JSON_OVERLAPS(`identity`, ''1'') then 1 else 0 end)", "sortDirection": "descending"}}', 1, '{"type": "RELATION_TABLE", "table": "t_profile_person_group_relation", "joinTo": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}, "joinFrom": {"table": "t_profile_group", "column": "id", "joinColumn": "group_id"}}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1638, '相关警情', 'relatedJq', 'fkGroup', 1645, 1, 4, NULL, NULL, '{}', 1, '{}', 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1637, '群体材料', 'groupFiles', 'fkGroup', NULL, 1, 4, NULL, NULL, NULL, 1, '{"type": "RELATION_TABLE", "table": "t_profile_group_file_relation", "column": "id", "joinTo": {"table": "t_file_info", "column": "id", "joinColumn": "file_id"}, "joinFrom": {"table": "t_profile_group", "column": "id", "joinColumn": "group_id"}}', 'FILE_SCHEMA', 'FILE_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1633, '关系图谱', 'groupRelation', 'fkGroup', 1647, 1, 6, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1632, '关联杆体预警人员', 'relatedWarningPerson', 'fkGroup', 1647, 1, 5, NULL, NULL, '{}', 0, '{}', 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);

UPDATE t_profile_module
SET cn_name='公安管控信息', en_name='policeControl', `type`='fkPerson', pid=1608, is_archive=1, show_order=2, table_schema='{"name": "公安管控信息", "type": "TABLE_SCHEMA", "table": "t_profile_person_police_control", "fields": [{"db": {"table": "t_profile_person_police_control", "column": "control_bureau", "mapping": "dept_code_to_dept_name", "jdbcType": "string"}, "name": "control_bureau", "tree": {"type": "dept"}, "tableSchema": {"span": 1, "type": "string", "title": "责任分局", "copyable": false}}, {"db": {"table": "t_profile_person_police_control", "column": "control_bureau_leader", "mapping": "user_id_array_to_single_user_card", "jdbcType": "string"}, "name": "control_bureau_leader", "tableSchema": {"span": 1, "type": "user", "title": "责任领导", "copyable": false}}, {"db": {"table": "t_profile_person_police_control", "column": "control_police", "mapping": "dept_code_to_dept_name", "jdbcType": "string"}, "name": "control_police", "tree": {"type": "dept"}, "tableSchema": {"span": 1, "type": "string", "title": "责任警种", "copyable": false}}, {"db": {"table": "t_profile_person_police_control", "column": "control_police_leader", "mapping": "user_id_array_to_single_user_card", "jdbcType": "string"}, "name": "control_police_leader", "tableSchema": {"span": 1, "type": "user", "title": "责任领导", "copyable": false}}, {"db": {"table": "t_profile_person_police_control", "column": "control_station", "mapping": "dept_code_to_dept_name", "jdbcType": "string"}, "name": "control_station", "tree": {"type": "dept"}, "tableSchema": {"span": 1, "type": "string", "title": "责任派出所", "copyable": false}}, {"db": {"table": "t_profile_person_police_control", "column": "control_station_leader", "mapping": "user_id_array_to_single_user_card", "jdbcType": "string"}, "name": "control_station_leader", "tableSchema": {"span": 1, "type": "user", "title": "责任领导", "copyable": false}}, {"db": {"table": "t_profile_person_police_control", "column": "control_person", "mapping": "user_id_array_to_user_card", "jdbcType": "string"}, "name": "control_person", "tableSchema": {"span": 2, "type": "multiUser", "title": "责任民警", "copyable": false}}], "moduleUi": {"column": 2, "bordered": true}}', form_schema='{"name": "公安管控信息", "type": "FORM_SCHEMA", "table": "t_profile_person_police_control", "fields": [{"db": {"table": "t_profile_person_police_control", "column": "control_bureau", "jdbcType": "string"}, "name": "controlBureau", "tree": {"root": "510500000000", "type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "责任分局"}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_bureau_leader", "jdbcType": "number"}, "name": "controlBureauLeader", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "options": "/permission/user/user-dept/list/no-page?deptId=$.controlBureau", "fieldNames": {"label": "name", "value": "userId"}, "titleLocation": "left"}}, "schema": {"type": "number", "title": "责任分局领导"}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_police", "jdbcType": "string"}, "name": "controlPolice", "tree": {"root": "510500000000", "type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "责任警种"}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_police_leader", "jdbcType": "number"}, "name": "controlPoliceLeader", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "options": "/permission/user/user-dept/list/no-page?deptId=$.controlPolice", "fieldNames": {"label": "name", "value": "userId"}, "titleLocation": "left"}}, "schema": {"type": "number", "title": "责任警种领导"}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_station", "jdbcType": "string"}, "name": "controlStation", "tree": {"type": "dept"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "shortName", "value": "deptCode", "children": "children"}, "titleLocation": "left", "notOnlyChildren": true}}, "schema": {"type": "string", "title": "责任派出所"}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_station_leader", "jdbcType": "number"}, "name": "controlStationLeader", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "options": "/permission/user/user-dept/list/no-page?deptId=$.controlStation", "fieldNames": {"label": "name", "value": "userId"}, "titleLocation": "left"}}, "schema": {"type": "number", "title": "责任派出所领导"}}}, {"db": {"table": "t_profile_person_police_control", "column": "control_person", "jdbcType": "json_id_array"}, "name": "controlPerson", "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "select", "options": "/permission/user/user-dept/list/no-page?deptId=$.controlStation", "fieldNames": {"label": "name", "value": "userId"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "number"}, "title": "责任民警", "minItems": 1}}}], "required": ["controlPerson", "controlStationLeader", "controlStation"]}', list_schema=NULL, is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person", "column": "person_id", "extendCondition": [{"value": 12, "column": "police_kind"}]}', show_schema_type='NO_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1610;
UPDATE t_profile_module
SET cn_name='政府管控信息', en_name='govControl', `type`='fkPerson', pid=1608, is_archive=1, show_order=1, table_schema='{"name": "政府管控信息", "table": "t_profile_person_government_control", "fields": [{"db": {"table": "t_profile_person_government_control", "column": "control_government", "jdbcType": "string"}, "name": "control_government", "tableSchema": {"span": 1, "type": "string", "title": "党政责任部门", "copyable": false}}, {"db": {"table": "t_profile_person_government_control", "column": "control_government_person", "jdbcType": "string"}, "name": "control_government_person", "tableSchema": {"span": 1, "type": "string", "title": "党政责任人", "copyable": false}}, {"db": {"table": "t_profile_person_government_control", "column": "control_government_person_duty", "jdbcType": "string"}, "name": "control_government_person_duty", "tableSchema": {"span": 1, "type": "string", "title": "党政责任人职务", "copyable": false}}, {"db": {"table": "t_profile_person_government_control", "column": "control_government_contact", "jdbcType": "string"}, "name": "control_government_contact", "tableSchema": {"span": 1, "type": "string", "title": "联系方式", "copyable": true}}, {"db": {"table": "t_profile_person_government_control", "column": "control_community", "jdbcType": "string"}, "name": "control_community", "tableSchema": {"span": 1, "type": "string", "title": "责任街道社区", "copyable": false}}, {"db": {"table": "t_profile_person_government_control", "column": "control_community_person", "jdbcType": "string"}, "name": "control_community_person", "tableSchema": {"span": 1, "type": "string", "title": "社区责任人", "config": {"fieldName": "control_community_person", "processType": "string", "processConfig": null}, "copyable": false}}, {"db": {"table": "t_profile_person_government_control", "column": "control_community_person_duty", "jdbcType": "string"}, "name": "control_community_person_duty", "tableSchema": {"span": 1, "type": "string", "title": "社区责任人职务", "copyable": false}}, {"db": {"table": "t_profile_person_government_control", "column": "control_community_contact", "jdbcType": "string"}, "name": "control_community_contact", "tableSchema": {"span": 1, "type": "string", "title": "联系方式", "copyable": true}}], "moduleUi": {"column": 3, "bordered": true}}', form_schema='{"name": "政府管控信息", "type": "FORM_SCHEMA", "table": "t_profile_person_government_control", "fields": [{"db": {"table": "t_profile_person_government_control", "column": "control_government", "jdbcType": "string"}, "name": "controlGovernment", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "党政责任部门"}}}, {"db": {"table": "t_profile_person_government_control", "column": "control_government_person", "jdbcType": "string"}, "name": "controlGovernmentPerson", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "党政责任人"}}}, {"db": {"table": "t_profile_person_government_control", "column": "control_government_person_duty", "jdbcType": "string"}, "name": "controlGovernmentPersonDuty", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "党政责任人职务"}}}, {"db": {"table": "t_profile_person_government_control", "column": "control_government_contact", "jdbcType": "string"}, "name": "controlGovernmentContact", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "党政责任人联系方式"}}}, {"db": {"table": "t_profile_person_government_control", "column": "control_community", "jdbcType": "string"}, "name": "controlCommunity", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "责任街道社区"}}}, {"db": {"table": "t_profile_person_government_control", "column": "control_community_person", "jdbcType": "string"}, "name": "controlCommunityPerson", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "社区责任人"}}}, {"db": {"table": "t_profile_person_government_control", "column": "control_community_person_duty", "jdbcType": "string"}, "name": "controlCommunityPersonDuty", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "社区责任人职务"}}}, {"db": {"table": "t_profile_person_government_control", "column": "control_community_contact", "jdbcType": "string"}, "name": "controlCommunityContact", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "社区责任人联系方式"}}}], "required": []}', list_schema=NULL, is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person", "column": "person_id", "extendCondition": [{"value": 12, "column": "police_kind"}]}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1609;
UPDATE t_profile_module
SET cn_name='人员信息', en_name='person', `type`='fkPerson', pid=1604, is_archive=1, show_order=1, table_schema='{"name": "人员信息", "type": "TABLE_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "id_number", "tableSchema": {"span": 1, "type": "string", "title": "居民身份证号", "copyable": true}}, {"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "姓名", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "gender", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "gender"}, "name": "gender", "tableSchema": {"span": 1, "type": "string", "title": "性别", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "tel", "mapping": "array_to_string", "jdbcType": "string"}, "name": "tel", "tableSchema": {"span": 1, "type": "string", "title": "通联号码", "copyable": true}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "registered_residence", "tree": {"root": "650000", "type": "district"}, "tableSchema": {"span": 1, "type": "string", "title": "户籍地址", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registered_residence_detail", "tableSchema": {"span": 1, "type": "string", "title": "户籍地址详细", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "basic_investigation_address", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "basic_investigation_address", "tree": {"root": "510000", "type": "district"}, "tableSchema": {"span": 1, "type": "string", "title": "基础摸排地址", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "basic_investigation_address_detail", "jdbcType": "string"}, "name": "basic_investigation_address_detail", "tableSchema": {"span": 1, "type": "string", "title": "基础摸排地址详细", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "work_address", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "work_address", "tree": {"root": "510000", "type": "district"}, "tableSchema": {"span": 1, "type": "string", "title": "务工地址", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "work_address_detail", "jdbcType": "string"}, "name": "work_address_detail", "tableSchema": {"span": 1, "type": "string", "title": "务工地址详细", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "work_category", "jdbcType": "string"}, "name": "work_category", "tableSchema": {"span": 1, "type": "string", "title": "工作类别", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "occupation_category", "jdbcType": "string"}, "name": "occupation_category", "tableSchema": {"span": 1, "type": "string", "title": "职业类别", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "is_slrylb", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "yes_or_not"}, "name": "is_slrylb", "tableSchema": {"span": 1, "type": "string", "title": "十类人员类别", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "inflow_time", "mapping": "date_to_text", "jdbcType": "string"}, "name": "inflow_time", "tableSchema": {"span": 2, "type": "string", "title": "流入时间", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "residence_reason", "jdbcType": "string"}, "name": "residence_reason", "tableSchema": {"span": 1, "type": "string", "title": "居住事由", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "other_illegal_activities", "jdbcType": "string"}, "name": "other_illegal_activities", "tableSchema": {"span": 4, "type": "string", "title": "其他违法犯罪情况", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "specific_analysis", "jdbcType": "string"}, "name": "specific_analysis", "tableSchema": {"span": 4, "type": "string", "title": "具体研判情况", "copyable": false}}], "moduleUi": {"column": 4, "bordered": true}}', form_schema='{"name": "人员信息", "type": "FORM_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "散居人员姓名"}}}, {"db": {"table": "t_profile_person", "column": "photo", "jdbcType": "json_image_array"}, "name": "photo", "formSchema": {"ui": {"ui:options": {"style": {"position": "absolute"}, "width": "0.5", "action": "/upload/imgs", "widget": "upload", "isShowTitle": false, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "object"}, "title": "照片"}}}, {"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "居民身份证号码"}}}, {"db": {"table": "t_profile_person", "column": "control_level", "jdbcType": "integer"}, "dict": {"type": "profile_person_control_level_fk"}, "name": "control_level", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "人员级别"}}}, {"db": {"table": "t_profile_person", "column": "person_label", "jdbcType": "json_id_array"}, "name": "personLabel", "tree": {"root": "person", "type": "label"}, "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array"}, "title": "人员标签"}}}, {"db": {"table": "t_profile_person", "column": "current_position", "jdbcType": "integer"}, "dict": {"type": "profile_current_position"}, "name": "nowLocation", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "是否成都户籍"}}}, {"db": {"table": "t_profile_person", "column": "tel", "jdbcType": "json_string_array"}, "name": "tel", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "multiple": true, "inputable": true, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "string", "pattern": "1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])[0-9]{8}"}, "title": "通联号码"}, "errorSchema": {"err:options": {"pattern": "通联号码格式异常"}}}}, {"db": {"table": "t_profile_person", "column": "gender", "jdbcType": "integer"}, "dict": {"type": "gender"}, "name": "gender", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "性别"}}}, {"db": {"table": "t_profile_person", "column": "inflow_time", "jdbcType": "timeString"}, "name": "inflow_time", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": false, "timeFormat": "YYYY-MM-DD", "titleLocation": "left"}}, "schema": {"type": "string", "title": "流入时间"}}}, {"db": {"table": "t_profile_person", "column": "is_slrylb", "jdbcType": "integer"}, "dict": {"type": "yes_or_not"}, "name": "is_slrylb", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "十类人员类别"}}}, {"db": {"table": "t_profile_person", "column": "occupation_category", "jdbcType": "string"}, "name": "occupation_category", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "职业类别"}}}, {"db": {"table": "t_profile_person", "column": "work_category", "jdbcType": "string"}, "name": "work_category", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "工作类别"}}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "jdbcType": "string"}, "name": "registerArea", "tree": {"root": "510000", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "户籍地址"}}}, {"db": {"table": "t_profile_person", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registered_residence_detail", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "户籍地址详细"}}}, {"db": {"table": "t_profile_person", "column": "work_address", "jdbcType": "string"}, "name": "work_address", "tree": {"root": "510000", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "务工地址"}}}, {"db": {"table": "t_profile_person", "column": "work_address_detail", "jdbcType": "string"}, "name": "work_address_detail", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "务工地址详细"}}}, {"db": {"table": "t_profile_person", "column": "basic_investigation_address", "jdbcType": "string"}, "name": "basic_investigation_address", "tree": {"root": "510000", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "基础摸排地址"}}}, {"db": {"table": "t_profile_person", "column": "basic_investigation_address_detail", "jdbcType": "string"}, "name": "basic_investigation_address_detail", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "基础摸排地址"}}}, {"db": {"table": "t_profile_person", "column": "residence_reason", "jdbcType": "string"}, "name": "residence_reason", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "居住事由"}}}, {"db": {"table": "t_profile_person", "column": "specific_analysis", "jdbcType": "string"}, "name": "specific_analysis", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "具体研判情况"}}}, {"db": {"table": "t_profile_person", "column": "other_illegal_activities", "jdbcType": "string"}, "name": "other_illegal_activities", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "其他违法犯罪情况"}}}], "required": ["idNumber", "name", "registerArea"]}', list_schema='{}', is_add=1, database_relation='{"type": "PRIMARY_KEY", "table": "t_profile_person", "column": "id"}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=0, is_mobile_content=1, is_web_content=1, is_fk_content=1
WHERE id=1605;