DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_bigscreen_duty_user_buffer' AND column_name='district_code')
    THEN
ALTER TABLE `tb_bigscreen_duty_user_buffer` ADD COLUMN `district_code` VARCHAR(100) NULL COMMENT '地域短码' AFTER `xm`;
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;