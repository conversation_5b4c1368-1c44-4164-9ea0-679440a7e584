UPDATE t_profile_module
SET cn_name='援助经历', en_name='assisTibetXinjiang', `type`='police', pid=NULL, is_archive=1, show_order=4, table_schema=NULL, form_schema=NULL, list_schema='{"name": "援助经历", "type": "LIST_SCHEMA", "table": "t_police_assist_tibet_xinjiang_relation", "fields": [{"db": {"table": "t_police_assist_tibet_xinjiang_relation", "column": "service_start_time", "mapping": "date_to_month", "jdbcType": "timestamp"}, "name": "service_start_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职开始时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_assist_tibet_xinjiang_relation", "column": "service_end_time", "mapping": "date_to_month", "jdbcType": "timestamp"}, "name": "service_end_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职结束时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": false, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_assist_tibet_xinjiang_relation", "column": "service_area", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_czyj_fudq", "codeToId": true}, "name": "service_area", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "服务地区"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"exist": false, "table": "t_police_assist_tibet_xinjiang_relation", "column": "(TIMESTAMPDIFF( MONTH, service_start_time , COALESCE(service_end_time , CURDATE())))", "jdbcType": "number", "databaseRelation": {"type": "FOREIGN_KEY", "column": "id"}}, "name": "time", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "服务期限（月）"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_police_assist_tibet_xinjiang_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1656;
UPDATE t_profile_module
SET cn_name='职务信息', en_name='resume', `type`='police', pid=NULL, is_archive=1, show_order=3, table_schema=NULL, form_schema=NULL, list_schema='{"name": "职务信息", "type": "LIST_SCHEMA", "table": "t_police_resume_relation", "fields": [{"db": {"table": "t_police_resume_relation", "column": "start_time", "jdbcType": "timestamp"}, "name": "start_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职开始时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_resume_relation", "column": "end_time", "jdbcType": "timestamp"}, "name": "end_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职结束时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": false, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_resume_relation", "column": "position", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_ll_rzzw", "codeToId": true}, "name": "position", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "任职职务"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_resume_relation", "column": "position_level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_ll_rzzwjb", "codeToId": true}, "name": "position_level", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "任职职务级别"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_police_resume_relation", "column": "department", "jdbcType": "string"}, "name": "department", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "任职部门"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_resume_relation", "column": "person_type", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_ll_person_type", "codeToId": true}, "name": "person_type", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "人员类别"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}], "selectable": false, "searchFields": []}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_police_resume_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1655;
UPDATE t_profile_module
SET cn_name='职级信息', en_name='rank', `type`='police', pid=NULL, is_archive=1, show_order=2, table_schema=NULL, form_schema=NULL, list_schema='{"name": "职级信息", "type": "LIST_SCHEMA", "table": "t_police_rank_relation", "fields": [{"db": {"table": "t_police_rank_relation", "column": "start_time", "jdbcType": "timestamp"}, "name": "start_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职开始时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_rank_relation", "column": "end_time", "jdbcType": "timestamp"}, "name": "end_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "任职结束时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": false, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_rank_relation", "column": "rank_series", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_rz_xl", "codeToId": true}, "name": "rank_series", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "职级序列"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_rank_relation", "column": "rank_code", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_zj", "codeToId": true}, "name": "ranks", "listSchema": {"style": {"align": "left"}, "schema": {"type": "treeSelect", "title": "职级"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "parentSelectable": false}}}, {"db": {"table": "t_police_rank_relation", "column": "qualifications", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_rz_qualifications", "codeToId": true}, "name": "qualifications", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "任职资格"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"exist": false, "table": "t_police_rank_relation", "column": "TIMESTAMPDIFF( YEAR, start_time , COALESCE(end_time , CURDATE()))", "jdbcType": "number", "databaseRelation": {"type": "FOREIGN_KEY", "column": "id"}}, "name": "time", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "任职年限（年）"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_police_rank_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1654;
UPDATE t_profile_module
SET cn_name='人员信息', en_name='person', `type`='police', pid=NULL, is_archive=1, show_order=1, table_schema='{"name": "人员信息", "type": "TABLE_SCHEMA", "table": "t_profile_police", "fields": [{"db": {"table": "t_profile_police", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "姓名", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "id_number", "jdbcType": "string"}, "name": "id_number", "tableSchema": {"span": 1, "type": "string", "title": "身份证号", "copyable": true}}, {"db": {"table": "t_profile_police", "column": "gender", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "gender"}, "name": "gender", "tableSchema": {"span": 1, "type": "string", "title": "性别", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "birthday", "mapping": "date_to_general_string", "jdbcType": "string"}, "name": "birthday", "tableSchema": {"span": 1, "type": "string", "title": "出生日期", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "type", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "police_type"}, "name": "type", "tableSchema": {"span": 1, "type": "string", "title": "类型", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "police_number", "jdbcType": "string"}, "name": "police_number", "tableSchema": {"span": 1, "type": "string", "title": "警号", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "nation", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "nation"}, "name": "nation", "tableSchema": {"span": 1, "type": "string", "title": "民族", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "political_status", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_political_status"}, "name": "political_status", "tableSchema": {"span": 1, "type": "string", "title": "政治面貌", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "martial_status", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_martial_status"}, "name": "martial_status", "tableSchema": {"span": 1, "type": "string", "title": "婚姻状况", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "tel", "mapping": "json_array_to_string", "jdbcType": "json_id_array"}, "name": "tel", "tableSchema": {"span": 1, "type": "label", "title": "电话号码", "copyable": true}}, {"db": {"table": "t_profile_police", "column": "join_work_date", "mapping": "date_to_general_string", "jdbcType": "string"}, "name": "join_work_date", "tableSchema": {"span": 1, "type": "string", "title": "参加工作日期", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "join_public_security_work_date", "mapping": "date_to_general_string", "jdbcType": "string"}, "name": "join_public_security_work_date", "tableSchema": {"span": 1, "type": "string", "title": "参加公安工作日期", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "registered_residence", "tableSchema": {"span": 2, "type": "string", "title": "户籍地", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registered_residence_detail", "tableSchema": {"span": 2, "type": "string", "title": "户籍地详细地址", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "current_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "current_residence", "tree": {"root": "000000", "type": "district"}, "tableSchema": {"span": 2, "type": "string", "title": "现住址", "copyable": false}}, {"db": {"table": "t_profile_police", "column": "current_residence_detail", "jdbcType": "string"}, "name": "current_residence_detail", "tableSchema": {"span": 2, "type": "string", "title": "现住址详细地址", "copyable": false}}], "moduleUi": {"column": 4, "bordered": true}}', form_schema='{"name": "人员信息", "type": "FORM_SCHEMA", "table": "t_profile_police", "fields": [{"db": {"table": "t_profile_police", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "姓名"}}}, {"db": {"table": "t_profile_police", "column": "photo", "jdbcType": "json_image_array"}, "name": "photo", "formSchema": {"ui": {"ui:options": {"style": {"position": "absolute"}, "width": "0.5", "action": "/upload/imgs", "widget": "upload", "isShowTitle": false, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "object"}, "title": "照片"}}}, {"db": {"table": "t_profile_police", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "身份证号"}}}, {"db": {"table": "t_profile_police", "column": "gender", "jdbcType": "integer"}, "dict": {"type": "gender"}, "name": "gender", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "性别"}}}, {"db": {"table": "t_profile_police", "column": "birthday", "jdbcType": "timeString"}, "name": "birthday", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "datePicker", "showTime": false, "timeFormat": "YYYY-MM-DD", "titleLocation": "left"}}, "schema": {"type": "string", "title": "出生日期"}}}, {"db": {"table": "t_profile_police", "column": "type", "jdbcType": "integer"}, "dict": {"type": "police_type"}, "name": "type", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "类型", "default": 1}}}, {"db": {"table": "t_profile_police", "column": "police_number", "jdbcType": "string"}, "name": "police_number", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "警号"}}}, {"db": {"table": "t_profile_police", "column": "nation", "jdbcType": "integer"}, "dict": {"type": "nation"}, "name": "nation", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "民族"}}}, {"db": {"table": "t_profile_police", "column": "political_status", "jdbcType": "integer"}, "dict": {"type": "profile_political_status"}, "name": "political_status", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "政治面貌"}}}, {"db": {"table": "t_profile_police", "column": "martial_status", "jdbcType": "integer"}, "dict": {"type": "profile_martial_status"}, "name": "maritalStatus", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "婚姻状况"}}}, {"db": {"table": "t_profile_police", "column": "tel", "jdbcType": "json_string_array"}, "name": "tel", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "select", "multiple": true, "inputable": true, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "string", "pattern": "1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])[0-9]{8}"}, "title": "联系方式"}, "errorSchema": {"err:options": {"pattern": "电话号码格式异常"}}}}, {"db": {"table": "t_profile_police", "column": "join_work_date", "jdbcType": "timeString"}, "name": "join_work_date", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": false, "timeFormat": "YYYY-MM-DD", "titleLocation": "left"}}, "schema": {"type": "string", "title": "参加工作日期"}}}, {"db": {"table": "t_profile_police", "column": "join_public_security_work_date", "jdbcType": "timeString"}, "name": "join_public_security_work_date", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "datePicker", "showTime": false, "timeFormat": "YYYY-MM-DD", "titleLocation": "left"}}, "schema": {"type": "string", "title": "参加公安工作日期"}}}, {"db": {"table": "t_profile_police", "column": "registered_residence", "jdbcType": "string"}, "name": "registerArea", "tree": {"root": "000000", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "户籍地"}}}, {"db": {"table": "t_profile_police", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registerAreaInfo", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "详细地址"}}}, {"db": {"table": "t_profile_police", "column": "current_residence", "jdbcType": "string"}, "name": "address", "tree": {"root": "510500", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "现住址"}}}, {"db": {"table": "t_profile_police", "column": "current_residence_detail", "jdbcType": "string"}, "name": "addressInfo", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "详细地址"}}}], "required": ["idNumber", "name"]}', list_schema='{}', is_add=1, database_relation='{"type": "PRIMARY_KEY", "table": "t_profile_police", "column": "id"}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=0, is_mobile_content=1, is_web_content=1, is_fk_content=1
WHERE id=1652;

delete from t_profile_module where id in (1669, 1668, 1667, 1666);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1669, '人员轨迹', 'track', 'controlPerson', NULL, 1, 20, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 0, 1, 1, 1);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1668, '相关业务', 'relatedService', 'controlPerson', NULL, 1, 10, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, 1, 1, 1);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1667, '相关预警', 'relatedWarning', 'controlPerson', 1668, 1, 11, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', NULL, 1, 1, 1, 1);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1666, '相关布控', 'relatedMonitor', 'controlPerson', 1668, 1, 12, NULL, NULL, NULL, 0, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);