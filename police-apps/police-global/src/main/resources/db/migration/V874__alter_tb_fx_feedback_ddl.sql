DELIMITER $$
DROP PROCEDURE IF EXISTS `modify_column` $$
CREATE PROCEDURE modify_column()
BEGIN
    IF NOT EXISTS(
       SELECT * FROM  information_schema.columns
       WHERE table_schema=(select database()) AND table_name='tb_fx_feedback' AND column_name='clue_excavate_type'
    )
    THEN
    ALTER TABLE tb_fx_feedback ADD clue_excavate_type int NULL COMMENT '线索挖掘类型';
    END IF;
END $$
DELIMITER ;
CALL modify_column;
DROP PROCEDURE IF EXISTS `modify_column`;