DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_fkrxyj' AND column_name='nation')
    THEN
        ALTER TABLE `t_warning_fkrxyj` ADD COLUMN `nation` int(11) NULL COMMENT '民族';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;