DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_jjwfjlb' AND column_name='rollback_type')
    THEN
        ALTER TABLE `tb_jjwfjlb` ADD `rollback_type` varchar(255) NULL COMMENT '回退类型，中文描述';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;