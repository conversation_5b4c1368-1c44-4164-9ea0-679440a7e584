DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict` $$
CREATE PROCEDURE insert_dict()
BEGIN
    DECLARE ppid INT;

     -- 删除历史数据
     delete from t_dict where `type` IN ('profile_event_happened_group','profile_event_happened');
     -- 插入新纪录
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'profile_event_happened_group',0,'是否发生',NULL,NULL,NULL,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'profile_event_happened',1,'是',NULL,NULL,NULL,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'profile_event_happened',0,'否',NULL,NULL,NULL,NULL,NULL,NULL);
    SET ppid = (select id from t_dict where `type`='profile_event_happened_group');
    update t_dict set p_id = ppid where `type` IN ('profile_event_happened_group','profile_event_happened');
END $$
DELIMITER ;
CALL insert_dict;
DROP PROCEDURE IF EXISTS `insert_dict`;
