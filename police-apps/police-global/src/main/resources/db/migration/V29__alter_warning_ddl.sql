DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning' AND column_name='overdue_time')
    THEN
alter table t_warning drop overdue_time;
END IF;
END $$
DELIMITER ;
CALL drop_column;


DEL<PERSON>ITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_process' AND column_name='notice_type')
    THEN
alter table t_warning_process drop notice_type;
END IF;
END $$
DELIMITER ;
CALL drop_column;

DE<PERSON>IMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_process' AND column_name='notice_target_id')
    THEN
alter table t_warning_process drop notice_target_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;
