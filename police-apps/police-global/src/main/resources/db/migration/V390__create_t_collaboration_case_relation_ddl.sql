DROP TABLE IF EXISTS `t_collaboration_case_relation`;
CREATE TABLE `t_collaboration_case_relation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `collaboration_id` bigint(20) NULL DEFAULT NULL COMMENT '协作id',
  `case_event_id` bigint(20) NULL DEFAULT NULL COMMENT '案件id',
  `create_dept_id` bigint(20) NULL DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户主键',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint(20) NULL DEFAULT NULL COMMENT '更新单位主键',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '协作案件关联表' ROW_FORMAT = Dynamic;