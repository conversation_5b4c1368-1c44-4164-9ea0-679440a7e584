DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict` $$
CREATE PROCEDURE insert_dict()
BEGIN
     DECLARE ppid INT;

     -- 删除历史数据
     delete from t_dict where `type` IN ('search_schema_data_source_group','search_schema_data_source');
     -- 插入新纪录
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'search_schema_data_source_group',0,'数据来源',NULL,NULL,0,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'search_schema_data_source',1,'互联网数据',NULL,NULL,1,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'search_schema_data_source',2,'政务网数据',NULL,NULL,2,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'search_schema_data_source',3,'上级公安数据',NULL,NULL,3,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'search_schema_data_source',4,'本级公安数据',NULL,NULL,4,NULL,NULL,NULL);
     SET ppid = (select id from t_dict where `type`='search_schema_data_source_group');
     UPDATE t_dict set p_id = ppid where `type` IN ('search_schema_data_source_group','search_schema_data_source');
END $$
DELIMITER ;
CALL insert_dict;
DROP PROCEDURE IF EXISTS `insert_dict`;
