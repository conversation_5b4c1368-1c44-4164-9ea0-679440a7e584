DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE jzflall INT;
    SET jzflall = (SELECT id FROM t_dict WHERE type='portal_clue_pool_navigation' and code = 29);
    DELETE FROM t_dict WHERE type LIKE 'portal_clue_pool_navigation%' and code in (30,31,32,33,34,35,36,37,38,39);
    INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color,status) VALUES
       (jzflall,'portal_clue_pool_navigation',30,'吸毒人员驾驶机动车',29,'module',101,NULL,1,NULL,1),
       (jzflall,'portal_clue_pool_navigation',31,'盗窃案件疑似人员',29,'module',102,NULL,0,NULL,1),
       (jzflall,'portal_clue_pool_navigation',32,'蝙蝠聊天疑似涉黄人员',29,'module',103,NULL,5,NULL,1),
       (jzflall,'portal_clue_pool_navigation',33,'涉黄人员重点部位预警',29,'module',104,NULL,6,NULL,1),
       (jzflall,'portal_clue_pool_navigation',34,'隐形涉邪人员线索挖掘',29,'module',105,NULL,3,NULL,1),
       (jzflall,'portal_clue_pool_navigation',35,'无证驾驶',29,'module',106,NULL,2,NULL,1),
       (jzflall,'portal_clue_pool_navigation',36,'盗窃三车风险预警',29,'module',107,NULL,100,NULL,1),
       (jzflall,'portal_clue_pool_navigation',37,'扒窃风险预警',29,'module',108,NULL,101,NULL,1),
       (jzflall,'portal_clue_pool_navigation',38,'隐形涉疆人员线索挖掘',29,'module',109,NULL,4,NULL,1),
       (jzflall,'portal_clue_pool_navigation',39,'疑似流动人口',29,'module',110,NULL,104,NULL,1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;