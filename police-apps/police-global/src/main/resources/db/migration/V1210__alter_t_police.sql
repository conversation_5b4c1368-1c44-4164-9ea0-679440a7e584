DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_police_democratic_evaluation_relation' AND column_name='yx_count')
THEN
alter table t_police_democratic_evaluation_relation add column `yx_count` int DEFAULT NULL COMMENT '优秀得票数';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_police_democratic_evaluation_relation' AND column_name='cz_count')
THEN
alter table t_police_democratic_evaluation_relation add column `cz_count` int DEFAULT NULL COMMENT '称职得票数';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_police_democratic_evaluation_relation' AND column_name='jbcz_count')
THEN
alter table t_police_democratic_evaluation_relation add column `jbcz_count` int DEFAULT NULL COMMENT '基本称职得票数';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_police_democratic_evaluation_relation' AND column_name='bcz_count')
THEN
alter table t_police_democratic_evaluation_relation add column `bcz_count` int DEFAULT NULL COMMENT '不称职票数';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_police_education_experience_relation' AND column_name='academic_degree')
THEN
alter table t_police_education_experience_relation add column `academic_degree` int DEFAULT NULL COMMENT '学位，码表，type = t_degree';
END IF;

END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;


CREATE TABLE if not exists `t_police_leader_evaluation_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `recommend_time` date NULL COMMENT '推荐时间',
  `recommend_level` int NULL COMMENT '推荐级别，字典：police_leader_recommend_level',
  `recommend_person` varchar(255) NULL COMMENT '推荐人',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='局领导班子评价';

CREATE TABLE if not exists `t_police_professional_qualification_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `obtain_time` date NULL COMMENT '获取时间',
  `professional_qualification` int NULL COMMENT '专业资格名称,字典：police_professional_qualification',
  `description` text NULL COMMENT '描述',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专业资格';


DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    -- 声明变量用于存储各级ID
    DECLARE
    police_wgw_id INT;
    DECLARE
    degree_id INT;
    DECLARE
    lgsj_id INT;
    DECLARE
    leader_recommend_level_id INT;
	DECLARE
    professional_qualification_id INT;

    -- 先删除旧数据
    DELETE
    FROM
      `t_dict`
    WHERE
      `type` LIKE 'police_wgw%'
      OR `type` LIKE 't_degree%'
      OR `type` LIKE 'police_lgsj%'
      OR `type` LIKE 'police_leader_recommend_level%'
      OR `type` LIKE 'police_professional_qualification%';

    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_wgwj_group', 0, '问题类型', 0, '问题类型', 1, NULL, 1, NULL, 1);
    SET police_wgw_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = police_wgw_id
    WHERE
      `id` = police_wgw_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (police_wgw_id, 'police_wgwj', 1, '政务处分警告', 0, '政务处分警告', 1, NULL, 3, NULL, 1),
    (police_wgw_id, 'police_wgwj', 2, '政务处分记过', 0, '政务处分记过', 1, NULL, 3, NULL, 1),
    (police_wgw_id, 'police_wgwj', 3, '政务处分记大过', 0, '政务处分记大过', 1, NULL, 3, NULL, 1),
    (police_wgw_id, 'police_wgwj', 4, '政务处分降级', 0, '政务处分降级', 1, NULL, 3, NULL, 1),
    (police_wgw_id, 'police_wgwj', 5, '党纪处分警告', 0, '党纪处分警告', 1, NULL, 3, NULL, 1),
    (police_wgw_id, 'police_wgwj', 6, '党纪处分严重警告', 0, '党纪处分严重警告', 1, NULL, 3, NULL, 1),
    (police_wgw_id, 'police_wgwj', 7, '党纪处分撤销党内职务', 0, '党纪处分撤销党内职务', 1, NULL, 3, NULL, 1),
    (police_wgw_id, 'police_wgwj', 8, '党纪处分留党察看', 0, '党纪处分留党察看', 1, NULL, 3, NULL, 1);

    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('t_degree_group', 0, '学历', 0, '学历', 1, NULL, NULL, NULL, 1);
    SET degree_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = degree_id
    WHERE
      `id` = degree_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (degree_id, 't_degree', 1, '小学', 0, '小学', 1, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 2, '初中', 0, '初中', 2, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 3, '高中', 0, '高中', 3, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 4, '大专', 0, '大专', 4, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 5, '大学本科', 0, '大学本科', 5, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 6, '硕士研究生', 0, '硕士研究生', 6, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 7, '博士研究生', 0, '博士研究生', 7, NULL, NULL, NULL, 1);


    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_lgsj_group', 0, '立功受奖', 0, '立功受奖', 1, NULL, NULL, NULL, 1);
    SET lgsj_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = lgsj_id
    WHERE
      `id` = lgsj_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (lgsj_id, 'police_lgsj', 1, '一、二级英模', 0, '一、二级英模', 1, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 2, '全国劳动模范', 0, '全国劳动模范', 2, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 3, '省部级劳动模范', 0, '省部级劳动模范', 3, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 4, '全国特级优秀人民警察', 0, '全国特级优秀人民警察', 4, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 5, '全省特级优秀人民警察', 0, '全省特级优秀人民警察', 5, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 6, '全国优秀人民警察', 0, '全国优秀人民警察', 6, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 7, '市地级劳动模范', 0, '市地级劳动模范', 7, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 8, '个人一等功', 0, '个人一等功', 8, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 9, '个人二等功', 0, '个人二等功', 9, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 10, '个人三等功', 0, '个人三等功', 10, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 11, '优秀共产党员', 0, '优秀共产党员', 11, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 12, '优秀党务工作者', 0, '优秀党务工作者', 12, NULL, NULL, NULL, 1);

    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_leader_recommend_level_group', 0, '推荐级别', 0, '推荐级别', 1, NULL, NULL, NULL, 1);
    SET leader_recommend_level_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = leader_recommend_level_id
    WHERE
      `id` = leader_recommend_level_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (leader_recommend_level_id, 'police_leader_recommend_level', 1, '党委书记', 0, '党委书记', 1, NULL, NULL, NULL, 1),
    (leader_recommend_level_id, 'police_leader_recommend_level', 2, '局领导班子成员', 0, '局领导班子成员', 2, NULL, NULL, NULL, 1);


    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_professional_qualification_group', 0, '推荐级别', 0, '推荐级别', 1, NULL, NULL, NULL, 1);
    SET professional_qualification_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = professional_qualification_id
    WHERE
      `id` = professional_qualification_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (professional_qualification_id, 'police_professional_qualification', 1, '国家统一法律职业资格考试', 0, '国家统一法律职业资格考试', 1, NULL, NULL, NULL, 1),
    (professional_qualification_id, 'police_professional_qualification', 2, '公安机关高级执法资格', 0, '公安机关高级执法资格', 2, NULL, NULL, NULL, 1);

END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;