
CREATE TABLE IF NOT EXISTS `tb_intelligence_province_clue_fail_reason` (
  `data_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cr_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `task_id` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '任务ID',
  `stxsbh` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT 'st线索编号',
  `reason_content` longtext COLLATE utf8mb4_bin NOT NULL COMMENT '失败原因',
  PRIMARY KEY (`data_id`),
  UNIQUE KEY `tb_intelligence_province_clue_fail_reason_unique` (`stxsbh`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;