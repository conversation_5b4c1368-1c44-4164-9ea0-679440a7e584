drop table if exists t_profile_goods_risk_control_relation;
drop table if exists t_profile_goods_sensitive_time_node_relation;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    ALTER TABLE t_profile_goods MODIFY COLUMN related_event_num int DEFAULT 0 NULL COMMENT '涉及事件数';
    ALTER TABLE t_profile_goods MODIFY COLUMN status bigint DEFAULT 1 NULL COMMENT '物品状态  1 生效 2 消除';
    ALTER TABLE t_profile_goods MODIFY COLUMN review_status bigint DEFAULT 1 NULL COMMENT '审核状态';

    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = (select database())
                AND table_name = 't_profile_goods'
                AND column_name = 'describe')
    then
        ALTER TABLE t_profile_goods
            CHANGE `describe` goods_describe text DEFAULT NULL;
    end if;
    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = (select database())
                AND table_name = 't_profile_archives_config'
                AND column_name = 'isEnable')
    then
        ALTER TABLE t_profile_archives_config
            CHANGE `isEnable` is_enable bigint DEFAULT NULL;
    end if;
    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = (select database())
                AND table_name = 't_profile_archives_config'
                AND column_name = 'jqType')
    then
        ALTER TABLE t_profile_archives_config
            CHANGE `jqType` jq_type bigint DEFAULT NULL;
    end if;
    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = (select database())
                AND table_name = 't_profile_archives_config'
                AND column_name = 'score')
    then
        ALTER TABLE t_profile_archives_config
            CHANGE `score` score FLOAT DEFAULT NULL;
    end if;
    IF EXISTS(SELECT *
              FROM information_schema.columns
              WHERE table_schema = (select database())
                AND table_name = 't_profile_archives_config'
                AND column_name = 'displayname')
    then
        ALTER TABLE t_profile_archives_config
            CHANGE `displayname` display_name varchar(255) DEFAULT NULL;
    end if;
    IF NOT EXISTS(SELECT *
                  FROM information_schema.columns
                  WHERE table_schema = (select database())
                    AND table_name = 't_profile_goods'
                    AND column_name = 'deleted')
    THEN
        ALTER TABLE t_profile_goods
            ADD deleted int default 0 COMMENT '逻辑删除字段';
    END IF;
    IF NOT EXISTS(SELECT *
                  FROM information_schema.columns
                  WHERE table_schema = (select database())
                    AND table_name = 't_profile_goods'
                    AND column_name = 'node_id')
    THEN
        ALTER TABLE t_profile_goods
            ADD node_id json NULL COMMENT '敏感时间id';
    END IF;
    IF NOT EXISTS(SELECT *
                  FROM information_schema.columns
                  WHERE table_schema = (select database())
                    AND table_name = 't_profile_goods_risk_control'
                    AND column_name = 'goods_id')
    THEN
        ALTER TABLE t_profile_goods_risk_control
            ADD goods_id bigint NULL COMMENT '物品id';
    END IF;
    IF NOT EXISTS(SELECT *
                  FROM information_schema.columns
                  WHERE table_schema = (select database())
                    AND table_name = 't_profile_goods_sensitive_time_node'
                    AND column_name = 'deleted')
    THEN
        ALTER TABLE t_profile_goods_sensitive_time_node
            ADD deleted int default 0 COMMENT '逻辑删除状态';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;