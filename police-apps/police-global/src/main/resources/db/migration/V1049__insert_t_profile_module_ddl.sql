delete from t_profile_module where id in(1585,1586,1587);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1585, '档案管理', 'archive', 'eventV2headerJz', NULL, 0, 1, NULL, NULL, '{\"name\": \"事件档案\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"person_estimation\", \"jdbcType\": \"number\"}, \"name\": \"personEstimation\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"number\", \"title\": \"估计人数\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"control_station\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"control_station\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主管单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"主管单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"control_police\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"control_police\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主责警种\"}, \"schema\": {\"type\": \"string\", \"title\": \"主责警种\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_group_event_relation r left join t_profile_group g on r.group_id = g.id\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"eventRelatedGroup\", \"databaseRelation\": {\"type\": \"EVENT_RELATED_GROUP\", \"table\": \"t_profile_group_event_relation r left join t_profile_group g on r.event_id = g.id\", \"column\": \"event_id\"}}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 120, \"ellipsis\": true}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"关联群体类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"关联群体类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_happened\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_happened\"}, \"name\": \"event_happened\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"event_happened\", \"type\": \"option\", \"value\": [\"%%profile_event_happened%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"是否发生\"}, \"schema\": {\"type\": \"string\", \"title\": \"是否发生\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": true, \"extendFields\": [{\"table\": \"t_profile_event_risk_point_info\", \"value\": 3, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}, {\"key\": \"relatedAddress\", \"name\": \"事发地点\"}]}', 0, '{}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1586, '档案管理', 'archive', 'eventV2headerZa', NULL, 0, 1, NULL, NULL, '{\"name\": \"事件档案\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"eventLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"eventLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&event_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"事件类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"事件类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"relatedTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"事件开始时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_end_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"relatedEndTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"事件结束时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address\", \"mapping\": \"map_location_string\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"relatedAddress\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件发生地址\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_happened\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_happened\"}, \"name\": \"event_happened\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"event_happened\", \"type\": \"option\", \"value\": [\"%%profile_event_happened%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"是否发生\"}, \"schema\": {\"type\": \"string\", \"title\": \"是否发生\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"dict\": {\"type\": \"profile_event_source_za\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_status\"}, \"name\": \"status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"control_station\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"control_station\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主管单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"主管单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"control_police\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"control_police\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主责警种\"}, \"schema\": {\"type\": \"string\", \"title\": \"主责警种\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_group_event_relation r left join t_profile_group g on r.group_id = g.id\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"eventRelatedGroup\", \"databaseRelation\": {\"type\": \"EVENT_RELATED_GROUP\", \"table\": \"t_profile_group_event_relation r left join t_profile_group g on r.event_id = g.id\", \"column\": \"event_id\"}}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 120, \"ellipsis\": true}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"关联群体类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"关联群体类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}], \"selectable\": true, \"extendFields\": [{\"table\": \"t_profile_event_risk_point_info\", \"value\": 4, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}, {\"key\": \"relatedAddress\", \"name\": \"事发地点\"}]}', 0, '{}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO `t_profile_module` (`id`, `cn_name`, `en_name`, `type`, `pid`, `is_archive`, `show_order`, `table_schema`, `form_schema`, `list_schema`, `is_add`, `database_relation`, `show_schema_type`, `add_schema_type`, `is_operation_content`, `is_mobile_content`, `is_web_content`, `is_fk_content`) VALUES (1587, '档案管理', 'archive', 'eventV2headerQt', NULL, 0, 1, NULL, NULL, '{\"name\": \"事件档案\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"isName\": true, \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"json_id_array\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"eventLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"eventLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&event_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"事件类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"事件类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"dict\": {\"type\": \"profile_event_level\"}, \"name\": \"level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"level\", \"type\": \"select\", \"value\": [\"%%profile_event_level%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}, \"displayName\": \"事件级别\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件级别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"relatedTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"维权开始时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_end_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"relatedEndTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"维权结束时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"related_address\", \"mapping\": \"map_location_string\", \"jdbcType\": \"string\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"relatedAddress\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"维权地址\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_happened\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_happened\"}, \"name\": \"event_happened\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"event_happened\", \"type\": \"option\", \"value\": [\"%%profile_event_happened%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\", \"children\": \"children\"}, \"displayName\": \"是否发生\"}, \"schema\": {\"type\": \"string\", \"title\": \"是否发生\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_status\"}, \"name\": \"status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_station\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"control_station\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"control_station\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主管单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"主管单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_event_risk_point_info\", \"column\": \"control_police\", \"mapping\": \"dept_code_to_dept_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"FOREIGN_KEY\", \"table\": \"t_profile_event_risk_point_info\", \"column\": \"event_id\"}}, \"name\": \"control_police\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"control_police\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"deptCode\", \"children\": \"children\"}, \"displayName\": \"主责警种\"}, \"schema\": {\"type\": \"string\", \"title\": \"主责警种\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"isRelatedShow\": true}}}, {\"db\": {\"table\": \"t_profile_group_event_relation r left join t_profile_group g on r.group_id = g.id\", \"column\": \"group_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"eventRelatedGroup\", \"databaseRelation\": {\"type\": \"EVENT_RELATED_GROUP\", \"table\": \"t_profile_group_event_relation r left join t_profile_group g on r.event_id = g.id\", \"column\": \"event_id\"}}, \"name\": \"groupLabel\", \"listSchema\": {\"style\": {\"align\": \"left\", \"width\": 120, \"ellipsis\": true}, \"filter\": {\"key\": \"groupLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&group_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"关联群体类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"关联群体类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2, \"isRelatedShow\": true}}}], \"selectable\": true, \"extendFields\": [{\"table\": \"t_profile_event_risk_point_info\", \"value\": 99, \"column\": \"police_kind\", \"jdbcType\": \"integer\"}], \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}, {\"key\": \"relatedAddress\", \"name\": \"事发地点\"}]}', 0, '{}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
