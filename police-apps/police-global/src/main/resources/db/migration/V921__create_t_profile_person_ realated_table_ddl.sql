CREATE TABLE IF NOT EXISTS `t_profile_person_gzcs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_user_id` bigint DEFAULT NULL,
  `create_dept_id` bigint DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_user_id` bigint DEFAULT NULL,
  `update_dept_id` bigint DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '详细',
  `data_source` int DEFAULT NULL COMMENT '数据来源',
  `data_source_remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '来源描述',
    `person_id` bigint DEFAULT NULL COMMENT '人员id',
    `police_kind` int DEFAULT NULL COMMENT '管控警种',
    deleted TINYINT DEFAULT 0 NULL COMMENT '删除状态',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


CREATE TABLE IF NOT EXISTS `t_profile_person_xsbx` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_user_id` bigint DEFAULT NULL,
  `create_dept_id` bigint DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_user_id` bigint DEFAULT NULL,
  `update_dept_id` bigint DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '详细',
  `person_id` bigint DEFAULT NULL COMMENT '人员id',
  `police_kind` int DEFAULT NULL COMMENT '管控警种',
  deleted TINYINT DEFAULT 0 NULL COMMENT '删除状态',
  PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `t_profile_person_xswh` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_user_id` bigint DEFAULT NULL,
  `create_dept_id` bigint DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_user_id` bigint DEFAULT NULL,
  `update_dept_id` bigint DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '详细',
  `person_id` bigint DEFAULT NULL COMMENT '人员id',
  `police_kind` int DEFAULT NULL COMMENT '管控警种',
  deleted TINYINT DEFAULT 0 NULL COMMENT '删除状态',
  PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `t_profile_person_dcqk` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_user_id` bigint DEFAULT NULL,
  `create_dept_id` bigint DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_user_id` bigint DEFAULT NULL,
  `update_dept_id` bigint DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '详细',
  `person_id` bigint DEFAULT NULL COMMENT '人员id',
  `police_kind` int DEFAULT NULL COMMENT '管控警种',
  deleted TINYINT DEFAULT 0 NULL COMMENT '删除状态',
  PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS `t_profile_person_jzdcqk` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `create_user_id` bigint DEFAULT NULL,
    `create_dept_id` bigint DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT NULL,
    `update_user_id` bigint DEFAULT NULL,
    `update_dept_id` bigint DEFAULT NULL,
    `update_time` timestamp NULL DEFAULT NULL,
    `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '详细',
    `person_id` bigint DEFAULT NULL COMMENT '人员id',
    `police_kind` int DEFAULT NULL COMMENT '管控警种',
    deleted TINYINT DEFAULT 0 NULL COMMENT '删除状态',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

