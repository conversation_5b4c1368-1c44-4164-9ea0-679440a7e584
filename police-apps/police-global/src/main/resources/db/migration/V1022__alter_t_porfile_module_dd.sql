UPDATE t_profile_module SET cn_name='群体信息', en_name='group', `type`='groupV2', pid=1312, is_archive=1, show_order=17, table_schema='{"name": "群体信息", "type": "TABLE_SCHEMA", "table": "t_profile_group", "fields": [{"db": {"table": "t_profile_group", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "群体名称", "copyable": false}}, {"db": {"table": "t_profile_group", "column": "group_label", "mapping": "label_id_array_to_name", "jdbcType": "json_id_array"}, "name": "group_label", "tableSchema": {"span": 1, "type": "label", "title": "群体类别", "copyable": false}}, {"db": {"table": "t_profile_group", "column": "control_level", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_control_level"}, "name": "control_level", "tableSchema": {"span": 1, "type": "string", "title": "群体级别", "copyable": false}}], "moduleUi": {"column": 4, "bordered": true}}', form_schema='{"name": "群体信息", "type": "FORM_SCHEMA", "table": "t_profile_group", "fields": [{"db": {"table": "t_profile_group", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "1", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "群体名称"}}}, {"db": {"table": "t_profile_group", "column": "group_label", "jdbcType": "json_id_array"}, "name": "groupLabel", "tree": {"root": "group", "type": "label"}, "formSchema": {"ui": {"ui:options": {"width": "1", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array"}, "title": "群体类别"}}}, {"db": {"table": "t_profile_group", "column": "control_level", "jdbcType": "string"}, "dict": {"type": "profile_person_control_level"}, "name": "control_level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "群体级别"}}}], "required": ["name"]}', list_schema=NULL, is_add=1, database_relation='{"type": "PRIMARY_KEY", "table": "t_profile_group", "column": "id"}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1314;

UPDATE t_profile_module SET cn_name='人员信息', en_name='person', `type`='person', pid=2, is_archive=1, show_order=3, table_schema='{"name": "人员信息", "type": "TABLE_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "id_number", "tableSchema": {"span": 1, "type": "string", "title": "证件号码", "copyable": true}}, {"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "姓名", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "id_type", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "id_type"}, "name": "id_type", "tableSchema": {"span": 1, "type": "string", "title": "证件类型", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "gender", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "gender"}, "name": "gender", "tableSchema": {"span": 1, "type": "string", "title": "性别", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "former_name", "jdbcType": "string"}, "name": "former_name", "tableSchema": {"span": 1, "type": "string", "title": "曾用名", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "nick_name", "jdbcType": "string"}, "name": "nick_name", "tableSchema": {"span": 1, "type": "string", "title": "绰号", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "nation", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "nation"}, "name": "nation", "tableSchema": {"span": 1, "type": "string", "title": "民族", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "political_status", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_political_status"}, "name": "political_status", "tableSchema": {"span": 1, "type": "string", "title": "政治面貌", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "martial_status", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_martial_status"}, "name": "martial_status", "tableSchema": {"span": 1, "type": "string", "title": "婚姻状况", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "current_job", "jdbcType": "string"}, "name": "current_job", "tableSchema": {"span": 1, "type": "string", "title": "现职业", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "current_position", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_current_position"}, "name": "current_position", "tableSchema": {"span": 1, "type": "string", "title": "目前所在地", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "person_label", "mapping": "label_id_array_to_name", "jdbcType": "json_id_array"}, "name": "person_label", "tableSchema": {"span": 1, "type": "label", "title": "人员标签", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "tel", "jdbcType": "string"}, "name": "tel", "tableSchema": {"span": 1, "type": "string", "title": "电话号码", "copyable": true}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "registered_residence", "tableSchema": {"span": 1, "type": "string", "title": "户籍地", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registered_residence_detail", "tableSchema": {"span": 1, "type": "string", "title": "户籍地详细地址", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "current_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "current_residence", "tree": {"root": "000000", "type": "district"}, "tableSchema": {"span": 1, "type": "string", "title": "现住址", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "current_residence_detail", "jdbcType": "string"}, "name": "current_residence_detail", "tableSchema": {"span": 4, "type": "string", "title": "现住址详细地址", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "main_demand", "jdbcType": "string"}, "name": "main_demand", "tableSchema": {"span": 4, "type": "string", "title": "主要诉求", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "work_measures", "jdbcType": "string"}, "name": "work_measures", "tableSchema": {"span": 4, "type": "string", "title": "工作措施", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "petition_info", "jdbcType": "string"}, "name": "petition_info", "tableSchema": {"span": 4, "type": "string", "title": "现实表现", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "punish_info", "jdbcType": "string"}, "name": "punish_info", "tableSchema": {"span": 4, "type": "string", "title": "被打击处理情况", "copyable": false}}], "moduleUi": {"column": 4, "bordered": true}}', form_schema='{"name": "人员信息", "type": "FORM_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "id_type", "jdbcType": "integer"}, "dict": {"type": "id_type"}, "name": "idType", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "证件类型"}}}, {"db": {"table": "t_profile_person", "column": "photo", "jdbcType": "json_image_array"}, "name": "photo", "formSchema": {"ui": {"ui:options": {"style": {"position": "absolute"}, "width": "0.5", "action": "/upload/imgs", "widget": "upload", "isShowTitle": false, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "object"}, "title": "照片"}}}, {"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "证件号码"}}}, {"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "姓名"}}}, {"db": {"table": "t_profile_person", "column": "current_position", "jdbcType": "integer"}, "dict": {"type": "profile_current_position"}, "name": "nowLocation", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "目前所在地"}}}, {"db": {"table": "t_profile_person", "column": "person_label", "jdbcType": "json_id_array"}, "name": "personLabel", "tree": {"root": "person", "type": "label"}, "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array"}, "title": "人员标签"}}}, {"db": {"table": "t_profile_person", "column": "tel", "jdbcType": "json_string_array"}, "name": "tel", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "select", "multiple": true, "inputable": true, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "string", "pattern": "1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])[0-9]{8}"}, "title": "电话号码"}, "errorSchema": {"err:options": {"pattern": "电话号码格式异常"}}}}, {"db": {"table": "t_profile_person", "column": "gender", "jdbcType": "integer"}, "dict": {"type": "gender"}, "name": "gender", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "性别"}}}, {"db": {"table": "t_profile_person", "column": "former_name", "jdbcType": "string"}, "name": "usedName", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "曾用名"}}}, {"db": {"table": "t_profile_person", "column": "nick_name", "jdbcType": "string"}, "name": "nickName", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "绰号"}}}, {"db": {"table": "t_profile_person", "column": "nation", "jdbcType": "integer"}, "dict": {"type": "nation"}, "name": "nation", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "民族"}}}, {"db": {"table": "t_profile_person", "column": "political_status", "jdbcType": "integer"}, "dict": {"type": "profile_political_status"}, "name": "politicalStatus", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "政治面貌"}}}, {"db": {"table": "t_profile_person", "column": "martial_status", "jdbcType": "integer"}, "dict": {"type": "profile_martial_status"}, "name": "maritalStatus", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "婚姻状态"}}}, {"db": {"table": "t_profile_person", "column": "control_level", "jdbcType": "integer"}, "dict": {"type": "profile_person_control_level"}, "name": "control_level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "人员级别"}}}, {"db": {"table": "t_profile_person", "column": "current_job", "jdbcType": "string"}, "name": "currentJob", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "现职业"}}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "jdbcType": "string"}, "name": "registerArea", "tree": {"root": "510500", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "户籍地"}}}, {"db": {"table": "t_profile_person", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registerAreaInfo", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "详细地址"}}}, {"db": {"table": "t_profile_person", "column": "current_residence", "jdbcType": "string"}, "name": "address", "tree": {"root": "000000", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "现住址"}}}, {"db": {"table": "t_profile_person", "column": "current_residence_detail", "jdbcType": "string"}, "name": "addressInfo", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "详细地址"}}}, {"db": {"table": "t_profile_person", "column": "work_target", "jdbcType": "string"}, "dict": {"type": "profile_work_target"}, "name": "work_target", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "工作目标"}}}, {"db": {"table": "t_profile_person", "column": "main_demand", "jdbcType": "string"}, "name": "mainDemand", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "风险背景"}}}, {"db": {"table": "t_profile_person", "column": "work_measures", "jdbcType": "string"}, "name": "measure", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "工作措施"}}}, {"db": {"table": "t_profile_person", "column": "petition_info", "jdbcType": "string"}, "name": "petition", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "化解难点"}}}, {"db": {"table": "t_profile_person", "column": "punish_info", "jdbcType": "string"}, "name": "handling", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "textarea", "titleLocation": "left"}}, "schema": {"type": "string", "title": "维权及打处情况"}}}, {"db": {"table": "t_profile_person", "column": "person_type", "jdbcType": "integer"}, "name": "personType", "formSchema": {"ui": {"ui:options": {"style": {"display": "none"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "integer", "title": "档案类型", "default": 0}}}], "required": ["idType", "idNumber", "name", "registerArea", "personLabel"]}', list_schema='{}', is_add=1, database_relation='{"type": "PRIMARY_KEY", "table": "t_profile_person", "column": "id"}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=0, is_mobile_content=1, is_web_content=1, is_fk_content=1 WHERE id=3;
UPDATE t_profile_module SET cn_name='人员信息', en_name='person', `type`='personV2', pid=1328, is_archive=1, show_order=3, table_schema='{"name": "人员信息", "type": "TABLE_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "id_number", "tableSchema": {"span": 1, "type": "string", "title": "证件号码", "copyable": true}}, {"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "tableSchema": {"span": 1, "type": "string", "title": "姓名", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "id_type", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "id_type"}, "name": "id_type", "tableSchema": {"span": 1, "type": "string", "title": "证件类型", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "gender", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "gender"}, "name": "gender", "tableSchema": {"span": 1, "type": "string", "title": "性别", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "former_name", "jdbcType": "string"}, "name": "former_name", "tableSchema": {"span": 1, "type": "string", "title": "曾用名", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "nick_name", "jdbcType": "string"}, "name": "nick_name", "tableSchema": {"span": 1, "type": "string", "title": "绰号", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "nation", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "nation"}, "name": "nation", "tableSchema": {"span": 1, "type": "string", "title": "民族", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "political_status", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_political_status"}, "name": "political_status", "tableSchema": {"span": 1, "type": "string", "title": "政治面貌", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "martial_status", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_martial_status"}, "name": "martial_status", "tableSchema": {"span": 1, "type": "string", "title": "婚姻状况", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "current_job", "jdbcType": "string"}, "name": "current_job", "tableSchema": {"span": 1, "type": "string", "title": "现职业", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "current_position", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_current_position"}, "name": "current_position", "tableSchema": {"span": 1, "type": "string", "title": "目前所在地", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "person_label", "mapping": "label_id_array_to_name", "jdbcType": "json_id_array"}, "name": "person_label", "tableSchema": {"span": 1, "type": "label", "title": "人员标签", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "tel", "mapping": "array_to_string", "jdbcType": "string"}, "name": "tel", "tableSchema": {"span": 1, "type": "string", "title": "电话号码", "copyable": true}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "registered_residence", "tableSchema": {"span": 1, "type": "string", "title": "户籍地所在区", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registered_residence_detail", "tableSchema": {"span": 1, "type": "string", "title": "户籍地详细地址", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "current_residence", "mapping": "district_code_to_name", "jdbcType": "string"}, "name": "current_residence", "tree": {"root": "000000", "type": "district"}, "tableSchema": {"span": 1, "type": "string", "title": "现住地所在区", "copyable": false}}, {"db": {"table": "t_profile_person", "column": "current_residence_detail", "jdbcType": "string"}, "name": "current_residence_detail", "tableSchema": {"span": 4, "type": "string", "title": "现住址详细地址", "copyable": false}}], "moduleUi": {"column": 4, "bordered": true}}', form_schema='{"name": "人员信息", "type": "FORM_SCHEMA", "table": "t_profile_person", "fields": [{"db": {"table": "t_profile_person", "column": "id_type", "jdbcType": "integer"}, "dict": {"type": "id_type"}, "name": "idType", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "证件类型"}}}, {"db": {"table": "t_profile_person", "column": "photo", "jdbcType": "json_image_array"}, "name": "photo", "formSchema": {"ui": {"ui:options": {"style": {"position": "absolute"}, "width": "0.5", "action": "/upload/imgs", "widget": "upload", "isShowTitle": false, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "object"}, "title": "照片"}}}, {"db": {"table": "t_profile_person", "column": "id_number", "jdbcType": "string"}, "name": "idNumber", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "证件号码"}}}, {"db": {"table": "t_profile_person", "column": "name", "jdbcType": "string"}, "name": "name", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "姓名"}}}, {"db": {"table": "t_profile_person", "column": "current_position", "jdbcType": "integer"}, "dict": {"type": "profile_current_position_zg"}, "name": "nowLocation", "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "目前所在地"}}}, {"db": {"table": "t_profile_person", "column": "person_label", "jdbcType": "json_id_array"}, "name": "personLabel", "tree": {"root": "person", "type": "label"}, "formSchema": {"ui": {"ui:options": {"style": {"display": "block"}, "width": "0.5", "widget": "cascader", "multiple": true, "fieldNames": {"label": "name", "value": "id", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "array"}, "title": "人员标签"}}}, {"db": {"table": "t_profile_person", "column": "tel", "jdbcType": "json_string_array"}, "name": "tel", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "multiple": true, "inputable": true, "titleLocation": "left"}}, "schema": {"type": "array", "items": {"type": "string", "pattern": "1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])[0-9]{8}"}, "title": "电话号码"}, "errorSchema": {"err:options": {"pattern": "电话号码格式异常"}}}}, {"db": {"table": "t_profile_person", "column": "current_job", "jdbcType": "string"}, "name": "currentJob", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "现职业"}}}, {"db": {"table": "t_profile_person", "column": "gender", "jdbcType": "integer"}, "dict": {"type": "gender"}, "name": "gender", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "性别"}}}, {"db": {"table": "t_profile_person", "column": "former_name", "jdbcType": "string"}, "name": "usedName", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "曾用名"}}}, {"db": {"table": "t_profile_person", "column": "nick_name", "jdbcType": "string"}, "name": "nickName", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "绰号"}}}, {"db": {"table": "t_profile_person", "column": "nation", "jdbcType": "integer"}, "dict": {"type": "nation"}, "name": "nation", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "select", "titleLocation": "left"}}, "schema": {"type": "number", "title": "民族"}}}, {"db": {"table": "t_profile_person", "column": "political_status", "jdbcType": "integer"}, "dict": {"type": "profile_political_status"}, "name": "politicalStatus", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "政治面貌"}}}, {"db": {"table": "t_profile_person", "column": "martial_status", "jdbcType": "integer"}, "dict": {"type": "profile_martial_status"}, "name": "maritalStatus", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "婚姻状态"}}}, {"db": {"table": "t_profile_person", "column": "registered_residence", "jdbcType": "string"}, "name": "registerArea", "tree": {"root": "510000", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "户籍地所在区"}}}, {"db": {"table": "t_profile_person", "column": "registered_residence_detail", "jdbcType": "string"}, "name": "registerAreaInfo", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "户籍地详细地址"}}}, {"db": {"table": "t_profile_person", "column": "current_residence", "jdbcType": "string"}, "name": "address", "tree": {"root": "510000", "type": "district"}, "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "cascader", "multiple": false, "fieldNames": {"label": "name", "value": "code", "children": "children"}, "titleLocation": "left"}}, "schema": {"type": "string", "title": "现住地所在区"}}}, {"db": {"table": "t_profile_person", "column": "current_residence_detail", "jdbcType": "string"}, "name": "addressInfo", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "现住地详细地址"}}}], "required": ["idType", "idNumber", "name", "registerArea", "personLabel"]}', list_schema='{}', is_add=1, database_relation='{"type": "PRIMARY_KEY", "table": "t_profile_person", "column": "id"}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=1 WHERE id=1327;

UPDATE t_profile_module SET cn_name='风险点信息', en_name='riskPoint', `type`='personV2', pid=1390, is_archive=1, show_order=1, table_schema='{"name": "风险点信息", "type": "TABLE_SCHEMA", "table": "t_profile_person_risk_za", "fields": [{"db": {"table": "t_profile_person_risk_za", "column": "control_level", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_control_level_za"}, "name": "control_level", "tableSchema": {"span": 1, "type": "string", "title": "管控级别", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_unit", "jdbcType": "string"}, "name": "work_unit", "tableSchema": {"span": 1, "type": "string", "title": "工作单位", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_duty", "jdbcType": "string"}, "name": "work_duty", "tableSchema": {"span": 1, "type": "string", "title": "职务和岗位", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_duty_type", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_duty_type"}, "name": "work_duty_type", "tableSchema": {"span": 1, "type": "string", "title": "类型", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "event_place", "jdbcType": "string"}, "name": "event_place", "tableSchema": {"span": 1, "type": "string", "title": "事发地", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "sksflry", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "yes_or_not"}, "name": "sksflry", "tableSchema": {"span": 1, "type": "string", "title": "是否三跨三分离人员", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "demand_situation", "jdbcType": "string"}, "name": "demand_situation", "tableSchema": {"span": 1, "type": "string", "title": "诉求情况", "copyable": false}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_difficulty", "jdbcType": "string"}, "name": "work_difficulty", "tableSchema": {"span": 1, "type": "string", "title": "工作难点", "copyable": false}}], "moduleUi": {"column": 3, "bordered": true}}', form_schema='{"name": "风险点信息", "type": "FORM_SCHEMA", "table": "t_profile_person_risk_za", "fields": [{"db": {"table": "t_profile_person_risk_za", "column": "control_level", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_person_control_level_za"}, "name": "control_level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "管控级别"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_unit", "jdbcType": "string"}, "name": "work_unit", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "工作单位"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_duty", "jdbcType": "string"}, "name": "work_duty", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "职务和岗位"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_duty_type", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_person_duty_type"}, "name": "work_duty_type", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "类型"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "event_place", "jdbcType": "string"}, "name": "event_place", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "事发地"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "sksflry", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "yes_or_not"}, "name": "sksflry", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "fieldNames": {"label": "name", "value": "code"}, "titleLocation": "left"}}, "schema": {"type": "number", "title": "是否三跨三分离人员"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "demand_situation", "jdbcType": "string"}, "name": "demand_situation", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "诉求情况"}}}, {"db": {"table": "t_profile_person_risk_za", "column": "work_difficulty", "jdbcType": "string"}, "name": "work_difficulty", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "工作难点"}}}], "required": []}', list_schema=NULL, is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person", "column": "person_id"}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1404;
UPDATE t_profile_module SET cn_name='风险点信息', en_name='riskPoint', `type`='personV2', pid=1391, is_archive=1, show_order=1, table_schema='{"name": "风险点信息", "type": "TABLE_SCHEMA", "table": "t_profile_person_risk_other", "fields": [{"db": {"table": "t_profile_person_risk_other", "column": "person_level", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_control_level"}, "name": "person_level", "tableSchema": {"span": 1, "type": "string", "title": "人员级别", "copyable": false}}, {"db": {"table": "t_profile_person_risk_other", "column": "main_demand", "jdbcType": "string"}, "name": "main_demand", "tableSchema": {"span": 1, "type": "string", "title": "风险背景", "copyable": false}}, {"db": {"table": "t_profile_person_risk_other", "column": "petition_info", "jdbcType": "string"}, "name": "petition_info", "tableSchema": {"span": 1, "type": "string", "title": "化解难点", "copyable": false}}], "moduleUi": {"column": 3, "bordered": true}}', form_schema='{"name": "风险点信息", "type": "FORM_SCHEMA", "table": "t_profile_person_risk_other", "fields": [{"db": {"table": "t_profile_person_risk_other", "column": "person_level", "mapping": "dict_code_to_name", "jdbcType": "integer"}, "dict": {"type": "profile_person_control_level"}, "name": "person_level", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "radio", "titleLocation": "left"}}, "schema": {"type": "number", "title": "人员级别"}}}, {"db": {"table": "t_profile_person_risk_other", "column": "main_demand", "jdbcType": "string"}, "name": "main_demand", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "风险背景"}}}, {"db": {"table": "t_profile_person_risk_other", "column": "petition_info", "jdbcType": "string"}, "name": "petition_info", "formSchema": {"ui": {"ui:options": {"width": "0.5", "widget": "input", "titleLocation": "left"}}, "schema": {"type": "string", "title": "化解难点"}}}], "required": [], "extendFields": [{"table": "t_profile_person_risk_other", "value": 99, "column": "police_kind", "jdbcType": "integer"}]}', list_schema=NULL, is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person", "column": "person_id", "extendCondition": [{"value": "99", "column": "police_kind"}]}', show_schema_type='TABLE_SCHEMA', add_schema_type='FORM_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1414;

UPDATE t_profile_module SET cn_name='工作措施', en_name='gzcs', `type`='personV2', pid=1390, is_archive=1, show_order=2, table_schema=NULL, form_schema=NULL, list_schema='{"name": "工作措施", "type": "LIST_SCHEMA", "table": "t_profile_person_gzcs", "fields": [{"db": {"table": "t_profile_person_gzcs", "column": "type", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_gzcs_type_za", "codeToId": true}, "name": "type", "listSchema": {"style": {"align": "center"}, "schema": {"type": "select", "title": "分类"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false}}}, {"db": {"table": "t_profile_person_gzcs", "column": "detail", "jdbcType": "string"}, "name": "detail", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "工作措施描述"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_gzcs", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_gzcs", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_gzcs", "value": 4, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "工作措施"}]}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person_gzcs", "column": "person_id", "idColumn": "id", "primaryColumn": "id", "extendCondition": [{"value": 4, "column": "police_kind"}]}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1405;
UPDATE t_profile_module SET cn_name='现实动向', en_name='xsbx', `type`='personV2', pid=1390, is_archive=1, show_order=3, table_schema=NULL, form_schema=NULL, list_schema='{"name": "现实动向", "type": "LIST_SCHEMA", "table": "t_profile_person_xsbx", "fields": [{"db": {"table": "t_profile_person_xsbx", "column": "type", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_xsbx_type_za", "codeToId": true}, "name": "type", "listSchema": {"style": {"align": "center"}, "schema": {"type": "select", "title": "分类"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false}}}, {"db": {"table": "t_profile_person_xsbx", "column": "detail", "jdbcType": "string"}, "name": "detail", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "现实动向描述"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_xsbx", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_xsbx", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_xsbx", "value": 4, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "现实表现"}]}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person_xsbx", "column": "person_id", "idColumn": "id", "primaryColumn": "id", "extendCondition": [{"value": 4, "column": "police_kind"}]}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1406;
UPDATE t_profile_module SET cn_name='打处情况', en_name='dcqk', `type`='personV2', pid=1390, is_archive=1, show_order=4, table_schema=NULL, form_schema=NULL, list_schema='{"name": "打处情况", "type": "LIST_SCHEMA", "table": "t_profile_person_dcqk", "fields": [{"db": {"table": "t_profile_person_dcqk", "column": "type", "mapping": "dict_code_to_name", "jdbcType": "string"}, "dict": {"type": "profile_person_dcqk_type_za", "codeToId": true}, "name": "type", "listSchema": {"style": {"align": "center"}, "schema": {"type": "select", "title": "分类"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false}}}, {"db": {"table": "t_profile_person_dcqk", "column": "detail", "jdbcType": "string"}, "name": "detail", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "打处情况描述"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_dcqk", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_dcqk", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_dcqk", "value": 4, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "打处情况"}]}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_profile_person_dcqk", "column": "person_id", "idColumn": "id", "primaryColumn": "id", "extendCondition": [{"value": 4, "column": "police_kind"}]}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1407;
UPDATE t_profile_module SET cn_name='相关线索', en_name='relatedClue', `type`='personV2', pid=1390, is_archive=1, show_order=10, table_schema=NULL, form_schema=NULL, list_schema='{"name": "相关线索", "type": "LIST_SCHEMA", "table": "t_profile_clue", "fields": [{"db": {"table": "t_profile_clue", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "线索名称"}, "properties": {"href": "/ys-app/archives/clue/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_clue", "column": "detail", "jdbcType": "string"}, "name": "detail", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "线索内容"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_clue", "column": "source", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_source_type"}, "name": "source", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "线索来源"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_clue", "column": "target_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "target_time", "listSchema": {"style": {"align": "center", "width": 130}, "schema": {"type": "string", "title": "指向日期"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_clue", "column": "target_location", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "profile_clue_location"}, "name": "target_location", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "指向地点"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_profile_clue", "column": "create_time", "mapping": "date_time_to_text", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center", "width": 120}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": true}}}, {"db": {"table": "t_profile_person_clue_relation", "column": "yj_status", "mapping": "dict_code_to_name", "jdbcType": "number", "databaseRelation": {"type": "RELATION_TABLE", "table": "t_profile_person_clue_relation", "joinTo": {"table": "t_profile_clue", "column": "id", "joinColumn": "clue_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}}}, "dict": {"type": "profile_person_clue_yj_status", "codeToId": true}, "name": "yj_status", "listSchema": {"style": {"align": "center"}, "schema": {"type": "select", "title": "是否预警"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_clue_relation", "column": "yjh_status", "mapping": "dict_code_to_name", "jdbcType": "number", "databaseRelation": {"type": "RELATION_TABLE", "table": "t_profile_person_clue_relation", "joinTo": {"table": "t_profile_clue", "column": "id", "joinColumn": "clue_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}}}, "dict": {"type": "profile_person_clue_yjh_status", "codeToId": true}, "name": "yjh_status", "listSchema": {"style": {"align": "center"}, "schema": {"type": "select", "title": "预警后状态"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_clue_relation", "value": 4, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "线索名称"}]}', is_add=1, database_relation='{"type": "RELATION_TABLE", "table": "t_profile_person_clue_relation", "joinTo": {"table": "t_profile_clue", "column": "id", "joinColumn": "clue_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}, "extendCondition": [{"value": 4, "column": "police_kind"}]}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0 WHERE id=1413;