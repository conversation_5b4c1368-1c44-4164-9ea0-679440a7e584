-- 情指行相关数据表的创建

DROP TABLE IF EXISTS tb_intelligence_zhiling_default_attributes;
CREATE TABLE `tb_intelligence_zhiling_default_attributes` (
  `data_id` bigint NOT NULL AUTO_INCREMENT,
  `yq_data_id` bigint NOT NULL COMMENT '关联的要情ID',
  `version_id` bigint NOT NULL COMMENT '版本ID',
  `cr_user` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `cr_time` datetime NOT NULL COMMENT '创建时间',
  `cr_dept_id` bigint DEFAULT NULL COMMENT '创建人所属部门ID',
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指令号',
  `order_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指令类型',
  `order_level` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '指令等级',
  `feedback_limit_time` datetime DEFAULT NULL COMMENT '反馈时限',
  `sign_limit_time` datetime DEFAULT NULL COMMENT '签收时限',
  `signer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '签发',
  `leader_unit` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '牵头部门',
  `contacts_user` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '联系人',
  `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电话',
  `accept_dept_ids` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接收单位ID串',
  `related_yao_qing` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联的要情ID',
  `related_clue` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联的线索ID',
  `attachment` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '附件',
  `is_del` int NOT NULL DEFAULT '0' COMMENT '是否删除\n0：否\n1：是',
  PRIMARY KEY (`data_id`),
  UNIQUE KEY `tb_intelligence_zhiling_default_attributes_unique` (`yq_data_id`,`version_id`),
  KEY `tb_intelligence_zhiling_default_attributes_version_id_IDX` (`version_id`,`yq_data_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


DROP TABLE IF EXISTS tb_intelligence_zhiling_version_info;
CREATE TABLE `tb_intelligence_zhiling_version_info` (
  `data_id` bigint NOT NULL AUTO_INCREMENT COMMENT '版本ID(主键)',
  `yq_data_id` bigint NOT NULL DEFAULT '0' COMMENT '所属要情ID',
  `cr_user` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `cr_time` datetime NOT NULL COMMENT '创建时间',
  `cr_dept_id` bigint DEFAULT NULL COMMENT '创建人所属部门ID',
  `is_del` int NOT NULL DEFAULT '0' COMMENT '是否删除\n0：否\n1：是',
  `version_flag` int DEFAULT '0' COMMENT '版本标识（0：首报，1：修订）',
  `version_data_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `version_data_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '版本内容',
  PRIMARY KEY (`data_id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

DROP TABLE IF EXISTS tb_intelligence_zhiling_base_info;
CREATE TABLE `tb_intelligence_zhiling_base_info` (
  `data_id` bigint NOT NULL AUTO_INCREMENT,
  `root_id` bigint NOT NULL DEFAULT '0' COMMENT '根ID',
  `cr_user` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `cr_user_true_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人真实姓名',
  `cr_dept_id` bigint DEFAULT NULL COMMENT '创建人所属部门ID',
  `cr_time` datetime NOT NULL COMMENT '创建时间',
  `is_del` int NOT NULL DEFAULT '0' COMMENT '是否删除\n0：否\n1：是',
  `data_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型',
  `data_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分类',
  `classical_flag` int DEFAULT '0' COMMENT '经典标识\n0：否\n1：是',
  `watch_flag` int DEFAULT '0' COMMENT '盯办标识\n0：否\n1：是',
  `data_no` int DEFAULT NULL COMMENT '编号',
  `data_year` int NOT NULL COMMENT '所属年份',
  `data_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `drafts_flag` int NOT NULL DEFAULT '0' COMMENT '草稿标识\n0：否\n1：是',
  `push_time` datetime DEFAULT NULL COMMENT '发布时间',
  `status_code` int NOT NULL COMMENT '状态码',
  `accept_dept_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上级部门ID（上报上级之后该字段不能为空）',
  `feedback_time_limit` datetime DEFAULT NULL COMMENT '反馈时限',
  `sign_time_limit` datetime DEFAULT NULL COMMENT '签收时限',
  `feedback_timeout` int DEFAULT '0' COMMENT '反馈超时\n0：否\n1：是',
  `sign_timeout` int DEFAULT '0' COMMENT '签收超时\n0：否\n1：是',
  `version_id` bigint DEFAULT NULL COMMENT '最新版本ID',
  `chat_id` bigint DEFAULT NULL COMMENT '所属会话ID',
  `fields_edit_info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段填报情况（格式：已填字段数;总字段数）',
  `feedback_info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '反馈情况（格式：已执行数;总数）',
  `sign_info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '签收情况（格式：已执行数;总数）',
  `accept_info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '送达情况（格式：已执行数;总数）',
  PRIMARY KEY (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

DELETE FROM tb_intelligence_attribute_templates WHERE data_id=18;
INSERT INTO tb_intelligence_attribute_templates (data_id, cr_user,cr_time,is_del,`type`,data_type,data_type_show_name,data_class,data_class_show_name,template_name,relation_table_name,fields,filter_fields,order_num,cr_dept_id) VALUES
	 (18,'system','2024-01-01 00:00:00',0,'zhiling','zhiling','指令','default','默认模板（前段不展示）','默认模板（前段不展示）','tb_intelligence_zhiling_default_attributes','[{"key":"orderNo","dbKey":"order_no","properties":{"required":true,"options":[]},"schema":{"title":"指令号","type":"string"}},{"key":"orderType","dbKey":"order_type","properties":{"required":true,"options":[{"label":"警情舆情处置","value":"警情舆情处置"},{"label":"勤务部署","value":"勤务部署"},{"label":"领导批示","value":"领导批示"},{"label":"其他","value":"其他"}]},"schema":{"title":"指令类型","type":"radio"}},{"key":"orderLevel","dbKey":"order_level","properties":{"required":true,"options":[{"label":"一级","value":"一级"},{"label":"二级","value":"二级"},{"label":"三级","value":"三级"},{"label":"四级","value":"四级"},{"label":"其他","value":"其他"}]},"schema":{"title":"指令等级","type":"radio"}},{"key":"feedbackLimitTime","dbKey":"feedback_limit_time","properties":{"required":true,"options":[]},"schema":{"title":"反馈时限","type":"datetime"}},{"key":"signLimitTime","dbKey":"sign_limit_time","properties":{"required":false,"options":[]},"schema":{"title":"签收时限","type":"datetime"}},{"key":"signer","dbKey":"signer","properties":{"required":true,"options":[]},"schema":{"title":"签发人","type":"string"}},{"key":"leaderUnit","dbKey":"leader_unit","properties":{"required":true,"options":[]},"schema":{"title":"牵头部门","type":"string"}},{"key":"contactsUser","dbKey":"contacts_user","properties":{"required":true,"options":[]},"schema":{"title":"联系人","type":"radio"}},{"key":"phone","dbKey":"phone","properties":{"required":true,"options":[]},"schema":{"title":"联系电话","type":"radio"}},{"key":"acceptDeptIds","dbKey":"accept_dept_ids","properties":{"required":true,"options":[]},"schema":{"title":"接收单位","type":"radio"}},{"key":"relatedYaoQing","dbKey":"related_yao_qing","properties":{"required":false,"options":[]},"schema":{"title":"关联要情","type":"yaoqing"}},{"key":"relatedClue","dbKey":"related_clue","properties":{"required":false,"options":[]},"schema":{"title":"关联线索","type":"clue"}},{"key":"attachment","dbKey":"attachment","properties":{"required":false,"options":[]},"schema":{"title":"附件","type":"file"}}]','[{"defaultValue":null,"displayName":"指令类型","type":"checkbox","key":"orderType","fieldNames":{"label":"label","value":"value"},"linkedKey":null,"queryKey":null,"url":null,"value":[{"label":"警情舆情处置","value":"警情舆情处置"},{"label":"勤务部署","value":"勤务部署"},{"label":"领导批示","value":"领导批示"},{"label":"其他","value":"其他"}]},{"defaultValue":null,"displayName":"指令等级","type":"checkbox","key":"orderLevel","fieldNames":{"label":"label","value":"value"},"linkedKey":null,"queryKey":null,"url":null,"value":[{"label":"一级","value":"一级"},{"label":"二级","value":"二级"},{"label":"三级","value":"三级"},{"label":"四级","value":"四级"},{"label":"其他","value":"其他"}]}]',1,0);
