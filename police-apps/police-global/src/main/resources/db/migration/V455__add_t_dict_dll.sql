DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict` $$
CREATE PROCEDURE insert_dict()
BEGIN
    DECLARE ppid,ppid2,ppid3 INT;
    -- 删除历史数据
    delete from t_dict where `type` = 'wrist_category' and name = '手段类别';
    delete from t_dict where `type` = 'request_type' and name = '提请类型';
    delete from t_dict where `type` = 'request_type' and name = '数据查询';
    delete from t_dict where `type` = 'request_type' and name = '数据分析';
    delete from t_dict where `type` = 'request_type' and name = '数据监测';
    delete from t_dict where `type` = 'collaboration_status' and name = '协作状态';
    delete from t_dict where `type` = 'collaboration_status' and name = '审批中';
    delete from t_dict where `type` = 'collaboration_status' and name = '已驳回';
    delete from t_dict where `type` = 'collaboration_status' and name = '待反馈';
    delete from t_dict where `type` = 'collaboration_status' and name = '已退回';
    delete from t_dict where `type` = 'collaboration_status' and name = '已反馈';
    delete from t_dict where `type` = 'collaboration_status' and name = '已撤销';
    -- 插入新纪录
    INSERT INTO t_dict(`p_id`, `type`, `name`,dict_desc, flag) VALUES(0, 'wrist_category', '手段类别','0002',1);
    INSERT INTO t_dict(`p_id`, `type`, `name`,dict_desc, flag) VALUES (0, 'request_type', '提请类型','0',1);
    INSERT INTO t_dict(`p_id`, `type`, `name`,dict_desc, flag) VALUES (0, 'request_type', '数据查询','0001',2);
    INSERT INTO t_dict(`p_id`, `type`, `name`,dict_desc, flag) VALUES (0, 'request_type', '数据分析','0002',2);
    INSERT INTO t_dict(`p_id`, `type`, `name`,dict_desc, flag) VALUES (0, 'request_type', '数据监测','0003',2);
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`,dict_desc, show_number,flag) VALUES(0, 'collaboration_status', 0,'协作状态','0',0,1);
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`,dict_desc, show_number,flag) VALUES(0, 'collaboration_status', 1,'审批中','0001',1,2);
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`,dict_desc, show_number,flag) VALUES(0, 'collaboration_status', 2,'已驳回','0002',2,2);
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`,dict_desc, show_number,flag) VALUES(0, 'collaboration_status', 3,'待反馈','0003',3,2);
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`,dict_desc, show_number,flag) VALUES(0, 'collaboration_status', 4,'已退回','0004',4,2);
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`,dict_desc, show_number,flag) VALUES(0, 'collaboration_status', 5,'已反馈','0005',5,2);
    INSERT INTO t_dict(`p_id`, `type`, `code`,`name`,dict_desc, show_number,flag) VALUES(0, 'collaboration_status', 6,'已撤销','0006',6,2);

    -- 更新PId
    SET ppid = (select id from t_dict where `type`='wrist_category' and `name`='手段类别');
    update t_dict set p_id = ppid where `type` = 'wrist_category' and `dict_desc` IN(000200050001,000200050002,000200050003);

    SET ppid2 = (select id from t_dict where `type`='request_type' and `name`='提请类型');
    update t_dict set p_id = ppid2 where `type` = 'request_type' and `dict_desc` IN(0001,0002,0003);

    SET ppid3 = (select id from t_dict where `type`='collaboration_status' and `name`='协作状态' and `code`=0);
    update t_dict set p_id = ppid3 where `type` = 'collaboration_status' and `code` IN(1,2,3,4,5,6);
END $$
DELIMITER ;
CALL insert_dict;
DROP PROCEDURE IF EXISTS `insert_dict`;
