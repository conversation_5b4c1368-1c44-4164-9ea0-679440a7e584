DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_intelligence_renwu_base_info' AND column_name='task_source')
    THEN
        ALTER TABLE tb_intelligence_renwu_base_info ADD task_source VARCHAR(100) NULL COMMENT '任务来源';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;
