DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_control_warning_source' AND column_name='is_local')
    THEN
        ALTER TABLE t_control_warning_source ADD is_local INT DEFAULT 1 NULL COMMENT '是否本地感知引擎。0:非本地引擎；1:本地引擎';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;