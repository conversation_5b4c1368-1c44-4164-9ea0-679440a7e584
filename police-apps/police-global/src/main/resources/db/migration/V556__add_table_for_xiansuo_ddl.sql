
CREATE TABLE IF NOT EXISTS `tb_intelligence_xiansuo_base_info` (
  `data_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `root_id` bigint(20) DEFAULT 0,
  `cr_user` varchar(100) NOT NULL COMMENT '创建人',
  `cr_user_true_name` varchar(100) NOT NULL COMMENT '创建人真实姓名',
  `cr_time` datetime NOT NULL COMMENT '创建时间',
  `cr_dept_id` bigint(20) NOT NULL COMMENT '创建人所属部门ID',
  `is_del` int(2) NOT NULL DEFAULT 0 COMMENT '是否删除\n0：否\n1：是',
  `data_type` varchar(100) DEFAULT NULL,
  `data_class` varchar(100) DEFAULT NULL,
  `classical_flag` int(2) DEFAULT 0 COMMENT '经典标识\n0：否\n1：是',
  `data_no` int(11) NOT NULL COMMENT '编号',
  `data_year` int(11) NOT NULL COMMENT '所属年份',
  `data_title` varchar(255) NOT NULL COMMENT '标题',
  `drafts_flag` int(2) DEFAULT 0 COMMENT '草稿标识\n0：否\n1：是',
  `version_id` bigint(20) DEFAULT 0 COMMENT '版本ID，缺省为0',
  `chat_id` bigint(20) DEFAULT 0 COMMENT '会话ID，缺省为0',
  `accept_dept_ids` longtext DEFAULT NULL COMMENT '上报的接收单位ID',
  `status_code` int(11) NOT NULL DEFAULT 0 COMMENT '状态码',
  `push_type` varchar(100) NOT NULL COMMENT '填写类型\npushUp:推送上级\npushDown:推送下级',
  `data_content` longtext DEFAULT NULL COMMENT '数据内容（合并后的内容）',
  `raw_data_content` longtext DEFAULT NULL COMMENT '版本内容（原始内容，合并不会对其产生影响）',
  `source_dept_id` bigint(20) DEFAULT NULL COMMENT '来源单位ID',
  `source_clue_no` varchar(100) DEFAULT NULL COMMENT '源线索编码',
  `clue_no` varchar(100) DEFAULT NULL COMMENT '线索编码',
  `target_time` varchar(50) DEFAULT NULL COMMENT '近期：近期\n具体日期：yyyy-MM-dd',
  `target_time_format` datetime DEFAULT NULL COMMENT '指向日期：yyyy-MM-dd格式\n选择近期时为创建的日期',
  `cr_date` datetime NOT NULL COMMENT '创建日期',
  `cr_district_code` varchar(100) NOT NULL COMMENT '创建人所属地域编码',
  `group_type` varchar(100) DEFAULT NULL COMMENT '群体类型',
  `group_detail` varchar(100) DEFAULT NULL COMMENT '群体细类',
  `wqfs` varchar(100) DEFAULT NULL COMMENT '维权方式',
  `xwfs` varchar(100) DEFAULT NULL COMMENT '行为方式',
  `zxdd` varchar(100) DEFAULT NULL COMMENT '指向地点',
  `xxdd` varchar(100) DEFAULT NULL COMMENT '详细地点',
  `cc_dept_ids` longtext DEFAULT NULL COMMENT '抄送单位ID串',
  `signer` varchar(100) DEFAULT NULL COMMENT '签发人',
  `contacts_user` varchar(100) DEFAULT NULL COMMENT '联系人',
  `phone` varchar(100) DEFAULT NULL COMMENT '联系电话',
  `attachment` longtext DEFAULT NULL COMMENT '附件',
  `check_level` varchar(100) DEFAULT NULL COMMENT '核查等级\n推送下级时必填',
  `feedback_limit_time` datetime DEFAULT NULL COMMENT '反馈时限\n推送下级时必填',
  `feedback_timeout` int(2) DEFAULT 0 COMMENT '反馈超时\n0：否\n1：是',
  `person_type` varchar(50) DEFAULT NULL COMMENT '省内：local\n省外：noLocal\n均包含：all',
  `repeat_flag` int(2) DEFAULT 0 COMMENT '是否重复',
  `repeat_data_ids` longtext DEFAULT NULL COMMENT '重复ID串',
  `merge_data_ids` longtext DEFAULT NULL COMMENT '合并ID串',
  `hidden_flag` int(2) DEFAULT 0 COMMENT '是否隐藏（合并的时候只保留一条，其他的都隐藏）',
  `related_person_zjhm` longtext DEFAULT NULL COMMENT '该线索原始关联的证件号码，方便后面拆分合并',
  PRIMARY KEY (`data_id`)
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS `tb_intelligence_xiansuo_person_library` (
  `data_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cr_user` varchar(100) NOT NULL,
  `cr_user_true_name` varchar(100) NOT NULL,
  `cr_time` datetime NOT NULL,
  `cr_dept_id` bigint(20) NOT NULL,
  `is_del` int(2) NOT NULL DEFAULT 0,
  `zjhm` varchar(100) NOT NULL,
  `xm` varchar(100) NOT NULL,
  `sjhwm` varchar(100) DEFAULT NULL,
  `hjd` varchar(100) DEFAULT NULL,
  `gsdy` varchar(100) DEFAULT NULL COMMENT '用户归属地域编码，可以手动修改（最多选择到区）',
  `gsdymc` varchar(100) DEFAULT NULL,
  `remarks_label` longtext DEFAULT NULL,
  `remarks_label_name` longtext DEFAULT NULL,
  `check_dept_id` bigint(20) DEFAULT NULL COMMENT '核查单位ID',
  `update_user` varchar(100) DEFAULT NULL,
  `update_user_true_name` varchar(100) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `last_clue_id` bigint(20) NOT NULL COMMENT '最后更新的线索ID',
  PRIMARY KEY (`data_id`)
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS `tb_intelligence_xiansuo_related_person` (
  `data_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cr_user` varchar(100) NOT NULL,
  `cr_user_true_name` varchar(100) NOT NULL,
  `cr_time` datetime NOT NULL,
  `cr_dept_id` bigint(20) NOT NULL,
  `is_del` int(2) NOT NULL DEFAULT 0,
  `zjhm` varchar(100) NOT NULL,
  `xm` varchar(100) NOT NULL,
  `sjhwm` varchar(100) DEFAULT NULL,
  `hjd` varchar(100) DEFAULT NULL,
  `gsdy` varchar(100) DEFAULT NULL COMMENT '用户归属地域编码，可以手动修改（最多选择到区）',
  `gsdymc` varchar(100) DEFAULT NULL,
  `remarks_label` longtext DEFAULT NULL,
  `remarks_label_name` longtext DEFAULT NULL,
  `is_searched` int(2) DEFAULT 0,
  `check_dept_id` bigint(20) DEFAULT NULL COMMENT '核查单位ID',
  `check_dept_code` varchar(100) DEFAULT NULL,
  `deal_user` varchar(100) DEFAULT NULL,
  `deal_phone` varchar(100) DEFAULT NULL,
  `work_status` varchar(100) DEFAULT NULL,
  `real_belong` varchar(100) DEFAULT NULL,
  `current_location_flag` varchar(100) DEFAULT NULL,
  `current_location` varchar(100) DEFAULT NULL,
  `content` longtext DEFAULT NULL,
  `valid_flag` int(2) DEFAULT 1,
  `attachment` longtext DEFAULT NULL,
  `assign_time` datetime DEFAULT NULL,
  `feedback_time` datetime DEFAULT NULL,
  PRIMARY KEY (`data_id`)
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS `tb_intelligence_xiansuo_related_person_mapping` (
  `data_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cr_user` varchar(100) NOT NULL,
  `cr_time` datetime NOT NULL,
  `cr_dept_id` bigint(20) NOT NULL,
  `is_del` int(2) NOT NULL DEFAULT 0,
  `clue_id` bigint(20) NOT NULL,
  `person_id` bigint(20) NOT NULL,
  PRIMARY KEY (`data_id`),
  KEY `tb_intelligence_xiansuo_related_person_mapping_clue_id_IDX` (`clue_id`,`person_id`) USING BTREE
) ENGINE=InnoDB;
