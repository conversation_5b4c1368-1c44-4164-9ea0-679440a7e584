-- 历史数据: {"name":"相关人员","type":"LIST_SCHEMA","table":"t_profile_person","fields":[{"db":{"table":"t_profile_person","column":"name","jdbcType":"string"},"name":"name","listSchema":{"style":{"align":"center","width":104},"schema":{"type":"string","title":"人员名称"},"properties":{"href":"/ys-app/archives/person/details?id={value}","copyable":false,"editable":false,"required":false,"sortable":false}}},{"db":{"table":"t_profile_person","column":"risk_level","jdbcType":"string"},"name":"riskLevel","listSchema":{"style":{"align":"center","width":128},"schema":{"type":"string","title":"风险等级"},"properties":{"colorMap":{"关注":"#333333 ","中风险":"#FFCE60","低风险":"#6088D6","高风险":"#FA8C34","重中之重":"#EC3939"},"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person","column":"id_number","jdbcType":"number"},"name":"idNumber","listSchema":{"style":{"align":"center","width":224},"schema":{"type":"string","title":"身份证号"},"properties":{"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person_event_relation","column":"risk_label_ids","mapping":"risk_label_ids_sum_score","jdbcType":"string","databaseRelation":{"type":"RELATION_TABLE","table":"t_profile_person_event_relation","joinTo":{"table":"t_profile_person","column":"id","joinColumn":"person_id"},"joinFrom":{"table":"t_profile_event","column":"id","joinColumn":"event_id"}}},"name":"riskScore","listSchema":{"style":{"align":"left","width":128},"schema":{"type":"string","title":"风险分值"},"properties":{"color":"#FF6C6C","copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person","column":"person_label","mapping":"label_id_array_to_name","jdbcType":"label_id_array"},"name":"personLabel","listSchema":{"style":{"align":"left","width":237},"filter":{"key":"personLabel","type":"multiple-tree","value":["&&person_label&&"],"fieldNames":{"label":"name","value":"id","children":"children"},"displayName":"人员标签"},"schema":{"type":"array","title":"人员标签"},"properties":{"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":2}}},{"db":{"table":"t_profile_person_event_relation","column":"risk_label_ids","mapping":"risk_label_id_array_to_name","jdbcType":"string","databaseRelation":{"type":"RELATION_TABLE","table":"t_profile_person_event_relation","joinTo":{"table":"t_profile_person","column":"id","joinColumn":"person_id"},"joinFrom":{"table":"t_profile_event","column":"id","joinColumn":"event_id"}}},"name":"riskLabels","listSchema":{"style":{"align":"left"},"schema":{"type":"risk_label_array","title":"风险标识"},"properties":{"copyable":false,"editable":true,"required":false,"sortable":false,"instrLength":3}}}],"selectable":false,"searchFields":[{"key":"name","name":"人员名称"}]}
UPDATE `t_profile_module` SET `list_schema` = '{"name":"相关人员","type":"LIST_SCHEMA","table":"t_profile_person","fields":[{"db":{"table":"t_profile_person","column":"name","jdbcType":"string"},"name":"name","listSchema":{"style":{"align":"center","width":104},"schema":{"type":"string","title":"人员名称"},"properties":{"href":"/ys-app/archives/person/details?id={value}","copyable":false,"editable":false,"required":false,"sortable":false}}},{"db":{"table":"t_profile_person","column":"risk_level","jdbcType":"string"},"name":"riskLevel","listSchema":{"style":{"align":"center","width":128},"schema":{"type":"string","title":"风险等级"},"properties":{"colorMap":{"关注":"#333333 ","中风险":"#FFCE60","低风险":"#6088D6","高风险":"#FA8C34","重中之重":"#EC3939"},"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person","column":"id_number","jdbcType":"number"},"name":"idNumber","listSchema":{"style":{"align":"center","width":224},"schema":{"type":"string","title":"身份证号"},"properties":{"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person_event_relation","column":"risk_label_ids","mapping":"risk_label_ids_sum_score","jdbcType":"string","exist":false,"databaseRelation":{"type":"RELATION_TABLE","table":"t_profile_person_event_relation","joinTo":{"table":"t_profile_person","column":"id","joinColumn":"person_id"},"joinFrom":{"table":"t_profile_event","column":"id","joinColumn":"event_id"}}},"name":"riskScore","listSchema":{"style":{"align":"left","width":128},"schema":{"type":"string","title":"风险分值"},"properties":{"color":"#FF6C6C","copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":1}}},{"db":{"table":"t_profile_person","column":"person_label","mapping":"label_id_array_to_name","jdbcType":"label_id_array"},"name":"personLabel","listSchema":{"style":{"align":"left","width":237},"filter":{"key":"personLabel","type":"multiple-tree","value":["&&person_label&&"],"fieldNames":{"label":"name","value":"id","children":"children"},"displayName":"人员标签"},"schema":{"type":"array","title":"人员标签"},"properties":{"copyable":false,"editable":false,"required":false,"sortable":false,"instrLength":2}}},{"db":{"table":"t_profile_person_event_relation","column":"risk_label_ids","mapping":"risk_label_id_array_to_name","jdbcType":"string","databaseRelation":{"type":"RELATION_TABLE","table":"t_profile_person_event_relation","joinTo":{"table":"t_profile_person","column":"id","joinColumn":"person_id"},"joinFrom":{"table":"t_profile_event","column":"id","joinColumn":"event_id"}}},"name":"riskLabels","listSchema":{"style":{"align":"left"},"schema":{"type":"risk_label_array","title":"风险标识"},"properties":{"copyable":false,"editable":true,"required":false,"sortable":false,"instrLength":3}}}],"selectable":false,"searchFields":[{"key":"name","name":"人员名称"}]}' WHERE `id` = 203;
