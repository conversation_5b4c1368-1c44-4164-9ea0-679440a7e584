DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_profile_police' AND column_name='type')
THEN
    ALTER TABLE t_profile_police ADD type int NULL COMMENT '类型，码表：police_type';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_police_resume_relation' AND column_name='person_type')
THEN
    ALTER TABLE t_police_resume_relation ADD person_type int NULL COMMENT '人员类别，码表：police_ll_person_type';
END IF;

IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_police_rank_relation' AND column_name='qualifications')
THEN
    ALTER TABLE t_police_rank_relation ADD qualifications int NULL COMMENT '任职资格，码表：police_rz_qualifications';
END IF;

END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;

-- 插入字典数据：相关人员身份
DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    DECLARE pid1 INT;
    DECLARE pid2 INT;
    DECLARE pid3 INT;
    DELETE FROM t_dict WHERE type LIKE 'police_type%';
    DELETE FROM t_dict WHERE type LIKE 'police_ll_person_type%';
    DELETE FROM t_dict WHERE type LIKE 'police_rz_qualifications%';
    INSERT INTO t_dict (`type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES ('police_type_group', 0, '警察类型', 0, 'police_type', 0, NULL, NULL, NULL, 1);
    SET pid1 = LAST_INSERT_ID();
    INSERT INTO t_dict (`type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES ('police_ll_person_type_group', 0, '人员类别', 0, 'police_ll_person_type', 0, NULL, NULL, NULL, 1);
    SET pid2 = LAST_INSERT_ID();
    INSERT INTO t_dict (`type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES ('police_rz_qualifications_group', 0, '任职资格', 0, 'police_rz_qualifications_group', 0, NULL, NULL, NULL, 1);
    SET pid3 = LAST_INSERT_ID();
    UPDATE t_dict SET p_id = id WHERE (type = 'police_type_group' or type = 'police_ll_person_type_group' or type = 'police_rz_qualifications_group') AND code = 0;

    INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES  (pid1, 'police_type', 1, '人民警察', 0, NULL, 1, NULL, NULL, NULL, 1),
            (pid1, 'police_type', 2, '事业单位', 0, NULL, 2, NULL, NULL, NULL, 1),
            (pid1, 'police_type', 3, '机关工勤', 0, NULL, 3, NULL, NULL, NULL, 1),
            (pid1, 'police_type', 4, '警务辅助', 0, NULL, 4, NULL, NULL, NULL, 1),
            (pid1, 'police_type', 5, '其他', 0, NULL, 5, NULL, NULL, NULL, 1);

    INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES  (pid2, 'police_ll_person_type', 1, '一级部门正职', 0, NULL, 1, NULL, NULL, NULL, 1),
            (pid2, 'police_ll_person_type', 2, '一级部门副职', 0, NULL, 2, NULL, NULL, NULL, 1),
            (pid2, 'police_ll_person_type', 3, '二级部门正职', 0, NULL, 3, NULL, NULL, NULL, 1),
            (pid2, 'police_ll_person_type', 4, '二级部门副职', 0, NULL, 4, NULL, NULL, NULL, 1),
            (pid2, 'police_ll_person_type', 5, '民警', 0, NULL, 5, NULL, NULL, NULL, 1);

    INSERT INTO t_dict (p_id, `type`, code, name, p_code, dict_desc, show_number, standard, flag, color, status)
    VALUES  (pid3, 'police_rz_qualifications', 1, '正高级警务技术任职资格', 0, NULL, 1, NULL, NULL, NULL, 1),
            (pid3, 'police_rz_qualifications', 2, '副高级警务技术任职资格', 0, NULL, 2, NULL, NULL, NULL, 1),
            (pid3, 'police_rz_qualifications', 3, '中级警务技术任职资格', 0, NULL, 3, NULL, NULL, NULL, 1),
            (pid3, 'police_rz_qualifications', 4, '初级警务技术任职资格', 0, NULL, 4, NULL, NULL, NULL, 1);

END $$
DELIMITER ;
CALL insert_dict_data;
