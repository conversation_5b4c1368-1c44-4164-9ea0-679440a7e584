delete
from t_dict
where type = 'control_warning_status';

INSERT
IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3471, 2060, 'control_warning_status', 1, '待签收', 0, NULL, 1, NULL, NULL, NULL);
INSERT
IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3472, 2060, 'control_warning_status', 2, '已签收', 0, NULL, 2, NULL, NULL, NULL);
INSERT
IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3473, 2060, 'control_warning_status', 3, '已反馈', 0, NULL, 3, NULL, NULL, NULL);
INSERT
IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3474, 2060, 'control_warning_status', 4, '处置完结', 0, NULL, 4, NULL, NULL, NULL);


DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_process' AND column_name='create_user_id')
    THEN
alter table t_warning_process drop create_user_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;


DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_process' AND column_name='create_dept_id')
    THEN
alter table t_warning_process drop create_dept_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_process' AND column_name='update_user_id')
    THEN
alter table t_warning_process drop update_user_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;


DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_process' AND column_name='update_dept_id')
    THEN
alter table t_warning_process drop update_dept_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;


DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_process' AND column_name='update_time')
    THEN
alter table t_warning_process drop update_time;
END IF;
END $$
DELIMITER ;
CALL drop_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_track' AND column_name='create_user_id')
    THEN
alter table t_warning_track drop create_user_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;


DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_track' AND column_name='create_dept_id')
    THEN
alter table t_warning_track drop create_dept_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;

DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_track' AND column_name='update_user_id')
    THEN
alter table t_warning_track drop update_user_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;


DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_track' AND column_name='update_dept_id')
    THEN
alter table t_warning_track drop update_dept_id;
END IF;
END $$
DELIMITER ;
CALL drop_column;


DELIMITER $$
DROP PROCEDURE IF EXISTS `drop_column` $$
CREATE PROCEDURE drop_column()
BEGIN
    IF  EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_warning_track' AND column_name='update_time')
    THEN
alter table t_warning_track drop update_time;
END IF;
END $$
DELIMITER ;
CALL drop_column;


drop table if exists t_warning_action;




DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
 IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=concat((select database()),'/','t_warning_process')) and name ='idx_warning_id' )
    THEN
CREATE INDEX idx_warning_id USING BTREE ON t_warning_process (warning_id);
END IF;
END $$
DELIMITER ;
CALL add_index;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
 IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=concat((select database()),'/','t_warning_user_relation')) and name ='idx_dept_id' )
    THEN
CREATE INDEX idx_dept_id USING BTREE ON t_warning_user_relation (dept_id);
END IF;
END $$
DELIMITER ;
CALL add_index;


DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
 IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=concat((select database()),'/','t_warning_user_relation')) and name ='idx_user_id' )
    THEN
CREATE INDEX idx_user_id USING BTREE ON t_warning_user_relation (user_id);
END IF;
END $$
DELIMITER ;
CALL add_index;


DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
 IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=concat((select database()),'/','t_warning_user_relation')) and name ='idx_process_id' )
    THEN
CREATE INDEX idx_process_id USING BTREE ON t_warning_user_relation (process_id);
END IF;
END $$
DELIMITER ;
CALL add_index;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
 IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=concat((select database()),'/','t_warning_user_relation')) and name ='idx_warning_id' )
    THEN
CREATE INDEX idx_warning_id USING BTREE ON t_warning_user_relation (warning_id);
END IF;
END $$
DELIMITER ;
CALL add_index;


DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
 IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=concat((select database()),'/','t_warning_user_relation')) and name ='uk_user_id_dept_id_warning_id' )
    THEN
CREATE INDEX uk_user_id_dept_id_warning_id USING BTREE ON t_warning_user_relation (user_id, dept_id, warning_id);
END IF;
END $$
DELIMITER ;
CALL add_index;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
 IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=concat((select database()),'/','t_warning_feedback')) and name ='idx_warning_id' )
    THEN
CREATE INDEX idx_warning_id USING BTREE ON t_warning_feedback(warning_id);
END IF;
END $$
DELIMITER ;
CALL add_index;

DELIMITER $$
DROP PROCEDURE IF EXISTS `add_index` $$
CREATE PROCEDURE add_index()
BEGIN
 IF NOT EXISTS( SELECT * FROM  information_schema.INNODB_INDEXES  WHERE TABLE_ID=(select TABLE_ID from  information_schema.INNODB_TABLES where name=concat((select database()),'/','t_warning_feedback')) and name ='idx_process_id' )
    THEN
CREATE INDEX idx_process_id USING BTREE ON t_warning_feedback(process_id);
END IF;
END $$
DELIMITER ;
CALL add_index;

