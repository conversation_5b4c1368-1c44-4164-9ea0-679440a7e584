DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_fx_action_excavate_dict' AND column_name='coefficient')
    THEN
        ALTER TABLE tb_fx_action_excavate_dict ADD coefficient DOUBLE NULL COMMENT '系数';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;