DELETE FROM t_profile_module WHERE id=1392;
DELETE FROM t_profile_module WHERE id=1588;
INSERT INTO t_profile_module (id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content) VALUES(1588, '管控措施', 'gkcs', 'personV2', 1345, 1, 1, NULL, NULL, '{"name": "管控措施", "type": "LIST_SCHEMA", "table": "t_profile_person_gkcs", "fields": [{"db": {"table": "t_profile_person_gkcs", "column": "detail", "jdbcType": "string"}, "name": "detail", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "管控措施"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false}}}, {"db": {"table": "t_profile_person_gkcs", "column": "expiration_date", "jdbcType": "date_to_text"}, "name": "expiration_date", "listSchema": {"style": {"align": "center", "format": "date"}, "schema": {"type": "string", "title": "管控到期日期"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false}}}, {"db": {"table": "t_profile_person_gkcs", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "录入单位"}, "schema": {"type": "string", "title": "录入单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_person_gkcs", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "录入时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "extendFields": [{"table": "t_profile_person_gkcs", "value": 5, "column": "police_kind", "jdbcType": "integer"}], "searchFields": [{"key": "name", "name": "管控措施"}]}', 1, '{"type": "FOREIGN_KEY", "table": "t_profile_person_gkcs", "column": "person_id", "idColumn": "id", "primaryColumn": "id", "extendCondition": [{"value": 5, "column": "police_kind"}]}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);