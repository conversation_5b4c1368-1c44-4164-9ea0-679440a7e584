DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS(
        SELECT * FROM  information_schema.columns
        WHERE table_schema=(select database())
        AND table_name='tb_profile_juveniles_score' AND column_name='search_keys'
        )
    THEN
        ALTER TABLE tb_profile_juveniles_score ADD search_keys LONGTEXT NULL COMMENT '得分的二级分类key';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;