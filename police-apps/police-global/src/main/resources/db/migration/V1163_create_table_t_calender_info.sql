create table if not exists t_calender_info
(
    id               bigint auto_increment primary key,
    create_time      timestamp         null,
    create_user_id   bigint            null,
    create_dept_id   bigint            null,
    update_time      timestamp         null,
    update_user_id   bigint            null,
    update_dept_id   bigint            null,
    title            varchar(255)      not null comment '标题',
    start_date       timestamp         not null comment '开始日期',
    end_date         timestamp         not null comment '结束日期',
    type             tinyint           not null comment '类型，1-敏感日，2-节假日',
    remark           text              null comment '备注',
    adjust_date      json              null comment '调班日'
)
    comment '日历信息表';


create table if not exists t_calender_arrange
(
    id               bigint auto_increment primary key,
    create_time      timestamp         null,
    create_user_id   bigint            null,
    create_dept_id   bigint            null,
    update_time      timestamp         null,
    update_user_id   bigint            null,
    update_dept_id   bigint            null,
    date             timestamp         not null comment '日期',
    calender_info_id   bigint          not null comment '日历id'
)
    comment '日历排列表';