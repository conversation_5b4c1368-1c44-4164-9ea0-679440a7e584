-- 模型表新增模型警种
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    -- 同步字段
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='t_statistics_warning_model' AND column_name='police_category_dm')
    THEN
        ALTER TABLE t_statistics_warning_model ADD police_category_dm int NULL COMMENT '模型警钟代码，1代表基础模型，2代表警种类模型';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;