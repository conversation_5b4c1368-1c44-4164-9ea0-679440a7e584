CREATE TABLE IF NOT EXISTS `tb_common_bigscreen_area_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `third_id` varchar(255) DEFAULT NULL COMMENT '三方id',
  `name` varchar(255) DEFAULT NULL COMMENT '地域',
  `type_code` varchar(100) DEFAULT NULL COMMENT '类型编码',
  `type_value` varchar(255) DEFAULT NULL COMMENT '类型值',
  `type_value_json` text DEFAULT NULL COMMENT '类型json',
  `district_code` varchar(255) DEFAULT NULL COMMENT '地域编码',
  `dept_name` varchar(255) DEFAULT NULL COMMENT '单位名称',
  `portray_mode_name` varchar(255) DEFAULT NULL COMMENT '描绘方式',
  `portray_mode_code` varchar(100) DEFAULT NULL COMMENT '描绘方式编码',
  `portray_mode_data` longtext DEFAULT NULL COMMENT 'json值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='大屏-地域信息';