DELIMITER //
CREATE PROCEDURE AddColumnIfNotExists(IN tableName VARCHAR(64), IN columnName VARCHAR(64), IN columnDetails VARCHAR(64))
BEGIN
    DECLARE existing INT DEFAULT 0;

    SELECT COUNT(*)
    INTO existing
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = tableName AND COLUMN_NAME = columnName;
    IF existing = 0 THEN
        SET @s = CONCAT('ALTER TABLE ', tableName, ' ADD ', columnName, ' ', columnDetails);
        PREPARE stmt FROM @s;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //
DELIMITER ;
CALL AddColumnIfNotExists('tb_fx_influx_person', 'sensing_type', 'varchar(100) NULL COMMENT \'感知源类型\'');
CALL AddColumnIfNotExists('t_approval_node_config', 'dynamic_approver', 'INT DEFAULT 0 NULL COMMENT \'是否是动态审批节点\'');
CALL AddColumnIfNotExists('t_approval_node_config', 'approver_strategy', 'varchar(100) NULL COMMENT \'动态审批节点配置\'');
CALL AddColumnIfNotExists('t_approval_node_result', 'remark', 'TEXT NULL COMMENT \'审批信息\'');
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;