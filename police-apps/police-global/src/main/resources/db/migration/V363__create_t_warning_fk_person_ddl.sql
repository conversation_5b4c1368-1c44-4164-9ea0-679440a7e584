CREATE TABLE `t_warning_fk_person`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '姓名',
  `id_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证',
  `person_status` int(0) NULL DEFAULT NULL COMMENT '人员状态：0-首次入去，1-入区三天',
  `first_into_time` datetime(0) NULL DEFAULT NULL COMMENT '首次进入时间',
  `photo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '照片',
  `on_record` int(0) NULL DEFAULT NULL COMMENT '是否建档：0为未建档，1为已建档',
  `person_profile_id` int(0) NULL DEFAULT NULL COMMENT '人档id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;