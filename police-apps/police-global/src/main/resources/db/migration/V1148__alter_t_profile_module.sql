UPDATE t_profile_module
SET cn_name='相关事件', en_name='relatedEvent', `type`='personV2', pid=1390, is_archive=1, show_order=9, table_schema=NULL, form_schema=NULL, list_schema='{"name": "相关事件", "type": "LIST_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件名称"}, "properties": {"href": "/ys-app/archives/police-archive-event/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "(SELECT event_id, CASE police_kind WHEN 3 THEN source_time ELSE related_time END as relatedTime FROM t_profile_event_risk_point_info ORDER BY FIELD(police_kind, 4, 3, 99)) za", "column": "relatedTime", "mapping": "date_time_to_general_string", "jdbcType": "timestamp", "databaseRelation": {"type": "FOREIGN_KEY", "column": "event_id"}}, "name": "za_start_time", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件开始时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "创建时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "创建单位"}, "schema": {"type": "string", "title": "创建单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "searchFields": [{"key": "name", "name": "事件名称"}]}', is_add=1, database_relation='{"type": "RELATION_TABLE", "table": "t_profile_person_event_relation", "joinTo": {"table": "t_profile_event", "column": "id", "joinColumn": "event_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1412;
UPDATE t_profile_module
SET cn_name='相关事件', en_name='relatedEvent', `type`='personV2', pid=1391, is_archive=1, show_order=7, table_schema=NULL, form_schema=NULL, list_schema='{"name": "相关事件", "type": "LIST_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件名称"}, "properties": {"href": "/ys-app/archives/police-archive-event/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "(SELECT event_id, CASE police_kind WHEN 3 THEN source_time ELSE related_time END as relatedTime FROM t_profile_event_risk_point_info ORDER BY FIELD(police_kind, 4, 3, 99)) za", "column": "relatedTime", "mapping": "date_time_to_general_string", "jdbcType": "timestamp", "databaseRelation": {"type": "FOREIGN_KEY", "column": "event_id"}}, "name": "qt_start_time", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件开始时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "创建时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "创建单位"}, "schema": {"type": "string", "title": "创建单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "searchFields": [{"key": "name", "name": "事件名称"}]}', is_add=1, database_relation='{"type": "RELATION_TABLE", "table": "t_profile_person_event_relation", "joinTo": {"table": "t_profile_event", "column": "id", "joinColumn": "event_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1420;
UPDATE t_profile_module
SET cn_name='相关事件', en_name='relatedEvent', `type`='personV2', pid=1307, is_archive=1, show_order=5, table_schema=NULL, form_schema=NULL, list_schema='{"name": "相关事件", "type": "LIST_SCHEMA", "table": "t_profile_event", "fields": [{"db": {"table": "t_profile_event", "column": "name", "jdbcType": "string"}, "name": "name", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件名称"}, "properties": {"href": "/ys-app/archives/police-archive-event/details?id={value}", "copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "(SELECT event_id, CASE police_kind WHEN 3 THEN source_time ELSE related_time END as relatedTime FROM t_profile_event_risk_point_info ORDER BY FIELD(police_kind, 4, 3, 99)) za", "column": "relatedTime", "mapping": "date_time_to_general_string", "jdbcType": "timestamp", "databaseRelation": {"type": "FOREIGN_KEY", "column": "event_id"}}, "name": "jz_start_time", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "事件开始时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "create_time", "mapping": "date_time_to_general_string", "jdbcType": "datetime"}, "name": "createTime", "listSchema": {"style": {"align": "center"}, "schema": {"type": "string", "title": "创建时间"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}, {"db": {"table": "t_profile_event", "column": "create_dept_id", "mapping": "dept_id_to_dept_name", "jdbcType": "number"}, "name": "create_dept_id", "listSchema": {"style": {"align": "center"}, "filter": {"key": "create_dept_id", "type": "tree", "value": ["&&dept&&"], "fieldNames": {"label": "shortName", "value": "id", "children": "children"}, "displayName": "创建单位"}, "schema": {"type": "string", "title": "创建单位"}, "properties": {"copyable": false, "editable": false, "required": false, "sortable": false}}}], "selectable": false, "searchFields": [{"key": "name", "name": "事件名称"}]}', is_add=1, database_relation='{"type": "RELATION_TABLE", "table": "t_profile_person_event_relation", "joinTo": {"table": "t_profile_event", "column": "id", "joinColumn": "event_id"}, "joinFrom": {"table": "t_profile_person", "column": "id", "joinColumn": "person_id"}}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1599;