DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM  information_schema.columns WHERE table_schema=(select database()) AND table_name='tb_fx_special_case_judge_person_relation' AND column_name='id_card')
    THEN
        ALTER TABLE tb_fx_special_case_judge_person_relation ADD id_card varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;