DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS( SELECT * FROM information_schema.columns WHERE table_schema=(select database()) AND table_name='t_user_role_relation' AND column_name='mac')
    THEN
ALTER TABLE t_user_role_relation ADD mac varchar(255) NULL COMMENT 'mac完整性信息';
END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;