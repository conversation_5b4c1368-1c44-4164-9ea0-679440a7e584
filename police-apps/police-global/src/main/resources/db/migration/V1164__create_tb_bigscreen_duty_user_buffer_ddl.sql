CREATE TABLE IF NOT EXISTS `tb_bigscreen_duty_user_buffer` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `cr_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `cr_user_id` bigint unsigned NOT NULL COMMENT '创建人ID',
    `cr_dept_id` bigint unsigned NOT NULL COMMENT '创建人所属部门ID',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `dept_id` bigint unsigned NOT NULL COMMENT '所属部门ID',
    `xm` varchar(255) NOT NULL COMMENT '姓名',
    `zjhm` varchar(255) NOT NULL COMMENT '证件号码（如身份证）',
    `sjh` varchar(255) DEFAULT NULL COMMENT '手机号',
    `jh` varchar(255) DEFAULT NULL COMMENT '警号',
    `duty` varchar(100) DEFAULT NULL COMMENT '职务名称',
    `post_code` int DEFAULT NULL COMMENT '岗位代码',
    `post_name` varchar(100) NOT NULL COMMENT '岗位名称',
    `profile_pic` varchar(1000) DEFAULT NULL COMMENT '个人头像图片路径',
    `police_kind` int DEFAULT 0 COMMENT '警种代码',
    `is_del` tinyint DEFAULT 0 COMMENT '删除标识。0-否',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='值班用户信息缓存表';