UPDATE t_profile_module
SET cn_name='教育经历', en_name='educationExperience', `type`='police', pid=NULL, is_archive=1, show_order=5, table_schema=NULL, form_schema=NULL, list_schema='{"name": "教育经历", "type": "LIST_SCHEMA", "table": "t_police_education_experience_relation", "fields": [{"db": {"table": "t_police_education_experience_relation", "column": "start_time", "jdbcType": "timestamp"}, "name": "start_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "在校开始时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_education_experience_relation", "column": "end_time", "jdbcType": "timestamp"}, "name": "end_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "在校结束时间"}, "properties": {"format": "YYYY-MM", "picker": "month", "copyable": false, "editable": true, "required": false, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_education_experience_relation", "column": "graduation_school", "jdbcType": "number"}, "name": "graduation_school", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "毕业学校"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_education_experience_relation", "column": "degree", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "t_degree", "codeToId": true}, "name": "degree", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "学历"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_education_experience_relation", "column": "academic_degree", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "t_degree", "codeToId": true}, "name": "academic_degree", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "学位"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_education_experience_relation", "column": "major", "jdbcType": "string"}, "name": "major", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "专业"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false}}}], "selectable": false, "searchFields": []}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_police_education_experience_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1657;
UPDATE t_profile_module
SET cn_name='民主测评', en_name='democraticEvaluation', `type`='police', pid=NULL, is_archive=1, show_order=11, table_schema=NULL, form_schema=NULL, list_schema='{"name": "民主测评", "type": "LIST_SCHEMA", "table": "t_police_democratic_evaluation_relation", "fields": [{"db": {"table": "t_police_democratic_evaluation_relation", "column": "evaluation_year", "mapping": "date_to_year", "jdbcType": "timestamp"}, "name": "evaluation_year", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "评价年份"}, "properties": {"format": "YYYY", "picker": "year", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "yx_count", "jdbcType": "number"}, "name": "yx_count", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "优秀得票数"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "cz_count", "jdbcType": "number"}, "name": "cz_count", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "称职得票数"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "jbcz_count", "jdbcType": "number"}, "name": "jbcz_conunt", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "基本称职得票数"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_democratic_evaluation_relation", "column": "bcz_count", "jdbcType": "number"}, "name": "bcz_count", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "不称职票数"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', is_add=1, database_relation='{"type": "FOREIGN_KEY", "table": "t_police_democratic_evaluation_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', show_schema_type='LIST_SCHEMA', add_schema_type='LIST_SCHEMA', is_operation_content=1, is_mobile_content=1, is_web_content=1, is_fk_content=0
WHERE id=1662;

delete from t_profile_module where id in (1671, 1670);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1671, '局领导班子评价', 'leaderEvaluation', 'police', NULL, 1, 12, NULL, NULL, '{"name": "局领导班子评价", "type": "LIST_SCHEMA", "table": "t_police_leader_evaluation_relation", "fields": [{"db": {"table": "t_police_leader_evaluation_relation", "column": "recommend_time", "mapping": "date_to_general_string", "jdbcType": "timestamp"}, "name": "recommend_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "推荐时间"}, "properties": {"format": "YYYY-MM-DD", "picker": "date", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_leader_evaluation_relation", "column": "recommend_level", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_leader_recommend_level", "codeToId": true}, "name": "recommend_level", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "推荐级别"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_leader_evaluation_relation", "column": "recommend_person", "jdbcType": "string"}, "name": "recommend_person", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "推荐人"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_leader_evaluation_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);
INSERT INTO t_profile_module
(id, cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
VALUES(1670, '专业资格', 'professionalQualification', 'police', NULL, 1, 13, NULL, NULL, '{"name": "专业资格", "type": "LIST_SCHEMA", "table": "t_police_professional_qualification_relation", "fields": [{"db": {"table": "t_police_professional_qualification_relation", "column": "obtain_time", "mapping": "date_to_general_string", "jdbcType": "timestamp"}, "name": "obtain_time", "listSchema": {"style": {"align": "center", "format": "string"}, "schema": {"type": "datetime", "title": "获取时间"}, "properties": {"format": "YYYY-MM-DD", "picker": "date", "copyable": false, "editable": true, "required": true, "showTime": false, "sortable": false}}}, {"db": {"table": "t_police_professional_qualification_relation", "column": "professional_qualification", "mapping": "dict_code_to_name", "jdbcType": "number"}, "dict": {"type": "police_professional_qualification", "codeToId": true}, "name": "professional_qualification", "listSchema": {"style": {"align": "left"}, "schema": {"type": "select", "title": "专业级别"}, "properties": {"copyable": false, "editable": true, "required": true, "sortable": false, "instrLength": 1}}}, {"db": {"table": "t_police_professional_qualification_relation", "column": "description", "jdbcType": "string"}, "name": "description", "listSchema": {"style": {"align": "left"}, "schema": {"type": "string", "title": "描述"}, "properties": {"copyable": false, "editable": true, "required": false, "sortable": false, "instrLength": 1}}}], "selectable": false, "searchFields": []}', 1, '{"type": "FOREIGN_KEY", "table": "t_police_professional_qualification_relation", "column": "profile_id", "idColumn": "id", "primaryColumn": "id"}', 'LIST_SCHEMA', 'LIST_SCHEMA', 1, 1, 1, 0);