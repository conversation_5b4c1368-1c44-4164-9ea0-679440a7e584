-- 要情中的人员标签
DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict` $$
CREATE PROCEDURE insert_dict()
BEGIN
     DECLARE ppid INT;

     -- 删除历史数据
     delete from t_dict where `type` IN ('intelligence_person_label_group','intelligence_person_label');
     -- 插入新纪录
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label_group',0,'要情人员标签',NULL,NULL,0,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',1,'涉医',NULL,NULL,1,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',2,'涉警',NULL,NULL,2,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',3,'涉外',NULL,NULL,3,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',4,'造事造祸精神病',NULL,NULL,4,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',5,'个人极端',NULL,NULL,5,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',6,'涉恐',NULL,NULL,6,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',7,'涉疆',NULL,NULL,7,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',8,'涉校',NULL,NULL,8,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',9,'涉枪',NULL,NULL,9,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',10,'涉疫',NULL,NULL,10,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',11,'涉未成年',NULL,NULL,11,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',12,'少数民族',NULL,NULL,12,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',13,'监所安全',NULL,NULL,13,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',14,'涉藏',NULL,NULL,14,NULL,NULL,NULL);
     INSERT INTO t_dict (p_id,`type`,code,name,p_code,dict_desc,show_number,standard,flag,color) VALUES (0,'intelligence_person_label',15,'其他',NULL,NULL,15,NULL,NULL,NULL);
     SET ppid = (select id from t_dict where `type`='intelligence_person_label_group');
     UPDATE t_dict set p_id = ppid where `type` IN ('intelligence_person_label_group','intelligence_person_label');
END $$
DELIMITER ;
CALL insert_dict;
DROP PROCEDURE IF EXISTS `insert_dict`;
