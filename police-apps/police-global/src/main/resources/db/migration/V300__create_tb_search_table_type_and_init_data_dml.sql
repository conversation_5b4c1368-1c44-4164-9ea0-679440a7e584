DROP TABLE IF EXISTS `tb_search_table_type`;
CREATE TABLE IF NOT EXISTS `tb_search_table_type`
(
    `id`            bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `type`          varchar(255) NOT NULL COMMENT '一级分类',
    `sub_type`      varchar(255) NOT NULL COMMENT '二级分类',
    `order_num`     int(11) NULL COMMENT '排序，从小到大排序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB;

INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(1, '活动轨迹类', '民航轨迹', 1);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(2, '活动轨迹类', '铁路轨迹', 2);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(3, '活动轨迹类', '旅馆轨迹', 3);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(4, '活动轨迹类', '网吧轨迹', 4);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(5, '活动轨迹类', '汽车客运', 5);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(6, '活动轨迹类', '车辆轨迹', 6);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(7, '活动轨迹类', '图像轨迹', 7);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(8, '活动轨迹类', '护照轨迹', 8);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(9, '活动轨迹类', '出入境轨迹', 9);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(10, '活动轨迹类', '实有人口轨迹', 10);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(11, '活动轨迹类', '其他轨迹', 11);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(12, '公安业务类', '事故信息', 12);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(13, '公安业务类', '警情类信息', 13);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(14, '公安业务类', '案件信息', 14);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(15, '公安业务类', '背景类信息', 15);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(16, '公安业务类', '登记类信息', 16);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(17, '公安业务类', '登记变更信息', 17);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(18, '公安业务类', '案事件人员信息', 18);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(19, '公安业务类', '涉案物品信息', 19);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(20, '公安业务类', '案件侦破信息', 20);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(21, '公安业务类', '书证物证信息', 21);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(22, '公安业务类', '生物特征信息', 22);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(23, '公安业务类', '身份信息', 23);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(24, '公安业务类', '其他物品信息', 24);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(25, '公安业务类', '管控类信息', 25);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(26, '公安业务类', '涉案场所信息', 26);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(27, '公安业务类', '一般场所信息', 27);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(28, '政务服务类', '居住产权信息', 28);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(29, '政务服务类', '保险金融信息', 29);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(30, '政务服务类', '医疗健康信息', 30);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(31, '政务服务类', '教育就业信息', 31);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(32, '政务服务类', '工商税务信息', 32);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(33, '政务服务类', '司法监管信息', 33);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(34, '政务服务类', '宗教旅游信息', 34);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(35, '政务服务类', '道路交通信息', 35);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(36, '政务服务类', '农业农村信息', 36);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(37, '政务服务类', '民政民生信息', 37);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(38, '社会登记类', '市场交易信息', 38);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(39, '社会登记类', '租赁典当信息', 39);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(40, '社会登记类', '生活缴费信息', 40);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(41, '社会登记类', '公交刷卡信息', 41);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(42, '社会登记类', '其他信息', 42);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(43, '时空网络类', '网络交易信息', 43);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(44, '时空网络类', '虚拟身份信息', 44);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(45, '时空网络类', '物流快递信息', 45);
INSERT INTO tb_search_table_type(id, type, sub_type, order_num)VALUES(46, '时空网络类', '通信类信息', 46);
