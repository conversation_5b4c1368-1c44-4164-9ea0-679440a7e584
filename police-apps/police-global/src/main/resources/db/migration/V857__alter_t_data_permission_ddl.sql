DROP PROCEDURE IF EXISTS AddColumnIfNotExists;
DELIMITER //
CREATE PROCEDURE AddColumnIfNotExists(IN tableName VARCHAR(64), IN columnName VARCHAR(64), IN columnDetails VARCHAR(64))
BEGIN
    DECLARE existing INT DEFAULT 0;

    SELECT COUNT(*)
    INTO existing
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = tableName AND COLUMN_NAME = columnName;
    IF existing = 0 THEN
        SET @s = CONCAT('ALTER TABLE ', tableName, ' ADD ', columnName, ' ', columnDetails);
        PREPARE stmt FROM @s;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //
DELIMITER ;
CALL AddColumnIfNotExists('t_data_permission', 'police_kinds', 'json NULL COMMENT \'指定警种\'');
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;