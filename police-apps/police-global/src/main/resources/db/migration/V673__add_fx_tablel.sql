CREATE TABLE IF NOT EXISTS tb_fx_influx_person (
	id BIGINT auto_increment NOT NULL,
	create_time DATETIME NULL,
	create_user_id BIGINT NULL,
	create_dept_id BIGINT NULL,
	update_time DATETIME NULL,
	update_user_id BIGINT NULL,
	update_dept_id BIGINT NULL,
	real_name varchar(100) NULL COMMENT '真实姓名',
	id_card varchar(100) NULL COMMENT '身份证号',
	area_code varchar(100) NULL COMMENT '地域编码',
	area_name varchar(100) NULL COMMENT '地域名称',
	person_type varchar(100) NULL COMMENT '人员类型',
	first_appearance_time DATETIME NULL COMMENT '首次出现时间',
	CONSTRAINT tb_fx_influx_person_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;

CREATE TABLE IF NOT EXISTS dwd_fx_qslfry (
	id BIGINT auto_increment NOT NULL,
	xm varchar(100) NULL COMMENT '姓名',
	rybh varchar(100) NULL COMMENT '人员编号',
	sfzh varchar(100) NULL COMMENT '身份证号码',
	zzlb varchar(100) NULL COMMENT '组织类别',
	zt varchar(100) NULL COMMENT '状态',
	lkjb varchar(100) NULL COMMENT '级别',
	zkdw varchar(100) NULL COMMENT '主控单位',
	zkdw_area_code varchar(100) NULL COMMENT '主控单位地域代码',
	CONSTRAINT dwd_fx_qslfry_pk PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin;
