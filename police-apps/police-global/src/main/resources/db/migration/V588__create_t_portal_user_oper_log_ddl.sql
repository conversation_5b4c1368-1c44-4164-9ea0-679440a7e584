CREATE TABLE IF NOT EXISTS `t_portal_user_oper_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_name` varchar(255) DEFAULT NULL COMMENT '用户姓名',
  `user_id_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作用户身份证号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建人id',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人id',
  `create_dept_id` bigint DEFAULT NULL,
  `update_dept_id` bigint DEFAULT NULL,
	PRIMARY KEY (`id`) USING BTREE
);