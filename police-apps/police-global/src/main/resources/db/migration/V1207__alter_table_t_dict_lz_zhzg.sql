DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    -- 声明变量用于存储各级ID
    DECLARE
    rzxl_id INT; -- 任职序列ID
    DECLARE
    rzxl_one_id INT; -- 任职序列ID
    DECLARE
    zj_id INT; -- 职级ID
    DECLARE
    jwjs_zj_id INT; -- 警务技术职级ID
    -- 先删除旧数据
    DELETE
    FROM
      `t_dict`
    WHERE
      `type` LIKE 'police_rz_xl%'
      OR `type` LIKE 'police_zj%';
    -- ========== 任职序列 (police_rz_xl) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_rz_xl_group', 0, '任职序列', 0, '任职序列', 1, NULL, 1, NULL, 1);
    SET rzxl_one_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = rzxl_one_id
    WHERE
      `id` = rzxl_one_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (rzxl_one_id, 'police_rz_xl', 1, '执法勤务警员职务', 0, '执法勤务警员职务', 1, NULL, 3, NULL, 1),
    (rzxl_one_id, 'police_rz_xl', 12, '警务技术职务', 0, '警务技术职务', 2, NULL, 3, NULL, 1);

    -- ========== 职级 (police_zj) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_zj_group', 0, '职级', 0, '职级', 1, NULL, 1, NULL, 1);
    SET zj_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = zj_id
    WHERE
      `id` = zj_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (zj_id, 'police_zj', 1, '执法勤务警员职务', 0, '执法勤务警员职务', 1, NULL, 2, NULL, 1);
     SET rzxl_id = LAST_INSERT_ID();
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (rzxl_id, 'police_zj', 2, '一级高级警长', 1, '一级高级警长', 1, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 3, '二级高级警长', 1, '二级高级警长', 2, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 4, '三级高级警长', 1, '三级高级警长', 3, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 5, '四级高级警长', 1, '四级高级警长', 4, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 6, '一级警长', 1, '一级警长', 5, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 7, '二级警长', 1, '二级警长', 6, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 8, '三级警长', 1, '三级警长', 7, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 9, '四级警长', 1, '四级警长', 8, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 10, '一级警员', 1, '一级警员', 9, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 11, '二级警员', 1, '二级警员', 10, NULL, 3, NULL, 1);
    -- ========== 职级 (police_zj) ==========
    INSERT INTO `t_dict` (`p_id`,`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (zj_id, 'police_zj', 12, '警务技术职务', 0, '警务技术职务', 2, NULL, 2, NULL, 1);
    SET rzxl_id = LAST_INSERT_ID();
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (rzxl_id, 'police_zj', 13, '一级主任', 12, '一级主任', 1, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 14, '二级主任', 12, '二级主任', 2, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 15, '三级主任', 12, '三级主任', 3, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 16, '四级主任  ', 12, '四级主任', 4, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 17, '一级主管', 12, '一级主管', 5, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 18, '二级主管', 12, '二级主管', 6, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 19, '三级主管', 12, '三级主管', 7, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 20, '四级主管', 12, '四级主管', 8, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 21, '警务技术员', 12, '警务技术员', 9, NULL, 3, NULL, 1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;