DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_pm` $$
CREATE PROCEDURE insert_pm()
BEGIN
    IF NOT EXISTS(select * from t_profile_module where type='group' and cn_name='管控工作记录') THEN
        INSERT INTO t_profile_module (cn_name, en_name, `type`, pid, is_archive, show_order, table_schema, form_schema, list_schema, is_add, database_relation, show_schema_type, add_schema_type, is_operation_content, is_mobile_content, is_web_content, is_fk_content)
        VALUES('管控工作记录', 'groupWorkRecord', 'group', NULL, 1, 15, NULL, NULL, NULL,1, NULL, 'NO_SCHEMA', 'NO_SCHEMA', 1, 1, 1, 0);
    END IF;

END $$
DELIMITER ;
CALL insert_pm;
DROP PROCEDURE IF EXISTS `insert_pm`;