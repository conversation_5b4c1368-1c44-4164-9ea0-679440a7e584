package com.trs.police.intelligence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.intelligence.vo.GroupVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseEntityMapper
 *
 * @param <T> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 14:46
 * @since 1.0
 */
public interface BaseEntityMapper<T> extends BaseMapper<T> {

    /**
     * doPageSelect<BR>
     *
     * @param from              参数
     * @param user              参数
     * @param realAllDeptIds    参数
     * @param baseFilter        参数
     * @param relationTableName 参数
     * @param attributesFilter  参数
     * @param searchParams      参数
     * @param sortParams        参数
     * @param page              参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 14:33
     */
    Page<T> doPageSelect(
            @Param("from") String from,
            @Param("user") CurrentUser user,
            @Param("realAllDeptIds") List<Long> realAllDeptIds,
            @Param("baseFilter") List<KeyValueTypeVO> baseFilter,
            @Param("relationTableName") String relationTableName,
            @Param("attributesFilter") List<KeyValueTypeVO> attributesFilter,
            @Param("searchParams") SearchParams searchParams,
            @Param("sortParams") SortParams sortParams,
            Page<T> page
    );

    /**
     * getUnReadNum<BR>
     *
     * @param from 参数
     * @param user 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 23:39
     */
    List<GroupVo> getUnReadNum(
            @Param("from") String from,
            @Param("user") CurrentUser user
    );
}
