package com.trs.police.intelligence.mgr.validator;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.intelligence.mgr.BaseFieldValidator;
import org.springframework.stereotype.Component;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/26 19:24
 * @since 1.0
 */
@Component
public class FileFieldValidator extends BaseFieldValidator {

    /**
     * 合法<BR>
     *
     * @param value 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/26 14:04
     */
    @Override
    public boolean isValid(String value) {
        return JsonUtils.isValidArray(value);
    }

    /**
     * 空<BR>
     *
     * @param value 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/26 14:04
     */
    @Override
    public boolean isEmpty(String value) {
        if (StringUtils.isEmpty(value)) {
            return true;
        } else if (isNotValid(value)) {
            return true;
        }
        return JSON.parseArray(value).isEmpty();
    }

    @Override
    public String key() {
        return "file";
    }

}
