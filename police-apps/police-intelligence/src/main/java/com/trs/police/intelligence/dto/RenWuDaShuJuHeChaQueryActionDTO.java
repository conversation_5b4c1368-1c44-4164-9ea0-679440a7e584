package com.trs.police.intelligence.dto;


import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.intelligence.ChePaiVo;
import lombok.Data;
import lombok.ToString;

import java.util.Collections;
import java.util.List;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/24 18:57
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class RenWuDaShuJuHeChaQueryActionDTO extends RenWuDaShuJuHeChaActionDTO {

    private Integer pageSize = 10;

    private Integer pageNum = 1;

    private String zjhm;

    private String ajbh;

    private String hpxx;

    /**
     * makeChePaiList<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/1 11:54
     */
    public List<ChePaiVo> makeChePaiList() {
        if (!JsonUtils.isValidArray(getHpxx())) {
            return Collections.emptyList();
        }
        return JsonUtil.parseArray(getHpxx(), ChePaiVo.class);
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getZjhm(), new ParamInvalidException("证件号码不能为空"));
        return super.checkParams();
    }
}
