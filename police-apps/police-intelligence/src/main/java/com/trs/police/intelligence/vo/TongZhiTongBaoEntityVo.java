package com.trs.police.intelligence.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.police.intelligence.constant.Constants;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YaoQingEntityVo
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 14:41
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class TongZhiTongBaoEntityVo extends BaseIntelligenceEntityVo {

    private String noticeNo;

    private String noticeType;

    /**
     * 反馈超时 0：否 1：是
     */
    private Integer feedbackTimeout;

    /**
     * 签收超时 0：否 1：是
     */
    private Integer signTimeout;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pushTime;

    private String showName;

    /**
     * objType<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/11 11:28
     */
    @Override
    public String getObjType() {
        return Constants.TONGZHITONGBAO;
    }
}
