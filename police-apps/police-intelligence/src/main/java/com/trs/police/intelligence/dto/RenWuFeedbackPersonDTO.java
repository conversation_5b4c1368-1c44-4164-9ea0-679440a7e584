package com.trs.police.intelligence.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.intelligence.constant.ActionEnum;
import lombok.Data;
import lombok.ToString;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;
import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/9 18:27
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class RenWuFeedbackPersonDTO extends BaseActionDTO {

    private Long personId;

    private String dealUser;

    private String dealPhone;

    private String workStatus;

    private String currentLocationFlag;

    private String currentLocation;

    private Double currentLocationJd;

    private Double currentLocationWd;

    private String content;

    private Integer validFlag;

    private String partyDealUser;

    private String partyDealPhone;

    public RenWuFeedbackPersonDTO() {
        setActionEnum(ActionEnum.RENWU_FEEDBACK_PERSON);
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotNull(getPersonId(), new ParamInvalidException("待反馈的人员ID不能为空"));
        checkNotEmpty(getDealUser(), new ParamInvalidException("责任民警不能为空"));
        checkNotEmpty(getDealPhone(), new ParamInvalidException("联系电话不能为空"));
        checkNotEmpty(getWorkStatus(), new ParamInvalidException("工作状态不能为空"));
        checkNotEmpty(getCurrentLocationFlag(), new ParamInvalidException("目前所在标识不能为空"));
        checkNotEmpty(getContent(), new ParamInvalidException("内容填报不能为空"));
        checkNotNull(getValidFlag(), new ParamInvalidException("有效标识不能为空"));
        return super.checkParams();
    }
}
