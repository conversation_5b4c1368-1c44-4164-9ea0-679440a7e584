package com.trs.police.intelligence.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.common.utils.StringUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/13 19:25
 * @since 1.0
 */
@Data
public class RenWuAssignPersonMappingDTO extends BaseDTO {

    private Long mainDept;
    private Long leaderDept;
    private String otherDepts;
    private List<RenWuDealUserDTO> dealUsers = Collections.emptyList();

    /**
     * makeDeptIds<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/13 19:49
     */
    public Set<Long> makeDeptIds() {
        Set<Long> dept = new HashSet<>();
        if (Objects.nonNull(getMainDept())) {
            dept.add(getMainDept());
        }
        if (Objects.nonNull(getLeaderDept())) {
            dept.add(getLeaderDept());
        }
        dept.addAll(makeOtherDepts());
        return dept;
    }

    /**
     * makeOtherDepts<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/13 19:29
     */
    public List<Long> makeOtherDepts() {
        return Arrays.stream(StringUtils.showEmpty(getOtherDepts()).split(StringUtils.SEPARATOR_COMMA))
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        Boolean haveMain = getMainDept() != null;
        Boolean haveLeader = getLeaderDept() != null;
        var other = makeOtherDepts();
        Boolean haveOthers = CollectionUtils.isNotEmpty(other);
        checkArgument(haveMain || haveLeader || haveOthers, new ParamInvalidException("指派的目标单位不能为空"));
        if (haveLeader && haveMain) {
            checkArgument(!Objects.equals(getMainDept(), getLeaderDept()), new ParamInvalidException("牵头单位跟主责单位不能是同一个"));
        }
        if (haveOthers) {
            if (haveMain) {
                checkArgument(!other.contains(getMainDept()), new ParamInvalidException("其他单位中不能包含主责单位"));
            }
            if (haveLeader) {
                checkArgument(!other.contains(getLeaderDept()), new ParamInvalidException("其他单位中不能包含牵头单位"));
            }
        }
        if (CollectionUtils.isNotEmpty(getDealUsers())) {
            for (RenWuDealUserDTO user : getDealUsers()) {
                user.isValid();
            }
        }
        return true;
    }
}
