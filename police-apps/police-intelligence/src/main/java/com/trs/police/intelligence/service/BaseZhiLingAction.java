package com.trs.police.intelligence.service;

import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.intelligence.dto.BaseActionDTO;
import com.trs.police.intelligence.entity.ZhiLingBaseInfoEntity;
import com.trs.police.intelligence.mapper.ZhiLingBaseInfoMapper;
import com.trs.police.intelligence.service.impl.ZhiLingEntityServiceImpl;
import lombok.Getter;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * @param <T>    参数
 * @param <R>    参数
 * @param <OneR> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/17 20:52
 * @since 1.0
 */
public abstract class BaseZhiLingAction<T extends BaseActionDTO, R, OneR>
        extends BaseAction<T, R, OneR, ZhiLingBaseInfoEntity> {

    @Resource
    @Getter
    private ZhiLingBaseInfoMapper mapper;

    @Getter
    @Resource
    private ZhiLingEntityServiceImpl entityService;

    /**
     * findByIds<BR>
     *
     * @param ids 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 13:13
     */
    @Override
    protected List<ZhiLingBaseInfoEntity> findByIds(List<Long> ids) {
        return getMapper().findByIds(ids);
    }

    /**
     * operateModule<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/7/2 14:57
     */
    @Override
    public OperateModule operateModule() {
        return OperateModule.INTELLIGENCE_ZHILING;
    }
}
