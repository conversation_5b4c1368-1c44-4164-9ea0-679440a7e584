package com.trs.police.intelligence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * ZhilingBaseInfoEntity
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/17 16:19
 * @since 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("tb_intelligence_zhiling_base_info")
public class ZhiLingBaseInfoEntity extends BaseIntelligenceEntity {

    @TableField(exist = false)
    private String orderNo;

    @TableField(exist = false)
    private String orderType;

    @TableField(exist = false)
    private String orderLevel;

    /**
     * 盯办标识 0：否 1：是
     */
    @TableField
    private Integer watchFlag;

    /**
     * 发布时间
     */
    @TableField
    private Date pushTime;

    /**
     * 反馈时限
     */
    @TableField
    private Date feedbackTimeLimit;

    /**
     * 签收时限
     */
    @TableField
    private Date signTimeLimit;

    /**
     * 反馈超时 0：否 1：是
     */
    @TableField
    private Integer feedbackTimeout;

    /**
     * 签收超时 0：否 1：是
     */
    @TableField
    private Integer signTimeout;

    @TableField
    private String fieldsEditInfo;

    @TableField
    private String feedbackInfo;

    @TableField
    private String signInfo;

    @TableField
    private String acceptInfo;

    @TableField
    private Integer feedbackMessageFlag = 0;

    /**
     * 创建类型
     * 0：原发
     * 1：追加
     * 2：转发
     */
    @TableField
    private Integer createType = 0;

    @TableField
    private Long sourceYaoQingId;

    @TableField
    private Boolean notGenerateNo = false;

    @TableField
    private Integer topMarking = 0;

    @TableField
    private Date topTime;

    /**
     * 用户串
     */
    @TableField
    private String acceptUsers;

    /**
     * makeShowName<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/19 00:42
     */
    @Override
    public String makeShowName() {
        return "指令";
    }
}
