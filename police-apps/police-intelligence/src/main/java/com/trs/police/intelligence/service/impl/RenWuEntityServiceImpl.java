package com.trs.police.intelligence.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.BigScreenPinDataEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.KeyNameVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.intelligence.ChePaiVo;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.common.openfeign.starter.service.SearchService;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.*;
import com.trs.police.intelligence.dto.third.PullDataDTO;
import com.trs.police.intelligence.entity.*;
import com.trs.police.intelligence.mapper.RenWuBaseInfoMapper;
import com.trs.police.intelligence.mapper.RenWuRelatedPersonMapper;
import com.trs.police.intelligence.mapper.RenWuRelatedPersonMappingMapper;
import com.trs.police.intelligence.mgr.EntityConvertToVo;
import com.trs.police.intelligence.service.BaseAction;
import com.trs.police.intelligence.service.BaseIntelligenceNoVersionEntityService;
import com.trs.police.intelligence.utils.DeptUtils;
import com.trs.police.intelligence.utils.FieldUtils;
import com.trs.police.intelligence.utils.ProvinceClueHelper;
import com.trs.police.intelligence.utils.RenWuDaShuJuHeChaUtils;
import com.trs.police.intelligence.vo.*;
import com.trs.police.intelligence.vo.third.ProvinceCluePersonVO;
import com.trs.police.intelligence.vo.third.ProvinceClueVO;
import com.trs.police.intelligence.vo.third.PullDataVo;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotEmpty;
import static com.trs.common.utils.AreaCodeUtil.getAreaShortCode;
import static com.trs.common.utils.AreaCodeUtil.spreadingAreaCode;
import static com.trs.police.intelligence.utils.RenWuDaShuJuHeChaUtils.addInfo;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * ZhilingEntityServiceImpl
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/17 16:37
 * @since 1.0
 */
@Service
@Slf4j
public class RenWuEntityServiceImpl extends BaseIntelligenceNoVersionEntityService<RenWuBaseInfoEntity, RenWuVersionEntity, RenWuSaveDTO, RenWuEntityVo, RenWuEntityVo> {

    @Resource
    @Getter
    private RenWuBaseInfoMapper mapper;

    @Resource
    @Getter
    private RenWuRelatedPersonMappingMapper relatedPersonMappingMapper;

    @Resource
    @Getter
    private RenWuRelatedPersonMapper relatedPersonMapper;

    @Resource
    private ProfileService profileService;

    @Resource
    private SearchService searchService;

    @Autowired(required = false)
    protected ProvinceClueHelper clueHelper;

    @Override
    public RenWuVersionEntity findVersion(EntityDetailDTO dto, Long versionId) {
        RenWuVersionEntity version = new RenWuVersionEntity();
        version.setFrom(dto.getFrom());
        version.setIsDel(0);
        version.setYqDataId(dto.getDataId());
        version.setDataId(versionId);
        version.setStatusCode(versionId.intValue());
        var login = Optional.ofNullable(AuthHelper.getCurrentUser());
        if (login.isPresent()) {
            if (Objects.equals(Constants.TODO, dto.getFrom())
                    || Objects.equals(Constants.UP, dto.getFrom())) {
                getDataRelationMappingMgr().getRelationList(
                                module(),
                                dto.getDataId(),
                                Constants.DEPT,
                                Constants.PUSHDOWN
                        ).stream().filter(it -> Objects.equals(it.getObjId(), login.get().getDeptId()))
                        .findAny()
                        .ifPresent(it -> version.setStatusCode(it.getStatusCode()));
            }
        }
        return version;
    }

    @Override
    public RenWuBaseInfoEntity findById(Long dataId) throws ServiceException {
        RenWuBaseInfoEntity entity = getMapper().findById(dataId);
        checkArgument(
                Objects.nonNull(entity) && entity.getIsDel() == 0,
                new ServiceException(String.format("不存在ID=[%s]的[%s]对象", dataId, desc()))
        );
        return entity;
    }

    /**
     * getMaxNo<BR>
     *
     * @param date      参数
     * @param dataClass 参数
     * @param user      参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/29 17:51
     */
    public Integer getMaxNo(Date date, String dataClass, CurrentUser user) throws ServiceException {
        if (Objects.isNull(user)) {
            user = AuthHelper.getNotNullUser();
        }
        final String code = user.getDept().getDistrictCode();
        checkNotEmpty(code, new ServiceException("用户部门编码不能为空"));
        return new LambdaQueryChainWrapper<>(getMapper())
                .select(RenWuBaseInfoEntity::getDataNo)
                .eq(RenWuBaseInfoEntity::getIsDel, 0)
                .eq(RenWuBaseInfoEntity::getCrDistrictCode, code)
                .eq(RenWuBaseInfoEntity::getCrDate, TimeUtils.dateToString(date, TimeUtils.YYYYMMDD))
                .eq(RenWuBaseInfoEntity::getDataYear, TimeUtils.getFieldOfDate(date, Calendar.YEAR))
                .orderByDesc(RenWuBaseInfoEntity::getDataNo)
                .page(new Page<>(1, 1))
                .getRecords()
                .stream()
                .filter(Objects::nonNull)
                .map(RenWuBaseInfoEntity::getDataNo)
                .findFirst()
                .orElse(0);
    }

    @Override
    public Integer getMaxNo(String dataClass, CurrentUser user) throws ServiceException {
        return getMaxNo(new Date(), dataClass, user);
    }

    @Override
    public String getOrderNo(String dataClass, CurrentUser user) throws ServiceException {
        return getOrderNo(new Date(), dataClass, user);
    }

    /**
     * getOrderNo<BR>
     *
     * @param time      参数
     * @param dataClass 参数
     * @param user      参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/29 17:57
     */
    public String getOrderNo(Date time, String dataClass, CurrentUser user) throws ServiceException {
        if (Objects.isNull(user)) {
            user = AuthHelper.getNotNullUser();
        }
        final String code = StringUtils.showEmpty(user.getDept().getDistrictCode());
        String date = TimeUtils.dateToString(time, TimeUtils.YYYYMMDD5);
        int dataNo = getMaxNo(time, dataClass, user) + 1;
        StringBuilder dataNoStr = new StringBuilder(Integer.toString(dataNo));
        while (dataNoStr.length() < 5) {
            dataNoStr.insert(0, "0");
        }
        return String.format("WK%s%s%s", code, date, dataNoStr);
    }

    @Override
    public RenWuEntityVo mergeDataOnDetail(EntityDetailDTO dto, RenWuBaseInfoEntity entity, RenWuVersionEntity version)
            throws ServiceException {
        final List<Long> deptIds = new ArrayList<>(0);
        // 待接收的
        Optional.ofNullable(findAcceptDeptIds(entity, version))
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(deptIds::addAll);
        // 创建的
        Optional.ofNullable(entity.getCrDeptId())
                .filter(it -> !deptIds.contains(it))
                .ifPresent(deptIds::add);
        // 来源的
        Optional.ofNullable(entity.getSourceDeptId())
                .filter(it -> !deptIds.contains(it))
                .ifPresent(deptIds::add);
        // 相关人员的
        Optional.ofNullable(entity.getRelatedPersons())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(it -> {
                    for (RenWuRelatedPersonEntity person : it) {
                        if (Objects.nonNull(person.getMainDeptId())) {
                            deptIds.add(person.getMainDeptId());
                        }
                        if (Objects.nonNull(person.getLeaderDeptId())) {
                            deptIds.add(person.getLeaderDeptId());
                        }
                        deptIds.addAll(person.makeOtherDeptIds());
                    }
                });
        Map<Long, SimpleDeptVO> deptMapping = Optional.of(deptIds)
                .filter(CollectionUtils::isNotEmpty)
                .map(this::findDeptByIds)
                .map(it -> it.stream().collect(Collectors.toMap(
                        SimpleDeptVO::getDeptId,
                        r -> r,
                        (a, b) -> a
                ))).orElse(new HashMap<>(0));
        Map<String, UserDto> userMapping = new HashMap<>(1);
        findUserByName(entity.getCrUser()).ifPresent(it -> userMapping.put(it.getUsername(), it));
        final RenWuEntityVo vo = makeVo(entity, deptMapping, userMapping);
        // 补充反馈时限按小时单位返回
        vo.setFeedbackLimitHour(TimeUtils.getNumHour(TimeUtils.dateToString(entity.getCrTime(), TimeUtils.YYYYMMDD_HH),
                TimeUtils.dateToString(entity.getFeedbackLimitTime(), TimeUtils.YYYYMMDD_HH)));
        // 回填关联的群体信息
        vo.setGroupDetail(Optional.ofNullable(entity.getRelatedGroupId())
                .filter(it -> it > 0L)
                .map(it -> Try.of(() -> getCommonService().findGroupDetailById(it)).getOrNull())
                .filter(CollectionUtils::isNotEmpty)
                .map(it -> it.get(0))
                .orElse(null));
        // 回填前端传入的状态码
        Optional.ofNullable(version)
                .map(RenWuVersionEntity::getStatusCode)
                .filter(it -> it > 0)
                .ifPresent(vo::setStatusCode);
        if (Objects.nonNull(version)) {
            if (Objects.equals(Constants.UP, version.getFrom())
                    || Objects.equals(Constants.TODO, version.getFrom())
                    || Objects.equals(Constants.PROCESSED, version.getFrom())) {
                final CurrentUser user = AuthHelper.getNotNullUser();
                vo.setRelatedPersons(
                        Optional.ofNullable(vo.getRelatedPersons())
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(it -> it.isAssignToUser(user))
                                .collect(Collectors.toList())
                );
            }
        }
        final List<Object> hjdDmList = Optional.ofNullable(vo.getRelatedPersons())
                .orElse(Collections.emptyList()).stream()
                .map(RenWuRelatedPersonVo::getHjdDm)
                .filter(StringUtils::isNotEmpty)
                .map(a -> (Object) a)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hjdDmList)) {
            final List<DistrictDto> districtDtoList = getDictService().getByDistrictCodes(hjdDmList);
            if (CollectionUtils.isNotEmpty(districtDtoList)) {
                final Map<String, String> map = districtDtoList.stream()
                        .collect(Collectors.toMap(DistrictDto::getCode, DistrictDto::getShortName, (a, b) -> a));
                vo.getRelatedPersons().forEach(a -> a.setHjdJc(map.get(a.getHjdDm())));
            }
        }
        return vo;
    }

    /**
     * check<BR>
     *
     * @param isAdd       参数
     * @param templatesVo 参数
     * @param saveDTO     参数
     * @param attributes  参数
     * @param entity      参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 02:01
     */
    @Override
    public void check(Boolean isAdd, AttributeTemplatesVo templatesVo, RenWuSaveDTO saveDTO, List<DataAttributesDTO> attributes, RenWuBaseInfoEntity entity) throws ServiceException {
        if (!isAdd) {
            boolean isDrafts = Optional.ofNullable(entity.getDraftsFlag()).filter(it -> it == 1).isPresent();
            checkArgument(
                    isDrafts,
                    new ParamInvalidException("草稿或不采用的数据才能进行编辑")
            );
        }
    }

    /**
     * saveOrUpdate<BR>
     *
     * @param saveDTO 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 22:39
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Report<String> saveOrUpdate(RenWuSaveDTO saveDTO, CurrentUser user) throws ServiceException {
        saveDTO.isValid();
        RenWuBaseInfoEntity entity;
        boolean isAdd = false;
        if (saveDTO.getDataId() != null && saveDTO.getDataId() > 0L) {
            entity = findById(saveDTO.getDataId());
            checkArgument(
                    entity.userCreateEntity(Optional.of(user)),
                    new ServiceException("暂不支持处理他人发布的" + desc())
            );
        } else {
            entity = new RenWuBaseInfoEntity();
            isAdd = true;
        }
        List<RenWuRelatedPersonSaveDTO> persons = saveDTO.makeRelatedPerson();
        check(isAdd, null, saveDTO, null, entity);
        mergeOnSave(user, isAdd, saveDTO, persons, entity);
        entity.setVersionId(0L);
        if (entity.getDataId() != null && entity.getDataId() > 0L) {
            getMapper().updateById(entity);
        } else {
            getMapper().insert(entity);
        }
        // 回填rootId
        if (Optional.ofNullable(entity.getRootId()).filter(it -> it > 0L).isEmpty()) {
            entity.setRootId(entity.getDataId());
            getMapper().updateById(entity);
        }
        rebuildRelatedPersonMapping(saveDTO.getSplitTaskFlag(), user, entity.getDataId(), persons);
        return new Report<>("保存线索", "成功进行保存", RESULT.SUCCESS, entity.getDataId());
    }

    /**
     * rebuildRelatedPersonMapping<BR>
     *
     * @param isSplit 参数
     * @param user    参数
     * @param taskId  参数
     * @param persons 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/8 13:49
     */
    @Transactional(rollbackFor = Throwable.class)
    public void rebuildRelatedPersonMapping(Boolean isSplit, CurrentUser user, Long taskId, List<RenWuRelatedPersonSaveDTO> persons) throws ServiceException {
        // 待存储的用户ID
        final var savePersonIds = persons
                .stream()
                .map(RenWuRelatedPersonSaveDTO::getDataId)
                .filter(it -> it != null && it > 0L)
                .collect(Collectors.toSet());
        // 已存在的关联关系
        final var existsMappings = new LambdaQueryChainWrapper<>(
                getRelatedPersonMappingMapper())
                .eq(RenWuRelatedPersonMappingEntity::getIsDel, 0)
                .eq(RenWuRelatedPersonMappingEntity::getTaskId, taskId)
                .list();
        // 存在关联关系的人员ID
        final var existsPersonIds = existsMappings
                .stream()
                .map(RenWuRelatedPersonMappingEntity::getPersonId)
                .collect(Collectors.toSet());

        if (!existsMappings.isEmpty()) {
            var mappingIds = existsMappings
                    .stream()
                    .map(RenWuRelatedPersonMappingEntity::getDataId)
                    .collect(Collectors.toSet());
            // 标记删除（如果待存储的用户ID中不存在，删除关联关系）
            new LambdaUpdateChainWrapper<>(getRelatedPersonMappingMapper())
                    .in(RenWuRelatedPersonMappingEntity::getDataId, mappingIds)
                    .notIn(CollectionUtils.isNotEmpty(savePersonIds), RenWuRelatedPersonMappingEntity::getPersonId, savePersonIds)
                    .ne(RenWuRelatedPersonMappingEntity::getIsDel, 1)
                    .set(RenWuRelatedPersonMappingEntity::getIsDel, 1)
                    .update();
            // 标记删除（如果待存储的用户ID中不存在，删除本身）
            new LambdaUpdateChainWrapper<>(getRelatedPersonMapper())
                    .in(RenWuRelatedPersonEntity::getDataId, existsPersonIds)
                    .notIn(CollectionUtils.isNotEmpty(savePersonIds), RenWuRelatedPersonEntity::getDataId, savePersonIds)
                    .ne(RenWuRelatedPersonEntity::getIsDel, 1)
                    .set(RenWuRelatedPersonEntity::getIsDel, 1)
                    .update();
        }
        for (RenWuRelatedPersonSaveDTO person : persons) {
            // 新增的数据
            if (person.getDataId() == null || person.getDataId() <= 0L) {
                RenWuRelatedPersonEntity save = RenWuRelatedPersonEntity
                        .addInfoOnCreate(
                                user,
                                EntityConvertToVo.INSTANCE.dtoToEntity(person)
                        );
                //XMKFB-2250 创建任务后 (相关人员)默认工作状态是”未指派“状态
                save.setWorkStatus(Constants.RELATION_PERSON_WORK_STATUS_WZP);
                getRelatedPersonMapper().insert(save);
                person.setDataId(save.getDataId());
            } else {
                new LambdaUpdateChainWrapper<>(getRelatedPersonMapper())
                        // 拆分时需要标记人员已拆分
                        .set(isSplit, RenWuRelatedPersonEntity::getSplitFlag, 1)
                        .set(RenWuRelatedPersonEntity::getZjhm, person.getZjhm())
                        .set(RenWuRelatedPersonEntity::getXm, person.getXm())
                        .set(RenWuRelatedPersonEntity::getSjh, person.getSjh())
                        .set(RenWuRelatedPersonEntity::getHjd, person.getHjd())
                        .set(RenWuRelatedPersonEntity::getHjdDm, person.getHjdDm())
                        .set(RenWuRelatedPersonEntity::getHjdjwd, person.getHjdjwd())
                        .set(RenWuRelatedPersonEntity::getXzd, person.getXzd())
                        .set(RenWuRelatedPersonEntity::getXzdjwd, person.getXzdjwd())
                        .set(RenWuRelatedPersonEntity::getSsd, person.getSsd())
                        .set(RenWuRelatedPersonEntity::getSsdjwd, person.getSsdjwd())
                        .set(RenWuRelatedPersonEntity::getWorkStatus, person.getWorkStatus())
                        .eq(RenWuRelatedPersonEntity::getDataId, person.getDataId())
                        .update();
            }
            // 不存在的就补充关联关系
            if (!existsPersonIds.contains(person.getDataId())) {
                RenWuRelatedPersonMappingEntity mapping = RenWuRelatedPersonEntity
                        .addInfoOnCreate(user, new RenWuRelatedPersonMappingEntity());
                mapping.setPersonId(person.getDataId());
                mapping.setZjhm(person.getZjhm());
                mapping.setTaskId(taskId);
                getRelatedPersonMappingMapper().insert(mapping);
            }
        }
    }

    /**
     * queryList<BR>
     *
     * @param from    参数
     * @param request 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 23:02
     */
    @Override
    public RestfulResultsV2<RenWuEntityVo> queryList(String from, ListParamsRequest request)
            throws ServiceException {
        final CurrentUser user = AuthHelper.getNotNullUser();
        List<KeyValueTypeVO> baseFilter = request.getFilterParams()
                .stream()
                .filter(it -> !it.getKey().startsWith(Constants.ATTRIBUTES_DOT))
                .peek(it -> {
                    if ("targetTime".equals(StringUtils.showEmpty(it.getKey()))) {
                        it.setKey("target_time_format");
                    }
                }).map(FieldUtils::reBuildFilter)
                .collect(Collectors.toList());
        Page<RenWuBaseInfoEntity> page = getMapper()
                .doPageSelect(
                        from,
                        user,
                        baseFilter,
                        request.getSearchParams(),
                        request.getSortParams(),
                        request.getPageParams().toPage()
                );
        List<Long> deptIds = new ArrayList<>();
        List<String> userNames = new ArrayList<>();
        List<Long> dataIds = new ArrayList<>();
        final List<Long> relatedGroupIds = new ArrayList<>();
        page.getRecords()
                .forEach(it -> {
                    Optional.ofNullable(it.makeAcceptDeptIds())
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(deptIds::addAll);
                    if (Objects.nonNull(it.getSourceDeptId())) {
                        deptIds.add(it.getSourceDeptId());
                    }
                    deptIds.add(it.getCrDeptId());
                    if (Objects.nonNull(it.getRelatedGroupId())) {
                        relatedGroupIds.add(it.getRelatedGroupId());
                    }
                    dataIds.add(it.getDataId());
                    userNames.add(it.getCrUser());
                    // 相关人员的
                    Optional.ofNullable(it.getRelatedPersons())
                            .filter(CollectionUtils::isNotEmpty)
                            .ifPresent(r -> {
                                for (RenWuRelatedPersonEntity person : r) {
                                    if (Objects.nonNull(person.getMainDeptId())) {
                                        deptIds.add(person.getMainDeptId());
                                    }
                                    if (Objects.nonNull(person.getLeaderDeptId())) {
                                        deptIds.add(person.getLeaderDeptId());
                                    }
                                    deptIds.addAll(person.makeOtherDeptIds());
                                }
                            });
                });
        Map<Long, SimpleDeptVO> deptMapping = CollectionUtils.isEmpty(deptIds) ? Collections.EMPTY_MAP
                : findDeptByIds(deptIds)
                .stream()
                .collect(Collectors.toMap(SimpleDeptVO::getDeptId, it -> it));
        Map<String, UserDto> userMapping = CollectionUtils.isEmpty(userNames) ? Collections.EMPTY_MAP
                : findUserByNames(userNames)
                .stream()
                .collect(Collectors.toMap(UserDto::getUsername, it -> it));
        Map<Long, CollectionEntity> collectIds =
                CollectionUtils.isEmpty(dataIds) ? Collections.EMPTY_MAP : getCollectionMapper()
                        .findCollectionEntityObjId(
                                Constants.USER,
                                user.getId(),
                                key(),
                                dataIds
                        ).stream().collect(Collectors.toMap(CollectionEntity::getObjId, it -> it, (a, b) -> a));
        final Map<Long, BigScreenPinDataEntity> pinDataMapping;
        if (Objects.equals(Constants.BIG_SCREEN, from)) {
            pinDataMapping = findPinData(dataIds, user);
        } else {
            pinDataMapping = Collections.EMPTY_MAP;
        }
        return RestfulResultsV2.ok(
                        page.getRecords()
                                .stream()
                                .map(it -> {
                                    var vo = makeVo(it, deptMapping, userMapping);
                                    vo.setCollectionFlag(0);
                                    vo.setFeedbackLimitHour(TimeUtils.getNumHour(TimeUtils.dateToString(it.getCrTime(), TimeUtils.YYYYMMDD_HH),
                                            TimeUtils.dateToString(it.getFeedbackLimitTime(), TimeUtils.YYYYMMDD_HH)));
                                    Optional.ofNullable(collectIds.get(it.getDataId()))
                                            .ifPresent(col -> {
                                                vo.setCollectionFlag(1);
                                                vo.setCollectionTitle(col.getCollectionTitle());
                                            });
                                    if (Objects.equals(Constants.BIG_SCREEN, from)) {
                                        BigScreenPinDataEntity pinData = pinDataMapping.get(it.getDataId());
                                        if (Objects.nonNull(pinData)) {
                                            vo.setShowName(StringUtils.showEmpty(pinData.getShowTitle(), it.getDataTitle()));
                                            vo.setIsAddScreen(Optional.ofNullable(pinData.getPinFlag()).orElse(0));
                                        } else {
                                            vo.setShowName(it.getDataTitle());
                                            vo.setIsAddScreen(0);
                                        }
                                    }
                                    return vo;
                                })
                                .collect(Collectors.toList())
                ).addPageNum(request.getPageParams().getPageNumber())
                .addPageSize(request.getPageParams().getPageSize())
                .addTotalCount(page.getTotal());
    }

    /**
     * mergeOnSave<BR>
     *
     * @param user    参数
     * @param isAdd   参数
     * @param saveDTO 参数
     * @param persons 参数
     * @param entity  参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/12 02:14
     */
    private void mergeOnSave(CurrentUser user, Boolean isAdd, RenWuSaveDTO saveDTO, List<RenWuRelatedPersonSaveDTO> persons, RenWuBaseInfoEntity entity) throws ServiceException {
        if (isAdd) {
            RenWuBaseInfoEntity.addInfoOnCreate(user, entity);
            if (TimeUtils.isValid(saveDTO.getXssj())) {
                entity.setCrTime(TimeUtils.stringToDate(saveDTO.getXssj()));
            } else {
                entity.setCrTime(new Date());
            }
            entity.setSystemCrTime(new Date());
            entity.setSyncTaskId(saveDTO.getSyncTaskId());
            entity.setChatId(saveDTO.getChatId());
            entity.setCrUserTrueName(StringUtils.showEmpty(user.getRealName(), user.getUsername()));
            entity.setRootId(Optional.ofNullable(saveDTO.getRootId()).orElse(0L));
            entity.setDataType(saveDTO.getDataType());
            entity.setDataClass(saveDTO.getDataClass());
            entity.setDataNo(getMaxNo(entity.getCrTime(), saveDTO.getDataClass(), user) + 1);
            entity.setTaskNo(getOrderNo(entity.getCrTime(), saveDTO.getDataClass(), user));
            entity.setDataYear(TimeUtils.getFieldOfDate(new Date(), Calendar.YEAR));
            entity.setCrDistrictCode(user.getDept().getDistrictCode());
            entity.setCrDate(TimeUtils.stringToDate(TimeUtils.dateToString(entity.getCrTime(), TimeUtils.YYYYMMDD)));
            entity.setDraftsFlag(0);
            entity.setStatusCode(Status.JINXINGZHONG.getCode());
            entity.setStxsbh(saveDTO.getStxsbh());
        }
        Optional.ofNullable(saveDTO.getCrDeptId()).ifPresent(entity::setCrDeptId);
        entity.setTaskSource(saveDTO.getTaskSource());
        entity.setSourceDeptId(saveDTO.getSourceDeptId());
        entity.setRelatedGroupId(saveDTO.getRelatedGroupId());
        entity.setClueNo(saveDTO.getClueNo());
        entity.setSourceTaskNo(saveDTO.getSourceTaskNo());
        entity.setDataTitle(saveDTO.getDataTitle());
        entity.setDataContent(saveDTO.getDataContent());
        entity.setClassicalFlag(Optional.ofNullable(saveDTO.getClassicalFlag()).orElse(0));
        entity.setTargetTime(saveDTO.getTargetTime());
        if (StringUtils.isNotEmpty(saveDTO.getTargetTimeFormat())) {
            entity.setTargetTimeFormat(TimeUtils.stringToDate(saveDTO.getTargetTimeFormat()));
        } else {
            if (Constants.CN_JIN_QI.equals(saveDTO.getTargetTime())) {
                entity.setTargetTimeFormat(entity.getCrDate());
            } else {
                entity.setTargetTimeFormat(TimeUtils.stringToDate(saveDTO.getTargetTime()));
            }
        }
        entity.setZxdd(saveDTO.getZxdd());
        entity.setXxdd(saveDTO.getXxdd());
        if (TimeUtils.isValid(saveDTO.getFeedbackLimitTime())) {
            entity.setFeedbackLimitTime(TimeUtils.stringToDate(saveDTO.getFeedbackLimitTime()));
        } else {
            entity.setFeedbackLimitTime(null);
        }
        if (TimeUtils.isValid(saveDTO.getSignTimeLimit())) {
            entity.setSignTimeLimit(TimeUtils.stringToDate(saveDTO.getSignTimeLimit()));
        } else {
            entity.setSignTimeLimit(null);
        }
        entity.setRelatedPersonZjhm(persons.stream()
                .map(RenWuRelatedPersonSaveDTO::getZjhm)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA)));
    }

    /**
     * export<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/22 10:58
     */
    @Override
    public Tuple2<String, String> export(ExportDTO dto) throws Exception {
        throw new ServiceException("待实现");
    }

    /**
     * makeVo<BR>
     *
     * @param it          参数
     * @param deptMapping 参数
     * @param userMapping 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/14 21:42
     */
    public RenWuEntityVo makeVo(RenWuBaseInfoEntity it, Map<Long, SimpleDeptVO> deptMapping, Map<String, UserDto> userMapping) {
        RenWuEntityVo vo = makeVo(it, EntityConvertToVo.INSTANCE.entityToVo(it), deptMapping, userMapping);
        vo.setSourceDept(deptMapping.get(it.getSourceDeptId()));
        if (CollectionUtils.isNotEmpty(it.getRelatedPersons())) {
            vo.setRelatedPersons(
                    it.getRelatedPersons().stream()
                            .map(p -> {
                                var pv = EntityConvertToVo.INSTANCE.entityToVo(p);
                                if (Objects.nonNull(p.getMainDeptId())) {
                                    pv.setMainDept(deptMapping.get(p.getMainDeptId()));
                                }
                                if (Objects.nonNull(p.getLeaderDeptId())) {
                                    pv.setLeaderDept(deptMapping.get(p.getLeaderDeptId()));
                                }
                                pv.setOtherDepts(
                                        p.makeOtherDeptIds()
                                                .stream()
                                                .map(deptMapping::get)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList())
                                );
                                pv.setDealUserList(
                                        p.makeDealUserList()
                                                .stream()
                                                .map(r -> getPermissionService().findSimpleUser(r.getUserId(), r.getDeptId()))
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList())
                                );
                                return pv;
                            }).collect(Collectors.toList())
            );
        }
        return addCommonInfo(it, vo);
    }

    @Override
    public Optional<Long> consumerThirdData(PullDataVo pullDataVo, CurrentUser cityUser, CurrentUser provinceUser) throws ServiceException {
        // 任务中进行消费
        var opt = consumerClueFor7834(pullDataVo, cityUser, provinceUser);
        if (opt.isPresent()) {
            var list = getDataRelationMappingMgr().getRelationList(
                    module(),
                    opt.get(),
                    Constants.DEPT,
                    Constants.PUSHDOWN
            );
            final boolean flag = list.stream().anyMatch(it ->
                    // 指派给了创建人且状态为待签收
                    Objects.equals(it.getObjId(), cityUser.getDeptId())
                            && Objects.equals(it.getStatusCode(), Status.DAIQIANSHOU.getCode())
            );
            // 说明还没签收
            if (flag) {
                DataActionDTO dto = new DataActionDTO();
                dto.setUser(cityUser);
                dto.setDataId(opt.get());
                dto.setActionEnum(ActionEnum.RENWU_SIGN_DATA);
                // 签收数据
                BaseAction.findAction(dto.getActionEnum()).doAction(dto);
            }
        }
        return opt;
    }

    @Override
    public void finishedConsumerThirdData(PullDataDTO dto, CurrentUser cityUser, CurrentUser provinceUser) throws ServiceException {
        final var depts = JsonUtil.parseArray(
                BeanFactoryHolder.getEnv().getProperty("intelligence.province.clue.needAssignPerson.depts", "[]"),
                KeyNameVO.class
        );
        if (CollectionUtils.isNotEmpty(depts)) {
            for (KeyNameVO dept : depts) {
                if (StringUtils.isEmpty(dept.getKey())) {
                    log.warn("[{}]单位[{}]没有设置单位代码，跳过指派人员", dto.getTaskId(), dept);
                    continue;
                }
                if (!StringUtils.isLong(dept.getName())) {
                    log.warn("[{}]单位[{}]没有设置单位ID，跳过指派人员", dto.getTaskId(), dept);
                    continue;
                }
                try {
                    // 先休眠61秒，避免接口超限
                    Thread.sleep(61 * 1000L);
                    PullDataDTO personDto = EntityConvertToVo.INSTANCE.clone(dto);
                    personDto.setSjdw(dept.getKey());
                    final Map<String, List<ProvinceCluePersonVO>> xsbhPersonMap = clueHelper.getCluePersonList(personDto)
                            .stream()
                            .collect(Collectors.groupingBy(ProvinceCluePersonVO::getXsbh));
                    // 循环判断
                    xsbhPersonMap.forEach((k, v) -> {
                        Set<String> zjhm = v.stream()
                                .map(ProvinceCluePersonVO::getRySfz)
                                .collect(Collectors.toSet());
                        if (CollectionUtils.isEmpty(zjhm)) {
                            log.warn("[{}]单位[{}]对线索数据[{}]中的人员为空", dto.getTaskId(), dept, k);
                            return;
                        }
                        Try.run(() -> {
                            log.info("[{}]单位[{}]对线索数据[{}]中的人员[{}]进行指派", dto.getTaskId(), dept, k, String.join(StringUtils.SEPARATOR_COMMA, zjhm));
                            RenWuBaseInfoEntity entity = findRenWuByStXsBh(k);
                            if (Objects.isNull(entity)) {
                                log.warn("[{}]线索数据[{}]还未回流", dto.getTaskId(), k);
                                return;
                            }
                            final var personList = getRelatedPersonMapper().relatedPersonList(entity.getDataId());
                            assignPerson(
                                    entity.getDataId(),
                                    cityUser,
                                    Long.valueOf(dept.getName()),
                                    personList.stream().filter(it -> zjhm.contains(it.getZjhm()))
                                            .map(RenWuRelatedPersonEntity::getDataId)
                                            .collect(Collectors.toSet())
                            );
                        }).onFailure(e -> log.error(
                                "[{}]单位[{}]对线索数据[{}]中的人员[{}]进行指派出错！ERR=[{}]",
                                dto.getTaskId(),
                                dept,
                                k,
                                String.join(StringUtils.SEPARATOR_COMMA, zjhm),
                                e.getMessage(),
                                e
                        ));
                    });
                } catch (Exception e) {
                    log.error("[{}]单位[{}]判断人员指派行为出错！ERR=[{}]", dto.getTaskId(), dept, e.getMessage(), e);
                }
            }
        } else {
            log.warn("[{}]同步数据到模块：{}，没有需要进行人员指派的单位信息", dto.getTaskId(), module());
        }
    }

    private RenWuSaveDTO buildSaveDto(ProvinceClueVO clue, List<ProvinceCluePersonVO> personVOList, CurrentUser provinceUser) throws ServiceException {
        RenWuSaveDTO dto = new RenWuSaveDTO();
        final Date time;
        if (TimeUtils.isValid(clue.getXssj())) {
            time = TimeUtils.stringToDate(clue.getXssj());
        } else {
            time = new Date();
        }
        dto.setSimpleCheck(true);
        dto.setUser(provinceUser);
        dto.setStxsbh(clue.getXsbh());
        dto.setTaskSource("省公安厅");
        dto.setSourceDeptId(provinceUser.getDeptId());
        Optional.ofNullable(findGroupId(clue, provinceUser))
                .ifPresent(dto::setRelatedGroupId);
        if (TimeUtils.isValid(clue.getZxsj())) {
            dto.setTargetTime(TimeUtils.stringToString(clue.getZxsj(), TimeUtils.YYYYMMDD));
        } else {
            dto.setTargetTime(StringUtils.showEmpty(clue.getZxsj(), Constants.CN_JIN_QI));
        }
        dto.setTargetTimeFormat(clue.getZxsj());
        dto.setTaskNo(getOrderNo(time, Constants.DEFAULT, provinceUser));
        dto.setDataNo(getMaxNo(time, Constants.DEFAULT, provinceUser) + 1);
        dto.setDataTitle(clue.getXsbt());
        dto.setDataContent(clue.getNr());
        //指向地点
        dto.setZxdd(clue.getZxddMc());
        dto.setXxdd(clue.getZxdXxddMc());
        dto.setXssj(clue.getXssj());
        dto.setSignTimeLimit(TimeUtils.dateToString(getAfterDateBySecond(time, 90), TimeUtils.YYYYMMDD_HHMMSS));
        dto.setFeedbackLimitTime(TimeUtils.dateToString(getAfterDateBySecond(time, 3600), TimeUtils.YYYYMMDD_HHMMSS));
        dto.setRelatedPerson(JsonUtil.toJsonString(makePersons(dto.getRelatedGroupId(), provinceUser, personVOList)));
        return dto;
    }

    private Long findGroupId(ProvinceClueVO clue, CurrentUser provinceUser) {
        // XMKFB-8290 配置项控制是否自动补缺群体 默认为否
        Boolean autoBl = BeanFactoryHolder.getEnv()
                .getProperty("intelligence.province.clue.auto.add.group", Boolean.class, false);
        if (!autoBl) {
            log.info("回流时默认不做自动创建和绑定群体!");
            return null;
        }
        // 涉及群体（在本系统中不存在的群体 需要先在群体档案中新增）
        if (StringUtils.isNotEmpty(clue.getQtlxMc())) {
            Long groupId = profileService.queryByGroupName(clue.getQtlxMc());
            if (Objects.nonNull(groupId)) {
                return groupId;
            }
            //设置特定用户
            Long relatedGroupId = profileService.saveGroupOrPerson(clue.getQtlxMc(), provinceUser.getId(), provinceUser.getDept().getId(), null);
            if (Objects.nonNull(relatedGroupId)) {
                return relatedGroupId;
            } else {
                log.error("新增群体档案[name = '{}']失败，请联系管理员！", clue.getQtlxMc());
            }
        } else {
            log.warn("省厅线索编号={}，群体类型名称为空，请检查！", clue.getXsbh());
        }
        return null;
    }

    private RenWuBaseInfoEntity findRenWuByStXsBh(String xsbh) {
        return mapper.selectList(new QueryWrapper<RenWuBaseInfoEntity>()
                .lambda()
                .eq(RenWuBaseInfoEntity::getIsDel, 0)
                .eq(RenWuBaseInfoEntity::getStxsbh, xsbh)
        ).stream().findFirst().orElse(null);
    }

    /**
     * <a href="http://10.18.20.137:8082/browse/XMKFB-7834">XMKFB-7834</a><BR>
     *
     * @param pullDataVo   参数
     * @param provinceUser 参数
     * @param cityUser     参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/4/21 10:57
     */
    public Optional<Long> consumerClueFor7834(PullDataVo pullDataVo, CurrentUser cityUser, CurrentUser provinceUser) throws ServiceException {
        // 没有相关人员的线索没有意义  直接过滤掉了
        if (CollectionUtils.isEmpty(pullDataVo.getPersonVOList())) {
            log.warn("[{}]ST线索[{}]没有关联人员", pullDataVo.getTaskId(), pullDataVo.getClueVO().getXsbh());
            return Optional.empty();
        }
        final ProvinceClueVO clueVO = pullDataVo.getClueVO();
        final List<ProvinceCluePersonVO> provinceCluePersonVOList = pullDataVo.getPersonVOList();
        //过滤掉指定地域上报数据
        List<String> codes = StringUtils.getList(
                BeanFactoryHolder.getEnv().getProperty("intelligence.st.renwu.filter.codes"),
                StringUtils.SEPARATOR_COMMA,
                true,
                true
        );
        // 过滤掉合并线索为空且上报单位为5106开头的数据
        if (CollectionUtils.isNotEmpty(codes)
                && codes.stream().anyMatch(it -> StringUtils.showEmpty(clueVO.getSbdwDm()).startsWith(it))
                && CollectionUtils.isEmpty(clueVO.getHbqXs())
        ) {
            log.warn(
                    "[{}]ST线索[{}]跳过消费，因为合并线索为空且上报单位为[{}]开头的数据",
                    pullDataVo.getTaskId(),
                    pullDataVo.getClueVO().getXsbh(),
                    String.join(StringUtils.SEPARATOR_COMMA, codes)
            );
            return Optional.empty();
        }
        RenWuBaseInfoEntity entity = findRenWuByStXsBh(clueVO.getXsbh());
        if (Objects.isNull(entity)) {
            RenWuSaveDTO save = buildSaveDto(clueVO, provinceCluePersonVOList, provinceUser);
            save.setSyncTaskId(pullDataVo.getTaskId());
            // 保存数据
            Report<String> report = saveOrUpdate(save);
            if (report.getResult() != RESULT.SUCCESS) {
                throw new ServiceException(report.getDetail());
            }
            var opt = Optional.ofNullable(report.getContext())
                    .map(Long.class::cast);
            entity = findById(opt.orElseThrow(() -> new ServiceException(String.format(
                    "[%s]ST线索[%s]保存失败",
                    pullDataVo.getTaskId(),
                    clueVO.getXsbh()
            ))));
        }
        // 补充新回流的相关人员
        saveOrUpdateRelatedPerson(entity, pullDataVo.getPersonVOList(), provinceUser, cityUser);
        return Optional.ofNullable(entity.getDataId());
    }

    /**
     * getAfterDateBySecond<BR>
     *
     * @param date    参数
     * @param seconds 参数
     * @return 结果
     */
    private Date getAfterDateBySecond(Date date, Integer seconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, seconds);
        return calendar.getTime();
    }

    /**
     * saveOrUpdateRelatedPerson<BR>
     *
     * @param data         参数
     * @param personVOList 参数
     * @param provinceUser 参数
     * @param cityUser     参数
     * @throws ServiceException 异常
     */
    private void saveOrUpdateRelatedPerson(
            RenWuBaseInfoEntity data,
            List<ProvinceCluePersonVO> personVOList,
            CurrentUser provinceUser,
            CurrentUser cityUser
    ) throws ServiceException {
        final List<RenWuRelatedPersonSaveDTO> persons = new ArrayList<>(0);
        // ST回流的人员
        final List<RenWuRelatedPersonSaveDTO> stPersons = makePersons(data.getRelatedGroupId(), provinceUser, personVOList);
        // 目前已存在人员
        final var personList = getRelatedPersonMapper().relatedPersonList(data.getDataId())
                .stream()
                .collect(Collectors.toMap(RenWuRelatedPersonEntity::getZjhm, EntityConvertToVo.INSTANCE::entityToDto));
        persons.addAll(personList.values());
        for (RenWuRelatedPersonSaveDTO person : stPersons) {
            if (!personList.containsKey(person.getZjhm())) {
                persons.add(person);
            }
        }
        if (CollectionUtils.isNotEmpty(persons)) {
            //相关人员
            rebuildRelatedPersonMapping(false, provinceUser, data.getDataId(), persons);
            //指派到市级情指部门
            assignPerson(
                    data.getDataId(),
                    cityUser,
                    cityUser.getDeptId(),
                    persons.stream()
                            .map(RenWuRelatedPersonSaveDTO::getDataId)
                            .collect(Collectors.toList())
            );
            //回填相关人员的证件号码
            Set<String> idCards = persons
                    .stream()
                    .map(RenWuRelatedPersonSaveDTO::getZjhm)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());
            idCards.addAll(StringUtils.getList(data.getRelatedPersonZjhm()));
            data.setRelatedPersonZjhm(String.join(StringUtils.SEPARATOR_COMMA, idCards));
            getMapper().updateById(data);
        }
    }

    /**
     * parseHjdDm<BR>
     *
     * @param hjdDm 参数
     * @param hjdMc 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/5/8 15:08
     */
    private String parseHjdDm(String hjdDm, String hjdMc) throws ServiceException {
        if (StringUtils.isNotEmpty(hjdDm) || StringUtils.isEmpty(hjdMc)) {
            return hjdDm;
        }
        return getCommonService().parseContentForArea(CommonContentDTO.builder().content(hjdMc).build())
                .stream()
                .map(AreaVO::getAreaCode)
                .filter(StringUtils::isNotEmpty)
                .findFirst()
                .map(i -> i.substring(0, 6))
                .orElse(hjdDm);
    }

    @NotNull
    private List<RenWuRelatedPersonSaveDTO> makePersons(Long relatedGroupId, CurrentUser user, List<ProvinceCluePersonVO> relatedPerson) throws ServiceException {
        List<RenWuRelatedPersonSaveDTO> persons = new ArrayList<>(0);
        for (ProvinceCluePersonVO person : relatedPerson) {
            //保存人员信息并关联任务
            RenWuRelatedPersonSaveDTO dto = new RenWuRelatedPersonSaveDTO();
            dto.setZjhm(person.getRySfz());
            dto.setXm(person.getXm());
            dto.setSjh(person.getSjh());
            dto.setHjd(person.getHjdMc());
            final String hjdDm = parseHjdDm(person.getHjdDm(), person.getHjdMc());
            dto.setHjdbm(hjdDm);
            dto.setHjdDm(hjdDm);
            // 因为户籍地跟现住地是必填项，目前ST只有户籍地，所以默认都用户籍地
            dto.setXzd(person.getHjdMc());
            DistrictListDto districtByCode = getDistrictByHjdDm(hjdDm);
            if (Objects.nonNull(districtByCode) && StringUtils.isNotEmpty(districtByCode.getCenter())) {
                dto.setHjd(StringUtils.showEmpty(dto.getHjd(), districtByCode.getName()));
                dto.setXzd(StringUtils.showEmpty(dto.getXzd(), districtByCode.getName()));
                dto.setHjdjwd(districtByCode.getCenter());
                dto.setXzdjwd(districtByCode.getCenter());
            } else {
                log.info("根据户籍地代码[{}]，在字典表中未找到相关信息，默认填充当前编码!", person.getHjdDm());
                Optional.ofNullable(getDistrictByHjdDm("all"))
                        .filter(i -> StringUtils.isNotEmpty(i.getCenter()))
                        .ifPresent(it -> {
                            dto.setHjdDm(StringUtils.showEmpty(dto.getHjdDm(), it.getCode()));
                            dto.setHjd(StringUtils.showEmpty(dto.getHjd(), districtByCode.getName()));
                            dto.setXzd(StringUtils.showEmpty(dto.getXzd(), districtByCode.getName()));
                            dto.setHjdjwd(it.getCenter());
                            dto.setXzdjwd(it.getCenter());
                        });
            }
            PersonVO idNumber = profileService.getPersonByIdNumber(person.getRySfz());
            if (Objects.isNull(idNumber)) {
                //补录行为
                log.info("根据证件号码[{}]，在人员档案中未找到相关人员信息!", person.getRySfz());
                Boolean needAdd = BeanFactoryHolder.getEnv().getProperty("intelligence.st.renwu.related.person.add",
                        Boolean.class, false);
                if (needAdd) {
                    JSONObject object = new JSONObject();
                    object.put("name", person.getXm());
                    object.put("sfz", person.getRySfz());
                    object.put("sjh", person.getSjh());
                    object.put("hjdDm", person.getHjdDm());
                    object.put("groupId", relatedGroupId);
                    Long personId = profileService.saveGroupOrPerson(null, user.getId(), user.getDept().getId(), object.toJSONString());
                    if (Objects.nonNull(personId) && personId > 0L) {
                        dto.setArchivePersonId(personId);
                    } else {
                        log.error("人员证件号码[{}]，在人员档案中补录失败，请在[profile]模块后台检查日志", person.getRySfz());
                    }
                }
            } else {
                dto.setArchivePersonId(idNumber.getId());
            }
            if (Optional.ofNullable(dto.getArchivePersonId()).filter(i -> i > 0L).isPresent()) {
                dto.setIsSearched(1);
            } else {
                dto.setIsSearched(0);
            }
            persons.add(dto);
        }
        return persons;
    }

    /**
     * sheAnXinXiList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:08
     */
    public List<RenWuDaShuJuHeChaJiaoTongWeiFaVo> jiaoTongWeiFaList(RenWuDaShuJuHeChaQueryActionDTO dto) throws ServiceException {
        final var cp = dto.makeChePaiList();
        checkArgument(CollectionUtils.isNotEmpty(cp), new ServiceException("号牌信息不能为空"));
        for (ChePaiVo chePai : cp) {
            checkArgument(chePai.isOk(), new ServiceException("号牌信息不正确"));
        }
        JSONArray data = RenWuDaShuJuHeChaUtils.scsjtwfjlcx(cp);
        if (CollectionUtils.isEmpty(data)) {
            return List.of();
        }
        List<RenWuDaShuJuHeChaJiaoTongWeiFaVo> list = new ArrayList<>(0);
        for (int i = 0; i < data.size(); i++) {
            JSONObject car = data.getJSONObject(i);
            if (Objects.nonNull(car)) {
                RenWuDaShuJuHeChaJiaoTongWeiFaVo vo = new RenWuDaShuJuHeChaJiaoTongWeiFaVo();
                addInfo(car, "PROC_TIME", RenWuDaShuJuHeChaUtils.convertTime(), vo::setClsj);
                addInfo(car, "ILLE_PLAC_ADDR_NAME", vo::setWfdz);
                addInfo(car, "TRAF_ILLE_ACT_NAME", vo::setWfxw);
                addInfo(car, "ILLE_TIME", RenWuDaShuJuHeChaUtils.convertTime(), vo::setWfsj);
                addInfo(car, "VEH_PLATE_NUM", vo::setHphm);
                addInfo(car, "VEH_LIC_TNAME", vo::setHpzl);
                addInfo(car, "ILLE_MAN_HAND_RCODE", vo::setCljg);
                list.add(vo);
            }
        }
        return list;
    }

    /**
     * sheAnXinXiList<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:08
     */
    public List<RenWuDaShuJuHeChaAnJianVo> sheAnXinXiList(RenWuDaShuJuHeChaQueryActionDTO dto) throws ServiceException {
        final JSONArray data = RenWuDaShuJuHeChaUtils.scsgmsfyajgxcx(dto.getZjhm());
        if (CollectionUtils.isEmpty(data)) {
            return List.of();
        }
        List<String> anJianNolist = new ArrayList<>(0);
        for (int i = 0; i < data.size(); i++) {
            addInfo(data.getJSONObject(i), "CASE_NO", anJianNolist::add);
        }
        return anJianNolist.stream()
                .map(it -> Try.of(() -> {
                            dto.setAjbh(it);
                            return sheAnXinXiDetail(dto);
                        }).onFailure(e -> log.error("根据案件编号[{}]获取数据出错！", it, e)).getOrNull()
                ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * sheAnXinXiDetail<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/31 18:11
     */
    public RenWuDaShuJuHeChaAnJianVo sheAnXinXiDetail(RenWuDaShuJuHeChaQueryActionDTO dto) throws ServiceException {
        checkNotEmpty(dto.getAjbh(), new ParamInvalidException("案件编号不能为空"));
        JSONArray data = RenWuDaShuJuHeChaUtils.scsgmsfyajgxcx(dto.getZjhm(), dto.getAjbh());
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException("案件不存在");
        }
        return Optional.ofNullable(RenWuDaShuJuHeChaUtils.scsajjbxxcx(dto.getAjbh()))
                .map(it -> it.getJSONObject(0))
                .map(it -> {
                    RenWuDaShuJuHeChaAnJianVo vo = new RenWuDaShuJuHeChaAnJianVo();
                    addInfo(it, "CASE_NAME", vo::setAjmc);
                    addInfo(it, "CASE_NO", vo::setAjbh);
                    addInfo(it, "CASCLA_NAME", vo::setAjlx);
                    addInfo(it, "CASE_DATE", RenWuDaShuJuHeChaUtils.convertTime(), vo::setLasj);
                    addInfo(it, "CASE_ORGA_PSAG", vo::setBadw);
                    addInfo(it, "ACCEP_UNIT_PSAG", vo::setSldw);
                    addInfo(it, "CASE_SOUR_DESC", vo::setAjly);
                    addInfo(it, "REPO_CASE_TIME", RenWuDaShuJuHeChaUtils.convertTime(), vo::setBasj);
                    addInfo(it, "ACCEP_TIME", RenWuDaShuJuHeChaUtils.convertTime(), vo::setSlsj);
                    addInfo(it, "SET_LAWS_DATE", RenWuDaShuJuHeChaUtils.convertTime(TimeUtils.YYYYMMDD), vo::setJarq);
                    addInfo(it, "DISC_TIME", RenWuDaShuJuHeChaUtils.convertTime(), vo::setFxasjsj);
                    addInfo(it, "BRIEF_CASE", vo::setJyaq);
                    return vo;
                }).orElseThrow(() -> new ServiceException("案件不存在"));
    }

    @Override
    public String key() {
        return Constants.RENWU;
    }

    @Override
    public String desc() {
        return "任务";
    }

    /**
     * 处理st任务历史数据
     */
    public void dealStHistoryData() throws ServiceException {
        //计算技侦数据跟st线索中相关人员的匹配度
        jzDataMatchCompare();
        //处理历史数据任务编号重复的问题
        dealTaskNotRepeat();
    }

    /**
     * 需要对任务编号重新排序
     */
    private void dealTaskNotRepeat() {
        List<RenWuBaseInfoEntity> collect = getMapper().selectList(new QueryWrapper<RenWuBaseInfoEntity>()
                .eq("is_del", 0)
                .le("cr_time", "2024-11-21 23:59:59"));
        collect.forEach(it -> {
            String stCrDate = TimeUtils.dateToString(it.getCrTime(), "yyyy-MM-dd");
            it.setCrDate(TimeUtils.stringToDate(stCrDate, "yyyy-MM-dd"));
        });
        //按照日期分组排序
        Map<Date, List<RenWuBaseInfoEntity>> map = collect
                .stream()
                .collect(Collectors.groupingBy(RenWuBaseInfoEntity::getCrDate));
        for (Date date : map.keySet()) {
            List<RenWuBaseInfoEntity> list = map.get(date);
            //排序
            list.sort(Comparator.comparing(RenWuBaseInfoEntity::getCrTime));
            String oldNo = list.get(0).getTaskNo();
            String pre = oldNo.substring(0, oldNo.length() - 3);
            Integer index = 1;
            for (RenWuBaseInfoEntity entity : list) {
                entity.setDataNo(index);
                int length = index.toString().length();
                String post;
                switch (length) {
                    case 1:
                        post = "00" + index;
                        break;
                    case 2:
                        post = "0" + index;
                        break;
                    default:
                        post = index.toString();
                        break;
                }
                entity.setTaskNo(pre + post);
                getMapper().updateById(entity);
                index++;
            }
        }
    }

    private void jzDataMatchCompare() {
        List<RenWuBaseInfoEntity> entities = getMapper().selectList(new QueryWrapper<RenWuBaseInfoEntity>()
                .isNotNull("stxsbh")
                .eq("is_del", 0));
        List<RenWuRelatedPersonMappingEntity> mapps = getRelatedPersonMappingMapper().selectList(new QueryWrapper<RenWuRelatedPersonMappingEntity>()
                .in("task_id", entities
                        .stream()
                        .map(RenWuBaseInfoEntity::getDataId)
                        .collect(Collectors.toList())));
        List<RenWuRelatedPersonEntity> personList = getRelatedPersonMapper().selectBatchIds(mapps
                .stream()
                .map(RenWuRelatedPersonMappingEntity::getPersonId)
                .collect(Collectors.toList()));
        log.info("st线索相关人员总数为：{}", personList.size());
        Map<String, List<String>> allTzz = searchService.getAllTzz();
        List<String> jzPhoneList = allTzz.get("phone");
        List<String> jzZjhmList = allTzz.get("zjhm");
        log.info("手机号码类型技侦数据条数：{}", jzPhoneList.size());
        log.info("证件号码类型技侦数据条数：{}", jzZjhmList.size());
        //手机号码
        List<String> sjhList = new ArrayList<>(0);
        //身份证
        List<String> zjhmList = new ArrayList<>(0);
        for (RenWuRelatedPersonEntity person : personList) {
            if (StringUtils.isNotEmpty(person.getSjh())
                    && jzPhoneList.contains(person.getSjh())) {
                sjhList.add(person.getSjh());
            }
            if (StringUtils.isNotEmpty(person.getZjhm())
                    && jzZjhmList.contains(person.getZjhm())) {
                zjhmList.add(person.getZjhm());
            }
        }
        DecimalFormat df = new DecimalFormat("#.00");
        double zjhmCoverage = ((double) zjhmList.size() / personList.size()) * 100;
        double sjhCoverage = ((double) sjhList.size() / personList.size()) * 100;
        log.info("身份证st线索人员匹配数量：{}，身份证覆盖率为：{}，匹配的身份证有：{}",
                zjhmList.size(),
                df.format(zjhmCoverage) + "%",
                String.join(",", zjhmList));
        log.info("手机号码st线索人员匹配数量：{}，手机号码覆盖率为：{}，匹配的手机号码有：{}",
                sjhList.size(),
                df.format(sjhCoverage) + "%",
                String.join(",", sjhList));
    }

    /**
     * 将所有相关人员重新指派到市局情指部门
     *
     * @param entityId     任务id
     * @param user         市级情指部门用户
     * @param targetDeptId 指派的目标单位
     * @param personIds    人员id集合
     * @throws ServiceException 异常
     */
    private void assignPerson(Long entityId, CurrentUser user, Long targetDeptId, Collection<Long> personIds)
            throws ServiceException {
        final Map<String, Object> map = new HashMap<>(1);
        for (Long personId : personIds) {
            RenWuRelatedPersonEntity person = getRelatedPersonMapper().selectById(personId);
            // 如果已经分配到了当前组织就跳过
            if (person.makeDeptIds().contains(targetDeptId)) {
                log.warn("人员[{}:{}]已经分配到了当前组织[{}]，跳过", person.getXm(), person.getZjhm(), targetDeptId);
                continue;
            }
            RenWuAssignPersonMappingDTO mapping = new RenWuAssignPersonMappingDTO();
            mapping.setMainDept(person.getMainDeptId());
            mapping.setLeaderDept(person.getLeaderDeptId());
            mapping.setOtherDepts(person.getOtherDeptIds());
            mapping.setDealUsers(JsonUtil.parseArray(person.getDealUsers(), RenWuDealUserDTO.class));
            if (Objects.isNull(person.getMainDeptId())) {
                // 如果用户没有分配主责单位，则优先分配为主责单位
                mapping.setMainDept(targetDeptId);
            } else if (Objects.isNull(person.getLeaderDeptId())) {
                // 如果用户没有分配牵头单位，其次为牵头单位
                mapping.setLeaderDept(targetDeptId);
            } else {
                // 如果都没有就弄到其他单位中
                Set<Long> tmp = new HashSet<>(StringUtils.getLongList(mapping.getOtherDepts()));
                tmp.add(targetDeptId);
                mapping.setOtherDepts(StringUtils.join(tmp.toArray(), StringUtils.SEPARATOR_COMMA));
            }
            map.put(personId.toString(), mapping);
        }
        if (!map.isEmpty()) {
            RenWuAssignPersonDTO dto = new RenWuAssignPersonDTO();
            dto.setDataId(entityId);
            dto.setUser(user);
            dto.setMap(JSON.toJSONString(map));
            dto.setSystemCall(
                    BeanFactoryHolder.getEnv()
                            .getProperty("intelligence.province.clue.systemAssignPerson.addFlag", Boolean.class, false)
            );
            BaseAction.findAction(dto.getActionEnum()).doAction(dto);
        }
    }

    @Override
    public List<RenWuRelatedPersonVo> relatedPersonList(RelatedPersonQueryDTO dto) throws ServiceException {
        dto.isValid();
        final CurrentUser user = AuthHelper.getNotNullUser();
        final String shortDeptAreaCode;
        final Long deptId;
        final String gsdyShortCode;
        if (Objects.equals(dto.getOnlyMyDept(), 1)) {
            if (DeptUtils.canSeeChildrenData(user)) {
                shortDeptAreaCode = getAreaShortCode(spreadingAreaCode(user.getDept().getDistrictCode(), false));
                deptId = null;
            } else {
                shortDeptAreaCode = null;
                deptId = user.getDeptId();
            }
        } else {
            shortDeptAreaCode = null;
            deptId = null;
        }
        if (Objects.equals(1, dto.getOnlyLocal())) {
            gsdyShortCode = getAreaShortCode(spreadingAreaCode(user.getDept().getDistrictCode(), false));
        } else {
            gsdyShortCode = null;
        }
        return Optional.ofNullable(detail(dto.getDataId()))
                .map(RenWuEntityVo::getRelatedPersons)
                .map(i -> i.stream()
                        .filter(it -> Objects.isNull(deptId) || it.isAssignToUser(user))
                        .filter(it -> StringUtils.isEmpty(shortDeptAreaCode) || StringUtils.showEmpty(it.getHjdDm()).startsWith(shortDeptAreaCode))
                        .filter(it -> StringUtils.isEmpty(gsdyShortCode) || StringUtils.showEmpty(it.getHjdDm()).startsWith(gsdyShortCode))
                        .collect(Collectors.toList()))
                .orElseGet(List::of);
    }
}
