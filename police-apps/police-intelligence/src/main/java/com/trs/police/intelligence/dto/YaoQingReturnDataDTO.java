package com.trs.police.intelligence.dto;

import com.trs.police.intelligence.constant.ActionEnum;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * YaoQingSignDataDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/16 13:29
 * @since 1.0
 */
@Data
public class YaoQingReturnDataDTO extends BaseActionDTO {

    private String reason;

    public YaoQingReturnDataDTO() {
        setActionEnum(ActionEnum.YAO_QING_RETURN_DATA);
    }
}
