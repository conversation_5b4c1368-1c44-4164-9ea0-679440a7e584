package com.trs.police.intelligence.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.intelligence.constant.ActionEnum;
import lombok.Data;
import lombok.ToString;

import static com.trs.common.base.PreConditionCheck.checkNotNull;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/13 21:04
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
public class RenWuSplitTaskDTO extends BaseActionDTO {

    private RenWuSaveDTO save;

    public RenWuSplitTaskDTO() {
        setActionEnum(ActionEnum.RENWU_SPLIT_TASK);
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotNull(getSave(), new ParamInvalidException("保存对象不能为空"));
        checkNotNull(getSave().getRootId(), new ParamInvalidException("主任务的数据ID不能为空"));
        getSave().setSplitTaskFlag(true);
        getSave().isValid();
        return super.checkParams();
    }
}
