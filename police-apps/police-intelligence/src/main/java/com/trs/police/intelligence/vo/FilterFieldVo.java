package com.trs.police.intelligence.vo;

import com.trs.common.pojo.BaseVO;
import lombok.Data;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * FilterFieldVo
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 17:47
 * @since 1.0
 */
@Data
public class FilterFieldVo extends BaseVO {

    private String defaultValue;

    private String displayName;

    private FieldNamesVo fieldNames;

    private String key;

    private String linkedKey;

    private String queryKey;

    private String type;

    private String url;

    private List value;

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2024</p>
     * <p>Company:      www.trs.com.cn</p>
     * FilterFieldVo
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2024/4/11 19:38
     * @since 1.0
     */
    @Data
    public static class FieldNamesVo extends BaseVO {

        private String children;

        private String label;

        private String value;

    }
}
