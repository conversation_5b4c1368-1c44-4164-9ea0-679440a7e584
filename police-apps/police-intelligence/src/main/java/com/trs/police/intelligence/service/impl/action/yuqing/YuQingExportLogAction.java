package com.trs.police.intelligence.service.impl.action.yuqing;

import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.dto.DataActionDTO;
import com.trs.police.intelligence.entity.YuQingBaseInfoEntity;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/15 09:47
 * @since 1.0
 */
@Component
public class YuQingExportLogAction
        extends BaseYuQingWithReportAction<DataActionDTO> {

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.YUQING_EXPORT_DOC;
    }

    @Override
    public void recordLog(Optional<CurrentUser> login, YuQingBaseInfoEntity entity, DataActionDTO dto, Report<String> one) {
        // 非草稿的数据才需要记录日志
        if (Objects.equals(entity.getDraftsFlag(), 0)) {
            super.recordLog(login, entity, dto, one);
        }
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public Report<String> doOneAction(
            Optional<CurrentUser> login,
            YuQingBaseInfoEntity entity,
            DataActionDTO dto
    ) throws ServiceException {
        return new Report<>(desc(), "完成操作");
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(
            Optional<CurrentUser> login,
            YuQingBaseInfoEntity entity,
            DataActionDTO dto,
            Report<String> one
    ) {
        return String.format("导出舆情 %s", entity.getDataTitle());
    }

}
