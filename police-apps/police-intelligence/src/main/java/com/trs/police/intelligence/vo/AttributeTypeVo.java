package com.trs.police.intelligence.vo;

import com.trs.common.pojo.BaseVO;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * AttributeTypeVo
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/7 12:19
 * @since 1.0
 */
@Data
public class AttributeTypeVo extends BaseVO {

    private String name;

    private List<AttributeTypeVo> children = Collections.emptyList();
}
