package com.trs.police.intelligence.service.impl.action.tongzhitongbao;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.dto.AddLogDTO;
import com.trs.police.intelligence.dto.DataActionDTO;
import com.trs.police.intelligence.entity.DataRelationMappingEntity;
import com.trs.police.intelligence.entity.TongZhiTongBaoBaseInfoEntity;
import com.trs.police.intelligence.service.BaseTongZhiTongBaoAction;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * ZhiLingAddExtInfoAction
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/18 15:01
 * @since 1.0
 */
@Component
public class TongZhiTongBaoForwardOrderAction
        extends BaseTongZhiTongBaoAction<AddLogDTO, Report<String>, Report<String>> {

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.TONGZHITONGBAO_FORWARD;
    }

    /**
     * mergeOneR<BR>
     *
     * @param login   参数
     * @param reports 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:36
     */
    @Override
    protected Report<String> mergeOneR(Optional<CurrentUser> login, List<Report<String>> reports) {
        return reports.stream()
                .filter(it -> it.getResult() == RESULT.FAIL)
                .findAny()
                .orElse(new Report<>(desc(), "成功处理"));
    }

    /**
     * check<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:43
     */
    @Override
    protected void check(
            Optional<CurrentUser> login,
            TongZhiTongBaoBaseInfoEntity entity,
            AddLogDTO dto
    ) throws ServiceException {
        super.check(login, entity, dto);
        List<DataRelationMappingEntity> list = getDataRelationMappingMgr().getRelationList(
                module(),
                entity.getRootId(),
                Constants.DEPT,
                Constants.DATA_RELATION_STATUS_TYPE_FORWARD
        );
        PreConditionCheck.checkArgument(
                list.stream().noneMatch(it -> it.getObjId().equals(login.orElseThrow().getDeptId())),
                new ParamInvalidException("该部门已经转发过了,不能重复转发")
        );
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public Report<String> doOneAction(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity, AddLogDTO dto)
            throws ServiceException {
        if (dto.getReport().getResult() == RESULT.FAIL) {
            return dto.getReport();
        }
        DataActionDTO actionDTO = new DataActionDTO();
        actionDTO.setActionEnum(ActionEnum.TONGZHITONGBAO_PUSH_DATA);
        actionDTO.setDataId(entity.getDataId());
        actionDTO.setOptSource(Constants.DATA_RELATION_STATUS_TYPE_FORWARD);
        Report<String> report = BeanUtil.getBean(TongZhiTongBaoPushAction.class).doAction(actionDTO);
        if (report.getResult() == RESULT.FAIL) {
            return report;
        }
        List<DataRelationMappingEntity> list = getDataRelationMappingMgr().getRelationList(
                module(),
                entity.getRootId(),
                Constants.DEPT,
                Constants.DATA_RELATION_STATUS_TYPE_FORWARD
        );
        Boolean isForward = false;
        final var user = login.orElseThrow(() -> new ServiceException("未登录"));
        for (DataRelationMappingEntity relationMapping : list) {
            if (relationMapping.getObjId().equals(user.getDeptId())) {
                relationMapping.setStatusCode(entity.getDataId().intValue());
                relationMapping.setCrTime(new Date());
                getDataRelationMappingMgr().updateEntity(relationMapping);
                isForward = true;
            }
        }
        if (!isForward) {
            DataRelationMappingEntity relationMapping = DataRelationMappingEntity
                    .addInfoOnCreate(user, new DataRelationMappingEntity());
            relationMapping.setRelationType(module());
            relationMapping.setRelationId(entity.getRootId());
            relationMapping.setObjType(Constants.DEPT);
            relationMapping.setObjId(user.getDeptId());
            relationMapping.setObjDeptId(user.getDeptId());
            relationMapping.setStatusType(Constants.DATA_RELATION_STATUS_TYPE_FORWARD);
            relationMapping.setStatusCode(entity.getDataId().intValue());
            getDataRelationMappingMgr().insertEntity(relationMapping);
        }
        return new Report<>(desc(), "完成操作");
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity, AddLogDTO dto,
                                    Report<String> one) {
        return String.format("转发%s %s", entity.makeShowName(), dto.getDataTitle());
    }

    /**
     * makeMessageContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:38
     */
    @Override
    protected String makeMessageContent(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity, AddLogDTO dto,
                                        Report<String> one) throws ServiceException {
        var detail = getEntityService().detail(entity.getDataId(), entity.getVersionId());
        JSONObject value = JSONObject.parseObject(JsonUtil.toJsonString(detail));
        value.put("dataId", detail.getDataId());
        value.put("id", detail.getDataId());
        value.put("versionId", detail.getVersionId());
        value.put("title", detail.getDataTitle());
        value.put("content", detail.getDataContent());
        value.put("versionDataTitle", detail.getVersionDataTitle());
        value.put("versionDataContent", detail.getVersionDataContent());
        value.put("attributes", detail.getAttributes());
        return String.format("notifycation(%s)", value);
    }

    /**
     * makeMessageSimpleContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 17:07
     */
    @Override
    protected String makeMessageSimpleContent(Optional<CurrentUser> login, TongZhiTongBaoBaseInfoEntity entity,
                                              AddLogDTO dto, Report<String> one) throws ServiceException {
        return String.format("转发%s %s", entity.makeShowName(), dto.getDataTitle());
    }
}
