package com.trs.police.intelligence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.intelligence.entity.DataRelationMappingEntity;
import com.trs.police.intelligence.vo.DealDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * DataRelationMappingMapper
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 01:31
 * @since 1.0
 */
@Mapper
public interface DataRelationMappingMapper extends BaseMapper<DataRelationMappingEntity> {

    /**
     * findDealDetailList<BR>
     *
     * @param relationType 参数
     * @param relationIds  参数
     * @param objId        参数
     * @param objType      参数
     * @param statusType   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/18 20:38
     */
    List<DealDetailVo> findDealDetailList(
            @Param("relationType") String relationType,
            @Param("relationIds") List<Long> relationIds,
            @Param("objType") String objType,
            @Param("objId") Long objId,
            @Param("statusType") String statusType
    );
}
