package com.trs.police.intelligence.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.common.utils.JsonUtils;
import com.trs.police.intelligence.vo.AttributeTemplatesVo;
import com.trs.police.intelligence.vo.FieldVo;
import com.trs.police.intelligence.vo.FilterFieldVo;
import lombok.Data;

import java.util.Collections;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * AttributeTemplatesEntity
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 17:27
 * @since 1.0
 */
@TableName("tb_intelligence_attribute_templates")
@Data
public class AttributeTemplatesEntity extends BaseEntity {

    private String type;

    private String dataType;

    private String dataTypeShowName;

    private String dataClass;

    private String dataClassShowName;

    private String templateName;

    @JsonIgnore
    private String relationTableName;

    private String fields;

    private String filterFields;

    private String exportFieldKeys;
    private String exportFieldKeysOneLine;

    private Integer orderNum;

    /**
     * toVo<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/11 17:55
     */
    public AttributeTemplatesVo toVo() {
        AttributeTemplatesVo vo = new AttributeTemplatesVo();
        vo.setType(getType());
        vo.setDataType(getDataType());
        vo.setDataTypeShowName(getDataTypeShowName());
        vo.setDataClass(getDataClass());
        vo.setDataClassShowName(getDataClassShowName());
        vo.setTemplateName(getTemplateName());
        vo.setRelationTableName(getRelationTableName());
        vo.setExportFieldKeys(getExportFieldKeys());
        vo.setExportFieldKeysOneLine(getExportFieldKeysOneLine());
        if (JsonUtils.isValidArray(getFields())) {
            vo.setFields(JSON.parseArray(getFields(), FieldVo.class));
        } else {
            vo.setFields(Collections.emptyList());
        }
        if (JsonUtils.isValidArray(getFilterFields())) {
            vo.setFilterFields(JSON.parseArray(getFilterFields(), FilterFieldVo.class));
        } else {
            vo.setFilterFields(Collections.emptyList());
        }
        return vo;
    }
}
