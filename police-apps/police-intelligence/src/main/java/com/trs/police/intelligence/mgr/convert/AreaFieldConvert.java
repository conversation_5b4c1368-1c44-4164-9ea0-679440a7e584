package com.trs.police.intelligence.mgr.convert;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.police.intelligence.vo.AttributeItemExportVo;
import com.trs.police.intelligence.vo.FieldVo;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/25 14:41
 * @since 1.0
 */
@Component
public class AreaFieldConvert extends CommonFieldConvert {

    @Override
    public String key() {
        return "area";
    }

    @Override
    public AttributeItemExportVo convertForExport(FieldVo field, String editValue, String showValue, Map<String, String> map) {
        return AttributeItemExportVo.of(
                field.getSchema().getTitle(),
                StringUtils.showEmpty(JSONObject.parseObject(editValue).getString("name"))
        );
    }
}
