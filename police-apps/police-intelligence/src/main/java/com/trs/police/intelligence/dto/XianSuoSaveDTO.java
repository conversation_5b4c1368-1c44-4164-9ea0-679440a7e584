package com.trs.police.intelligence.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.intelligence.constant.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.trs.common.base.PreConditionCheck.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/12 01:46
 * @since 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class XianSuoSaveDTO extends BaseSaveDTO {

    private Boolean needArchive;

    private String pushType;

    private String clueSourceType;

    private Long sourceDeptId;

    /**
     * 线索池ID
     */
    private Long cluePoolId;

    private String sourceClueNo;

    private String targetTime;

    private String groupType;

    private String groupDetail;

    private String wqfs;

    private String xwfs;

    private String relatedPerson;

    private String zxdd;

    private String xxdd;

    private String ccDeptIds;

    private String signer;

    private String contactsUser;

    private String phone;

    private String attachment;

    private String checkLevel;

    private String feedbackLimitTime;

    private String xssj;

    private String syncTaskId;
    private String stReportDeptNames;

    private Boolean simpleCheck;

    public XianSuoSaveDTO() {
        setDataClass(Constants.DEFAULT);
        setDataType(Constants.XIANSUO);
        this.needArchive = false;
        this.simpleCheck = false;
    }

    /**
     * makeRelatedPerson<BR>
     *
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/8 13:03
     */
    public List<RelatedPersonSaveDTO> makeRelatedPerson() throws ServiceException {
        checkNotEmpty(getRelatedPerson(), new ParamInvalidException("涉及人员不能为空"));
        if (JsonUtils.isValidArray(getRelatedPerson())) {
            return JsonUtil.parseArray(getRelatedPerson(), RelatedPersonSaveDTO.class);
        }
        return Collections.emptyList();
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkArgument(Constants.CN_JIN_QI.equals(getTargetTime())
                        || TimeUtils.isHitPattern(getTargetTime(), TimeUtils.YYYYMMDD),
                new ParamInvalidException("指向时间格式错误")
        );
        checkNotEmpty(getClueSourceType(), new ParamInvalidException("来源类别不能为空"));
        if (Objects.equals(Constants.SJDW, getClueSourceType())) {
            checkNotNull(getSourceDeptId(), new ParamInvalidException("来源单位不能为空"));
        }
        if (!getSimpleCheck()) {
            checkNotEmpty(getGroupType(), new ParamInvalidException("群体类型不能为空"));
            checkNotEmpty(getGroupDetail(), new ParamInvalidException("群体细类不能为空"));
            checkNotEmpty(getWqfs(), new ParamInvalidException("维权方式不能为空"));
        }
        checkNotEmpty(getSigner(), new ParamInvalidException("签发人不能为空"));
        checkNotEmpty(getContactsUser(), new ParamInvalidException("联系人不能为空"));
        checkNotEmpty(getPhone(), new ParamInvalidException("联系电话不能为空"));
        checkNotEmpty(getDataTitle(), new ParamInvalidException("标题不能为空"));
        var persons = makeRelatedPerson();
        checkArgument(CollectionUtils.isNotEmpty(persons), new ParamInvalidException("涉及人员不能为空"));
        List<String> zjhm = new ArrayList<>(0);
        for (RelatedPersonSaveDTO person : persons) {
            person.isValid();
            if (Constants.PUSHDOWN.equals(getPushType())) {
                if (StringUtils.isEmpty(getStxsbh())) {
                    checkNotNull(person.getCheckDeptId(), new ParamInvalidException("推送下级时核查单位不能为空"));
                }
            }
            if (zjhm.contains(person.getZjhm())) {
                throw new ParamInvalidException("证件号码[" + person.getZjhm() + "]重复了");
            } else {
                zjhm.add(person.getZjhm());
            }
        }
        if (Constants.PUSHDOWN.equals(getPushType())) {
            checkNotEmpty(getCheckLevel(), new ParamInvalidException("推送下级时核查等级不能为空"));
        }
        return true;
    }
}
