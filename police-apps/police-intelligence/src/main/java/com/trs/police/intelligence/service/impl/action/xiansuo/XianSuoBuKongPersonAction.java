package com.trs.police.intelligence.service.impl.action.xiansuo;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.BuKongPersonDTO;
import com.trs.police.intelligence.entity.XianSuoBaseInfoEntity;
import com.trs.police.intelligence.entity.XianSuoRelatedPersonEntity;
import com.trs.police.intelligence.entity.XianSuoRelatedPersonMappingEntity;
import com.trs.police.intelligence.service.BaseXianSuoAction;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkArgument;
import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/8 21:38
 * @since 1.0
 */
@Component
public class XianSuoBuKongPersonAction extends
        BaseXianSuoAction<BuKongPersonDTO, Report<String>, Report<String>> {

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.XIANSUO_BU_KONG_PERSON;
    }

    /**
     * mergeOneR<BR>
     *
     * @param login   参数
     * @param reports 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:36
     */
    @Override
    protected Report<String> mergeOneR(Optional<CurrentUser> login, List<Report<String>> reports) {
        return reports
                .stream()
                .filter(it -> it.getResult() == RESULT.FAIL)
                .findAny()
                .orElse(new Report<>(desc(), "成功处理"));
    }

    /**
     * check<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:43
     */
    @Override
    protected void check(Optional<CurrentUser> login, XianSuoBaseInfoEntity entity, BuKongPersonDTO dto)
            throws ServiceException {
        super.check(login, entity, dto);
        checkArgument(
                !entity.getStatusCode().equals(Status.CAOGAOXIANG.getCode()),
                new ServiceException("草稿箱的数据不能布控")
        );
        List<Long> personIds = StringUtils.getLongList(dto.getPersonIds());
        var persons = entity.getRelatedPersons()
                .stream()
                .filter(it -> personIds.contains(it.getDataId()))
                .filter(it -> !Objects.equals(it.getBuKongStatusNew(), dto.getFlag()))
                .collect(Collectors.toList());
        checkNotEmpty(
                persons,
                new ServiceException(String.format(
                        "线索中没有对应人员或都已被%s",
                        Objects.equals(1, dto.getFlag()) ? "布控" : "取消布控"
                ))
        );
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public Report<String> doOneAction(
            Optional<CurrentUser> login, XianSuoBaseInfoEntity entity,
            BuKongPersonDTO dto
    ) throws ServiceException {
        List<Long> personIds = StringUtils.getLongList(dto.getPersonIds());
        var persons = entity.getRelatedPersons()
                .stream()
                .filter(it -> personIds.contains(it.getDataId()))
                .filter(it -> !Objects.equals(it.getBuKongStatusNew(), dto.getFlag()))
                .collect(Collectors.toList());
        new LambdaUpdateChainWrapper<>(getRelatedPersonMappingMapper())
                .set(XianSuoRelatedPersonMappingEntity::getBuKongStatus, dto.getFlag())
                .eq(XianSuoRelatedPersonMappingEntity::getClueId, entity.getDataId())
                .in(
                        XianSuoRelatedPersonMappingEntity::getPersonId,
                        persons.stream()
                                .map(XianSuoRelatedPersonEntity::getDataId)
                                .collect(Collectors.toSet())
                ).update();
        return new Report<>(desc(), "人员布控成功", RESULT.SUCCESS, persons);
    }

    @Override
    public Report<String> doOneSomeThingAfter(
            Optional<CurrentUser> login,
            XianSuoBaseInfoEntity entity,
            BuKongPersonDTO dto,
            Report<String> report
    ) throws ServiceException {
        if (report.getResult() == RESULT.SUCCESS) {
            List<XianSuoRelatedPersonEntity> persons = (List<XianSuoRelatedPersonEntity>) report.getContext();
            for (XianSuoRelatedPersonEntity person : persons) {
                recordPersonLog(
                        login,
                        entity,
                        person,
                        ActionEnum.XIANSUO_BU_KONG_PERSON.getLogType(),
                        dto,
                        report
                );
                updatePersonLibrary(login, entity, person);
            }
        }
        // 清空
        report.setContext(null);
        return super.doOneSomeThingAfter(login, entity, dto, report);
    }

    /**
     * makeLogContent<BR>
     *
     * @param login   参数
     * @param entity  参数
     * @param person  参数
     * @param logType 参数
     * @param dto     参数
     * @param one     参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 23:08
     */
    @Override
    protected String makePersonLogContent(
            Optional<CurrentUser> login,
            XianSuoBaseInfoEntity entity,
            XianSuoRelatedPersonEntity person,
            String logType,
            BuKongPersonDTO dto,
            Report<String> one
    ) throws ServiceException {
        return Objects.equals(1, dto.getFlag()) ? "已进行布控" : "已取消布控";
    }

    /**
     * needRecordPersonLog<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/9 23:04
     */
    @Override
    public boolean needRecordPersonLog() {
        return true;
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(Optional<CurrentUser> login, XianSuoBaseInfoEntity entity,
                                    BuKongPersonDTO dto,
                                    Report<String> one) {
        List<Long> personIds = StringUtils.getLongList(dto.getPersonIds());
        var persons = entity.getRelatedPersons()
                .stream()
                .filter(it -> personIds.contains(it.getDataId()))
                .map(it -> String.format("%s（%s）", it.getXm(), it.getZjhm()))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("，"));
        return String.format(
                "线索 %s 中%s人员 %s。",
                entity.getDataTitle(),
                Objects.equals(1, dto.getFlag()) ? "布控了" : "取消布控",
                persons
        );
    }

}
