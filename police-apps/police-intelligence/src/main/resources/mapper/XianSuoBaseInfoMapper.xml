<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.intelligence.mapper.XianSuoBaseInfoMapper">

    <resultMap id="XianSuoBaseInfoEntity" type="com.trs.police.intelligence.entity.XianSuoBaseInfoEntity">
        <id column="data_id" jdbcType="BIGINT" property="dataId"/>
        <result column="cr_dept_id" property="crDeptId"/>
        <result column="cr_user" property="crUser"/>
        <result column="cr_time" property="crTime"/>
        <result column="is_del" property="isDel"/>
        <result column="cr_user_true_name" property="crUserTrueName"/>
        <result column="root_id" property="rootId"/>
        <result column="data_type" property="dataType"/>
        <result column="data_class" property="dataClass"/>
        <result column="classical_flag" property="classicalFlag"/>
        <result column="data_no" property="dataNo"/>
        <result column="data_year" property="dataYear"/>
        <result column="data_title" property="dataTitle"/>
        <result column="drafts_flag" property="draftsFlag"/>
        <result column="version_id" property="versionId"/>
        <result column="chat_id" property="chatId"/>
        <result column="accept_dept_ids" property="acceptDeptIds"/>
        <result column="status_code" property="statusCode"/>
        <result column="push_type" property="pushType"/>
        <result column="data_content" property="dataContent"/>
        <result column="raw_data_content" property="rawDataContent"/>
        <result column="clue_source_type" property="clueSourceType"/>
        <result column="source_dept_id" property="sourceDeptId"/>
        <result column="source_clue_no" property="sourceClueNo"/>
        <result column="clue_no" property="clueNo"/>
        <result column="target_time" property="targetTime"/>
        <result column="target_time_format" property="targetTimeFormat"/>
        <result column="group_type" property="groupType"/>
        <result column="group_detail" property="groupDetail"/>
        <result column="wqfs" property="wqfs"/>
        <result column="xwfs" property="xwfs"/>
        <result column="zxdd" property="zxdd"/>
        <result column="xxdd" property="xxdd"/>
        <result column="cc_dept_ids" property="ccDeptIds"/>
        <result column="signer" property="signer"/>
        <result column="contacts_user" property="contactsUser"/>
        <result column="phone" property="phone"/>
        <result column="attachment" property="attachment"/>
        <result column="check_level" property="checkLevel"/>
        <result column="feedback_limit_time" property="feedbackLimitTime"/>
        <result column="feedback_timeout" property="feedbackTimeout"/>
        <result column="person_type" property="personType"/>
        <result column="repeat_flag" property="repeatFlag"/>
        <result column="repeat_data_ids" property="repeatDataIds"/>
        <result column="merge_data_ids" property="mergeDataIds"/>
        <result column="hidden_flag" property="hiddenFlag"/>
        <result column="related_person_zjhm" property="relatedPersonZjhm"/>
        <result column="up_status_code" property="upStatusCode"/>
        <result column="push_up_dept_ids" property="pushUpDeptIds"/>
        <result column="stxsbh" property="stxsbh"/>
        <result column="sthbxsbh" property="sthbxsbh"/>
        <result column="gzyq" property="gzyq"/>
        <result column="czyq" property="czyq"/>
        <result column="fhqk" property="fhqk"/>
        <result column="xslx" property="xslx"/>
        <result column="merge_main_data_id" property="mergeMainDataId"/>
        <result column="sync_task_id" property="syncTaskId"/>
        <result column="st_report_dept_names" property="stReportDeptNames"/>
        <result column="pub_dept_id" property="pubDeptId"/>
        <result column="system_cr_time" property="systemCrTime"/>
        <collection property="relatedPersons"
                    ofType="com.trs.police.intelligence.entity.XianSuoRelatedPersonEntity"
                    columnPrefix="related_person_list_"
                    resultMap="relatedPerson"/>
    </resultMap>

    <resultMap id="relatedPerson" type="com.trs.police.intelligence.entity.XianSuoRelatedPersonEntity">
        <id column="data_id" property="dataId"/>
        <result column="cr_dept_id" property="crDeptId"/>
        <result column="cr_user" property="crUser"/>
        <result column="cr_time" property="crTime"/>
        <result column="is_del" property="isDel"/>
        <result column="cr_user_true_name" property="crUserTrueName"/>
        <result column="zjhm" property="zjhm"/>
        <result column="xm" property="xm"/>
        <result column="sjhwm" property="sjhwm"/>
        <result column="hjd" property="hjd"/>
        <result column="gsdy" property="gsdy"/>
        <result column="gsdymc" property="gsdymc"/>
        <result column="remarks_label" property="remarksLabel"/>
        <result column="remarks_label_name" property="remarksLabelName"/>
        <result column="is_searched" property="isSearched"/>
        <result column="check_dept_id" property="checkDeptId"/>
        <result column="check_dept_code" property="checkDeptCode"/>
        <result column="deal_user" property="dealUser"/>
        <result column="deal_phone" property="dealPhone"/>
        <result column="work_status" property="workStatus"/>
        <result column="real_belong" property="realBelong"/>
        <result column="current_location_flag" property="currentLocationFlag"/>
        <result column="current_location" property="currentLocation"/>
        <result column="content" property="content"/>
        <result column="valid_flag" property="validFlag"/>
        <result column="attachment" property="attachment"/>
        <result column="assign_time" property="assignTime"/>
        <result column="feedback_time" property="feedbackTime"/>
        <result column="bu_kong_status" property="buKongStatus"/>
        <result column="bu_kong_status_new" property="buKongStatusNew"/>
    </resultMap>
    <!-- 新全部条件 (创建单位在自己权限范围内+接收方为权限范围内的数据)-->
    <sql id="from_realAll">
        (
        tb.cr_dept_id IN
        <foreach collection="realAllDeptIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
        OR
        tb.data_id IN (
        select
        relation_id
        from tb_intelligence_data_relation_mapping
        where
        relation_type='xiansuo'
        AND obj_dept_id IN
        <foreach collection="realAllDeptIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
        AND is_del=0
        AND status_type IN ('pushUp','pushDown')
        )
        )
        AND tb.drafts_flag=0 AND tb.hidden_flag=0
    </sql>
    <!-- 我接收的(上级下发或下级上报到本单位的数据) -->
    <sql id="from_up">
        mp.status_type IN ('pushUp','pushDown') AND tb.drafts_flag=0 AND tb.hidden_flag=0
    </sql>
    <sql id="from_bigscreen">
        mp.status_type ='pushDown' AND tb.drafts_flag=0 AND tb.hidden_flag=0
    </sql>
    <!-- 维稳大屏 -->
    <sql id="from_stabilityBigScreen">
        tb.drafts_flag=0 AND tb.hidden_flag=0
    </sql>
    <!-- 我待办的 接受上级中待签收，接收下级中未采用的数据-->
    <sql id="from_todo">
        tb.drafts_flag=0
        AND tb.hidden_flag=0
        AND (
        (mp.status_type='pushDown' AND mp.status_code = 100)
        OR
        (mp.status_type='pushUp' AND mp.status_code = 101)
        )
    </sql>
    <!-- 我发起的（我创建的） -->
    <sql id="from_down">
        tb.cr_dept_id = #{user.deptId}
        AND tb.drafts_flag=0
    </sql>
    <!-- 草稿箱 -->
    <sql id="from_drafts">
        tb.cr_user=#{user.username}
        AND tb.cr_dept_id = #{user.deptId}
        AND tb.drafts_flag=1
    </sql>
    <!-- 抄送我的 -->
    <sql id="from_cc">
        mp.status_type = 'cc' AND tb.drafts_flag=0
    </sql>
    <sql id="from_collection">
        col.relation_id = #{user.id} AND tb.drafts_flag=0
    </sql>

    <sql id="base_from">
        tb.is_del = 0
        <choose>
            <when test='from == "fuhe"'>
                and tb.xslx = 1
            </when>
            <when test='from == "archive"'>
                and tb.xslx = 2
            </when>
            <otherwise>
                and tb.xslx = 0
            </otherwise>
        </choose>
        <choose>
            <when test='from == "drafts"'>
                and
                <include refid="from_drafts"></include>
            </when>
            <when test='from == "down"'>
                and
                <include refid="from_down"></include>
            </when>
            <when test='from == "up"'>
                and
                <include refid="from_up"></include>
            </when>
            <when test='from == "bigscreen"'>
                and
                <include refid="from_bigscreen"></include>
            </when>
            <when test='from == "stabilityBigScreen"'>
                and
                <include refid="from_stabilityBigScreen"></include>
            </when>
            <when test='from == "todo"'>
                and
                <include refid="from_todo"></include>
            </when>
            <when test='from == "cc"'>
                and
                <include refid="from_cc"></include>
            </when>
            <when test='from == "collection"'>
                and
                <include refid="from_collection"></include>
            </when>
            <when test='from == "realAll"'>
                and
                <include refid="from_realAll"></include>
            </when>
            <when test='from == "fuhe"'>
                and
                <include refid="from_realAll"></include>
            </when>
            <when test='from == "archive"'>
                and
                <include refid="from_realAll"></include>
            </when>
            <otherwise>
                <!-- 一个永远为假的条件 -->
                and tb.is_del != 0
            </otherwise>
        </choose>
    </sql>

    <sql id="fields">
        tb.*,
        p.data_id AS related_person_list_data_id,
        p.cr_dept_id AS related_person_list_cr_dept_id,
        p.cr_user AS related_person_list_cr_user,
        p.cr_time AS related_person_list_cr_time,
        p.is_del AS related_person_list_is_del,
        p.cr_user_true_name AS related_person_list_cr_user_true_name,
        p.zjhm AS related_person_list_zjhm,
        p.xm AS related_person_list_xm,
        p.sjhwm AS related_person_list_sjhwm,
        p.hjd AS related_person_list_hjd,
        p.gsdy AS related_person_list_gsdy,
        p.gsdymc AS related_person_list_gsdymc,
        p.remarks_label AS related_person_list_remarks_label,
        p.remarks_label_name AS related_person_list_remarks_label_name,
        p.is_searched AS related_person_list_is_searched,
        p.check_dept_id AS related_person_list_check_dept_id,
        p.check_dept_code AS related_person_list_check_dept_code,
        p.deal_user AS related_person_list_deal_user,
        p.deal_phone AS related_person_list_deal_phone,
        p.work_status AS related_person_list_work_status,
        p.real_belong AS related_person_list_real_belong,
        p.current_location_flag AS related_person_list_current_location_flag,
        p.current_location AS related_person_list_current_location,
        p.content AS related_person_list_content,
        p.valid_flag AS related_person_list_valid_flag,
        p.attachment AS related_person_list_attachment,
        p.assign_time AS related_person_list_assign_time,
        p.feedback_time AS related_person_list_feedback_time,
        p.bu_kong_status AS related_person_list_bu_kong_status,
        r.bu_kong_status AS related_person_list_bu_kong_status_new
    </sql>
    <sql id="baseTable">
        tb_intelligence_xiansuo_base_info tb
        join tb_intelligence_xiansuo_related_person_mapping r on tb.data_id = r.clue_id and tb.is_del=0 and r.is_del=0
        left join tb_intelligence_xiansuo_related_person p on p.data_id=r.person_id and r.is_del=0 and p.is_del=0
    </sql>

    <select id="findByIds" resultMap="XianSuoBaseInfoEntity">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="baseTable"/>
        <where>
            tb.data_id IN
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
        ORDER BY
        <if test="orders != null and orders.size() > 0">
            <foreach collection="orders" item="it">
                tb.${it.fieldName} ${it.orderType},
            </foreach>
        </if>
        tb.cr_time desc,
        tb.data_id desc
    </select>

    <sql id="xiansuoSql">
        tb_intelligence_xiansuo_base_info tb
        <if test='from == "up" or from == "todo" or from=="bigscreen"'>
            left join tb_intelligence_data_relation_mapping mp
            on mp.relation_id = tb.data_id
            AND mp.relation_type='xiansuo'
            AND mp.obj_type='dept'
            AND mp.obj_id = #{user.deptId}
            AND mp.status_type IN ('pushUp','pushDown')
            AND mp.is_del=0
        </if>
        <if test='from == "cc"'>
            left join tb_intelligence_data_relation_mapping mp
            on mp.relation_id = tb.data_id
            AND mp.relation_type='xiansuo'
            AND mp.obj_type='dept'
            AND mp.obj_id = #{user.deptId}
            AND mp.status_type = 'cc'
            AND mp.status_code=0
            AND mp.is_del=0
        </if>
        <if test='from == "collection"'>
            left join tb_intelligence_collection_data col
            on col.obj_id = tb.data_id
            AND col.relation_type='user'
            AND col.relation_id = #{user.id}
            AND col.obj_Type='xiansuo'
            AND col.is_del=0
        </if>
        <where>
            <include refid="base_from"/>
            <if test="searchParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.getSearchValue())">
                <bind name="pattern" value="'%' + searchParams.getSearchValue() + '%'"/>
                <choose>
                    <when test='searchParams.searchField == "dataTitle"'>
                        AND tb.data_title like #{pattern}
                    </when>
                    <when test='searchParams.searchField == "dataContent"'>
                        AND tb.data_content like #{pattern}
                    </when>
                    <when test='searchParams.searchField == "clueNo"'>
                        AND tb.clue_no like #{pattern}
                    </when>
                    <when test='searchParams.searchField == "all"'>
                        AND (
                        tb.clue_no like #{pattern}
                        OR tb.data_title like #{pattern}
                        OR tb.data_content like #{pattern}
                        )
                    </when>
                    <when test='searchParams.searchField == "crUser"'>
                        AND (tb.cr_user like #{pattern} OR tb.cr_user_true_name like #{pattern})
                    </when>
                </choose>
            </if>
            <foreach collection="baseFilter" item="param">
                <choose>
                    <!-- 时间范围,查全部时不加条件 -->
                    <when test="param.type.equals('timeParams')">
                        <if test="param.getProcessedValue().isAll() == false">
                            AND tb.${param.key} between #{param.value.beginTime} and #{param.value.endTime}
                        </if>
                    </when>
                    <!-- array -->
                    <when test="param.type.equals('array') or param.type.equals('in') ">
                        <bind name="inValues" value="param.getProcessedValue()"/>
                        <if test="inValues != null and inValues.size()>0">
                            <choose>
                                <when test='from == "up" and param.key == "status_code"'>
                                    AND mp.${param.key} IN
                                </when>
                                <when test='from == "up" and param.key == "push_type"'>
                                    AND mp.status_type IN
                                </when>
                                <when test='param.key=="cluePoolIds"'>
                                    AND tb.data_id IN(
                                    select relation_id from tb_intelligence_data_relation_mapping
                                    where
                                    relation_type = 'xiansuo'
                                    AND obj_type = 'cluePool'
                                    AND obj_id IN
                                    <foreach collection="inValues" item="item" open="(" separator="," close=")">
                                        #{item}
                                    </foreach>
                                    AND status_type = '0'
                                    AND status_code = 0
                                    )
                                </when>
                                <otherwise>
                                    AND tb.${param.key} IN
                                </otherwise>
                            </choose>
                            <if test='param.key != "cluePoolIds"'>
                                <foreach collection="inValues" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </if>
                    </when>
                    <!-- array_find_in_set -->
                    <when test="param.type.equals('array_find_in_set') or param.type.equals('find_in_set')">
                        <bind name="inValues" value="param.getProcessedValue()"/>
                        <if test="inValues != null and inValues.size()>0">
                            AND
                            <foreach collection="inValues" item="item" open="(" separator=" OR " close=")">
                                FIND_IN_SET(#{item}, tb.${param.key})
                            </foreach>
                        </if>
                    </when>
                    <!-- 直接本字段比较 -->
                    <otherwise>
                        AND tb.${param.key} = #{param.value}
                    </otherwise>
                </choose>
            </foreach>
        </where>
    </sql>
    <select id="categoryList" resultType="com.trs.police.statistic.domain.bean.CountItem">
        SELECT
        ${groupField} as `key`,
        ${groupField} as `name`,
        ${groupField} as `dateTime`,
        count(1) as count
        FROM
        <include refid="xiansuoSql"></include>
        group by name;
    </select>

    <select id="doPageSelect" resultType="com.trs.police.intelligence.entity.XianSuoBaseInfoEntity">
        SELECT
        tb.*
        <if test='from == "up" or from == "todo" or from=="bigscreen"'>
            ,mp.status_code AS up_status_code
            ,mp.status_type AS up_status_type
        </if>
        <if test='from == "collection"'>
            ,col.collection_title AS collection_title
        </if>
        FROM
        <include refid="xiansuoSql"></include>
        <choose>
            <when test="sortParams != null and sortParams.sortField != null">
                ORDER BY tb.${sortParams.sortField} ${sortParams.getProcessedValue()}, tb.data_id desc
            </when>
            <otherwise>
                order by tb.cr_time desc,tb.data_id desc
            </otherwise>
        </choose>
    </select>


    <select id="getUnReadNum" resultType="com.trs.police.intelligence.vo.GroupVo">
        select
        t.chat_id AS groupName,
        count(1) AS groupValue
        from
        tb_intelligence_chat_message_status t
        <where>
            t.is_del = 0
            AND t.read_flag = 0
            AND t.user_id = #{user.id}
            AND t.chat_id = 0
        </where>
        group by t.chat_id;
    </select>

    <select id="findXianSuoBaseInfoEntities" resultMap="XianSuoBaseInfoEntity">
        select
        <include refid="fields"/>
        FROM
        <include refid="baseTable"/>
        <where>
            tb.is_del=0
            AND tb.drafts_flag=0
            AND r.person_id IN
            <foreach collection="personIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="findCanMergeXianSuoBaseInfoIds" resultType="java.lang.Long">
        select
        tb.data_id
        FROM
        <include refid="baseTable"/>
        <where>
            tb.is_del=0
            AND tb.drafts_flag=0
            AND tb.status_code IN (111,101)
            AND tb.hidden_flag=0
            AND tb.data_id != #{entity.dataId}
            <!-- 上报到了同一个单位时。-->
            AND tb.push_up_dept_ids = #{entity.pushUpDeptIds}
            <!-- 合并规则：维权方式相同+指向日期相同+至少有一个人重复。-->
            AND tb.wqfs=#{entity.wqfs}
            <choose>
                <!--
                近期的数据可跟近期以及具体日期的数据合并
                具体日期的只能跟具体日期合并
                近期日期规则: 如果一个数据选择了近期，且其是2024-05-01创建的，那么其能合并的数据范围为：
                    1、同样选择近期且创建时间在[2024-04-01 TO 2024-05-31]的数据；
                    2、选择了具体日期在[2024-05-01 TO 2024-05-31]的数据
                -->
                <when test='entity.targetTime != "" and entity.targetTime == "近期"'>
                    AND (
                    (tb.target_time = '近期' AND tb.target_time_format between #{startTime} AND #{endTime})
                    OR
                    (tb.target_time != '近期' AND tb.target_time_format between #{entityTime} AND #{endTime})
                    )
                </when>
                <otherwise>
                    AND tb.target_time = #{entity.targetTime}
                </otherwise>
            </choose>
            AND p.zjhm IN
            <foreach collection="zjhm" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            AND r.person_id NOT IN
            <foreach collection="notInPersonIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
</mapper>