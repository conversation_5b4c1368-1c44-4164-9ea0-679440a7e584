<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.intelligence.mapper.XianSuoPersonLibraryMapper">

    <select id="findByZjhm" resultType="com.trs.police.intelligence.entity.XianSuoPersonLibraryEntity">
        select * from tb_intelligence_xiansuo_person_library
        <where>
            is_del = 0 and zjhm =#{zjhm}
        </where>
    </select>
    <select id="doPageSelect" resultType="com.trs.police.intelligence.entity.XianSuoPersonLibraryEntity">
        select * from tb_intelligence_xiansuo_person_library tb
        <where>
            tb.is_del = 0
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.getSearchValue())">
                <bind name="pattern" value="'%' + searchParams.getSearchValue() + '%'"/>
                <choose>
                    <when test='searchParams.searchField == "zjhm"'>
                        AND tb.zjhm like #{pattern}
                    </when>
                    <when test='searchParams.searchField == "name"'>
                        AND tb.xm like #{pattern}
                    </when>
                    <otherwise>
                        AND (
                        tb.zjhm like #{pattern}
                        OR
                        tb.xm like #{pattern}
                        )
                    </otherwise>
                </choose>
            </if>
            <foreach collection="baseFilter" item="param">
                <choose>
                    <!-- 时间范围,查全部时不加条件 -->
                    <when test="param.type.equals('timeParams')">
                        <if test="param.getProcessedValue().isAll() == false">
                            AND tb.${param.key} between #{param.value.beginTime} and #{param.value.endTime}
                        </if>
                    </when>
                    <!-- array -->
                    <when test="param.type.equals('array') or param.type.equals('in') ">
                        <bind name="inValues" value="param.getProcessedValue()"/>
                        <if test="inValues != null and inValues.size()>0">
                            AND tb.${param.key} IN
                            <foreach collection="inValues" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </when>
                    <!-- array_find_in_set -->
                    <when test="param.type.equals('array_find_in_set') or param.type.equals('find_in_set')">
                        <bind name="inValues" value="param.getProcessedValue()"/>
                        <if test="inValues != null and inValues.size()>0">
                            AND
                            <foreach collection="inValues" item="item" open="(" separator=" OR " close=")">
                                FIND_IN_SET(#{item}, tb.${param.key})
                            </foreach>
                        </if>
                    </when>
                    <!-- 直接本字段比较 -->
                    <otherwise>
                        AND tb.${param.key} = #{param.value}
                    </otherwise>
                </choose>
            </foreach>
        </where>
        ORDER BY
        <if test="sortParams != null and sortParams.sortField != null">
            tb.${sortParams.sortField} ${sortParams.getProcessedValue()},
        </if>
        tb.cr_time desc,
        tb.update_time desc,
        tb.data_id desc
    </select>

    <select id="findClueIdsByZjhm" resultType="java.lang.Long">
        select data_id from tb_intelligence_xiansuo_base_info t
        <where>
            is_del=0
            AND
            data_id IN
            (
            select clue_id from tb_intelligence_xiansuo_related_person_mapping
            where
            is_del = 0
            AND
            person_id IN (
            select data_id from tb_intelligence_xiansuo_related_person
            where
            is_del = 0
            AND
            zjhm=#{zjhm}
            )
            )
        </where>
    </select>
    <select id="findArchivesIdsByZjhm" resultType="com.trs.police.intelligence.vo.GroupVo">
        select
        id_number as  groupName,
        id as  groupValue
        from t_profile_person
        <where>
            id_number IN
            <foreach collection="zjhms" item="zj" open="(" close=")" separator=",">
                #{zj}
            </foreach>
        </where>
    </select>
</mapper>
