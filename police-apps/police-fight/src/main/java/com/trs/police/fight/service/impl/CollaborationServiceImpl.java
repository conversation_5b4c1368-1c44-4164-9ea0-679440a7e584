package com.trs.police.fight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.ExceptionMessageConstant;
import com.trs.police.common.core.constant.enums.CollaborationStatusEnum;
import com.trs.police.common.core.constant.enums.CompositeRoleEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.Dept;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.DeptMapper;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.DataUtils;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.core.vo.FightClueVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.approval.ApprovalInfoVO;
import com.trs.police.common.core.vo.fight.CollaborationCaseListVO;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.core.vo.permission.UserVO;
import com.trs.police.common.core.vo.profile.PersonCaseVO;
import com.trs.police.common.openfeign.starter.service.*;
import com.trs.police.common.openfeign.starter.vo.ApprovalDetailVO;
import com.trs.police.fight.constant.enums.*;
import com.trs.police.fight.converter.CollaborationConvertor;
import com.trs.police.fight.converter.JqCollaborationConvertor;
import com.trs.police.fight.domain.bean.DyCollaborationFrom;
import com.trs.police.fight.domain.bean.JqAssistancePerson;
import com.trs.police.fight.domain.dto.CollaborationDto;
import com.trs.police.fight.domain.dto.FeedbackRefreshDTO;
import com.trs.police.fight.domain.entity.*;
import com.trs.police.fight.domain.params.collaboration.ApprovalParams;
import com.trs.police.fight.domain.request.CollaborationListRequest;
import com.trs.police.fight.domain.vo.Collaboration.CollaborationJqVO;
import com.trs.police.fight.domain.vo.Collaboration.ReviewInfoVO;
import com.trs.police.fight.domain.vo.*;
import com.trs.police.fight.helper.CollaborationTypeHelper;
import com.trs.police.fight.helper.FightSendDwdMessageHelper;
import com.trs.police.fight.mapper.*;
import com.trs.police.fight.properties.FeedbackRefreshProperties;
import com.trs.police.fight.service.*;
import com.trs.police.fight.service.collaboration.SpbExportServiceFactory;
import com.trs.police.fight.service.collaboration.action.ApprovalAction;
import com.trs.police.fight.service.collaboration.impl.CollaborationProcessServiceImpl;
import com.trs.police.fight.service.impl.relation.CollaborationUserRelationImpl;
import com.trs.police.fight.sync.constant.DataSource;
import com.trs.police.fight.sync.service.YsPermissionService;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.common.core.constant.DwdServiceCodeConstant.DWD_HECHENG_COLLABORATION;
import static com.trs.police.common.core.constant.enums.CollaborationStatusEnum.APPROVING;
import static com.trs.police.common.core.constant.enums.CollaborationStatusEnum.INIT;
import static com.trs.police.fight.constant.CollaborationConstant.*;
import static com.trs.police.fight.constant.CollaborationConstants.COLLABORATION_TYPE;
import static com.trs.police.fight.constant.enums.JzQueryTaskStatusEnum.PUSHED;
import static com.trs.police.fight.constant.enums.JzQueryTaskStatusEnum.RUNNING;

/**
 * @author: dingkeyu
 * @date: 2024/03/07
 * @description: 警务协作服务层实现
 */
@Slf4j
@Service
public class CollaborationServiceImpl implements CollaborationService {

    @Resource
    private CollaborationMapper collaborationMapper;

    @Resource
    private CollaborationApprovalMapper approvalMapper;

    @Resource
    private CollaborationRelationSubjectService collaborationRelationSubjectService;

    @Resource
    private FightCompositeCollaborationRelationMapper fightCompositeCollaborationRelationMapper;

    @Resource
    private CollaborationCaseRelationMapper collaborationCaseRelationMapper;

    @Resource
    private CollaborationUserRelationMapper collaborationUserRelationMapper;

    @Resource
    private List<CommonCollaborationService> commonCollaborationServiceList;

    @Resource
    private CollaborationTimeAxisService collaborationTimeAxisService;

    @Autowired
    private CompositeCollaborationMpService compositeCollaborationMpService;

    @Autowired
    private CollaborationApprovalMapper collaborationApprovalMapper;

    @Resource
    private CollaborationFeedbackMapper collaborationFeedbackMapper;

    @Autowired
    private CollaborationAppraiseMpService collaborationAppraiseMpService;

    @Autowired
    private CollaborationProcessServiceImpl collaborationProcessService;

    @Autowired
    private ApprovalAction approvalAction;

    @Resource
    private ApprovalService approvalService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private FightSendDwdMessageHelper fightSendDwdMessageHelper;

    @Resource
    private CompositeService compositeService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private CollaborationSceneRelationMapper collaborationSceneRelationMapper;

    @Autowired
    private CollaborationMpService collaborationMpService;

    @Autowired
    private DictService dictService;

    @Autowired
    private DeptMapper deptMapper;

    @Autowired
    private CollaborationDefaultDeptMapper collaborationDefaultDeptMapper;

    @Autowired
    private CollaborationJqMapper collaborationJqMapper;

    @Autowired
    private CollaborationUserRelationMpService collaborationUserRelationMpService;

    @Autowired
    private FeedbackRefreshContentMapper feedbackRefreshContentMapper;

    @Autowired
    private CollaborationUserRelationImpl collaborationUserRelation;

    @Autowired
    private YsPermissionService ysPermissionService;

    @Autowired
    private FeedbackRefreshProperties feedbackRefreshProperties;

    @Autowired
    private JzQueryTaskMapper jzQueryTaskMapper;

    @Autowired
    private JzService jzService;

    @Autowired
    private CollaborationJzService collaborationJzService;

    @Autowired
    private CollaborationTypeHelper collaborationTypeHelper;

    @Autowired
    private SpbExportServiceFactory spbExportServiceFactory;

    @Autowired
    private SpbService spbService;

    private Map<Long, CommonCollaborationService> collaborationMap = new HashMap<>();

    private Map<String, Integer> approvalMap = new HashMap<>();

    private Map<Integer, String> xzczryMap = new HashMap<>();

    private Map<Integer, String> identityMap = new HashMap<>();

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        for (CommonCollaborationService service : commonCollaborationServiceList) {
            collaborationMap.put(service.getSearchType().getCode(), service);
        }
        approvalMap.put("已同意", APPROVAL_PASS);
        approvalMap.put("已驳回", APPROVAL_REJECT);
        approvalMap.put("待审批", APPROVAL_NOT_REVIEW);
        xzczryMap = dictService.commonSearch("t_collaboration_xzczry", null, null, null).stream()
                        .collect(Collectors.toMap(Dict2VO::getId, Dict2VO::getName));
        identityMap = dictService.commonSearch("t_collaboration_rysf", null, null, null).stream()
                        .collect(Collectors.toMap(Dict2VO::getId, Dict2VO::getName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Collaboration createCollaboration(CollaborationDto dto) {
        CurrentUser user = AuthHelper.getCurrentUser();
        // 构造协作DO并插入数据库
        Collaboration collaboration = CollaborationDto.collaboration(dto);
        collaboration.setStatus(INIT);
        collaboration.setTaskNumber(generateTaskNumber(user.getDept().getDistrictCode(), LocalDateTime.now()));
        collaborationMapper.insert(collaboration);
        // 如果有环境信息 构造全局唯一id
        Optional<DataSource> dataSource = ysPermissionService.tryGetLocalDataSource();
        if (dataSource.isPresent()) {
            collaboration.setDataSource(dataSource.get().getCode());
            collaboration.setSourcePrimaryKey(collaboration.getId().toString());
            collaborationMapper.updateById(collaboration);
        }
        dto.setCollaborationId(collaboration.getId());
        // 添加关联关系
        setRelation(dto, collaboration);
        // 根据是否需要审核来执行不同的状态扭转
        CollaborationStatusEnum newStatue = approvalAction.needApproval(dto.getCollaborationType())
                ? collaborationProcessService.submitReview(dto)
                : collaborationProcessService.endReview(collaboration.getStatus(), collaboration);
        collaborationProcessService.changeStatue(collaboration, newStatue);
        // 一般协作直接发送业务档案消息
        if (!approvalAction.needApproval(dto.getCollaborationType())) {
            // 发送协作业务档案消息
            Collaboration collaboration1 = collaborationMapper.getCollaborationById(collaboration.getId());
            collaboration.setRelatedCase(collaboration1.getRelatedCase());
            collaboration.setRelatedComposite(collaboration1.getRelatedComposite());
            fightSendDwdMessageHelper.sendDwdMessage(collaboration, DWD_HECHENG_COLLABORATION, "insert", "collaboration_id",
                    (map) -> {
                        map.put("status", collaboration.getStatus().getCode());
                        map.put("attachments", JSON.toJSONString(collaboration.getAttachments()));
                    });
            collaborationProcessService.pushThird(collaboration);
        }
        // 时间轴记录
        collaborationTimeAxisService.addTimeAxis(collaboration.getId(), CollaborationOperateEnum.ADD_COLLABORATION, collaboration.getId());
        return collaboration;
    }

    @Override
    public PageResult<CollaborationVO> collaborationList(CollaborationListRequest collaborationParams) {
        CommonCollaborationService service = collaborationMap.get(collaborationParams.getType());
        if (Objects.isNull(service)) {
            throw new TRSException("无法匹配的检索类型");
        }
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException("无法获取当前用户信息！");
        }
        // 理论上 能看到该页面就有协作权限
        collaborationParams.setHasCollaborationPermission(true);
        PageResult<CollaborationVO> pageResults = service.getCollaborationList(collaborationParams, currentUser);
        if (CollectionUtils.isEmpty(pageResults.getItems())) {
            return PageResult.empty(collaborationParams.getPageParams());
        }
        buildCollaborationVO(pageResults.getItems());
        return pageResults;
    }

    @Override
    public CollaborationVO collaborationDetail(Long id, Boolean userNullAble) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser) && !Boolean.TRUE.equals(userNullAble)) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        PreConditionCheck.checkArgument(Objects.nonNull(id), "协作id不能为空");
        Collaboration collaboration = collaborationMapper.selectById(id);
        CollaborationVO collaborationVO = CollaborationVO.of(collaboration);
        buildCollaborationVO(Arrays.asList(collaborationVO));
        // 发起人信息
        collaborationVO.setStartUserId(collaboration.getCreateUserId());
        // 标为已读
        if (!Boolean.TRUE.equals(userNullAble)) {
            markReadCollaboration(currentUser, collaboration);
        }
        // 如果是紧急警情。添加紧急警情信息
        if (CollaborationFormType.SIX.getCode().equals(collaborationVO.getFormOrder())) {
            buildCollaborationJqVO(collaborationVO);
        }
        return collaborationVO;
    }

    @Override
    public CollaborationUnreadVO unreadCollaborationCount(Long type) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new TRSException("无法获取当前用户信息！");
        }
        CollaborationUnreadVO collaborationUnreadVO = new CollaborationUnreadVO();
        if (Objects.nonNull(type)) {
            CommonCollaborationService service = collaborationMap.get(type);
            if (Objects.isNull(service)) {
                throw new TRSException("无法匹配的检索类型");
            }
            Long unreadCount = service.unreadCollaborationCount(currentUser);
            setUnreadCount(CollaborationPersonnelTypeEnum.codeOf(type), unreadCount, collaborationUnreadVO);
            return collaborationUnreadVO;
        }
        for (Map.Entry<Long, CommonCollaborationService> entry : collaborationMap.entrySet()) {
            Long unreadCount = entry.getValue().unreadCollaborationCount(currentUser);
            setUnreadCount(CollaborationPersonnelTypeEnum.codeOf(entry.getKey()), unreadCount, collaborationUnreadVO);
        }
        return collaborationUnreadVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void relateFight(Long compositeId, Long collaborationId) {
        Objects.requireNonNull(compositeId, "作战id不能为空");
        Objects.requireNonNull(collaborationId, "协作id不能为空");
        // 由于只能关联一个作战 所以已经关联的给删掉
        List<Long> savedIds = compositeCollaborationMpService.lambdaQuery()
                .eq(CompositeCollaborationRelation::getCollaborationId, collaborationId)
                .list()
                .stream()
                .map(CompositeCollaborationRelation::getId)
                .collect(Collectors.toList());
        compositeCollaborationMpService.removeBatchByIds(savedIds);
        // 重新关联
        CompositeCollaborationRelation collaborationRelation = new CompositeCollaborationRelation(collaborationId, compositeId);
        compositeCollaborationMpService.save(collaborationRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCollaboration(CollaborationDto dto) {
        PreConditionCheck.checkArgument(Objects.nonNull(dto.getCollaborationId()), "协作id不能为空");
        Collaboration collaboration = collaborationMapper.selectById(dto.getCollaborationId());
        // 构造协作DO
        Collaboration newCollaboration = CollaborationDto.collaboration(dto);
        newCollaboration.setId(dto.getCollaborationId());
        newCollaboration.setStatus(collaboration.getStatus());
        newCollaboration.setIsUrged(collaboration.getIsUrged());
        newCollaboration.setIsNeedSms(dto.getIsNeedSms());
        collaborationMapper.updateById(newCollaboration);
        // 删除关联关系
        deleteRelation(dto.getCollaborationId());
        // 新建关联关系
        setRelation(dto, newCollaboration);
    }

    @Override
    public List<ReviewInfoVO> reviewInfo(Long collaborationId, String status) {
        List<ApprovalInfoVO> approvals = approvalService.getApprovals(OperateModule.COMPOSITE.getCode(), collaborationId, Operation.COLLABORATION_APPROVAL.getCode().toString());
        //PreConditionCheck.checkArgument(!CollectionUtils.isEmpty(approvals), "未找到审批信息");
        if (CollectionUtils.isEmpty(approvals)) {
            return new ArrayList<>();
        }
        ApprovalDetailVO approval = approvalService.getApproval(approvals.get(0).getApprovalId());
        List<ReviewInfoVO> reviewInfoVOList = approval.getNodes().stream()
                .map(node -> {
                    // 构造单个节点（选取第1个审批人作为该节点展示的审批人）
                    ReviewInfoVO reviewInfo = buildReviewInfo(node);
                    if (Objects.isNull(reviewInfo)) {
                        return null;
                    }
                    reviewInfo.setNodeName(node.getName());
                    Optional.ofNullable(node.getCommentDetails())
                            .orElse(new ArrayList<>())
                            .stream()
                            .map(ApprovalDetailVO.ApproverCommentDetail::getComment)
                            .filter(StringUtils::isNotEmpty)
                            .findAny()
                            .ifPresent(comment -> reviewInfo.setComment(comment));
                    // 当前节点全部审批人

                    List<UserVO> approver = node.getCommentDetails().stream()
                            .map(ApprovalDetailVO.ApproverCommentDetail::getApprover)
                            .map(user -> {
                                UserVO userDeptVO = new UserVO();
                                userDeptVO.setName(user.getName());
                                userDeptVO.setDeptId(user.getDeptId());
                                userDeptVO.setDeptName(null);
                                return userDeptVO;
                            })
                            .collect(Collectors.toList());
                    // 匹配部门名称
                    List<Long> dptIds = approver.stream()
                            .map(UserVO::getDeptId)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList());
                    List<DeptDto> dept = CollectionUtils.isEmpty(dptIds)
                            ? new ArrayList<>() : permissionService.getDeptByIds(dptIds);
                    approver.forEach(user -> dept.stream()
                            .filter(d -> d.getId().equals(user.getDeptId()))
                            .findAny()
                            .map(DeptDto::getName)
                            .ifPresent(user::setDeptName)
                    );
                    reviewInfo.setAllApproveUser(approver);
                    return reviewInfo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 组织信息赋值
        addUserInfo(reviewInfoVOList);
        // 其它信息赋值
        for (ReviewInfoVO vo : reviewInfoVOList) {
            if (Objects.isNull(vo.getApprovalResult())) {
                vo.setApprovalResult(APPROVAL_NOT_REVIEW);
            }
        }
        // 过滤
        List<ReviewInfoVO> vos = reviewInfoVOList;
        if (StringUtils.isNotEmpty(status)) {
            Set<Integer> allowStatus = Stream.of(status.split(",|;"))
                    .map(Integer::valueOf)
                    .collect(Collectors.toSet());
            vos = reviewInfoVOList.stream()
                    .filter(vo -> allowStatus.contains(vo.getApprovalResult()))
                    .collect(Collectors.toList());
        }
        return vos;
    }

    @Override
    public List<ReviewInfoVO> spbReviewInfo(Long collaborationId, String status, String type) {
        Collaboration collaboration = collaborationMpService.getById(collaborationId);
        Optional<CollaborationType> typeEnum = collaborationTypeHelper.getTypeEnumById(collaboration.getCollaborationType());
        List<ReviewInfoVO> vos = reviewInfo(collaborationId, status);
        if ("spb".equals(type)) {
            // 根据节点类型获取虚拟节点配置
            String key = typeEnum.isPresent() && CollaborationType.URGENT_ALERT.equals(typeEnum.get())
                    ? "fight.collaboration.spb.review"
                    : null;
            return spbService.generateSpb(vos, key);
        }
        if ("zld".equals(type)) {
            return spbService.generateZld(vos, null);
        }
        return vos;
    }

    private ReviewInfoVO buildReviewInfo(ApprovalDetailVO.Node node) {
        if ("发起申请".equals(node.getStatus())) {
            return null;
        }
        List<ApprovalDetailVO.ApproverCommentDetail> commentDetails = node.getCommentDetails();
        // 如果是待审批 则后面的审批流程不返回
        if ("待审批".equals(node.getStatus())) {
            if (!CollectionUtils.isEmpty(commentDetails)) {
                return detailToReviewInfoVO(commentDetails.get(0), node);
            }
            return null;
        } else {
            List<ApprovalDetailVO.ApproverCommentDetail> details = commentDetails.stream()
                    .filter(detail -> !"待审批".equals(detail.getResult()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(details)) {
                return detailToReviewInfoVO(details.get(0), node);
            }
        }
        return null;
    }

    @Override
    public CollaborationVO approveFormInfo(Long collaborationId) {
        // 查询协作详情
        CollaborationVO detailVO = collaborationDetail(collaborationId, true);
        List<ApprovalInfoVO> approvals = approvalService.getApprovals(OperateModule.COMPOSITE.getCode(), collaborationId, Operation.COLLABORATION_APPROVAL.getCode().toString());
        //PreConditionCheck.checkArgument(!CollectionUtils.isEmpty(approvals), "未找到审批信息");
        if (CollectionUtils.isEmpty(approvals)) {
            return detailVO;
        }
        ApprovalDetailVO approval = approvalService.getApproval(approvals.get(0).getApprovalId());
        // 发起审核信息
        List<ApprovalDetailVO.Node> approvalNode = approval.getNodes().stream()
                .filter(node -> !"发起申请".equals(node.getStatus())
                        && node.getIsSampleOrg() == 1)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(approvalNode) && approvalNode.size() > 1) {
            // 提请单位审核意见
            List<ApprovalDetailVO.ApproverCommentDetail> details = approvalNode.get(0).getCommentDetails().stream()
                    .filter(node -> !"待审批".equals(node.getResult()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(details)) {
                ApprovalDetailVO.ApproverCommentDetail detail = details.get(0);
                detailVO.setStartApprovalInfo(detail.getComment());
                detailVO.setStartApprovalTime(detail.getFullTime());
                detailVO.setStartApprovalUserName(detail.getApprover().getName());
            }
            // 提请单位负责人审核意见
            List<ApprovalDetailVO.ApproverCommentDetail> details1 = approvalNode.get(1).getCommentDetails().stream()
                    .filter(node -> !"待审批".equals(node.getResult()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(details1)) {
                ApprovalDetailVO.ApproverCommentDetail detail1 = details1.get(0);
                detailVO.setStartParentApprovalInfo(detail1.getComment());
                detailVO.setStartParentApprovalTime(detail1.getFullTime());
                detailVO.setStartParentApprovalUserName(detail1.getApprover().getName());
            }
        } else if (!CollectionUtils.isEmpty(approvalNode)) {
            List<ApprovalDetailVO.ApproverCommentDetail> details = approvalNode.get(0).getCommentDetails().stream()
                    .filter(node -> !"待审批".equals(node.getResult()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(details)) {
                ApprovalDetailVO.ApproverCommentDetail detail = details.get(0);
                detailVO.setStartApprovalInfo(detail.getComment());
                detailVO.setStartApprovalTime(detail.getFullTime());
                detailVO.setStartApprovalUserName(detail.getApprover().getName());
                detailVO.setStartParentApprovalInfo(detail.getComment());
                detailVO.setStartParentApprovalTime(detail.getFullTime());
                detailVO.setStartParentApprovalUserName(detail.getApprover().getName());
            }
        }

        // 协作审核信息
        List<ApprovalDetailVO.Node> collaborationNode = approval.getNodes().stream()
                .filter(node -> !"发起申请".equals(node.getStatus())
                        && node.getIsSampleOrg() == 0)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collaborationNode)) {
            List<ApprovalDetailVO.ApproverCommentDetail> details = collaborationNode.get(0).getCommentDetails().stream()
                    .filter(node -> !"待审批".equals(node.getResult()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(details)) {
                ApprovalDetailVO.ApproverCommentDetail detail = details.get(0);
                detailVO.setCollaborationApprovalInfo(detail.getComment());
                detailVO.setCollaborationApprovalTime(detail.getFullTime());
            }
        }
        // 反馈信息
        List<CollaborationFeedback> feedbacks = collaborationFeedbackMapper.selectList(new QueryWrapper<CollaborationFeedback>()
                .eq("collaboration_id", detailVO.getId())
                .orderByDesc("create_time"));
        if (!CollectionUtils.isEmpty(feedbacks)) {
            CollaborationFeedback feedback = feedbacks.get(0);
            detailVO.setFeedbackInfo(feedback.getContent());
        }
        return detailVO;
    }

    @Override
    public void uploadCollaborationFile(ApprovalParams params) {
        Collaboration collaboration = collaborationMapper.selectById(params.getCollaborationId());
        PreConditionCheck.checkArgument(Objects.nonNull(collaboration), "协作不存在");
        collaboration.setAttachments(params.getAttachments());
        collaborationMapper.updateById(collaboration);
    }

    @Override
    public Collaboration getCollaborationById(Long collaborationId) {
        PreConditionCheck.checkArgument(Objects.nonNull(collaborationId), "协作id不能为空");
        return collaborationMapper.selectById(collaborationId);
    }

    private void buildCollaborationJqVO(CollaborationVO detailVO) {
        CollaborationJq collaborationJq = collaborationJqMapper.selectOne(
                Wrappers.lambdaQuery(CollaborationJq.class)
                        .eq(CollaborationJq::getCollaborationId, detailVO.getId())
        );
        CollaborationJqVO collaborationJqVO = JqCollaborationConvertor.CONVERTER.doToVo(collaborationJq);
        collaborationJqVO.setJqlbmc(xzczryMap.get(Integer.valueOf(collaborationJq.getJqlbdm())));
        collaborationJqVO.getAssistancePerson().forEach(person -> {
            person.setIdentityName(identityMap.get(Integer.valueOf(person.getIdentity())));
            person.setPersonCategoryName(xzczryMap.get(Integer.valueOf(person.getPersonCategoryCode())));
        });
        StringBuilder assistancePersonsInfo = new StringBuilder();
        for (JqAssistancePerson person : collaborationJqVO.getAssistancePerson()) {
            assistancePersonsInfo.append(person.getName())
                    .append("（手机号：")
                    .append(person.getTel())
                    .append("）、");
        }
        // 移除最后一个多余的分号
        if (assistancePersonsInfo.length() > 0) {
            assistancePersonsInfo.setLength(assistancePersonsInfo.length() - 1);
        }
        String personCategoryName = collaborationJqVO.getAssistancePerson()
                .stream().map(JqAssistancePerson::getPersonCategoryName)
                .distinct()
                .collect(Collectors.joining(","));
        // 构造指令内容
        String content = String.format("技侦支队：\n" + "%s\n\n" +"先期处置情况：\n"+"%s\n"+
                "\n" +
                "依据《公安JZ部门参与处置紧急警情工作规则》对查询%s类警情的%s类人员规定，请你支队对以下人员及关联手机号进行定位查找%s。" +
                "联系人：%s %s，电话：%s。\n" +
                "【提示：本内容严禁原文转发和通过互联网及微信等即时聊天工具流转】",
                collaborationJqVO.getJqJyQk(),collaborationJqVO.getXqCzQk(),collaborationJqVO.getJqlbmc(),
                personCategoryName,assistancePersonsInfo.toString(),
                detailVO.getStartDeptName(), collaborationJqVO.getLxMjXm(), collaborationJqVO.getLxMjTel());
        collaborationJqVO.setInstructionContent(content);
        // 构造查找人员或目标情况
        List<String> findPersons = collaborationJqVO.getAssistancePerson()
                .stream()
                .map(vo -> String.format("%s,%s,%s,%s,%s,%s,%s,%s;",
                        vo.getName(), vo.getSex(), vo.getAge(), vo.getIdentityName(), "非特殊对象",
                        vo.getPersonCategoryName(), vo.getTel(), vo.getBjrRelation()))
                .collect(Collectors.toList());
        collaborationJqVO.setFindPersonOrTarget(String.join("\n", findPersons));
        detailVO.setCollaborationJq(collaborationJqVO);

        // 反馈刷新情况
        Long ct = jzQueryTaskMapper.selectCount(
                Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                        .eq(JzQueryTaskEntity::getCollaborationId, detailVO.getId())
                        .in(JzQueryTaskEntity::getStatus, Arrays.asList(PUSHED.getCode(), RUNNING.getCode()))
        );
        detailVO.setLastJzFeedbackIsFinish(ct == 0L);
        List<JzQueryTaskEntity> jzQueryTaskEntities = jzQueryTaskMapper.selectList(
                Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                        .eq(JzQueryTaskEntity::getCollaborationId, detailVO.getId())
                        .isNotNull(JzQueryTaskEntity::getFinishTime)
                        .orderByDesc(JzQueryTaskEntity::getFinishTime)
        );
        if (!CollectionUtils.isEmpty(jzQueryTaskEntities)) {
            String time = jzQueryTaskEntities.get(0).getFinishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            detailVO.setLastJzFeedbackRefreshTime(time);
        }
    }

    private void addUserInfo(List<ReviewInfoVO> vos) {
        // 找到用户信息
        final PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        List<Long> ids = vos.stream().map(ReviewInfoVO::getRelatedUserId).collect(Collectors.toList());
        List<UserDto> userListById = permissionService.getUserListById(ids);
        Map<Long, UserDto> idAndUser = userListById.stream()
                .collect(Collectors.toMap(UserDto::getId, usr -> usr, (u1, u2) -> u1));
        // 为用户赋值
        for (ReviewInfoVO vo : vos) {
            UserDto userDto = idAndUser.get(vo.getRelatedUserId());
            if (Objects.isNull(userDto)) {
                continue;
            }
            vo.setDuty(userDto.getDuty());
            vo.setRelatedUserRealName(userDto.getRealName());
            userDto.getDeptList().stream()
                    .filter(dp -> Objects.equals(dp.getId(), vo.getRelatedUserDeptId()))
                    .findAny()
                    .ifPresent(dp -> vo.setRelatedUserDeptName(dp.getName()));
        }
    }

    /**
     * 设置关联关系
     *
     * @param dto           协作dto
     * @param collaboration 协作DO
     */
    public void setRelation(CollaborationDto dto, Collaboration collaboration) {
        collaborationRelationSubjectService.createRelation(dto, collaboration);
    }

    /**
     * 删除关联关系
     *
     * @param collaborationId 协作id
     */
    public void deleteRelation(Long collaborationId) {
        collaborationRelationSubjectService.deleteRelation(collaborationId);
    }

    /**
     * 设置未读数
     *
     * @param type 类型
     * @param count 未读数
     * @param vo vo
     */
    private void setUnreadCount(CollaborationPersonnelTypeEnum type, Long count, CollaborationUnreadVO vo) {
        switch (type) {
            case ALL :
                vo.setAllUnreadCount(count);
                break;
            case ME_START:
                vo.setMeStartUnreadCount(count);
                break;
            case ME_APPROVED:
                vo.setMeApprovedUnreadCount(count);
                break;
            case ME_PROCESSED:
                vo.setMeProcessedUnreadCount(count);
                break;
            default:
                break;
        }
    }

    /**
     * 数据处理
     *
     * @param collaborationVos 协作list
     */
    private void buildCollaborationVO(List<CollaborationVO> collaborationVos) {
        if (CollectionUtils.isEmpty(collaborationVos)) {
            return;
        }
        // 反查作战表
        List<Long> collaborationIds = collaborationVos.stream().map(CollaborationVO::getId).collect(Collectors.toList());
        List<CollaborationVO> relations = fightCompositeCollaborationRelationMapper.getCollaborationsByIds(collaborationIds);
        Map<Long, CollaborationVO> map = relations.stream().collect(Collectors.toMap(CollaborationVO::getId,
                e -> e, (v1, v2) -> v2));
        // 反查协作类型码表
        Function<CollaborationVO, Optional<Dict2VO>> findType = DataUtils.findSingleByT(
                collaborationVos,
                vos -> BeanUtil.getBean(DictService.class).commonSearch(COLLABORATION_TYPE, null, null,null),
                (vo, dict) -> Objects.equals(vo.getCollaborationType(), Long.valueOf(dict.getId().longValue())),
                CollaborationVO::getId
        );
        // 反查提请类型码表
        Map<Long, String> requestTypeMap = getCollaborationTypeMapping("request_type");
        // 反查查询类别码表
        Map<Long, String> queryCategoryMap = getCollaborationTypeMapping("query_category", "query_category_jz");
        // 反查数据类型码表
        Map<Long, String> queryDataTypeMap = getCollaborationTypeMappingV2("collaboration_query_data_type");
        // 反查使用目的
        Map<Long, String> userPurposeTypeMap = getCollaborationTypeMappingV2("user_purpose_type");
        // 反查使用目的-其他类型信息
        QueryWrapper<CollaborationSceneRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("collaboration_id", collaborationIds);
        queryWrapper.eq("related_type", CollaborationRelatedTypeEnum.USER_PURPOSE.getCode());
        Map<Long, String> userPurposeOtherInfoMap = collaborationSceneRelationMapper.selectList(queryWrapper).stream()
                .collect(Collectors.toMap(
                        CollaborationSceneRelation::getCollaborationId, CollaborationSceneRelation::getRelatedInfo));
        // 反查密级
        Map<Long, String> securityTypeMap = getCollaborationTypeMappingV2("security_type");
        // 查询发起人和发起单位
        List<CollaborationUserRelation> relationList = collaborationUserRelationMapper.selectList(new QueryWrapper<CollaborationUserRelation>()
                .eq("personnel_type", CollaborationPersonnelTypeEnum.ME_START.getCode())
                .in("collaboration_id", collaborationIds));
        Map<Long, CollaborationUserRelation> userRelationMap = relationList.stream()
                .collect(Collectors.toMap(CollaborationUserRelation::getCollaborationId, e -> e));
        // 反查关联案件
        List<CollaborationCaseListVO> events = collaborationCaseRelationMapper.getCaseEventByCollaborationId(collaborationIds);
        Map<Long, List<CollaborationCaseListVO>> eventMap = events.stream().collect(Collectors.groupingBy(CollaborationCaseListVO::getCollaborationId));
        // 关联场景
        List<CollaborationSceneRelation> sceneRelationList = collaborationSceneRelationMapper.selectList(new QueryWrapper<CollaborationSceneRelation>()
                .in("collaboration_id", collaborationIds));
        Map<Long, List<CollaborationSceneRelation>> sceneMap = sceneRelationList.stream().collect(Collectors.groupingBy(CollaborationSceneRelation::getCollaborationId));
        // 反查紧急警情字段
        List<CollaborationJq> jqList = collaborationJqMapper.selectList(new QueryWrapper<CollaborationJq>()
                .in("collaboration_id", collaborationIds));
        Map<Long, CollaborationJq> jqMap = jqList.stream().collect(Collectors.toMap(CollaborationJq::getCollaborationId, Function.identity()));
        collaborationVos.forEach(e -> {
            e.setSubmitTypeName(requestTypeMap.get(e.getSubmitType()));
            e.setSecurityTypeName(securityTypeMap.get(e.getSecurityType()));
            findType.apply(e).ifPresent(dict -> {
                e.setCollaborationTypeName(dict.getName());
                e.setDictDesc(dict.getDictDesc());
            });
            collaborationTypeHelper.getTypeCodeById(e.getCollaborationType()).ifPresent(e::setTypeCode);
            e.setQueryCategoryName(queryCategoryMap.get(e.getQueryCategory()));
            if (StringUtils.isNotEmpty(e.getQueryResources())){
                String queryResources = Arrays.stream(e.getQueryResources().split(","))
                        .map(Long::valueOf)
                        .map(queryDataTypeMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));
                e.setQueryDataTypeName(queryResources);
            }
            String userPurposeTypeName = null;
            if (StringUtils.isNotEmpty(e.getUserPurposeType())){
                 userPurposeTypeName = Arrays.stream(e.getUserPurposeType().split(","))
                        .map(Long::valueOf)
                        .map(userPurposeTypeMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));
                e.setUserPurposeTypeName(userPurposeTypeName);
            }
            e.setUserPurposeOtherInfo(userPurposeOtherInfoMap.getOrDefault(e.getId(),null));
            CollaborationVO collaborationVO = map.get(e.getId());
            if (Objects.nonNull(collaborationVO)) {
                e.setCompositeId(collaborationVO.getCompositeId());
                e.setCompositeTitle(collaborationVO.getCompositeTitle());
            }
            CollaborationUserRelation relation = userRelationMap.get(e.getId());
            if (Objects.nonNull(relation)) {
                e.setStartUserName(relation.getRelatedUserName());
                e.setStartDeptName(relation.getRelatedUserDeptName());
                e.setStartDeptCode(permissionService.getDeptById(relation.getRelatedUserDeptId()).getCode());
                e.setStartDept(relation.getRelatedUserDeptId());
                e.setStartDeptName(relation.getRelatedUserDeptName());
            }
            // 计算反馈倒计时
            if (!CollaborationStatusEnum.FEEDBACK.equals(e.getStatus()) && Objects.nonNull(e.getFeedbackTime())) {
                long daysBetweenDates = TimeUtil.getDaysBetweenDates(
                        LocalDateTime.now().toLocalDate(),
                        TimeUtil.stringToLocalDateTime(e.getFeedbackTime(), TimeUtils.YYYYMMDD_HHMMSS).toLocalDate());
                e.setCountDown(daysBetweenDates <= 0L ? daysBetweenDates - 1L : daysBetweenDates);
            }
            e.setEvents(eventMap.getOrDefault(e.getId(), new ArrayList<>()));
            List<CollaborationSceneRelation> sceneRelations = sceneMap.get(e.getId());
            if (!CollectionUtils.isEmpty(sceneRelations)) {
                Map<Integer, List<CollaborationSceneRelation>> eMap = sceneRelations.stream().collect(Collectors.groupingBy(CollaborationSceneRelation::getRelatedType));
                e.setClues(JSONArray.parseArray(getRelatedScenes(eMap, CollaborationRelatedTypeEnum.RELATED_CLUE.getCode()), FightClueVO.class));
                e.setPoliceIntelligenceInfo(JSONArray.parseArray(getRelatedScenes(eMap, CollaborationRelatedTypeEnum.RELATED_JQ.getCode()), JqListVO.class));
                e.setPersons(JSONArray.parseArray(getRelatedScenes(eMap, CollaborationRelatedTypeEnum.RELATED_PERSON.getCode()), PersonCaseVO.class));
                e.setWarningInfo(JSONArray.parseArray(getRelatedScenes(eMap, CollaborationRelatedTypeEnum.RELATED_WARNING.getCode()), WarningListVO.class));
                List<CollaborationSceneRelation> collaborationSceneRelations = eMap.getOrDefault(CollaborationRelatedTypeEnum.SIGNATURE_MESSAGE.getCode(),new ArrayList<>());
                if (!CollectionUtils.isEmpty(collaborationSceneRelations)) {
                    e.setSignatureMessage(collaborationSceneRelations.get(0).getRelatedInfo());
                }
                Optional<DyCollaborationFrom> dy = eMap.getOrDefault(CollaborationRelatedTypeEnum.DY.getCode(), new ArrayList<>())
                        .stream()
                        .findFirst()
                        .map(CollaborationSceneRelation::getRelatedInfo)
                        .map(str -> JSONObject.parseObject(str, DyCollaborationFrom.class));
                dy.ifPresent(e::setDyFrom);
            }
            CollaborationJq collaborationJq = jqMap.get(e.getId());
            if (Objects.nonNull(collaborationJq)) {
                CollaborationJqVO jqVO = JqCollaborationConvertor.CONVERTER.doToVo(collaborationJq);
                e.setCollaborationJq(jqVO);
            }
        });

        // 添加评价
        Map<Long, List<CollaborationAppraise>> appraiseById = collaborationAppraiseMpService.lambdaQuery()
                .in(CollaborationAppraise::getCollaborationId, collaborationIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(CollaborationAppraise::getCollaborationId));
        for (CollaborationVO vo : collaborationVos) {
            if (Objects.nonNull(appraiseById.get(vo.getId()))) {
                vo.setCollaborationAppraise(appraiseById.get(vo.getId()).get(0));
            }
        }
        // 反查表单类型
        Map<Long, CollaborationFormType> formTypeMap = collaborationQfMap();
        for (CollaborationVO vo : collaborationVos) {
            Integer formOrder = Objects.nonNull(vo.getCollaborationType()) && formTypeMap.containsKey(vo.getCollaborationType())
                    ? formTypeMap.get(vo.getCollaborationType()).getCode()
                    : CollaborationFormType.COMMON_FROM.getCode();
            vo.setFormOrder(formOrder);
        }
        // 反查协理人
        List<CollaborationUserRelation> assistantList = collaborationUserRelationMapper.selectList(new QueryWrapper<CollaborationUserRelation>()
                .in("collaboration_id", collaborationIds)
                .eq("personnel_type", CollaborationPersonnelTypeEnum.ASSISTANT.getCode()));
        Map<Long, List<CollaborationUserRelation>> assistantMap = assistantList.stream().collect(Collectors.groupingBy(CollaborationUserRelation::getCollaborationId));
        for (CollaborationVO vo : collaborationVos) {
            List<CollaborationUserRelation> userRelationList = assistantMap.get(vo.getId());
            if (!CollectionUtils.isEmpty(userRelationList)) {
                List<UserDeptVO> assistants = userRelationList.stream().map(userRelation -> {
                    UserDeptVO userDeptVO = new UserDeptVO();
                    userDeptVO.setUserId(userRelation.getRelatedUserId());
                    userDeptVO.setUserName(userRelation.getRelatedUserName());
                    userDeptVO.setDeptId(userRelation.getRelatedUserDeptId());
                    userDeptVO.setDeptName(userRelation.getRelatedUserDeptName());
                    return userDeptVO;
                }).collect(Collectors.toList());
                vo.setAssistants(assistants);
            }
        }
    }

    private Map<Long, String> getCollaborationTypeMapping(String... types) {
        DictService dictService = BeanUtil.getBean(DictService.class);
        Map<Long, String> map = new HashMap<>();
        for (String type : types) {
            List<Dict2VO> dictDtos = dictService.commonSearch(type, null, null,null);
            for (Dict2VO dto : dictDtos) {
                map.put(dto.getId().longValue(), dto.getName());
            }

        }
        return map;
    }

    private Map<Long, String> getCollaborationTypeMappingV2(String... types) {
        DictService dictService = BeanUtil.getBean(DictService.class);
        Map<Long, String> map = new HashMap<>();
        for (String type : types) {
            List<Dict2VO> dictDtos = dictService.commonSearch(type, null, null,null);
            for (Dict2VO dto : dictDtos) {
                map.put(dto.getCode(), dto.getName());
            }

        }
        return map;
    }

    private ReviewInfoVO detailToReviewInfoVO(ApprovalDetailVO.ApproverCommentDetail detail, ApprovalDetailVO.Node node) {
        ReviewInfoVO reviewInfo = new ReviewInfoVO();
        reviewInfo.setApplyTime(detail.getFullTime());
        reviewInfo.setReason(detail.getComment());
        reviewInfo.setAttachments(JSON.toJSONString(detail.getAttachments()));
        reviewInfo.setRelatedUserId(detail.getApprover().getUserId());
        reviewInfo.setRelatedUserDeptId(detail.getApprover().getDeptId());
        reviewInfo.setApprovalResult(approvalMap.get(detail.getResult()));
        reviewInfo.setType(detail.getType());
        reviewInfo.setReviewTime(detail.getFullTime());
        reviewInfo.setConfigId(node.getConfigId());
        return reviewInfo;
    }

    @Override
    public CollaborationVO convertToFight(Long id) {
        Collaboration collaboration = collaborationMapper.selectById(id);
        CollaborationStatusEnum status = collaboration.getStatus();
        if (INIT.equals(status) || APPROVING.equals(status)) {
            throw new TRSException("当前协作未审批通过，无法转为作战");
        }
        // 发起合成作战
        CreateCompositeVO vo = new CreateCompositeVO();
        vo.setTitle(collaboration.getTitle());
        vo.setRequire("--");
        vo.setTypeId(BeanFactoryHolder.getEnv().getProperty("composite.collaboration.default.fight.type", Long.class, 1L));
        vo.setSubtypeId(BeanFactoryHolder.getEnv().getProperty("composite.collaboration.default.fight.subtype", Long.class, 21L));
        vo.setCaseTag(new ArrayList<>());
        vo.setIsClassified(false);
        vo.setPartners(getPartners(collaboration));
        FightComposite fightComposite = compositeService.createCompositeNotApproval(vo);
        // 关联到合成作战
        this.relateFight(fightComposite.getId(), id);
        return collaborationDetail(id, false);
    }

    @Override
    public List<QueryCategoryVO> getQueryCategory(Long pid, String type, Long collaborationTypeId) {
        List<QueryCategoryVO> list = Optional.ofNullable(dictService.commonSearch(type, pid, null, null)).orElse(new ArrayList<>())
                .stream()
                .map(CollaborationConvertor.CONVERTER::dictToQcVO)
                .collect(Collectors.toList());
        // 如果配置了默认表单、添加默认表单（即前端表填默认填写内容、非必要项）
        List<Dict2VO> defaultContents = dictService.commonSearch("collaboration_default_content", 0L, null, null);
        for (QueryCategoryVO categoryVO : list) {
            defaultContents.stream()
                    .filter(dc -> String.format("%s-%s", collaborationTypeId, categoryVO.getId()).equals(dc.getName()))
                    .findAny()
                    .ifPresent(dc -> categoryVO.setDefaultContent(dc.getDictDesc()));
        }
        // 根据配置过滤查询类别
        DictDto map = dictService.getDictByTypeAndName("collaboration_category_qc_map", String.valueOf(collaborationTypeId));
        if (Objects.isNull(map)) {
            return list;
        }
        List<Integer>   queryCategoryIds = JSONArray.parseArray(map.getDictDesc(), Integer.class);
        if (CollectionUtils.isEmpty(queryCategoryIds)) {
            return new ArrayList<>();
        }
        List<QueryCategoryVO> result = queryCategoryIds
                .stream()
                .map(id -> list.stream().filter(vo -> Objects.equals(vo.getId(), id)).findAny().orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public Integer formOrder(Long collaborationTypeId) {
        // 获取协作类型对应的表单
        if (Objects.isNull(collaborationTypeId)) {
            return CollaborationFormType.COMMON_FROM.getCode();
        }
        Map<Long, CollaborationFormType> formTypeMap = collaborationQfMap();
        return formTypeMap.containsKey(collaborationTypeId)
                ? formTypeMap.get(collaborationTypeId).getCode()
                : CollaborationFormType.COMMON_FROM.getCode();

    }

    @Override
    public DefaultCollaborationDeptVO getDefaultCollaborationDept(Long collaborationTypeId) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        if (currentUser == null) {
            throw new TRSException(ExceptionMessageConstant.CANT_FIND_CURRENT_USER);
        }
        DefaultCollaborationDeptVO defaultCollaborationDeptVO = new DefaultCollaborationDeptVO();
        QueryWrapper<CollaborationDefaultDept> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("collaboration_type_id", collaborationTypeId);
        CollaborationDefaultDept collaborationDefaultDept = collaborationDefaultDeptMapper.selectOne(queryWrapper);
        if (collaborationDefaultDept == null) {
            return defaultCollaborationDeptVO;
        }
        if (collaborationDefaultDept.getDefaultDeptId() != null) {
            // 配置了默认部门id直接返回
            Dept dept = deptMapper.selectById(collaborationDefaultDept.getDefaultDeptId());
            defaultCollaborationDeptVO.setDeptPath(getDeptPath(dept));
            defaultCollaborationDeptVO.setName(dept.getName());
            defaultCollaborationDeptVO.setShortName(dept.getShortName());
        } else {
            // 根据组织类别返回默认部门id
            QueryWrapper<Dept> deptQueryWrapper = new QueryWrapper<>();
            deptQueryWrapper.eq("district_code", currentUser.getDept().getDistrictCode());
            deptQueryWrapper.eq("type", collaborationDefaultDept.getType());
            deptQueryWrapper.eq("deleted", 0);
            deptQueryWrapper.orderByAsc("show_order");
            List<Dept> dept = deptMapper.selectList(deptQueryWrapper);
            if (!CollectionUtils.isEmpty(dept)) {
                defaultCollaborationDeptVO.setDeptPath(getDeptPath(dept.get(0)));
                defaultCollaborationDeptVO.setName(dept.get(0).getName());
                defaultCollaborationDeptVO.setShortName(dept.get(0).getShortName());
            }
        }
        return defaultCollaborationDeptVO;
    }

    @Override
    public String getDefaultFeedbackTime() {
        Integer hour = BeanFactoryHolder.getEnv().getProperty("com.trs.collaboration.feedback.hour", Integer.class, 48);
        String defaultTime = TimeUtils.hourDefOrAft(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS), hour, TimeUtils.YYYYMMDD_HHMMSS);
        return defaultTime;
    }

    @Override
    public List<DeptVO> getCollaborationDept(Long collaborationTypeId) {
        QueryWrapper<CollaborationDefaultDept> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("collaboration_type_id", collaborationTypeId);
        CollaborationDefaultDept collaborationDefaultDept = collaborationDefaultDeptMapper.selectOne(queryWrapper);
        List<DeptVO> deptTreeAll = permissionService.getDeptTreeAll();
        if (collaborationDefaultDept != null && !StringUtils.isEmpty(collaborationDefaultDept.getShowDeptTypes())) {
            List<String> typeList = Arrays.asList(collaborationDefaultDept.getShowDeptTypes().split(","));
            List<DeptVO> childrenList = buildDeptVO(deptTreeAll.get(0).getChildren(), typeList, collaborationDefaultDept.getDefaultDeptId());
            deptTreeAll.get(0).setChildren(childrenList);
        }
        return deptTreeAll;
    }

    @Override
    public void addAssistants(Long collaborationId, List<UserDeptVO> assistantsVO) {
        // 查询协作已有的协理人 进行排重
        List<CollaborationUserRelation> userRelationList = collaborationUserRelationMapper.selectList(new QueryWrapper<CollaborationUserRelation>()
                .eq("collaboration_id", collaborationId)
                .eq("personnel_type", CollaborationPersonnelTypeEnum.ASSISTANT.getCode()));
        // 已经关联的用户id
        List<Long> userList = userRelationList.stream()
                .map(CollaborationUserRelation::getRelatedUserId)
                .collect(Collectors.toList());
        // 新增的
        List<CollaborationUserRelation> saveUserRelation = new ArrayList<>();
        assistantsVO.forEach(vo -> {
            if (!userList.contains(vo.getUserId())) {
                CollaborationUserRelation userRelation = new CollaborationUserRelation();
                userRelation.setCollaborationId(collaborationId);
                userRelation.setRelatedUserId(vo.getUserId());
                userRelation.setRelatedUserName(vo.getUserName());
                userRelation.setRelatedUserDeptId(vo.getDeptId());
                userRelation.setRelatedUserDeptName(vo.getDeptName());
                userRelation.setPersonnelType(CollaborationPersonnelTypeEnum.ASSISTANT.getCode());
                saveUserRelation.add(userRelation);
            }
        });
        collaborationUserRelationMpService.saveBatch(saveUserRelation);
        // 删除的
        Set<Long> currentUserIds = assistantsVO.stream()
                .map(UserDeptVO::getUserId)
                .collect(Collectors.toSet());
        List<CollaborationUserRelation> deleteList = userRelationList.stream()
                .filter(userRelation -> !currentUserIds.contains(userRelation.getRelatedUserId()))
                .collect(Collectors.toList());
        collaborationUserRelationMpService.removeBatchByIds(deleteList);
    }

    @Override
    public void feedbackRefresh(Long collaborationId, FeedbackRefreshDTO dto) {
        Long ct = jzQueryTaskMapper.selectCount(
                Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                        .eq(JzQueryTaskEntity::getCollaborationId, collaborationId)
                        .in(JzQueryTaskEntity::getStatus, Arrays.asList(PUSHED.getCode(), RUNNING.getCode()))
        );
        if (ct > 0L) {
            throw new TRSException("还有未完成的任务，请等待");
        }
        // 判断是否超过了72小时，如果超过了，禁止再刷行
        List<JzQueryTaskEntity> jzQueryTaskEntities = jzQueryTaskMapper.selectList(
                Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                        .eq(JzQueryTaskEntity::getCollaborationId, collaborationId)
        );
        Optional<JzQueryTaskEntity> min = jzQueryTaskEntities.stream()
                .min(Comparator.comparing(JzQueryTaskEntity::getCreateTime));
        if (min.isPresent()) {
            Integer overTime = BeanFactoryHolder.getEnv().getProperty("ys.fight.jz.task.overTime", Integer.class, 72);
            if (min.get().getCreateTime().plusHours(overTime).isBefore(LocalDateTime.now())) {
                throw new TRSException(String.format("协作已超过%d小时，刷新反馈无效",overTime));
            }
        }
        String way = BeanFactoryHolder.getEnv().getProperty("com.trs.collaboration.feedback.refresh.way", "");
        CollaborationFeedback feedback = new CollaborationFeedback();
        if (StringUtils.isNotEmpty(way)) {
            for (String tel : dto.getTel()) {
                // 开发环境测试用
                List<FeedbackRefreshContent> refreshContents = feedbackRefreshContentMapper.selectList(new QueryWrapper<FeedbackRefreshContent>()
                        .eq("tel", tel));
                String collect = refreshContents.stream().map(FeedbackRefreshContent::getContent).collect(Collectors.joining("\n"));
                feedback.setContent(collect);
                // 写入推送表
                String sjpc = getSjpc(collaborationId, tel);
                JzQueryTaskEntity en = jzQueryTaskMapper.selectOne(
                        Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                                .eq(JzQueryTaskEntity::getSjpc, sjpc)
                );
                // 模拟jz反馈
                jzService.addFeedBack(en, collect);
            }
        } else  {
            // 生产环境用
            if (CollectionUtils.isEmpty(dto.getTel()) && CollectionUtils.isEmpty(dto.getAssistancePersonList())) {
                return;
            }
            CollaborationJq collaborationJq = collaborationJqMapper.selectOne(
                    Wrappers.lambdaQuery(CollaborationJq.class)
                            .eq(CollaborationJq::getCollaborationId, collaborationId)
            );
            List<JqAssistancePerson> savedPerson = JSONArray.parseArray(collaborationJq.getAssistancePerson(), JqAssistancePerson.class)
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(p -> StringUtils.isNotEmpty(p.getTel()))
                    .collect(Collectors.toList());
            List<JqAssistancePerson> personList = org.apache.commons.collections.CollectionUtils.isNotEmpty(dto.getAssistancePersonList())
                    ? dto.getAssistancePersonList()
                    : dto.getTel()
                        .stream()
                        .map(tel -> {
                            Optional<JqAssistancePerson> any = savedPerson.stream()
                                    .filter(p -> Objects.equals(p.getTel(), tel))
                                    .findAny();
                            if (any.isPresent()) {
                                return any.get();
                            }
                            JqAssistancePerson person = new JqAssistancePerson();
                            person.setTel(tel);
                            return person;
                        })
                        .collect(Collectors.toList());
            for (JqAssistancePerson assistancePerson : personList) {
                // 获取当前数据批次
                String sjpc = getSjpc(collaborationId, assistancePerson.getTel());
                try {
                    Tuple2<String, byte[]> pdfTuple = getPdfFile(collaborationId);
                    // 1、非技侦数据接入技侦
                    jzService.inputData(assistancePerson.getTel(), sjpc, assistancePerson.getName());
                    // 2、通知技侦自动运行模型接口
                    Map<String, Object> paramsMap = collaborationJzService.buildParamsMap(collaborationId, sjpc, assistancePerson.getTel());
                    Integer count = BeanFactoryHolder.getEnv().getProperty("com.trs.collaboration.feedback.refresh.retry", Integer.class, 2);
                    Boolean result = retry(() -> {
                        jzService.noticeRunModel(collaborationId, sjpc, paramsMap, pdfTuple);
                        return true;
                    }, count, 60 * 1000);
                    log.info("调用反馈刷新接口结果：{}", result);
                } catch (Exception e) {
                    log.error("调用反馈刷新接口异常:", e);
                    // 更新状态为失败
                    JzQueryTaskEntity taskEntity = jzQueryTaskMapper.selectOne(
                            Wrappers.lambdaQuery(JzQueryTaskEntity.class)
                                    .eq(JzQueryTaskEntity::getSjpc, sjpc)
                    );
                    taskEntity.setStatus(JzQueryTaskStatusEnum.RUN_ERROR.getCode());
                    jzQueryTaskMapper.updateById(taskEntity);
                    // 添加一条反馈记录
                    jzService.addFeedBack(taskEntity, String.format("调用JZ接口发生异常：{%s}，请联系市局指挥长！", e.getMessage()));
                }
            }
        }

    }

    private List<DeptVO> buildDeptVO(List<DeptVO> deptVOList, List<String> typeList, Long id) {
        List<DeptVO> list = new ArrayList<>();
        for (DeptVO vo : deptVOList) {
            if (typeList.contains(vo.getDeptType().toString())) {
                list.add(vo);
            } else if (vo.getDeptId().equals(id)) {
                list.add(vo);
            } else {
                if (vo.getChildren() == null) {
                    continue;
                }
                List<DeptVO> vos = vo.getChildren().stream()
                        .filter(e -> typeList.contains(e.getDeptType().toString()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(vos)) {
                    vo.setChildren(vos);
                    list.add(vo);
                }
            }
        }
        return list;
    }

    private List<Long> getDeptPath(Dept dept) {
        List<Long> list = Arrays.stream(dept.getPath().split("-"))
                .filter(s -> s != null && !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());
        list.add(dept.getId());
        return list;
    }

    private List<CompositeUserInfo> getPartners(Collaboration collaboration) {
        // 创建人
        CurrentUser user = permissionService.findCurrentUser(collaboration.getCreateUserId(), collaboration.getCreateDeptId());
        CompositeUserInfo creator = new CompositeUserInfo();
        initPartnersUser(creator, user);
        initPartnersDept(creator, user.getDept());
        creator.setStatus(3);
        creator.setRole(CompositeRoleEnum.ORGANIZER.getCode());
        List<CompositeUserInfo> partners = new ArrayList<>();
        partners.add(creator);
        // 参与人员
        DeptDto deptById = permissionService.getDeptById(collaboration.getCollaborationDeptId());
        if (Objects.isNull(collaboration.getCollaborationUserId())) {
            List<SimpleUserVO> users = permissionService.getUserOfDept(deptById.getCode());
            List<CompositeUserInfo> pts = users.stream()
                    .map(u -> {
                        CompositeUserInfo pt = new CompositeUserInfo();
                        initPartnersSimpleUser(pt, u);
                        initPartnersDept(pt, deptById);
                        pt.setStatus(3);
                        pt.setRole(CompositeRoleEnum.PARTNER.getCode());
                        return pt;
                    })
                    .collect(Collectors.toList());
            partners.addAll(pts);
        } else {
            CurrentUser collaborationUser = permissionService.findCurrentUser(collaboration.getCollaborationUserId(), deptById.getId());
            CompositeUserInfo pt = new CompositeUserInfo();
            initPartnersUser(pt, collaborationUser);
            initPartnersDept(pt, deptById);
            pt.setStatus(3);
            pt.setRole(CompositeRoleEnum.PARTNER.getCode());
            partners.add(pt);
        }
        // 对人员去重
        List<CompositeUserInfo> result = partners.stream()
                .collect(Collectors.groupingBy(CompositeUserInfo::getUserId))
                .values()
                .stream()
                .map(sameUserList -> {
                    // 取权限最大的
                    List<CompositeUserInfo> list = sameUserList.stream()
                            .sorted(Comparator.comparingLong(CompositeUserInfo::getRole))
                            .collect(Collectors.toList());
                    return list.get(0);
                })
                .collect(Collectors.toList());
        return result;
    }

    private void initPartnersDept(CompositeUserInfo pt, DeptDto dept) {
        pt.setDeptId(dept.getId());
        pt.setDeptCode(dept.getCode());
        pt.setDeptName(dept.getName());
    }

    private void initPartnersUser(CompositeUserInfo pt, CurrentUser user) {
        pt.setUserId(user.getId());
        pt.setUserName(user.getRealName());
    }

    private void initPartnersSimpleUser(CompositeUserInfo pt, SimpleUserVO user) {
        pt.setUserId(user.getUserId());
        pt.setUserName(user.getUserName());
    }

    private String getRelatedScenes(Map<Integer, List<CollaborationSceneRelation>> map, Integer relatedType) {
        JSONArray jsonArray = new JSONArray();
        List<JSONObject> list = map.getOrDefault(relatedType, new ArrayList<>()).stream()
                .map(CollaborationSceneRelation::getRelatedInfo)
                .map(JSONObject::parseObject)
                .collect(Collectors.toList());
        for (JSONObject jsonObject : list) {
            jsonArray.add(jsonObject);
        }
        return jsonArray.toJSONString();
    }

    private String generateTaskNumber(@Nullable String area, LocalDateTime dateTime) {
        String prefix = new StringBuilder()
                .append("XZ")
                .append(StringUtils.isEmpty(area) ? AuthHelper.getCurrentUser().getDept().getDistrictCode() : area)
                .append(dateTime.getYear())
                .append(String.format("%02d", dateTime.getMonthValue()))
                .append(String.format("%02d", dateTime.getDayOfMonth())).toString();
        Page<Collaboration> list = collaborationMpService.lambdaQuery()
                .likeRight(Collaboration::getTaskNumber, prefix)
                .orderByDesc(Collaboration::getId)
                .page(new Page<>(1, 1));
        Integer number = CollectionUtils.isEmpty(list.getRecords())
                ? 1
                : findMaxNumber(list.getRecords().get(0).getTaskNumber()) + 1;
        // 任务编号的生成规则为GD+六位地区编码+年月日+四位序号；
        return new StringBuilder()
                .append(prefix)
                .append(String.format("%05d", number))
                .toString();
    }

    private Integer findMaxNumber(String taskNumber) {
        if (StringUtils.isEmpty(taskNumber)) {
            return 0;
        }
        return Integer.valueOf(taskNumber.substring(taskNumber.length() - 5));
    }

    private Map<Long, CollaborationFormType> collaborationQfMap() {
        List<DictDto> list = dictService.getDictByType("collaboration_category_qf_map");
        Map<Long, CollaborationFormType> result = new HashMap<>();
        for (DictDto dictDto : list) {
            result.put(Long.valueOf(dictDto.getName()), CollaborationFormType.findByNameOrDefault(dictDto.getDictDesc()));
        }
        return result;
    }

    private void markReadCollaboration(CurrentUser currentUser, Collaboration collaboration) {
        Long count = collaborationUserRelationMapper.selectCount(
                Wrappers.lambdaQuery(CollaborationUserRelation.class)
                        .eq(CollaborationUserRelation::getCollaborationId, collaboration.getId())
                        .eq(CollaborationUserRelation::getRelatedUserId, currentUser.getId())
        );
        if (count > 0) {
            // 如果关联了此用户 直接标记已读
            collaborationUserRelationMapper.setCollaborationIsRead(collaboration.getId(), true, currentUser.getId());
        } else if (Objects.isNull(collaboration.getCollaborationUserId()) && Objects.equals(collaboration.getCollaborationDeptId(), currentUser.getDeptId())) {
            // 如果没有关联此用户 关联此用户 (能看到此协作 说明具有协作权限)
            SimpleUserVO user = permissionService.findSimpleUser(currentUser.getId(), currentUser.getDeptId());
            CollaborationUserRelation r = collaborationUserRelation.buildRelationBySimpleUser(collaboration.getId(), user, CollaborationPersonnelTypeEnum.ME_PROCESSED);
            r.setIsRead(true);
            collaborationUserRelationMapper.insert(r);
        }
    }

    private String getSjpc(Long collaborationId, String dhhm) {
        QueryWrapper<JzQueryTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("create_time", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD) + " 00:00:00");
        queryWrapper.le("create_time", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD) + " 23:59:59");
        queryWrapper.orderByDesc("sjpcbh");
        List<JzQueryTaskEntity> jzQueryTaskEntities = jzQueryTaskMapper.selectList(queryWrapper);
        Integer sjpcbh = CollectionUtils.isEmpty(jzQueryTaskEntities) ? 1 : jzQueryTaskEntities.get(0).getSjpcbh() + 1;
        String sjpc = String.format(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS_SSS2));
        JzQueryTaskEntity entity = new JzQueryTaskEntity();
        entity.setCreateTime(LocalDateTime.now());
        entity.setCollaborationId(collaborationId);
        entity.setDhhm(dhhm);
        entity.setSjpc(sjpc);
        entity.setSjpcbh(sjpcbh);
        entity.setStatus(PUSHED.getCode());
        jzQueryTaskMapper.insert(entity);
        return sjpc;
    }

    private Tuple2<String, byte[]> getPdfFile(Long collaborationId) {
        byte[] bytes = spbExportServiceFactory.exportZld(collaborationId);
        return Tuple.of("警情协查指令单.pdf", bytes);
    }

    private <T> T retry(Supplier<T> operation, int maxRetries, long delayMillis) throws Exception {
        int attempt = 0;
        while (true) {
            try {
                T t = operation.get();
                log.info("第  {} 次尝试成功", attempt);
                return t;
            } catch (Exception e) {
                attempt++;
                if (attempt > maxRetries) {
                    throw e;
                }
                Thread.sleep(delayMillis * attempt); // 线性或指数退避
            }
        }
    }
}

