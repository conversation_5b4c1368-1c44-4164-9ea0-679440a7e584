package com.trs.police.fight.service.compositeItem;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.core.constant.enums.CompositeRoleEnum;
import com.trs.police.fight.domain.dto.CompositeItemSearchDTO;
import com.trs.police.common.core.entity.FightCompositeUserRelation;
import com.trs.police.fight.domain.vo.CompositeUserInfo;
import com.trs.police.fight.mapper.FightCompositeUserRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 主办人检索
 *
 * <AUTHOR>
 */
@Component
public class OrganizerServiceImpl implements ICompositeItemSearchService{

    @Autowired
    private FightCompositeUserRelationMapper fightCompositeUserRelationMapper;

    @Autowired
    private PermissionService permissionService;

    /**
     * 检索合成子项
     *
     * @param compositeItemSearchDTO CompositeItemSearchDTO
     * @return CompositeItemSearchDTO
     */
    @Override
    public CompositeItemSearchDTO searchCompositeItems(CompositeItemSearchDTO compositeItemSearchDTO) {
        List<Long> compositeItemIds = compositeItemSearchDTO.getCompositeItemIds();
        Map<Long, CompositeUserInfo> compositeIdAndZbrUserInfoMap = new HashMap<>(0);
        if (!CollectionUtils.isEmpty(compositeItemIds)) {
            QueryWrapper<FightCompositeUserRelation> queryWrapper = new QueryWrapper<FightCompositeUserRelation>()
                    .in("composite_id", compositeItemIds)
                    .eq("role", CompositeRoleEnum.ORGANIZER.getCode());
            List<FightCompositeUserRelation> userRelations = fightCompositeUserRelationMapper.selectList(queryWrapper);
            List<Long> userIdList = userRelations.stream().map(FightCompositeUserRelation::getUserId).collect(Collectors.toList());
            List<UserDto> userDtos = permissionService.getUserListById(userIdList);
            Map<Long, UserDto> userIdAnduserDtoMap = userDtos.stream().collect(Collectors.toMap(UserDto::getId, userDto -> userDto, (u1, u2) -> u1));
            compositeIdAndZbrUserInfoMap = userRelations.stream()
                    .collect(Collectors.groupingBy(FightCompositeUserRelation::getCompositeId,
                            Collectors.collectingAndThen(Collectors.toList(),
                                    list -> list.stream().map(fightCompositeUserRelation -> matchDept(fightCompositeUserRelation, userIdAnduserDtoMap.get(fightCompositeUserRelation.getUserId())))
                                            .findAny()
                                            .orElse(new CompositeUserInfo()))));
        }
        compositeItemSearchDTO.setCompositeIdAndZbrUserInfoMap(compositeIdAndZbrUserInfoMap);
        return null;
    }

    /**
     * 匹配主办人部门
     *
     * @param fightCompositeUserRelation 合成用户关联
     * @param user 用户
     * @return info
     */
    private CompositeUserInfo matchDept(FightCompositeUserRelation fightCompositeUserRelation, UserDto user) {
        Long deptId = fightCompositeUserRelation.getDeptId();
        DeptDto dept = user.getDeptList().stream().filter(d -> d.getId().equals(deptId)).findAny()
                .orElse(new DeptDto());
        CompositeUserInfo userInfo = new CompositeUserInfo();
        userInfo.setUserId(user.getId());
        userInfo.setRole(fightCompositeUserRelation.getRole().getCode());
        userInfo.setUserName(user.getRealName());
        userInfo.setDeptId(dept.getId());
        userInfo.setDeptCode(dept.getCode());
        userInfo.setDeptName(dept.getShortName());
        userInfo.setApprovalId(fightCompositeUserRelation.getApprovalId());
        userInfo.setStatus(fightCompositeUserRelation.getStatus());
        return userInfo;
    }

}
