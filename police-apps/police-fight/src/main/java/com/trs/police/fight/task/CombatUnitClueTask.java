package com.trs.police.fight.task;

import com.trs.police.fight.task.analysis.impl.CombatUnitClueAnalysis;
import com.trs.police.fight.task.context.ClueAnalysisContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @author: dingkeyu
 * @date: 2024/03/26
 * @description: 作战单元线索统计定时任务
 */
@Slf4j
@Component
public class CombatUnitClueTask {

    @Autowired
    private CombatUnitClueAnalysis combatUnitClueAnalysis;

    /**
     *  作战单元线索统计
     */
    @Scheduled(cron = "${com.trs.schedule.combatUnitClue.task.cron:0 0/30 * * * ?}")
    public void run() {
        try {
            combatUnitClueAnalysis.analyzeScene(new ClueAnalysisContext());
        } catch (Exception e) {
            log.error("作战单元线索统计失败", e);
        }
    }
}
