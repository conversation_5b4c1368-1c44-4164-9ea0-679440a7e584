package com.trs.police.fight.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.permission.DataPermissionInfo;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.enums.CollaborationPersonnelTypeEnum;
import com.trs.police.fight.domain.request.CollaborationListRequest;
import com.trs.police.fight.domain.vo.CollaborationVO;
import com.trs.police.fight.mapper.CollaborationMapper;
import com.trs.police.fight.service.CommonCollaborationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: dingkeyu
 * @date: 2024/03/08
 * @description: 我处理的协作
 */
@Service
public class MeProcessedCollaborationService implements CommonCollaborationService {

    @Resource
    private CollaborationMapper collaborationMapper;

    @Override
    public PageResult<CollaborationVO> getCollaborationList(CollaborationListRequest collaborationParams, CurrentUser currentUser) {
        // 获取当前登录用户的数据权限信息
        final PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        DataPermissionInfo permissionInfo = permissionService.getCurrentUserDataPermissionInfo();
        PageParams pageParams = collaborationParams.getPageParams();
        Page<CollaborationVO> pageList = collaborationMapper.getMeProcessedCollaborations(collaborationParams, currentUser, permissionInfo, pageParams.toPage());
        return PageResult.of(pageList.getRecords(), pageParams.getPageNumber(), pageList.getTotal(), pageParams.getPageSize());
    }

    @Override
    public Long unreadCollaborationCount(CurrentUser currentUser) {
        // 获取当前登录用户的数据权限信息
        final PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        DataPermissionInfo permissionInfo = permissionService.getCurrentUserDataPermissionInfo();
        return collaborationMapper.meProcessedUnreadCount(currentUser, permissionInfo);
    }

    @Override
    public CollaborationPersonnelTypeEnum getSearchType() {
        return CollaborationPersonnelTypeEnum.ME_PROCESSED;
    }
}
