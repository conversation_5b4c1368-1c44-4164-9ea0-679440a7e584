package com.trs.police.fight.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: dingkeyu
 * @date: 2024/03/12
 * @description: 协作未读VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CollaborationUnreadVO {

    /**
     * 我发起的协作未读数
     */
    private Long meStartUnreadCount;

    /**
     * 我审核的协作未读数
     */
    private Long meApprovedUnreadCount;

    /**
     * 我处理的协作未读数
     */
    private Long meProcessedUnreadCount;

    /**
     * 全部协作未读数
     */
    private Long allUnreadCount;
}
