package com.trs.police.fight.service.impl.relation;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.ApprovalStatusEnum;
import com.trs.police.common.core.constant.enums.CompositeMembersStatusEnum;
import com.trs.police.common.core.constant.enums.CompositeRoleEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.GovWxDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.entity.FightCompositeUserRelation;
import com.trs.police.common.core.entity.GovWxUserInfo;
import com.trs.police.common.core.mapper.GovWxUserInfoMapper;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.GovWxUtils;
import com.trs.police.common.core.utils.ListUtil;
import com.trs.police.common.core.vo.permission.UserVO;
import com.trs.police.common.openfeign.starter.service.MessageService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.CompositeConstant;
import com.trs.police.fight.constant.enums.CompositeType;
import com.trs.police.fight.constant.enums.PlanSchedulingMeasureEnum;
import com.trs.police.fight.domain.entity.PlanSchedulingMeasureRelation;
import com.trs.police.fight.domain.vo.CompositeUserInfo;
import com.trs.police.fight.domain.vo.CreateCompositeVO;
import com.trs.police.fight.domain.vo.MergeCompositeVO;
import com.trs.police.fight.mapper.FightCompositeMapper;
import com.trs.police.fight.mapper.PlanSchedulingMeasureRelationMapper;
import com.trs.police.fight.service.FightCompositeUserRelationService;
import com.trs.police.fight.service.RelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.common.core.constant.enums.CompositeRoleEnum.PARTNER;
import static com.trs.police.fight.constant.FightCompositeConstant.USER_SOURCE_DEFAULT;
import static com.trs.police.fight.constant.FightCompositeConstant.USER_SOURCE_PLAN;
import static com.trs.police.fight.constant.enums.CompositeType.URGENT;
import static com.trs.police.fight.constant.enums.PlanSchedulingMeasureEnum.UNIT_DEPT;

/**
 * 参与人员
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserRelationServiceImpl implements RelationService {

    @Resource
    private FightCompositeUserRelationService fightCompositeUserRelationService;

    @Resource
    private PlanSchedulingMeasureRelationMapper planSchedulingMeasureRelationMapper;

    @Resource
    private PermissionService permissionService;

    @Autowired
    private FightCompositeMapper fightCompositeMapper;

    @Autowired
    private GovWxUserInfoMapper govWxUserInfoMapper;


    @Autowired
    private MessageService messageService;

    @Autowired
    private GovWxUtils govWxUtils;

    @Override
    public Set<CompositeType> acceptType() {
        return new HashSet<>(Arrays.asList(CompositeType.values()));
    }

    @Override
    public void createRelation(CreateCompositeVO createCompositeVO, FightComposite fightComposite) {
        // 编辑时不修改人员
        if (Objects.equals(2, createCompositeVO.getScene())) {
            return;
        }
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        // 所有参与人
        List<FightCompositeUserRelation> partner = createCompositeVO.getPartners()
                .stream()
                .map(c -> {
                    Integer code = currentUser.getId().equals(c.getUserId())
                            ? CompositeMembersStatusEnum.RECEIVED.getCode()
                            : CompositeMembersStatusEnum.NO_RECEIVE.getCode();
                    FightCompositeUserRelation entity = new FightCompositeUserRelation(fightComposite.getId(), c.getUserId(), CompositeRoleEnum.codeOf(c.getRole()), c.getDeptId());
                    entity.setReceiveStatus(code);
                    return entity;
                })
                .collect(Collectors.toList());
        // 如果是添加了责任单位的预案、还要添加上责任单位
        Optional<CompositeType> tp = CompositeType.ofCode(fightComposite.getCompositeType());
        if (URGENT.equals(tp.orElse(null))) {
            List<FightCompositeUserRelation> urPlanAddList = urPlanAddList(partner, fightComposite.getPlanId(), fightComposite.getId());
            fightCompositeUserRelationService.saveBatch(urPlanAddList);
        }
        if (CompositeConstant.BUILD_GOVERNMENT_GROUP_CHAT.equals(createCompositeVO.getIsBuildGovernmentChatGroup())){
            String chatId = buildGovWxGroupChat(createCompositeVO);
            fightComposite.setGovWxGroupChatId(chatId);
            fightCompositeMapper.updateById(fightComposite);
        }
        fightCompositeUserRelationService.saveBatch(partner);
    }

    private String buildGovWxGroupChat(CreateCompositeVO vo){
        if (CollectionUtils.isEmpty(vo.getPartners())){
            return null;
        }
        GovWxDto dto = new GovWxDto();
        List<Long> userIds = vo.getPartners().stream().map(CompositeUserInfo::getUserId).collect(Collectors.toList());
        //查询用户是否在政务微信中存在
        List<GovWxUserInfo> govWxUserInfoList = checkPartnersExistInGovWx(userIds);
        if (CollectionUtils.isEmpty(govWxUserInfoList)){
            return null;
        }
        Map<Long, Long> partnerRoleMap = vo.getPartners().stream()
                .collect(Collectors.toMap(CompositeUserInfo::getUserId, CompositeUserInfo::getRole));
        //userRoleMa用来设置添加的人和群主
        Map<String,Long> userRoleMap = govWxUserInfoList.stream()
                .collect(Collectors.toMap(GovWxUserInfo::getIdCard,e->partnerRoleMap.get(e.getYsUserId())));
        dto.setUserRoleMap(userRoleMap);
        log.info("userRoleMap的值为：{}", userRoleMap);
        List<String> userlist = new ArrayList<>();
        String owner = null;
        for (String userid : userRoleMap.keySet()) {
            userlist.add(userid);
            owner = CompositeRoleEnum.ORGANIZER.getCode().equals(userRoleMap.get(userid)) ? userid : owner;
        }
        dto.setOwner(owner);
        dto.setAddListIdCards(userlist);
        dto.setTitle(vo.getTitle());
        try {
            String chatId = messageService.buildGovWxGroupChat(dto);
            return chatId;
        }catch (Exception e){
            log.error("政务微信建群失败，原因：{}", e.getMessage());
        }
        return null;
    }


    /**
     * 更新政务微信群聊人员
     *
     * @param vo vo
     * @param addList addList
     * @param deleteList deleteList
     * @param savedList savedList
     * @param groupChatId groupChatId
     * @return 结果
     */
    public String updateGovWxGroupChat(CreateCompositeVO vo, List<FightCompositeUserRelation> addList,
                                        List<FightCompositeUserRelation> deleteList, List<FightCompositeUserRelation> savedList,
                                        String groupChatId) {
        List<FightCompositeUserRelation> allList = ListUtil.mergeListAndRemoveDuplicates(savedList, addList);
        allList = ListUtil.mergeListAndRemoveDuplicates(allList, deleteList);
        List<Long> userIds = CollectionUtils.isNotEmpty(allList) ? allList.stream().map(FightCompositeUserRelation::getUserId)
                .collect(Collectors.toList()) : new ArrayList<>();
        //查询用户是否在政务微信中存在
        List<GovWxUserInfo> govWxUserInfoList = checkPartnersExistInGovWx(userIds);
        if (CollectionUtils.isEmpty(govWxUserInfoList)){
            return null;
        }
        GovWxDto dto = new GovWxDto();
        List<Long> addListIds = addList.stream().map(FightCompositeUserRelation::getUserId).collect(Collectors.toList());
        //设置需要添加的用户身份证
        if (CollectionUtils.isNotEmpty(addListIds)){
            List<String> addListIdCards = govWxUserInfoList.stream().filter(e -> addListIds.contains(e.getYsUserId()))
                    .map(GovWxUserInfo::getIdCard).collect(Collectors.toList());
            dto.setAddListIdCards(addListIdCards);
        }
        //设置需要删除的用户身份证
        List<Long> deleteListIds = deleteList.stream().map(FightCompositeUserRelation::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteListIds)){
            List<String> deleteListIdCards = govWxUserInfoList.stream().filter(e -> deleteListIds.contains(e.getYsUserId()))
                    .map(GovWxUserInfo::getIdCard).collect(Collectors.toList());
            dto.setDeleteListIdCards(deleteListIdCards);
        }
        //过滤掉role为空的
        List<CompositeUserInfo> partners = CollectionUtils.isEmpty(vo.getPartners()) ? new ArrayList<>()
                : vo.getPartners().stream().filter(e -> Objects.nonNull(e.getRole())).collect(Collectors.toList());
        Map<Long, Long> partnerRoleMap = CollectionUtils.isEmpty(partners) ? new HashMap<>()
                :  partners.stream().collect(Collectors.toMap(CompositeUserInfo::getUserId, CompositeUserInfo::getRole));
        Map<String,Long> userRoleMap = CollectionUtils.isEmpty(partners) ? new HashMap<>() : govWxUserInfoList.stream()
                .collect(Collectors.toMap(GovWxUserInfo::getIdCard,e->partnerRoleMap.get(e.getYsUserId())));
        dto.setUserRoleMap(userRoleMap);
        String owner = null;
        for (String userid : userRoleMap.keySet()) {
            owner = CompositeRoleEnum.ORGANIZER.getCode().equals(userRoleMap.get(userid)) ? userid : owner;
        }
        dto.setOwner(owner);
        log.info("userRoleMap的值为：{}", userRoleMap);
        dto.setGroupChatId(groupChatId);
        dto.setTitle(vo.getTitle());
        try {
            String msg = messageService.updateGovWxGroupChat(dto);
            return msg;
        } catch (Exception e) {
            log.error("政务微信更新群聊人员失败，原因：{}", e.getMessage());
        }
        return null;
    }

    private List<GovWxUserInfo> checkPartnersExistInGovWx(List<Long> userIds) {
        List<GovWxUserInfo> govWxUserInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIds)){
            govWxUserInfoList = govWxUserInfoMapper.selectList(new QueryWrapper<GovWxUserInfo>()
                    .in("ys_user_id", userIds));
        }
        if (CollectionUtils.isEmpty(govWxUserInfoList)){
            log.error("参与人员在政务微信中都不存在");
            return new ArrayList<>();
        }
        return govWxUserInfoList;
    }


    @Override
    public void rebuildRelation(MergeCompositeVO mergeCompositeVO, FightComposite source, FightComposite target) {
        // 由于主力人只能有一个，所以保留一个主理人
        changeRole(mergeCompositeVO, source, target);
        // 删除掉重复的用户
        deleteRepeatUser(mergeCompositeVO, source, target);
        // 修改用户关联的作战
        fightCompositeUserRelationService.lambdaUpdate()
                .in(FightCompositeUserRelation::getCompositeId, mergeCompositeVO.getMergeIds())
                .set(FightCompositeUserRelation::getCompositeId, target.getId())
                .update();
    }

    /**
     * 编辑作战的用户
     *
     * @param createCompositeVO v1
     * @param fightComposite f
     * @param oldPlanId 旧的预案id
     * @return 需要新增的用户列表
     */
    public List<FightCompositeUserRelation> editUser(CreateCompositeVO createCompositeVO, FightComposite fightComposite, Long oldPlanId) {
        final Boolean planIsChanged = !Objects.equals(oldPlanId, fightComposite.getPlanId());
        // 数据库已经保存的用户列表
        List<FightCompositeUserRelation> savedList = fightCompositeUserRelationService.lambdaQuery()
                .eq(FightCompositeUserRelation::getCompositeId, fightComposite.getId())
                .list();
        // 删除掉需要删除的
        Set<String> resultUserIdSet = createCompositeVO.getPartners()
                .stream()
                .map(this::buildId)
                .collect(Collectors.toSet());
        List<FightCompositeUserRelation> deleteList = savedList.stream()
                .filter(user -> !resultUserIdSet.contains(user.buildKey()))
                .collect(Collectors.toList());
        fightCompositeUserRelationService.removeBatchByIds(deleteList);
        // 未删除的人员根据新表单提交的角色类型更新角色类型
        List<FightCompositeUserRelation> currentList = new ArrayList<>();
        for (FightCompositeUserRelation saved : savedList) {
            Optional<CompositeUserInfo> any = createCompositeVO.getPartners()
                    .stream()
                    .filter(p -> Objects.equals(buildId(p), saved.buildKey()))
                    .findAny();
            if (any.isPresent()) {
                // 如果角色类型变了或者来源变了才更改
                if (!CompositeRoleEnum.codeOf(any.get().getRole()).equals(saved.getRole()) || USER_SOURCE_PLAN.equals(saved.getUserSource())) {
                    saved.setRole(CompositeRoleEnum.codeOf(any.get().getRole()));
                    if (planIsChanged) {
                        saved.setUserSource(USER_SOURCE_DEFAULT);
                    }
                    fightCompositeUserRelationService.updateById(saved);
                }
                currentList.add(saved);
            }
        }
        // 如果是处突 预案id变了还需要修改预案对应关联的人员
        Optional<CompositeType> tp = CompositeType.ofCode(fightComposite.getCompositeType());
        if (URGENT.equals(tp.orElse(null)) && planIsChanged) {
            List<FightCompositeUserRelation> urPlanAddList = urPlanAddList(currentList, fightComposite.getPlanId(), fightComposite.getId());
            fightCompositeUserRelationService.saveBatch(urPlanAddList);
        }
        // 筛选出新增的构造出新增的do
        Set<String> oldUserIdSet = savedList.stream().map(FightCompositeUserRelation::buildKey).collect(Collectors.toSet());
        List<FightCompositeUserRelation> addList = createCompositeVO.getPartners()
                .stream()
                .filter(user -> !oldUserIdSet.contains(buildId(user)))
                .map(c -> new FightCompositeUserRelation(fightComposite.getId(), c.getUserId(), CompositeRoleEnum.codeOf(c.getRole()), c.getDeptId(), ApprovalStatusEnum.WAITING.getCode()))
                .collect(Collectors.toList());
        if (CompositeConstant.BUILD_GOVERNMENT_GROUP_CHAT.equals(createCompositeVO.getIsBuildGovernmentChatGroup())){
            if (StringUtils.isEmpty(fightComposite.getGovWxGroupChatId())){
                String chatId = buildGovWxGroupChat(createCompositeVO);
                fightComposite.setGovWxGroupChatId(chatId);
                fightCompositeMapper.updateById(fightComposite);
            }else {
                String msg = updateGovWxGroupChat(createCompositeVO, addList, deleteList, savedList,
                        fightComposite.getGovWxGroupChatId());
                log.info(msg);
            }
        }
        return addList;
    }


    /**
     * 删除掉重复的用户
     *
     * @param mergeCompositeVO mergeCompositeVO
     * @param source source
     * @param target target
     */
    private void deleteRepeatUser(MergeCompositeVO mergeCompositeVO, FightComposite source, FightComposite target) {
        Map<Long, List<FightCompositeUserRelation>> groupByUserId = fightCompositeUserRelationService.lambdaQuery()
                .in(FightCompositeUserRelation::getCompositeId, mergeCompositeVO.getMergeIds())
                .list()
                .stream()
                .collect(Collectors.groupingBy(FightCompositeUserRelation::getUserId));
        // 按照角色等级排序，这样后续删除的就是等级较低的
        for (List<FightCompositeUserRelation> value : groupByUserId.values()) {
            value.sort(Comparator.comparingLong(relation -> relation.getRole().getCode()));
        }
        // 删除掉重复的user
        List<Long> repeatId = new ArrayList<>();
        for (List<FightCompositeUserRelation> value : groupByUserId.values()) {
            if (value.size() > 1) {
                for (int i = 1; i < value.size(); i++) {
                    repeatId.add(value.get(i).getId());
                }
            }
        }
        fightCompositeUserRelationService.removeBatchByIds(repeatId);
    }

    /**
     * 修改角色 多个作战合并成一个作战时，只能有一个主理人
     *
     * @param mergeCompositeVO mergeCompositeVO
     * @param source source
     * @param target target
     */
    private void changeRole(MergeCompositeVO mergeCompositeVO, FightComposite source, FightComposite target) {
        // 查询所有主理人
        List<FightCompositeUserRelation> list = fightCompositeUserRelationService.lambdaQuery()
                .in(FightCompositeUserRelation::getCompositeId, mergeCompositeVO.getMergeIds())
                .eq(FightCompositeUserRelation::getRole, CompositeRoleEnum.ORGANIZER.getCode())
                .list();
        // 不是当前登录用户主理人都设置成协理人
        if (CollectionUtils.isNotEmpty(list)) {
            List<FightCompositeUserRelation> toChange = list;
            for (FightCompositeUserRelation relation : toChange) {
                relation.setRole(CompositeRoleEnum.ASSISTANT);
            }
            fightCompositeUserRelationService.updateBatchById(toChange);
        }
        // 查询当前登录用户
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        List<FightCompositeUserRelation> currentList = fightCompositeUserRelationService.lambdaQuery()
                .in(FightCompositeUserRelation::getCompositeId, mergeCompositeVO.getMergeIds())
                .eq(FightCompositeUserRelation::getUserId, currentUser.getId())
                .list();
        if (CollectionUtils.isEmpty(currentList)) {
            // 新建
            FightCompositeUserRelation relation = new FightCompositeUserRelation(
                    source.getId(), currentUser.getId(), CompositeRoleEnum.ORGANIZER, currentUser.getDeptId()
            );
            fightCompositeUserRelationService.save(relation);
        } else {
            // 更新成主理人
            fightCompositeUserRelationService.lambdaUpdate()
                    .in(FightCompositeUserRelation::getCompositeId, mergeCompositeVO.getMergeIds())
                    .eq(FightCompositeUserRelation::getUserId, currentUser.getId())
                    .set(FightCompositeUserRelation::getRole, CompositeRoleEnum.ORGANIZER.getCode())
                    .update();
        }
    }

    private String buildId(CompositeUserInfo info) {
        FightCompositeUserRelation relation = new FightCompositeUserRelation();
        relation.setUserId(info.getUserId());
        relation.setDeptId(info.getDeptId());
        return relation.buildKey();
    }

    private List<CompositeUserInfo> getPlanPartners(Long planId) {
        if (Objects.isNull(planId)) {
            return new ArrayList<>();
        }
        PlanSchedulingMeasureRelation mr = planSchedulingMeasureRelationMapper.selectOne(
                Wrappers.lambdaQuery(PlanSchedulingMeasureRelation.class)
                        .eq(PlanSchedulingMeasureRelation::getPlanId, planId)
                        .eq(PlanSchedulingMeasureRelation::getMeasureType, UNIT_DEPT.getCode())
        );
        if (Objects.isNull(mr)
                || !UNIT_DEPT.equals(PlanSchedulingMeasureEnum.codeOf(mr.getMeasureType().intValue()))
                || StringUtils.isEmpty(mr.getUnitDeptId())) {
            return new ArrayList<>();
        }
        List<Long> deptIds = Stream.of(mr.getUnitDeptId().split(",|;"))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deptIds)) {
            return new ArrayList<>();
        }
        // 获取部门
        Map<Long, DeptDto> deptIdMap = permissionService.getDeptByIds(deptIds)
                .stream()
                .collect(Collectors.toMap(DeptDto::getId, d -> d));
        List<UserVO> userByDeptIds = permissionService.getUserByDeptIds(deptIds);
        // 转换成CompositeUserInfo实体
        List<CompositeUserInfo> list = userByDeptIds.stream()
                .map(user ->
                        Stream.of(user.getDeptId())
                                .filter(dept -> deptIds.contains(dept))
                                .map(dept -> {
                                    DeptDto deptDto = deptIdMap.get(dept);
                                    CompositeUserInfo info = new CompositeUserInfo();
                                    info.setUserId(user.getUserId());
                                    info.setUserName(user.getName());
                                    info.setDeptId(deptDto.getId());
                                    info.setRole(PARTNER.getCode());
                                    info.setDeptName(deptDto.getName());
                                    info.setDeptShortName(deptDto.getShortName());
                                    info.setDeptCode(deptDto.getCode());
                                    info.setStatus(ApprovalStatusEnum.PASSED.getCode());
                                    return info;
                                })
                                .collect(Collectors.toList())
                )
                .flatMap(List::stream)
                .collect(Collectors.toList());
        return list;
    }

    private List<FightCompositeUserRelation> urPlanAddList(List<FightCompositeUserRelation> saved, Long planId, Long fightId) {
        List<CompositeUserInfo> planPartners = getPlanPartners(planId);
        List<FightCompositeUserRelation> fcur = planPartners
                .stream()
                .map(c -> new FightCompositeUserRelation(fightId, c.getUserId(), CompositeRoleEnum.codeOf(c.getRole()), c.getDeptId()))
                .collect(Collectors.toList());
        List<FightCompositeUserRelation> newList = new ArrayList<>();
        for (FightCompositeUserRelation relation : fcur) {
            Optional<FightCompositeUserRelation> any = saved.stream()
                    .filter(p -> Objects.equals(p.getDeptId(), relation.getDeptId()) && Objects.equals(p.getUserId(), relation.getUserId()))
                    .findAny();
            if (any.isEmpty()) {
                relation.setUserSource(USER_SOURCE_PLAN);
                newList.add(relation);
            }
        }
        return newList;
    }
}
