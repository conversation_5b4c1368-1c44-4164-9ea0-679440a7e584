package com.trs.police.fight.statistic.service.impl;

import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.fight.mapper.CollaborationMapper;
import com.trs.police.fight.statistic.DTO.CommonDTO;
import com.trs.police.fight.statistic.DTO.NoParamsDTO;
import com.trs.police.fight.statistic.DTO.StatisticContext;
import com.trs.police.fight.statistic.constant.SceneType;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.statistic.domain.VO.CountStatisticVO;
import com.trs.police.fight.statistic.mapper.StDeptMapper;
import com.trs.police.fight.statistic.service.CountStatisticService;
import com.trs.police.fight.statistic.service.FightStatisticAreaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 警种协同作战榜
 *
 * <AUTHOR>
 */
@Component
public class CorporationTrendImpl implements CountStatisticService<NoParamsDTO, Map<String,Long>> {

    @Resource
    private CollaborationMapper collaborationMapper;

    @Autowired
    private StDeptMapper deptMapper;


    @Override
    public CountStatisticVO<Map<String,Long>> doStatistic(StatisticContext<NoParamsDTO> context) {
        // 当前父级地域
        String currentAreaCode = context.getCommonParams().getAreaCode();
        List<CountItem> countItems = buildVoList(context, currentAreaCode);
        Map<String,Long> map = bulidAllResult(countItems);
        CountStatisticVO<Map<String,Long>> vo = new CountStatisticVO<>(countItems, map);
        return vo;
    }


    @Override
    public SceneType sceneType() {
        return SceneType.CORPORATION_TREND;
    }

    private List<CountItem> buildVoList(StatisticContext<NoParamsDTO> context, String currentAreaCode) {
        // 获取当前地域信息，地域不存在，返回空集合
        currentAreaCode = FightStatisticAreaUtils.getRelaCode(currentAreaCode);
        //判断开始日期是否是同一天
        boolean result = timeRangeIsOneDay(context.getCommonParams());
        //分别获取发起支撑数和办结数
        List<CountItem> fqList = collaborationMapper.getFqCountByDate(currentAreaCode,context.getCommonParams(),result);
        List<CountItem> bjList = collaborationMapper.getBjCountByDate(currentAreaCode,context.getCommonParams(),result);
        List<Collaboration> collaborationList = collaborationMapper.getCsCountByDate(currentAreaCode,context.getCommonParams());
        //过滤支撑数和办结数中包含需要展示的字段值
        Map<String, Long> fqxzTimeAndCount = CollectionUtils.isEmpty(fqList)
                ? new HashMap<>()
                : fqList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        Map<String, Long> bjxzTimeAndCount = CollectionUtils.isEmpty(bjList) ? new HashMap<>()
                : bjList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        List<CountItem> collect = new ArrayList<>();
        //获取起始截至日期之间的所有日期
        List<LocalDateTime> dateList = result ? getHourDateByDto(context.getCommonParams()) : getDateByDto(context.getCommonParams());
        //处理返回值
        collect = result
                ? getCountItemsByHour(dateList, fqxzTimeAndCount, bjxzTimeAndCount, collaborationList)
                : getCountItems(dateList, fqxzTimeAndCount, bjxzTimeAndCount, collaborationList);
        if (CollectionUtils.isEmpty(collect)){
            return collect;
        }
        return collect;
    }

    private List<LocalDateTime> getHourDateByDto(CommonDTO commonParams) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDate = LocalDateTime.parse(commonParams.getStartTime(), formatter);
        LocalDateTime endDate = LocalDateTime.parse(commonParams.getEndTime(), formatter);
        List<LocalDateTime> list = new ArrayList<>();
        while (startDate.isBefore(endDate)){
            startDate = startDate.plusHours(1);
            list.add(startDate);
        }
        return list;
    }

    private boolean timeRangeIsOneDay(CommonDTO commonParams) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(commonParams.getStartTime(), formatter);
        LocalDateTime endTime = LocalDateTime.parse(commonParams.getEndTime(), formatter);
        return startTime.toLocalDate().equals(endTime.toLocalDate());
    }

    /**
     * 获取起始截止日期之间的数据
     *
     * @param commonParams 测试
     * @return 结果
     */
    private List<LocalDateTime> getDateByDto(CommonDTO commonParams) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDate = LocalDateTime.parse(commonParams.getStartTime(), formatter);
        LocalDateTime endDate = LocalDateTime.parse(commonParams.getEndTime(), formatter);
        List<LocalDateTime> dateTimeList = new ArrayList<>();
        LocalDate currentDate = startDate.toLocalDate();
        LocalTime startTime = LocalTime.of(23,59,59); // 每天的开始时间

        while (!currentDate.isAfter(endDate.toLocalDate())) {
            LocalDateTime dateTime = LocalDateTime.of(currentDate, startTime);
            dateTimeList.add(dateTime);
            // 如果想要每天的结束时间，可以添加以下代码（但这里为了简单只添加了开始时间）
            // LocalDateTime endOfDay = LocalDateTime.of(currentDate, LocalTime.MAX);
            // dateTimeList.add(endOfDay); // 注意这样会添加两倍的日期数量，通常不需要这样做
            // 移动到下一天
            currentDate = currentDate.plusDays(1);
        }

        // 注意：这里没有添加结束日期那天的结束时间（如果有需要，应该单独处理）
        // 因为通常我们不需要一个区间的结束时间点（除非有特定需求）

        return dateTimeList;
    }

    /**
     * xx
     *
     * @param dateList xx
     * @param fqMap xx
     * @param bjMap xx
     * @param collaborationList xx
     * @return 结果
     */
    private static List<CountItem> getCountItems(List<LocalDateTime> dateList, Map<String, Long> fqMap,
                         Map<String, Long> bjMap, List<Collaboration> collaborationList) {
        List<CountItem> collect = dateList.stream().map(dateTime -> {

            CountItem parent = new CountItem();
            String key = dateTime.format(DateTimeFormatter.BASIC_ISO_DATE);
            parent.setName(key);
            String date = dateTime.format(DateTimeFormatter.ofPattern("MM月dd日"));
            parent.setShowName(date);

            // 发起协作数量
            CountItem fqxzItem = new CountItem();
            fqxzItem.setKey("fqxzCount");
            fqxzItem.setShowName(date);
            fqxzItem.setCount(fqMap.getOrDefault(key, 0L));
            // 办结协作数量
            CountItem bjxzItem = new CountItem();
            bjxzItem.setKey("bjxzCount");
            bjxzItem.setShowName(date);
            bjxzItem.setCount(bjMap.getOrDefault(key, 0L));
            // 超时协作数量
            CountItem csxzItem = new CountItem();
            csxzItem.setKey("csxzCount");
            csxzItem.setShowName(date);
            long csCount = 0;
            List<Collaboration> result;
            if (dateTime.toLocalDate().equals(LocalDateTime.now().toLocalDate())){
               result = collaborationList.stream().filter(collaboration -> collaboration.getFeedbackTime().isBefore(LocalDateTime.now())
                        && collaboration.getFeedbackTime().isAfter(dateTime.minusDays(1))).collect(Collectors.toList());
                csCount = CollectionUtils.isEmpty(result) ? 0 : result.size();
            }else {
                result = collaborationList.stream().filter(collaboration -> collaboration.getFeedbackTime().isBefore(dateTime)
                        && collaboration.getFeedbackTime().isAfter(dateTime.minusDays(1))).collect(Collectors.toList());
                csCount = CollectionUtils.isEmpty(result) ? 0 : result.size();
            }
            csxzItem.setCount(Objects.nonNull(csCount) ? csCount : 0);
            parent.setChildren(Arrays.asList(fqxzItem,bjxzItem,csxzItem));
            return parent;
        }).collect(Collectors.toList());
        return collect;
    }

    private List<CountItem> getCountItemsByHour(List<LocalDateTime> dateList, Map<String, Long> fqMap, Map<String, Long> bjMap, List<Collaboration> collaborationList) {
        List<CountItem> collect = new ArrayList<>();
        collect = dateList.stream().map(e->{
            CountItem item = new CountItem();
            String date = e.format(DateTimeFormatter.ofPattern("HH:mm"));
            item.setName(date);
            item.setShowName(date);
            Map<String,CountItem> childrenMap = new HashMap<>();
            //发起写作数量
            CountItem fqxzItem = new CountItem();
            fqxzItem.setKey("fqxzCount");
            fqxzItem.setShowName(date);
            LocalDateTime start = e.minusHours(1);
            String startDate = start.format(DateTimeFormatter.ofPattern("HH:mm"));
            fqxzItem.setCount(Objects.nonNull(fqMap.get(startDate)) ? fqMap.get(startDate) : 0);
            //办结协作数量
            CountItem bjxzItem = new CountItem();
            bjxzItem.setKey("bjxzCount");
            bjxzItem.setShowName(date);
            bjxzItem.setCount(Objects.nonNull(bjMap.get(startDate)) ? bjMap.get(startDate) : 0);
            //超时协作数量
            CountItem csxzItem = new CountItem();
            csxzItem.setKey("csxzCount");
            csxzItem.setShowName(date);
            long csCount = 0;
            List<Collaboration> result;
            result = collaborationList.stream().filter(collaboration -> collaboration.getFeedbackTime().isBefore(e)
                    && collaboration.getFeedbackTime().isAfter(start)).collect(Collectors.toList());
            csCount = CollectionUtils.isEmpty(result) ? 0 : result.size();
            csxzItem.setCount(Objects.nonNull(csCount) ? csCount : 0);
            item.setChildren(Arrays.asList(fqxzItem,bjxzItem,csxzItem));
            return item;
        }).collect(Collectors.toList());
        return collect;
    }

    private Map<String, Long> bulidAllResult(List<CountItem> countItems) {
        Map<String, Long> map = new HashMap<>();
        Long csxzCount = countItems.stream().map(e -> e.getChildren().stream()
                        .filter(a->a.getKey().equals("csxzCount")).findFirst().orElse(new CountItem())).map(it->it.getCount())
                        .filter(count->Objects.nonNull(count))
                        .mapToLong(Long::valueOf).sum();
        map.put("csxzCount",csxzCount);
        Long fqxzCount = countItems.stream()
                .map(e -> e.getChildren()
                        .stream()
                        .filter(a -> a.getKey().equals("fqxzCount"))
                        .findFirst()
                        .map(CountItem::getCount)
                        .orElse(0L))
                .reduce(0L, Long::sum);
        map.put("fqxzCount",fqxzCount);
        Long bjxzCount = countItems.stream().map(e -> e.getChildren().stream()
                        .filter(a->a.getKey().equals("bjxzCount")).findFirst().orElse(new CountItem())).map(it->it.getCount())
                        .filter(count->Objects.nonNull(count))
                        .mapToLong(Long::valueOf).sum();
        map.put("bjxzCount",bjxzCount);
        return map;
    }

}
