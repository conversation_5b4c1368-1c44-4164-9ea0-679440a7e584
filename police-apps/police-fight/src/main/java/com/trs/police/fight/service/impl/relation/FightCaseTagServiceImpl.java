package com.trs.police.fight.service.impl.relation;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.vo.CaseTagVO;
import com.trs.police.fight.constant.enums.CompositeType;
import com.trs.police.fight.domain.entity.FightCaseTagRelation;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.fight.domain.vo.CreateCompositeVO;
import com.trs.police.fight.domain.vo.MergeCompositeVO;
import com.trs.police.fight.service.FightCaseTagRelationService;
import com.trs.police.fight.service.RelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 案件标签关联
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FightCaseTagServiceImpl implements RelationService {

    @Resource
    private FightCaseTagRelationService fightcasetagrelationservice;

    @Override
    public Set<CompositeType> acceptType() {
        return new HashSet<>(Arrays.asList(CompositeType.COMPOSITE));
    }

    /**
     * 创建案件标签关联
     *
     * @param createCompositeVO 创建作战的vo
     * @param fightComposite    作战
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void createRelation(CreateCompositeVO createCompositeVO, FightComposite fightComposite) {
        //根据合成id删除案件标签关联信息
        fightcasetagrelationservice.remove(new QueryWrapper<FightCaseTagRelation>()
                .eq("composite_id", fightComposite.getId()));
        //保存案件标签关联信息
        saveCaseTagRelation(createCompositeVO, fightComposite);

    }

    /**
     * 保存案件标签关联
     *
     * @param createCompositeVO 创建作战的vo
     * @param fightComposite    作战
     */
    private void saveCaseTagRelation(CreateCompositeVO createCompositeVO, FightComposite fightComposite) {
        try {
            List<FightCaseTagRelation> list = new ArrayList<>();
            List<CaseTagVO> caseTagIdList = createCompositeVO.getCaseTag();
            if (caseTagIdList == null || caseTagIdList.isEmpty()) {
                return;
            }
            for (CaseTagVO vo : caseTagIdList) {
                FightCaseTagRelation relation = new FightCaseTagRelation();
                Long id = vo.getId();
                String name = vo.getName();
                relation.setCompositeId(fightComposite.getId());
                relation.setCaseTagId(id);
                relation.setCaseTagName(name);
                list.add(relation);
            }
            fightcasetagrelationservice.saveBatch(list);
        } catch (Exception e) {
            throw new RuntimeException(String.format("案件标签关联：%s", e.getMessage()));
        }
    }

    @Override
    public void rebuildRelation(MergeCompositeVO mergeCompositeVO, FightComposite source, FightComposite target) {
        // 标签关联到新作战上
        fightcasetagrelationservice.lambdaUpdate()
                .in(FightCaseTagRelation::getCompositeId, mergeCompositeVO.getMergeIds())
                .set(FightCaseTagRelation::getCompositeId, target.getId())
                .update();
        // 去掉重复的标签
        Map<String, List<FightCaseTagRelation>> groupByTagName = fightcasetagrelationservice.lambdaQuery()
                .eq(FightCaseTagRelation::getCompositeId, target.getId())
                .list()
                .stream()
                .collect(Collectors.groupingBy(FightCaseTagRelation::getCaseTagName));
        List<Long> repeatId = new ArrayList<>();
        for (List<FightCaseTagRelation> value : groupByTagName.values()) {
            if (value.size() > 1) {
                for (int i = 1; i < value.size(); i++) {
                    repeatId.add(value.get(i).getId());
                }
            }
        }
        fightcasetagrelationservice.removeBatchByIds(repeatId);
    }
}
