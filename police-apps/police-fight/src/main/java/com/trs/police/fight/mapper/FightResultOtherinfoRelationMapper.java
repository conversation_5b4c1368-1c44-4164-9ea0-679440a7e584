package com.trs.police.fight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.fight.domain.entity.FightResultOtherInfoRelation;
import com.trs.police.fight.statistic.DTO.CommonDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/03/19
 * @description: t_fight_result_otherinfo_relation
 */
@Mapper
public interface FightResultOtherinfoRelationMapper extends BaseMapper<FightResultOtherInfoRelation> {

    /**
     * 省厅协作大屏获取其他战国
     *
     * @param currentAreaCode code
     * @param commonParams params
     * @return 结果
     */
    List<FightResultOtherInfoRelation> selectRelatedResult(@Param("code") String currentAreaCode, @Param("dto") CommonDTO commonParams);

}
