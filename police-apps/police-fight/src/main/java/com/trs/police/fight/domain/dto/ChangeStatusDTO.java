package com.trs.police.fight.domain.dto;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * ChangeStatusDTO
 *
 * <AUTHOR>
 * @version v0
 * @since 1.0.0
 * @since 2024-03-20 15:26:14
 */
@Data
public class ChangeStatusDTO extends BaseDTO {

    /**
     * 线索ID
     */
    private String id;

    /**
     * 状态 10-关注 11-无效
     */
    private String status;

    private String reason;

    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getId(),"线索ID不能为空");
        checkNotEmpty(getStatus(),"状态不能为空");
        checkNotEmpty(getReason(),"关注/无效说明不能为空");
        return true;
    }
}
