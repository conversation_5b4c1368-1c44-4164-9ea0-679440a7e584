package com.trs.police.fight.sync.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.fight.domain.entity.CollaborationUserRelation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 省厅协作用户 关联
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "v_collaboration_user_relation")
@NoArgsConstructor
public class StCollaborationUser extends CollaborationUserRelation {

    /**
     * 用户对应的身份证号码
     */
    @TableField(value = "user_id_card")
    private String userIdCard;

    /**
     * 用户对应的部门的编码
     */
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 创建用户的身份证号码
     */
    @TableField(value = "create_user_id_card")
    private String createUserIdCard;

    /**
     * 创建部门的编码
     */
    @TableField(value = "create_dept_code")
    private String createDeptCode;

    /**
     * 更新用户的身份证号码
     */
    @TableField(value = "update_user_id_card")
    private String updateUserIdCard;

    /**
     * 更新用户的部门编码
     */
    @TableField(value = "update_dept_code")
    private String updateDeptCode;
}
