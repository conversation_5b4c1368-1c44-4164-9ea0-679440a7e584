package com.trs.police.fight.constant.enums;

import lombok.Getter;

import java.util.Optional;

/**
 * 技侦查询任务状态
 *
 * <AUTHOR>
 */
public enum JzQueryTaskStatusEnum {

    PUSHED(0), // 已推送（推送+通知运行）
    COMPLETED(1), // 已完成
    RUNNING(2), // 运行中
    RUN_ERROR(3), // 运行错误
    OVER_TIME(4); //超时

    @Getter
    private Integer code;

    JzQueryTaskStatusEnum(Integer code) {
        this.code = code;
    }

    /**
     * 根据code获取到枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static Optional<JzQueryTaskStatusEnum> fromCode(Integer code) {
        for (JzQueryTaskStatusEnum status : JzQueryTaskStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return Optional.of(status);
            }
        }
        return Optional.empty();
    }
}