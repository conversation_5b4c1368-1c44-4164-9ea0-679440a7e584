package com.trs.police.fight.sync.service.impl.consumer.fight;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.fight.constant.enums.FightResultRelatedType;
import com.trs.police.fight.domain.dto.FightResultDto;
import com.trs.police.fight.domain.entity.FightResult;
import com.trs.police.fight.mapper.FightCompositeMapper;
import com.trs.police.fight.service.FightResultFillingService;
import com.trs.police.fight.service.impl.FightResultMpServiceImpl;
import com.trs.police.fight.sync.VO.PermissionVO;
import com.trs.police.fight.sync.bean.DataSyncContext;
import com.trs.police.fight.sync.codec.BatchDecoder;
import com.trs.police.fight.sync.constant.DataSource;
import com.trs.police.fight.sync.constant.FightMessageType;
import com.trs.police.fight.sync.service.AbstractFightMsgConsumer;
import com.trs.police.fight.sync.service.FileService;
import com.trs.police.fight.sync.service.FileServiceFactory;
import com.trs.police.fight.sync.service.YsPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/12/11
 * @description: 战果关联信息的实现
 */
@Component
public class FightResultRelationInfoConsumer extends AbstractFightMsgConsumer<FightResultDto> {

    @Autowired
    private FightCompositeMapper fightCompositeMapper;

    @Autowired
    private List<FightResultFillingService> fightResultFillingServices;

    @Autowired
    private FightResultMpServiceImpl fightResultMpService;

    @Autowired
    private YsPermissionService ysPermissionService;

    @Autowired
    private FileServiceFactory fileServiceFactory;

    @Override
    public FightMessageType type() {
        return FightMessageType.FIGHT_RESULT;
    }

    @Override
    protected BatchDecoder<FightResultDto> batchDecoder() {
        return msg -> {
            // 解析主体
            List<FightResultDto> dtoList = JSONArray.parseArray(msg.getMessageBody(), FightResultDto.class);
            if (CollectionUtils.isEmpty(dtoList)) {
                return new ArrayList<>();
            }
            // 把第三方系统的用户匹配到本系统中
            PermissionVO permissionVO = ysPermissionService.buildPermissionMapping(msg);
            Integer dataSource = DataSource.findByFlag(msg.getSystem()).get().getCode();
            List<FightResultDto> result = new ArrayList<>();
            for (FightResultDto dto : dtoList) {
                CurrentUser createUser = ysPermissionService.findUser(
                        dto.getCreateUserId(),
                        dto.getCreateDeptId(),
                        permissionVO
                ).orElse(ysPermissionService.defaultUser());
                dto.setCreateUserId(createUser.getId());
                dto.setCreateDeptId(createUser.getDeptId());
                doAttachments(dto.getSupervises(), dataSource);
                doAttachments(dto.getCongratulatoryTelegram(), dataSource);
                doAttachments(dto.getLeadershipInstruction(), dataSource);
                doAttachments(dto.getClusterBattle(), dataSource);
                result.add(dto);
            }
            FightComposite fightComposite = fightCompositeMapper.selectOne(new QueryWrapper<FightComposite>()
                    .eq("source_primary_key", msg.getSourcePrimaryKey())
                    .eq("data_source", msg.getDataSource()));
            if (Objects.isNull(fightComposite)) {
                return new ArrayList<>();
            }
            result.forEach(r -> r.setCompositeId(fightComposite.getId()));
            return result;
        };
    }

    @Override
    protected List<FightResultDto> savedData(List<FightResultDto> msgData) {
        // 同步关联信息逻辑为先删除再插入，所以这里直接返回空集合
        return new ArrayList<>();
    }

    @Override
    protected Boolean equalToTheSaved(FightResultDto msgData, FightResultDto saved) {
        // 同步关联信息逻辑为先删除再插入，所以这里直接返回false
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected void processingContext(DataSyncContext<FightResultDto> context) {
        // 处理新增数据
        List<FightResultDto> newData = context.findNewData();
        if (CollectionUtils.isEmpty(newData)) {
            return;
        }
        FightResultDto fightResultDto = newData.get(0);

        FightResult localFightResult;
        List<FightResult> fightResults = fightResultMpService.lambdaQuery()
                .eq(FightResult::getCompositeId, fightResultDto.getCompositeId())
                .orderByDesc(AbstractBaseEntity::getId)
                .list();
        if (CollectionUtils.isEmpty(fightResults)) {
            FightResult fightResult = new FightResult();
            fightResult.setCompositeId(fightResultDto.getCompositeId());
            fightResult.setRelatedType(FightResultRelatedType.COMPOSITE.getCode());
            fightResult.setId(null);
            localFightResult = fightResult;
        } else {
            localFightResult = fightResults.get(0);
        }
        fightResultMpService.saveOrUpdate(localFightResult);

        for (FightResultDto dto : newData) {
            dto.setFightResultId(localFightResult.getId());
            dto.setCaseEvent(dto.getCaseEvent().stream().map(FightResultDto::caseOf).collect(Collectors.toList()));
            // 战果关联信息填报
            for (FightResultFillingService service : fightResultFillingServices) {
                // 暂时不进行删除
                service.fightResultDelete(dto);
                service.fightResultFilling(dto);
            }
        }

    }

    private void doAttachments(List<FightResultDto.OtherInfo> list, Integer dataSource) {
        FileService fileService = fileServiceFactory.getService(dataSource);

        for (FightResultDto.OtherInfo info : list) {
            List<FileInfoVO> files = new ArrayList<>();
            for (FileInfoVO file : info.getAttachments()) {
                FileInfoVO convert = fileService.convert(file).orElse(new FileInfoVO());
                files.add(convert);
            }
            info.setAttachments(files);
        }
    }
}
