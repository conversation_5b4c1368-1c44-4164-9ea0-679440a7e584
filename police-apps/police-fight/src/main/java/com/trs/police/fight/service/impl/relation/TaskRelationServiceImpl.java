package com.trs.police.fight.service.impl.relation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.fight.constant.enums.CompositeType;
import com.trs.police.fight.domain.entity.PlanTaskRelation;
import com.trs.police.fight.domain.vo.CreateCompositeVO;
import com.trs.police.fight.domain.vo.MergeCompositeVO;
import com.trs.police.fight.service.PlanTaskRelationService;
import com.trs.police.fight.service.RelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: dingkeyu
 * @date: 2024/12/26
 * @description: 关联任务
 */
@Service
@Slf4j
public class TaskRelationServiceImpl implements RelationService {

    @Autowired
    private PlanTaskRelationService planTaskRelationService;

    @Override
    public Set<CompositeType> acceptType() {
        return new HashSet<>(Arrays.asList(CompositeType.COMPOSITE));
    }

    @Override
    public void createRelation(CreateCompositeVO createCompositeVO, FightComposite fightComposite) {
        // 先删除旧关联数据
        planTaskRelationService.remove(new QueryWrapper<PlanTaskRelation>()
                .eq("relation_id", fightComposite.getId())
                .eq("relation_type", 2));
        if (StringUtils.isNotEmpty(createCompositeVO.getTasks())) {
            List<PlanTaskRelation> planTaskRelations = JSON.parseArray(createCompositeVO.getTasks(), PlanTaskRelation.class);
            planTaskRelations.forEach(relation -> {
                relation.setId(null);
                relation.setRelationId(fightComposite.getId());
                relation.setRelationType(2);
                String deptIds = JSON.parseArray(relation.getLeadDeptInfo()).stream()
                        .map(e -> ((JSONObject) e).getString("deptId"))
                        .collect(Collectors.joining(","));
                relation.setLeadDeptId(deptIds);
            });
            planTaskRelationService.saveBatch(planTaskRelations);
        }
    }

    @Override
    public void rebuildRelation(MergeCompositeVO mergeCompositeVO, FightComposite source, FightComposite target) {
        planTaskRelationService.lambdaUpdate()
                .eq(PlanTaskRelation::getRelationType, 2)
                .in(PlanTaskRelation::getRelationId, mergeCompositeVO.getMergeIds())
                .set(PlanTaskRelation::getRelationId, target.getId())
                .update();
    }
}
