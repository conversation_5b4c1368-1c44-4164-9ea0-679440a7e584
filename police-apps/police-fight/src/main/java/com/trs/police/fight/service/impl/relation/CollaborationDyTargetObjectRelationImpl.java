package com.trs.police.fight.service.impl.relation;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.fight.constant.enums.CollaborationRelatedTypeEnum;
import com.trs.police.fight.domain.bean.DyCollaborationFrom;
import com.trs.police.fight.domain.bean.TargetObject;
import com.trs.police.fight.domain.dto.CollaborationDto;
import com.trs.police.fight.domain.entity.CollaborationSceneRelation;
import com.trs.police.fight.service.CollaborationRelationService;
import com.trs.police.fight.service.CollaborationSceneRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 德阳 目标对象关联关系
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollaborationDyTargetObjectRelationImpl implements CollaborationRelationService {

    @Resource
    private CollaborationSceneRelationService collaborationSceneRelationService;

    @Autowired
    private OssService ossService;

    @Override
    public void createRelation(CollaborationDto dto, Collaboration collaboration) {
        try {
            CollaborationSceneRelation entity = new CollaborationSceneRelation();
            entity.setCollaborationId(collaboration.getId());
            entity.setRelatedType(CollaborationRelatedTypeEnum.DY.getCode());
            entity.setRelatedId(null);
            entity.setRelatedStrId(null);
            // 解析附件
            List<TargetObject> xlsx = Optional.ofNullable(collaboration.getAttachments())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(f -> Objects.nonNull(f.getExt()))
                    .filter(file -> file.getExt().contains("xlsx"))
                    .map(this::getJzFileTargetObject)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            DyCollaborationFrom dyFrom = dto.getDyFrom();
            if (Objects.isNull(dyFrom)) {
                dyFrom = new DyCollaborationFrom();
                dyFrom.setJzTargetObject(new ArrayList<>());
            }
            dyFrom.setJzFileTargetObject(xlsx);
            if (CollectionUtils.isEmpty(xlsx) && CollectionUtils.isEmpty(dyFrom.getJzTargetObject())) {
                return;
            }
            entity.setRelatedInfo(JSON.toJSONString(dyFrom));
            collaborationSceneRelationService.save(entity);
        } catch (Exception e) {
            log.error("创建德阳表单失败", e);
        }
    }

    @Override
    public void deleteRelation(Long collaborationId) {
        // 删除德阳关联关系
        QueryWrapper<CollaborationSceneRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("collaboration_id", collaborationId);
        queryWrapper.eq("related_type", CollaborationRelatedTypeEnum.DY.getCode());
        collaborationSceneRelationService.remove(queryWrapper);
    }

    private List<TargetObject> getJzFileTargetObject(FileInfoVO file) {
        try {
            ResponseEntity<byte[]> excelByte = ossService.download(String.valueOf(file.getId()));
            if (excelByte.getStatusCode().is2xxSuccessful()) {
                byte[] bytes = excelByte.getBody();
                return getJzFileTargetObjectByExcel(bytes);
            }
        } catch (Exception e) {
            log.error("解析excel错误", e);
        }
        return null;
    }

    /**
     * 根据excel 获取目标对象
     *
     * @param excel excel
     * @return 目标兑现
     * @throws Exception 异常
     */
    public List<TargetObject> getJzFileTargetObjectByExcel(byte[] excel) throws Exception {
        InputStream inputStream = new ByteArrayInputStream(excel);
        List<Map<Integer, String>> headData = new ArrayList<>();

        HeadListener headListener = new HeadListener(headData);
        EasyExcel.read(inputStream, headListener).headRowNumber(1).sheet().doRead();

        if (headData.isEmpty()) {
            return null;
        }

        Map<Integer, String> headMap = headData.get(0);
        if (!"姓名".equals(headMap.get(0)) || !"证件类型及号码".equals(headMap.get(1)) || !"标识码类型及标识码".equals(headMap.get(2)) || !"特殊身份".equals(headMap.get(3))) {
            return null;
        }

        inputStream.reset();
        List<TargetObject> targetObjects = new ArrayList<>();
        DataListener dataListener = new DataListener(targetObjects);
        EasyExcel.read(inputStream, TargetObject.class, dataListener).sheet().doRead();
        if (!targetObjects.isEmpty()) {
            return targetObjects;
        }
        return null;
    }

    private static class HeadListener extends AnalysisEventListener<Map<Integer, String>> {
        private final List<Map<Integer, String>> headData;

        public HeadListener(List<Map<Integer, String>> headData) {
            this.headData = headData;
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            headData.add(headMap);
        }

        @Override
        public void invoke(Map<Integer, String> integerStringMap, AnalysisContext analysisContext) {
            // Do nothing
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // Do nothing
        }
    }

    private static class DataListener extends AnalysisEventListener<TargetObject> {

        private final List<TargetObject> targetObjects;

        private Function<Map<Integer, String>, TargetObject> dataFunction;

        public DataListener(List<TargetObject> targetObjects) {
            this.targetObjects = targetObjects;
        }

        @Override
        public void invoke(TargetObject data, AnalysisContext context) {
            targetObjects.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // Do nothing
        }
    }
}
