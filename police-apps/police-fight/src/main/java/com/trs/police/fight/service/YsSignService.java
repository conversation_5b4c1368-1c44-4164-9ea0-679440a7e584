package com.trs.police.fight.service;

import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.fight.domain.vo.Collaboration.ApproveFormVO;
import com.trs.police.fight.domain.vo.Collaboration.SignRequestResultVO;
import com.trs.police.fight.domain.vo.Collaboration.SignResultVO;

/**
 * 签章服务
 *
 * <AUTHOR>
 */
public interface YsSignService {

    /**
     * 获取审批表信息
     *
     * @param id 业务id
     * @param relatedServiceCode 服务代码 {@link OperateModule}
     * @param actionCode 操作代码 {@link Operation}
     * @return 审批表信息
     */
    ApproveFormVO approveFormInfo(Long id, String relatedServiceCode, Integer actionCode);

    /**
     * 发起审批请求
     *
     * @param id 业务id
     * @param relatedServiceCode 服务代码 {@link OperateModule}
     * @param actionCode 操作代码 {@link Operation}
     * @return 发起审批请求结果
     */
    SignRequestResultVO signApply(Long id, String relatedServiceCode, Integer actionCode);

    /**
     * 保存签章结果
     *
     * @param id 业务id
     * @param relatedServiceCode 服务代码 {@link OperateModule}
     * @param actionCode 操作代码 {@link Operation}
     * @return 获取签章系统审批结果
     */
    SignResultVO saveSingResult(Long id, String relatedServiceCode, Integer actionCode);
}
