package com.trs.police.fight.sync.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文件服务工厂
 *
 * <AUTHOR>
 */
@Component
public class FileServiceFactory {

    @Autowired
    private List<FileService> fileService;

    /**
     * 获得文件服务
     *
     * @param dataSource 数据来源
     * @return 文件服务
     */
    public FileService getService(Integer dataSource) {
        return fileService.stream()
                .filter(service -> service.dataSource().getCode().equals(dataSource))
                .findAny()
                .get();
    }
}
