package com.trs.police.fight.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.constant.enums.CaseTypeEnum;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/31 14:44
 */
@Data
@TableName(value = "t_profile_case", autoResultMap = true)
@NoArgsConstructor
public class StProfileCaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新用户主键
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_user_id")
    private Long updateUserId;

    /**
     * 更新单位主键
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_dept_id")
    private Long updateDeptId;

    /**
     * 立案时间
     */
    @TableField("filing_time")
    private Date filingTime;

    /**
     * 案事件编号
     */
    private String asjbh;
    /**
     * 智能分类
     */
    private Integer intelligentClassify;
    /**
     * 重要程度
     */
    private Integer importantLevel;
    /**
     * 刑事、行政
     */
    private CaseTypeEnum caseType;
    /**
     * 案件标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> description;
    /**
     * 案件标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> infringementTarget;
    /**
     * 案件标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> suspectedPerson;
    /**
     * 案件标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> suspectedVehicle;

    /**
     * 涉案人
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> relatedPerson;
    /**
     * 串案
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> seriesCase;

    /**
     * 案件名称
     */
    private String ajmc;

    public StProfileCaseEntity(String asjbh, String ajmc, CaseTypeEnum caseType, Date larq) {
        this.asjbh = asjbh;
        this.caseType = caseType;
        this.ajmc = ajmc;
        this.filingTime = larq;
        setUpdateTime(LocalDateTime.now());
    }
}
