package com.trs.police.fight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.fight.domain.entity.FightResult;
import com.trs.police.fight.statistic.DTO.CommonDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/02/28
 * @description: t_fight_result表查询接口
 */
@Mapper
public interface FightResultMapper extends BaseMapper<FightResult> {

    /**
     * getFightResultById
     *
     * @param fightResultId 战果id
     * @return {@link FightResult}
     */
    FightResult getFightResultById(@Param("fightResultId") Long fightResultId);

    /**
     * 获取省厅大屏的战果信息
     *
     * @param currentAreaCode 区域代码
     * @param commonParams 参数
     * @return 结果
     */
    List<FightResult> getStBigScreenFightResult(@Param("code") String currentAreaCode,@Param("dto") CommonDTO commonParams);
}
