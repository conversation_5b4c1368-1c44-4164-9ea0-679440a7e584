package com.trs.police.fight.service.compositeItem;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.core.constant.enums.CompositeRoleEnum;
import com.trs.police.fight.domain.dto.CompositeItemSearchDTO;
import com.trs.police.common.core.entity.FightCompositeUserRelation;
import com.trs.police.fight.mapper.FightCompositeUserRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户部门检索
 *
 * <AUTHOR>
 */
@Component
public class UserDeptServiceImpl implements ICompositeItemSearchService {

    @Autowired
    private FightCompositeUserRelationMapper fightCompositeUserRelationMapper;

    @Autowired
    private PermissionService permissionService;

    /**
     * 检索合成子项
     *
     * @param compositeItemSearchDTO CompositeItemSearchDTO
     * @return CompositeItemSearchDTO
     */
    @Override
    public CompositeItemSearchDTO searchCompositeItems(CompositeItemSearchDTO compositeItemSearchDTO) {
        List<Long> compositeItemIds = compositeItemSearchDTO.getCompositeItemIds();
        Map<Long, List<FightCompositeUserRelation>> compositeIdAndFightCompositeUserRelationMap = new HashMap<>(0);
        Map<Long, List<DeptDto>> compositeIdAndDeptListMap = new HashMap<>(0);
        if (!CollectionUtils.isEmpty(compositeItemIds)) {
            QueryWrapper<FightCompositeUserRelation> queryWrapper = new QueryWrapper<FightCompositeUserRelation>()
                    .in("composite_id", compositeItemIds);
            List<FightCompositeUserRelation> userRelations = fightCompositeUserRelationMapper.selectList(queryWrapper);
            compositeIdAndFightCompositeUserRelationMap = userRelations.stream()
                    .collect(Collectors.groupingBy(FightCompositeUserRelation::getCompositeId));
            List<Long> deptIds = userRelations.stream()
                    .filter(relation -> relation.getRole() == CompositeRoleEnum.ORGANIZER
                            || relation.getRole() == CompositeRoleEnum.ASSISTANT)
                    .map(FightCompositeUserRelation::getDeptId)
                    .distinct()
                    .collect(Collectors.toList());
            List<DeptDto> dtoList = deptIds.isEmpty() ? new ArrayList<>() : permissionService.getDeptByIds(deptIds);
            compositeIdAndDeptListMap = userRelations.stream().collect(Collectors.groupingBy(FightCompositeUserRelation::getCompositeId,
                    Collectors.collectingAndThen(Collectors.toList(),
                            relations -> getDeptList(relations, dtoList))));
        }
        compositeItemSearchDTO.setCompositeIdAndDeptListMap(compositeIdAndDeptListMap);
        compositeItemSearchDTO.setCompositeIdAndFightCompositeUserRelationMap(compositeIdAndFightCompositeUserRelationMap);
        return null;
    }

    /**
     * 获取合成关联关系下，部门数据
     *
     * @param relations 单个合成下的关联
     * @param dtoList 所有的部门dto
     * @return 单个合成作战对应的部门dto
     */
    private List<DeptDto> getDeptList(List<FightCompositeUserRelation> relations, List<DeptDto> dtoList) {
        List<Long> deptIds = relations.stream().map(FightCompositeUserRelation::getDeptId).distinct().collect(Collectors.toList());
        return dtoList.stream().filter(deptDto -> deptIds.contains(deptDto.getId())).collect(Collectors.toList());
    }

}
