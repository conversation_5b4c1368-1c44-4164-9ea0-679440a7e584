package com.trs.police.fight.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName CluePoolApproveList
 * @Description 线索池审批单实体
 * <AUTHOR>
 * @Date 2024/3/19 18:25
 **/
@Data
@TableName("tb_clue_approve_list")
public class ClueApproveList implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 线索主键ID
     */
    private Long clueId;

    /**
     * 审批单类型
     * <ul>
     *     <li>specific:主侦</li>
     *     <li>center：中心</li>
     * </ul>
     */
    private String approveType;

    /**
     * 审批单位的ID
     */
    private Long approveDeptId;

    /**
     * 审批人
     */
    private String approveUser;

    /**
     * 审批人真实姓名
     */
    private String approveUserTrueName;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 是否已处理
     * 0：否
     * 1：是
     */
    private Integer isDeal;

    /**
     * 创建时间
     */
    private LocalDateTime crTime;

    /**
     * 创建人
     */
    private String crUser;

    /**
     * 创建人真实姓名
     */
    private String crUserTrueName;

    /**
     * 创建单位的ID
     */
    private Long crDeptId;

    /**
     * 是否删除
     * 0：否
     * 1：是
     */
    private Integer isDel;

    public ClueApproveList() {
        this.isDeal = 0;
        this.isDel = 0;
    }
}
