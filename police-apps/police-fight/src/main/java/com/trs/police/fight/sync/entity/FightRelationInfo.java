package com.trs.police.fight.sync.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.fight.domain.entity.FightCompositeCaseEventRelation;
import com.trs.police.fight.domain.entity.FightCompositePersonRelation;
import com.trs.police.fight.domain.entity.FightCompositePoliceIntelligenceDO;
import com.trs.police.fight.sync.bean.RelationInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @author: dingkeyu
 * @date: 2024/12/11
 * @description: 作战关联信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_fight_composite_relation_view")
@NoArgsConstructor
public class FightRelationInfo extends RelationInfo {

    /**
     * 作战id
     */
    private Long compositeId;

    /**
     * 关联案件id
     */
    private Long caseEventId;

    /**
     * 关联案件code
     */
    private String caseEventCode;

    /**
     * 关联人员id
     */
    private Long personId;

    /**
     * 关联人员身份证号码
     */
    private String personIdNumber;

    /**
     * 关联人员类型，0：嫌疑人，1：相关人员，2：临控人员，3：常控人员，4：在逃人员
     */
    private Integer personKind;

    /**
     * 关联警情id
     */
    private String intelligenceId;

    /**
     * 数据转换
     *
     * @param fightRelationInfo fightrelationinfo
     * @return {@link FightCompositeCaseEventRelation}
     */
    public static FightCompositeCaseEventRelation caseOf(FightRelationInfo fightRelationInfo) {
        FightCompositeCaseEventRelation relation = new FightCompositeCaseEventRelation();
        BeanUtil.copyPropertiesIgnoreNull(fightRelationInfo, relation, "caseEventId");
        relation.setCreateTime(TimeUtil.stringToLocalDateTime(fightRelationInfo.getCreateTime(), ""));
        relation.setUpdateTime(TimeUtil.stringToLocalDateTime(fightRelationInfo.getUpdateTime(), ""));
        final ProfileService profileService = BeanUtil.getBean(ProfileService.class);
        String caseId = profileService.getCaseIdByBh(fightRelationInfo.getCaseEventCode());
        relation.setCaseEventId(Objects.nonNull(caseId) ? Long.valueOf(caseId) : null);
        return relation;
    }

    /**
     * 数据转换
     *
     * @param fightRelationInfo fightrelationinfo
     * @return {@link FightCompositePersonRelation}
     */
    public static FightCompositePersonRelation personOf(FightRelationInfo fightRelationInfo) {
        FightCompositePersonRelation relation = new FightCompositePersonRelation();
        BeanUtil.copyPropertiesIgnoreNull(fightRelationInfo, relation, "personId");
        relation.setCreateTime(TimeUtil.stringToLocalDateTime(fightRelationInfo.getCreateTime(), ""));
        relation.setUpdateTime(TimeUtil.stringToLocalDateTime(fightRelationInfo.getUpdateTime(), ""));
        relation.setIdNumber(fightRelationInfo.getPersonIdNumber());
        final ProfileService profileService = BeanUtil.getBean(ProfileService.class);
        PersonVO personVO = profileService.getPersonByIdNumber(relation.getIdNumber());
        relation.setPersonId(Objects.nonNull(personVO) ? personVO.getId() : null);
        return relation;
    }

    /**
     * 数据转换
     *
     * @param fightRelationInfo fightrelationinfo
     * @return {@link FightCompositePoliceIntelligenceDO}
     */
    public static FightCompositePoliceIntelligenceDO jqOf(FightRelationInfo fightRelationInfo) {
        FightCompositePoliceIntelligenceDO relation = new FightCompositePoliceIntelligenceDO();
        BeanUtil.copyPropertiesIgnoreNull(fightRelationInfo, relation, "intelligenceId");
        relation.setCreateTime(TimeUtil.stringToLocalDateTime(fightRelationInfo.getCreateTime(), ""));
        relation.setUpdateTime(TimeUtil.stringToLocalDateTime(fightRelationInfo.getUpdateTime(), ""));
        final ProfileService profileService = BeanUtil.getBean(ProfileService.class);
        String jqId = profileService.getJqIdByBh(fightRelationInfo.getIntelligenceId());
        relation.setIntelligenceId(Objects.nonNull(jqId) ? Long.valueOf(jqId) : null);
        return relation;
    }

}
