package com.trs.police.fight.builder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.police.common.core.constant.enums.CompositeMembersStatusEnum;
import com.trs.police.common.core.constant.enums.CompositeRoleEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.entity.FightCompositeUserRelation;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.DataUtils;
import com.trs.police.common.core.vo.CaseTagVO;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.core.vo.FightClueVO;
import com.trs.police.common.core.vo.approval.ApprovalInfoVO;
import com.trs.police.common.core.vo.profile.SeriesCaseListVO;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.constant.FightCompositeConstant;
import com.trs.police.fight.constant.enums.CompositeJoinEnum;
import com.trs.police.fight.constant.enums.PlanSchedulingMeasureEnum;
import com.trs.police.fight.domain.CompositeTypeInfo;
import com.trs.police.fight.domain.entity.*;
import com.trs.police.fight.domain.vo.CompositeDetailVO;
import com.trs.police.fight.domain.vo.CompositeUserInfo;
import com.trs.police.fight.domain.vo.FightCompositeWorkSceneVO;
import com.trs.police.fight.domain.vo.PlanTaskRelationVO;
import com.trs.police.fight.helper.DutyUserHelper;
import com.trs.police.fight.mapper.FightCompositeApplyMapper;
import com.trs.police.fight.mapper.FightCompositeCaseEventRelationMapper;
import com.trs.police.fight.mapper.FightCompositeWorkSceneRelationMapper;
import com.trs.police.fight.mapper.PlanTaskRelationMapper;
import com.trs.police.fight.service.FightCompositeClueRelationService;
import com.trs.police.fight.service.FightCompositeUserRelationService;
import com.trs.police.fight.service.impl.FightCaseTagServiceRelationImpl;
import com.trs.police.fight.service.impl.FightCompositePersonRelationServiceImpl;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trs.police.fight.constant.enums.PlanSchedulingMeasureEnum.*;

/**
 * 合成详情builder
 *
 * <AUTHOR>
 * @since 2022/4/22 10:58
 **/
@Data
@Builder
public class CompositeDetailBuilder {

    private final PermissionService permissionService;
    private final DictService dictService;
    private final FightCompositeUserRelationService fightCompositeUserRelationService;
    private final FightCompositeCaseEventRelationMapper fightCompositeCaseEventRelationMapper;
    private final OssService ossService;
    private final FightComposite fightComposite;
    private final CurrentUser currentUser;
    private final FightCompositeApplyMapper fightCompositeApplyMapper;
    private final FightCaseTagServiceRelationImpl fightCaseTagServiceRelation;
    private final FightCompositeClueRelationService fightCompositeClueRelationService;
    private final FightCompositePersonRelationServiceImpl fightCompositePersonRelationService;
    private final PlanEntity planEntity;
    private final FightCompositeWorkSceneRelationMapper fightCompositeWorkSceneRelationMapper;
    private final PlanTaskRelationMapper planTaskRelationMapper;
    private final DutyUserHelper dutyUserHelper;
    // 创建用户
    private final CurrentUser createUser;
    // 主办用户
    private final CompositeUserInfo organization;
    // 预案关联得调度资源
    private final List<PlanSchedulingMeasureRelation>  measureRelationList;


    /**
     * 数据处理
     *
     * @return {@link CompositeDetailVO}
     */
    public CompositeDetailVO of() {
        CompositeDetailVO compositeDetailVO = new CompositeDetailVO();
        //查询案件标签名称
        compositeDetailVO.setCaseTag(getCaseTag(fightComposite.getId()));
        compositeDetailVO.setRequire(fightComposite.getRequire());
        compositeDetailVO.setCreateTime(fightComposite.getCreateTime());
        compositeDetailVO.setStatus(fightComposite.getStatus().getCode());
        compositeDetailVO.setIsAllowed(fightComposite.getIsAllowApply());
        compositeDetailVO.setTitle(fightComposite.getTitle());
        compositeDetailVO.setIsClassified(fightComposite.getIsClassified());

        // 类别信息
        DictDto type = dictService.getDictByTypeAndCode(FightCompositeConstant.FIGHT_COMPOSITE_TYPE,
                fightComposite.getType());
        compositeDetailVO.setType(Objects.nonNull(type) ? type.getName() : null);
        CompositeTypeInfo typeInfo = new CompositeTypeInfo();
        typeInfo.setCode(fightComposite.getType());
        typeInfo.setName(type.getName());
        typeInfo.setDesc(type.getDictDesc());
        if (Objects.nonNull(fightComposite.getSubType())) {
            DictDto subType = dictService.getDictByTypeAndCode(FightCompositeConstant.FIGHT_COMPOSITE_TYPE,
                    fightComposite.getSubType());
            compositeDetailVO.setSubtype(Objects.nonNull(subType) ? subType.getName() : null);
            typeInfo.setSubCode(subType.getCode());
            typeInfo.setSubName(subType.getName());
            typeInfo.setSubDesc(subType.getDictDesc());
        }
        compositeDetailVO.setTypeInfo(typeInfo);

        compositeDetailVO.setCompositionType(compositeDetailVO.getType());
        // 审批信息
        ApprovalService approvalService = BeanUtil.getBean(ApprovalService.class);
        ApprovalInfoVO approval = approvalService.getApprovals(OperateModule.COMPOSITE.getCode(),
                fightComposite.getId(), null).stream().findFirst().orElse(new ApprovalInfoVO());
        // 线索
        List<FightClueVO> clues = fightCompositeClueRelationService.getRelationList(Collections.singletonList(fightComposite.getId()));
        compositeDetailVO.setClues(clues);
        compositeDetailVO.setApprovalInfo(approval);
        compositeDetailVO.setFile(fightComposite.getAttachments());
        compositeDetailVO.setEvents(new ArrayList<>());
        compositeDetailVO.setSuspects(new ArrayList<>());
        compositeDetailVO.setCompositeType(fightComposite.getCompositeType());
        compositeDetailVO.setUrgentTypeId(fightComposite.getUrgentTypeId());
        compositeDetailVO.setPlanId(fightComposite.getPlanId());
        compositeDetailVO.setWarningInfo(fightComposite.getWarningInfo());
        compositeDetailVO.setPoliceIntelligenceInfo(fightComposite.getJqInfo());
        compositeDetailVO.setPlanName(planEntity.getName());
        compositeDetailVO.setPlanLevel(planEntity.getLevel());
        compositeDetailVO.setWorkObject(getWorkScene(fightComposite.getId()));
        compositeDetailVO.setPoliceIntelligenceInfo(fightComposite.getJqInfo());
        compositeDetailVO.setCreateUserName(createUser.getRealName());
        compositeDetailVO.setCreateDeptName(createUser.getDept().getShortName());
        compositeDetailVO.setOrganizer(organization.getUserName());
        compositeDetailVO.setOrganizedDept(organization.getDeptShortName());
        compositeDetailVO.setCompositeStatus(fightComposite.getStatus().getCode());
        compositeDetailVO.setCompositeStatusName(fightComposite.getStatus().getName());
        compositeDetailVO.setEventPlace(fightComposite.getEventPlace());
        compositeDetailVO.setFormType(fightComposite.getFormType());
        compositeDetailVO.setJqbh(fightComposite.getJqbh());
        compositeDetailVO.setJqlxdm(fightComposite.getJqlxdm());
        compositeDetailVO.setJqContent(fightComposite.getJqContent());
        compositeDetailVO.setTarget(fightComposite.getTarget());
        compositeDetailVO.setElement(fightComposite.getElement());
        // 调度资源
        if (!CollectionUtils.isEmpty(measureRelationList)) {
            measureRelationList.stream()
                    .filter(mr -> POLICE.equals(PlanSchedulingMeasureEnum.codeOf(mr.getMeasureType().intValue())))
                    .findAny()
                    .ifPresent(mr -> compositeDetailVO.setJinYuanRange(mr.getMeasureRange()));
            measureRelationList.stream()
                    .filter(mr -> POLICE_CAR.equals(PlanSchedulingMeasureEnum.codeOf(mr.getMeasureType().intValue())))
                    .findAny()
                    .ifPresent(mr -> compositeDetailVO.setJinCheRange(mr.getMeasureRange()));
            measureRelationList.stream()
                    .filter(mr -> CAMERA.equals(PlanSchedulingMeasureEnum.codeOf(mr.getMeasureType().intValue())))
                    .findAny()
                    .ifPresent(mr -> compositeDetailVO.setSourceRange(mr.getMeasureRange()));
        }
        // 警情类别明文
        List<DictDto> sthyJqLabel = dictService.getDictListByType("sthy_jq_label");
        Map<String, String> map = org.springframework.util.CollectionUtils.isEmpty(sthyJqLabel)
                ? new HashMap<>()
                : sthyJqLabel.stream()
                        .collect(Collectors.toMap(DictDto::getDictDesc, DictDto::getName));
        compositeDetailVO.setJqlxdmmc(map.get(compositeDetailVO.getJqlxdm()));
        compositeDetailVO.setIsNeedSms(fightComposite.getIsNeedSms());
        return compositeDetailVO;
    }

    /**
     * 设置文件信息
     *
     * @param compositeDetailVO 详情
     */
    public void setRelation(CompositeDetailVO compositeDetailVO) {
        //案事件信息
        List<SeriesCaseListVO> event = fightCompositeCaseEventRelationMapper.getCaseEventByCompositeId(fightComposite.getId());
        List<Dict2VO> jwzhCaseStatus = dictService.commonSearch("jwzh_BD_D_XSAJYWZTMXDM", null, null, null);
        event.forEach(e -> {
            jwzhCaseStatus.stream()
                    .filter(j -> Objects.equals(j.getDictDesc(), e.getCaseStatus()))
                    .findAny()
                    .map(Dict2VO::getName)
                    .ifPresent(e::setCaseStatusName);
        });
        compositeDetailVO.setEvents(event);
        //用户信息
        List<FightCompositeUserRelation> fightCompositeUserRelation = fightCompositeUserRelationService.list(
                Wrappers.lambdaQuery(FightCompositeUserRelation.class)
                        .eq(FightCompositeUserRelation::getCompositeId, fightComposite.getId()));
        compositeDetailVO.setPartners(getUserInfoList(fightCompositeUserRelation));
        //设置状态
        FightCompositeUserRelation one = fightCompositeUserRelationService.getOne(
                Wrappers.lambdaQuery(FightCompositeUserRelation.class)
                        .eq(FightCompositeUserRelation::getCompositeId, fightComposite.getId())
                        .eq(FightCompositeUserRelation::getUserId, currentUser.getId())
                        .eq(FightCompositeUserRelation::getDeptId, currentUser.getDept().getId()));
        if (Objects.nonNull(one)) {
            compositeDetailVO.setUserRole(one.getRole().getCode());
            compositeDetailVO.setJoinStatus(String.valueOf(CompositeJoinEnum.IN_COMPOSITE.getId()));
        } else {
            FightCompositeApply applyListByCompositeApply = fightCompositeApplyMapper.getApplyByUserIdAndCompositeId(
                    currentUser.getId(), fightComposite.getId());
            if (Objects.nonNull(applyListByCompositeApply) && Objects.isNull(applyListByCompositeApply.getIsAgree())) {
                compositeDetailVO.setJoinStatus(String.valueOf(CompositeJoinEnum.APPLY_COMPOSITE.getId()));
            } else {
                compositeDetailVO.setJoinStatus(Boolean.TRUE.equals(fightComposite.getIsAllowApply()) ? String.valueOf(
                        CompositeJoinEnum.CAN_COMPOSITE.getId()) : null);
            }
            compositeDetailVO.setUserRole(CompositeRoleEnum.CUSTOM.getCode());
        }
        //设置涉案人员
        compositeDetailVO.setPersons(fightCompositePersonRelationService.getRelationList(fightComposite.getId()));
        //设置关联任务
        List<PlanTaskRelationVO> planTaskRelations = planTaskRelationMapper.selectList(new QueryWrapper<PlanTaskRelation>()
                .eq("relation_type", 2)
                .eq("relation_id", fightComposite.getId())).stream()
                .map(PlanTaskRelationVO::of).collect(Collectors.toList());;
        compositeDetailVO.setTasks(JSON.toJSONString(planTaskRelations));
    }

    /**
     * 获取案件标签名称
     *
     * @param id 合成id
     * @return {@link CaseTagVO}
     */
    private List<CaseTagVO> getCaseTag(Long id) {
        List<CaseTagVO> caseTagVOList = new ArrayList<>();
        List<FightCaseTagRelation> caseTagRelations = fightCaseTagServiceRelation.list(
                new QueryWrapper<FightCaseTagRelation>().eq("composite_id", id));
        for (FightCaseTagRelation caseTagRelation : caseTagRelations) {
            CaseTagVO vo = new CaseTagVO();
            vo.setName(caseTagRelation.getCaseTagName());
            caseTagVOList.add(vo);
        }
        return caseTagVOList;
    }

    /**
     * 用户信息
     *
     * @param fightCompositeUserRelation 用户信息
     * @return {@link CompositeUserInfo}
     */
    public List<CompositeUserInfo> getUserInfoList(
            List<FightCompositeUserRelation> fightCompositeUserRelation) {
        List<CompositeUserInfo> list = fightCompositeUserRelation.stream()
                .filter(e -> !CompositeMembersStatusEnum.REFUSED.getCode().equals(e.getReceiveStatus())) // 过滤掉已拒绝的
                .map(getFightCompositeUserRelationUserInfoFunction())
                .collect(Collectors.toList());
        List<Long> userId = list.stream()
                .map(CompositeUserInfo::getUserId)
                .distinct()
                .collect(Collectors.toList());
        List<UserDto> user = CollectionUtils.isEmpty(userId)
                ? new ArrayList<>()
                : permissionService.getUserListById(userId);
        Function<CompositeUserInfo, Optional<UserDto>> findUserById = DataUtils.findSingleByT(
                list,
                (us) -> user,
                (d1, d2) -> Objects.equals(d1.getUserId(), d2.getId()),
                u -> new StringBuilder().append(u.getUserId()).append("-").append(u.getDeptId()).toString()
        );
        Map<Long, String> postMap = dictService.getDictByType("post_type").stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName));
        list.forEach(u -> findUserById.apply(u).ifPresent(userDto -> {
            u.setDuty(userDto.getDuty());
            u.setPoliceCode(userDto.getPoliceCode());
            u.setPostName(postMap.get(userDto.getPostCode()));
        }));
        List<String> todayDutyUsers = Objects.isNull(dutyUserHelper) ? new ArrayList<>() :  dutyUserHelper.getTodayDutyUser();
        list.forEach(u -> {
            if (todayDutyUsers.contains(String.format("%s-%s", u.getDeptId(), u.getUserId()))) {
                u.setZbStatus(1);
            }
        });
        list.sort(Comparator.comparingLong(u -> Optional.ofNullable(u.getRole()).orElse(CompositeRoleEnum.PARTNER.getCode())));
        return list;
    }

    /**
     * 查询
     *
     * @return 信息
     */
    public Function<FightCompositeUserRelation, CompositeUserInfo> getFightCompositeUserRelationUserInfoFunction() {
        return relation -> {
            UserDto user = permissionService.getUserById(relation.getUserId());
            DeptDto dept = user.getDeptList().stream().filter(d -> d.getId().equals(relation.getDeptId())).findAny()
                    .orElse(new DeptDto());
            CompositeUserInfo userInfo = new CompositeUserInfo();
            userInfo.setUserId(user.getId());
            userInfo.setRole(relation.getRole().getCode());
            userInfo.setUserName(user.getRealName());
            userInfo.setDeptId(dept.getId());
            userInfo.setDeptCode(dept.getCode());
            userInfo.setDeptName(dept.getName());
            userInfo.setDeptShortName(dept.getShortName());
            userInfo.setApprovalId(relation.getApprovalId());
            userInfo.setStatus(relation.getStatus());
            userInfo.setReceiveStatus(relation.getReceiveStatus());
            userInfo.setParticipated(1);
            return userInfo;
        };
    }

    /**
     * 获取合成关联的工作对象
     *
     * @param compositeId 合成id
     * @return 不同场景的工作对象map
     */
    private List<FightCompositeWorkSceneVO> getWorkScene(Long compositeId) {
        Map<Long, List<JSONObject>> map = fightCompositeWorkSceneRelationMapper.selectList(
                        Wrappers.lambdaQuery(FightCompositeWorkSceneRelation.class)
                                .eq(FightCompositeWorkSceneRelation::getCompositeId, compositeId))
                .stream().collect(Collectors.groupingBy(FightCompositeWorkSceneRelation::getRelateType,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream()
                                        .map(data -> JSONObject.parseObject(data.getWorkObject()))
                                        .collect(Collectors.toList()))));
        return map.entrySet().stream().map(entry -> new FightCompositeWorkSceneVO(entry.getKey(), entry.getValue())).collect(Collectors.toList());
    }

}
