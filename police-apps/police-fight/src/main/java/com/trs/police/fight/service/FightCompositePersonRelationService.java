package com.trs.police.fight.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trs.police.common.core.vo.profile.PersonCaseVO;
import com.trs.police.fight.domain.entity.FightCompositePersonRelation;

import java.util.List;

/**
 * 涉案人员
 *
 * <AUTHOR>
 * @date 2024/5/29
 */
public interface FightCompositePersonRelationService extends IService<FightCompositePersonRelation> {

    /**
     * 获取涉案人员列表
     *
     * @param compositeId 合成id
     * @return {@link List}<{@link PersonCaseVO}>
     */
    List<PersonCaseVO> getRelationList(Long compositeId);
}
