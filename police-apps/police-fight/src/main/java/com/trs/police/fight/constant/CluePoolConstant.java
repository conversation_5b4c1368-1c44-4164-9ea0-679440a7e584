package com.trs.police.fight.constant;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * CluePoolConstant
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/19 14:00
 * @since 1.0
 */
public class CluePoolConstant {

    public static final String POLICE_KIND = "police_kind";

    /**
     * 主侦移交线索
     */
    public static final String CLUE_ACTION_LOGS_KEY_SPECIFIC_APPROVE_TRANSFER = "specificApprove.transfer";

    /**
     * 主侦关注线索
     */
    public static final String CLUE_ACTION_LOGS_KEY_SPECIFIC_APPROVE_FOCUS = "specificApprove.focus";

    /**
     * 主侦无效线索
     */
    public static final String CLUE_ACTION_LOGS_KEY_SPECIFIC_APPROVE_INVALID = "specificApprove.invalid";

    /**
     * 主侦初研立线
     */
    public static final String CLUE_ACTION_LOGS_KEY_SPECIFIC_APPROVE_CREATE_LINE = "specificApprove.create.line";

    /**
     * 主侦初研成案
     */
    public static final String CLUE_ACTION_LOGS_KEY_SPECIFIC_APPROVE_CREATE_CASE = "specificApprove.create.case";

    /**
     * 中心审批同意
     */
    public static final String CLUE_ACTION_LOGS_KEY_CENTER_APPROVE_AGREE = "centerApprove.agree";

    /**
     * 中心审批不同意
     */
    public static final String CLUE_ACTION_LOGS_KEY_CENTER_APPROVE_DISAGREE = "centerApprove.disagree";

    /**
     * 中心审批驳回
     */
    public static final String CLUE_ACTION_LOGS_KEY_CENTER_APPROVE_REJECT = "centerApprove.reject";

    /**
     * 审批单类型 - 主侦
     */
    public static final String CLUE_APPROVE_LIST_TYPE_SPECIFIC = "specific";

    /**
     * 审批单类型 - 中心
     */
    public static final String CLUE_APPROVE_LIST_TYPE_CENTER = "center";

    /**
     * 操作日志key - 创建线索
     */
    public static final String CLUE_ACTION_LOGS_KEY_CREATE = "create";

    /**
     * 数据类型 - 我上报的
     */
    public static final String CLUE_LIST_DATA_TYPE_OWN = "own";

    /**
     * 数据类型 - 本部门的
     */
    public static final String CLUE_LIST_DATA_TYPE_DEPT = "dept";

    /**
     * 倒序排序标识
     */
    public static final String ORDER_BY_DESC_SYMBOL = "-";

    /**
     * 线索类型 - 涉稳
     */
    public static final String CLUE_TYPE_SHE_WEN = "涉稳";

    /**
     * 线索类型 - 涉案
     */
    public static final String CLUE_TYPE_SHE_AN = "涉案";

    /**
     * 线索类型 - 线索
     */
    public static final String CLUE_TYPE_XIAN_SUO = "线索";

    /**
     * 线索类型 - 其他
     */
    public static final String CLUE_TYPE_QI_TA = "其他";

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2024</p>
     * <p>Company:      www.trs.com.cn</p>
     * CluePoolConstant
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2024/3/19 14:01
     * @since 1.0
     */
    public enum Status {
        CAOGAOXING(-1, "草稿箱"),
        DAICHULI(0, "待处理"),
        GUANZHU(10, "关注"),
        WUXIAO(11, "无效"),
        LIXIAN(20, "立线"),
        CHENGAN(21, "成案");

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        private Integer code;

        private String name;

        /**
         * getCode<BR>
         *
         * @return 结果
         * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
         * @date 创建时间：2024/3/19 14:07
         */
        public Integer getCode() {
            return code;
        }

        /**
         * getName<BR>
         *
         * @return 结果
         * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
         * @date 创建时间：2024/3/19 14:07
         */
        public String getName() {
            return name;
        }

        /**
         * getNameByCode<BR>
         *
         * @param code 参数
         * @return 结果
         * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
         * @date 创建时间：2024/3/19 14:09
         */
        public static String getNameByCode(Integer code) {
            for (Status value : Status.values()) {
                if (value.getCode().equals(code)) {
                    return value.getName();
                }
            }
            throw new RuntimeException("根据Code[" + code + "]无法得到对应的状态");
        }

        /**
         * 获取到现有支持的Code
         *
         * @return 结果
         */
        public static List<Integer> getCodes() {
            List<Integer> result = new ArrayList<>();
            for (Status value : Status.values()) {
                result.add(value.getCode());
            }
            return result;
        }
    }

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2024</p>
     * <p>Company:      www.trs.com.cn</p>
     * CluePoolConstant
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2024/3/19 14:12
     * @since 1.0
     */
    public enum DeptType {
        SHIZHANWUFUZHONGXIN(6, "实战服务中心"),
        ZHUZHENJINGZHONG(7, "主侦警种研判岗"),
        QITAJINGZHONG(8, "其他警种研判岗"),
        ZUOZHANDANYUAN(9, "作战单元"),
        ;
        private Integer code;

        private String name;

        /**
         * getCode<BR>
         *
         * @return 结果
         * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
         * @date 创建时间：2024/3/19 14:07
         */
        public Integer getCode() {
            return code;
        }

        /**
         * getName<BR>
         *
         * @return 结果
         * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
         * @date 创建时间：2024/3/19 14:07
         */
        public String getName() {
            return name;
        }

        DeptType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        /**
         * getNameByCode<BR>
         *
         * @param code 参数
         * @return 结果
         * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
         * @date 创建时间：2024/3/19 14:09
         */
        public static Optional<String> getNameByCode(Integer code) {
            for (DeptType value : DeptType.values()) {
                if (value.getCode().equals(code)) {
                    return Optional.of(value.getName());
                }
            }
            return Optional.empty();
        }
    }

}
