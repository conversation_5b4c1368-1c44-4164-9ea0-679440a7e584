package com.trs.police.fight.domain.dto;

import com.trs.police.fight.constant.enums.CollaborationOperateEnum;
import com.trs.police.fight.domain.bean.JqAssistancePerson;
import lombok.Data;

import java.util.List;

/**
 * @author: dingkeyu
 * @date: 2024/10/30
 * @description:
 */
@Data
public class FeedbackRefreshDTO {

    private CollaborationOperateEnum operateType;

    private List<String> tel;

    /**
     * 协作人员列表，如果这个字段有值，优先级比tel高（tel将失效）
     */
    private List<JqAssistancePerson> assistancePersonList;
}
