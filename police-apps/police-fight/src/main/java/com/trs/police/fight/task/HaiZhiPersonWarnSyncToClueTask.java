package com.trs.police.fight.task;


import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.PoliceKindEnum;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.OkHttpUtil;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.fight.domain.dto.ClueReportDTO;
import com.trs.police.fight.domain.entity.HaiZhiWarnPersonEntity;
import com.trs.police.fight.domain.vo.HaiZhiPersonWarnRespVo;
import com.trs.police.fight.mapper.HaiZhiWarnPersonMapper;
import com.trs.police.fight.properties.HaiZhiPersonWarnProperties;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/5 19:01
 * @since 1.0
 */
@Slf4j
@Component
@ConditionalOnBean(value = HaiZhiPersonWarnProperties.class)
public class HaiZhiPersonWarnSyncToClueTask extends BaseClueSyncService<HaiZhiPersonWarnProperties> {

    private final HaiZhiWarnPersonMapper mapper;

    public HaiZhiPersonWarnSyncToClueTask(HaiZhiPersonWarnProperties properties, HaiZhiWarnPersonMapper mapper) {
        super(properties);
        this.mapper = mapper;
    }

    /**
     * sync<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/5 19:03
     */
    @Scheduled(cron = "${fight.sync.haizhi.person.warn.cron:0 10 6,12 * * ?}")
    public void sync() {
        String msg = doSync(false);
        log.info("[{}]运行结果为：{}", desc(), msg);
    }

    @Override
    public String doSync(Boolean force) {
        try {
            final CurrentUser user = getSpecificUser();
            if (Objects.isNull(user)) {
                log.warn("未配置同步用户！");
                return "未配置同步用户！";
            }
            final Date now = new Date();
            final String redisKey = "HaiZhiPersonWarnSyncToClueTask:" + TimeUtils.dateToString(now, TimeUtils.YYYYMMDD5);
            if (!force) {
                String v = Optional.ofNullable(getRedisService().get(redisKey))
                        .map(Object::toString)
                        .orElse(null);
                if (StringUtils.isNotEmpty(v)) {
                    log.warn("今日已同步！同步时间:{}", v);
                    return "今日已同步！同步时间:" + v;
                }
            }
            List<HaiZhiWarnPersonEntity> list = parseJson(OkHttpUtil.getInstance().getData(
                    getProperties().getUrl(),
                    Map.of("authorization", String.format("Bearer %s", getProperties().getToken()))
            ));
            if (CollectionUtils.isEmpty(list)) {
                log.warn("未获取到数据！");
                return "未获取到数据！";
            }
            var old = new LambdaQueryChainWrapper<>(mapper)
                    .select(HaiZhiWarnPersonEntity::getId, HaiZhiWarnPersonEntity::getUniqueKey)
                    .in(
                            HaiZhiWarnPersonEntity::getUniqueKey, list.stream()
                                    .map(HaiZhiWarnPersonEntity::getUniqueKey)
                                    .collect(Collectors.toSet())
                    ).list()
                    .stream()
                    .collect(Collectors.toMap(HaiZhiWarnPersonEntity::getUniqueKey, HaiZhiWarnPersonEntity::getId));
            // 更新插入数据
            for (HaiZhiWarnPersonEntity entity : list) {
                if (old.containsKey(entity.getUniqueKey())) {
                    entity.setId(old.get(entity.getUniqueKey()));
                    mapper.updateById(entity);
                } else {
                    mapper.insert(entity);
                }
            }
            reportClue(now, user, list);
            // 正常上报的存入Redis
            getRedisService().set(redisKey, TimeUtils.dateToString(now, TimeUtils.YYYYMMDD_HHMMSS), 60 * 60 * 24L);
            return "同步成功！";
        } catch (Exception e) {
            log.error("同步失败！", e);
            return "同步失败！Err=" + e.getMessage();
        }
    }

    private String createAndUploadFile(Date now, List<HaiZhiWarnPersonEntity> list) throws ServiceException {
        PreConditionCheck.checkNotEmpty(list, new ParamInvalidException("未获取到数据！"));
        String rawFileName = String.format("个人极端风险人员-%s.xlsx", TimeUtils.dateToString(now, TimeUtils.YYYYMMDD));
        String filePath = "/tmp/excel/" + UUID.randomUUID();
        final File path = new File(filePath);
        if (!path.exists()) {
            path.mkdirs();
        }
        String fileName = String.format("%s/%s", filePath, rawFileName);
        EasyExcel.write(fileName, HaiZhiWarnPersonEntity.class)
                .sheet("个人极端风险人员")
                .doWrite(list);
        final var file = new File(fileName);
        try (FileInputStream input = new FileInputStream(file)) {
            final MultipartFile multipartFile = new MockMultipartFile(file.getName(), rawFileName, null, input);
            final FileInfoVO fileInfo = getOssService().upload(multipartFile, false);
            // 删除本地文件
            file.delete();
            return JsonUtil.toJsonString(List.of(fileInfo));
        } catch (IOException e) {
            log.error("文件[{}]上传失败", fileName, e);
            throw new ServiceException(String.format("文件[%s]上传失败", fileName), e);
        }
    }

    private void reportClue(Date now, CurrentUser user, List<HaiZhiWarnPersonEntity> list) throws ServiceException {
        ClueReportDTO dto = new ClueReportDTO();
        dto.setCollectUserDeptId(getProperties().getDeptId());
        dto.setCollectUserId(getProperties().getUserId());
        dto.setClueTitle("个人极端风险人员-" + TimeUtils.dateToString(now, TimeUtils.YYYYMMDD));
        dto.setClueType(StringUtils.showEmpty(
                BeanFactoryHolder.getEnv()
                        .getProperty("fight.sync.haizhi.person.warn.defaultClueType", getProperties().getDefaultClueType()),
                "涉案"
        ));
        dto.setPoliceKind(PoliceKindEnum.ZHIAN.getCode().toString());
        dto.setClueContent(String.format(
                "%s，个人极端风险模型推送风险人员工%s人，其中红色预警%s人，黄色预警%s人，蓝色预警%s人。具体详见附件。",
                TimeUtils.dateToString(now, TimeUtils.YYYYMMDD2),
                list.size(),
                list.stream().filter(it -> Objects.equals(it.getFxdj(), "红色预警")).count(),
                list.stream().filter(it -> Objects.equals(it.getFxdj(), "黄色预警")).count(),
                list.stream().filter(it -> Objects.equals(it.getFxdj(), "蓝色预警")).count()
        ));
        dto.setFileList(createAndUploadFile(now, list));
        getCluePoolService().reportClue(dto, user);
    }

    /**
     * parseJson<BR>
     *
     * @param json 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/5 20:17
     */
    public List<HaiZhiWarnPersonEntity> parseJson(String json) {
        HaiZhiPersonWarnRespVo vo = JsonUtil.parseObject(json, HaiZhiPersonWarnRespVo.class);
        if (Objects.isNull(vo)) {
            log.warn("解析json失败！");
            return List.of();
        }
        return Optional.ofNullable(vo.getData())
                .map(HaiZhiPersonWarnRespVo.DataVo::getResult)
                .filter(it -> CollectionUtils.isNotEmpty(it.getFileList())
                        && CollectionUtils.isNotEmpty(it.getValues()))
                .map(it -> {
                    final List<HaiZhiWarnPersonEntity> list = new ArrayList<>(0);
                    List<String> fields = it.getFileList();
                    int len = fields.size();
                    for (List<String> value : it.getValues()) {
                        Map<String, String> map = new HashMap<>(len);
                        for (int i = 0; i < len; i++) {
                            map.put(fields.get(i), StringUtils.showEmpty(value.get(i)));
                            map.put(fields.get(i).toLowerCase(), StringUtils.showEmpty(value.get(i)));
                        }
                        Optional.ofNullable(convertMapToEntity(map)).ifPresent(list::add);
                    }
                    list.sort(Comparator.comparing(HaiZhiWarnPersonEntity::getVjsyzhzzzf).reversed());
                    return list;
                }).orElse(List.of());
    }

    private HaiZhiWarnPersonEntity convertMapToEntity(Map<String, String> map) {
        if (Objects.isNull(map) || map.isEmpty()) {
            return null;
        }
        Date rq = Optional.ofNullable(map.get("rq"))
                .filter(TimeUtils::isValid)
                .map(it -> TimeUtils.stringToDate(TimeUtils.stringToString(it, TimeUtils.YYYYMMDD)))
                .orElse(null);
        if (Objects.isNull(rq)) {
            log.warn("rq字段为空！");
            return null;
        }
        String zjhm = map.get("zjhm");
        if (StringUtils.isEmpty(zjhm)) {
            log.warn("zjhm字段为空！");
            return null;
        }
        HaiZhiWarnPersonEntity entity = HaiZhiWarnPersonEntity.of(rq, zjhm);
        Optional.ofNullable(map.get("zjychwsj"))
                .filter(TimeUtils::isValid)
                .map(TimeUtils::stringToDate)
                .ifPresent(entity::setZjychwsj);
        entity.setSgbqzs(map.get("sgbqzs"));
        entity.setLxdh(map.get("lxdh"));
        entity.setNzyszbq(map.get("nzyszbq"));
        entity.setZjychwbq(map.get("zjychwbq"));
        entity.setYbsszbq(map.get("ybsszbq"));
        entity.setYjfyzbq(map.get("yjfyzbq"));
        entity.setFxdj(map.get("fxdj"));
        entity.setYthdyddfdys(map.get("ythdyddfdys"));
        entity.setWzyszbq(map.get("wzyszbq"));
        entity.setHlwhw(map.get("hlwhw"));
        entity.setBjbq(map.get("bjbq"));
        entity.setNzysbqs(map.get("nzysbqs"));
        entity.setPx(map.get("px"));
        entity.setZbqs(map.get("zbqs"));
        entity.setYbssbqqzyz(map.get("ybssbqqzyz"));
        entity.setYjfybqqzyz(map.get("yjfybqqzyz"));
        entity.setZxhdxss(map.get("zxhdxss"));
        entity.setYjfypcqzyz(map.get("yjfypcqzyz"));
        entity.setDyquerydzs(map.get("dyquerydzs"));
        entity.setWzysbqs(map.get("wzysbqs"));
        entity.setVjczf(Optional.ofNullable(map.get("vjczf")).filter(StringUtils::isDouble).map(Double::parseDouble).orElse(0.0));
        entity.setYbssbqs(map.get("ybssbqs"));
        entity.setYjfybqs(map.get("yjfybqs"));
        entity.setWzyspcqzyz(map.get("wzyspcqzyz"));
        entity.setNzysbqqzyz(map.get("nzysbqqzyz"));
        entity.setSjqzyz(map.get("sjqzyz"));
        entity.setVjsyzhzzzf(Optional.ofNullable(map.get("vjsyzhzzzf")).filter(StringUtils::isDouble).map(Double::parseDouble).orElse(0.0));
        entity.setYbsspcqzyz(map.get("ybsspcqzyz"));
        entity.setWzysbqqzyz(map.get("wzysbqqzyz"));
        entity.setNzyspcqzyz(map.get("nzyspcqzyz"));
        entity.setBjbqs(map.get("bjbqs"));
        entity.setAjmc(map.get("ajmc"));
        entity.setBjsj(map.get("bjsj"));
        return entity;
    }

    @Override
    public String key() {
        return "syncHaiZhi";
    }

    @Override
    public String desc() {
        return "海致预警模型";
    }
}
