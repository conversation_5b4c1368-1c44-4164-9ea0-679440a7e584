package com.trs.police.fight.sync.VO;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.UserDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Optional;
import java.util.function.Function;

/**
 * 权限vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PermissionVO implements Serializable {

    private Function<String, Optional<UserDto>> findUserBySync;

    private Function<String, Optional<DeptDto>> findDeptBySync;

    public PermissionVO(Function<String, Optional<UserDto>> findUserBySync, Function<String, Optional<DeptDto>> findDeptBySync) {
        this.findUserBySync = findUserBySync;
        this.findDeptBySync = findDeptBySync;
    }
}
