package com.trs.police.fight.constant.enums;

/**
 * 协作的相关事件
 *
 * <AUTHOR>
 */
public enum CollaborationEvent {

    // 创建
    EV_CREATE("创建协作"),
    // 送审
    EV_SUBMIT_REVIEW("送审"),
    // 审核并扭转给下一位
    EV_NEXT_APPROVAL("扭转审核人"),
    // 审核通过
    EV_TO_HANDING("指定处理人"),
    // 审核驳回
    EV_REVIEW_REJECTED("驳回"),
    // 退回
    EV_RETURNED("退回"),
    // 反馈
    EV_FEEDBACK("反馈"),
    // 评价
    EV_APPRAISE("评价"),
    EV_REPORT("上报"),
    EV_TRANSFER("转发");

    /**
     * 事件名称
     */
    private String eventName;

    CollaborationEvent(String eventName) {
        this.eventName = eventName;
    }


    @Override
    public String toString() {
        return eventName;
    }
}
