package com.trs.police.fight.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：值班人员表
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/8/15 11:07
 * @since 1.0
 */
@Data
@ToString(callSuper = true)
@TableName("tb_common_bigscreen_duty_users")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BigScreenDutyUserEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField
    private Date crTime;

    @TableField
    private Date updateTime;

    /**
     * 值班日期（yyyy-MM-dd）
     */
    @TableField
    private Date dutyTime;

    @TableField
    private String unitName;

    @TableField
    private String districtCode;

    /**
     * 警种 {@link com.trs.police.common.core.constant.enums.PoliceKindEnum}
     */
    @TableField
    private Integer policeKind;

    @TableField
    private String name;

    @TableField
    private String jh;

    @TableField
    private String dh;

    @TableField
    private String nature;

    @TableField
    private String level;

    @TableField
    private String profilePic;

    @TableField
    private String jjsUserIds;

    @TableField
    private String ddgUserIds;

    @TableField
    private String qbgUserIds;

    @TableField
    private String zhbStatus;

    @TableField
    private String event;

    @TableField
    private String lhczzb;

    @TableField
    private String dutyPhone;

    @TableField
    private String uniqueSign;

    @TableField
    private String xzgUserIds;

    @TableField
    private String fkgUserIds;

    @TableField
    private String dbUserIds;

    @TableField
    private String zagUserIds;

    @TableField
    private String xingZhenUserIds;

    @TableField
    private String wagUserIds;

    @TableField
    private String jzgUserIds;

    @TableField
    private String zbgUserIds;

    @TableField
    private String xcgUserIds;

    @TableField
    private String jingZhenUserIds;

    @TableField
    private String kxgUserIds;

    @TableField
    private String jbgUserIds;

    @TableField
    private String jjgUserIds;
}
