package com.trs.police.fight.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.mq.utils.CollectionUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.utils.VoParameterConstructor;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.fight.JingYuanVO;
import com.trs.police.common.core.vo.permission.UserVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProjectsService;
import com.trs.police.fight.constant.PlanConstant;
import com.trs.police.fight.constant.enums.CompositeType;
import com.trs.police.fight.constant.enums.PlanOperationEnum;
import com.trs.police.fight.constant.enums.PlanSchedulingMeasureEnum;
import com.trs.police.fight.domain.dto.CompositePartnerDTO;
import com.trs.police.fight.domain.dto.LabelListDTO;
import com.trs.police.fight.domain.entity.PlanEntity;
import com.trs.police.fight.domain.entity.PlanRecordEntity;
import com.trs.police.fight.domain.entity.PlanSchedulingMeasureRelation;
import com.trs.police.fight.domain.entity.PlanTaskRelation;
import com.trs.police.fight.domain.vo.*;
import com.trs.police.fight.mapper.PlanMapper;
import com.trs.police.fight.mapper.PlanRecordMapper;
import com.trs.police.fight.mapper.PlanSchedulingMeasureRelationMapper;
import com.trs.police.fight.mapper.PlanTaskRelationMapper;
import com.trs.police.fight.service.CompositeService;
import com.trs.police.fight.service.PlanRecordService;
import com.trs.police.fight.service.PlanService;
import com.trs.police.fight.service.compositePartner.CompositePartnerFactory;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.police.fight.constant.FightCompositeConstant.COMPOSITE_PLAN;
import static com.trs.police.fight.constant.enums.PlanSchedulingMeasureEnum.POLICE;
import static com.trs.police.fight.constant.enums.PlanSchedulingMeasureEnum.UNIT_DEPT;

/**
 * <AUTHOR>
 * @date 2022/6/18 11:03
 */
@Slf4j
@Service
public class PlanServiceImpl implements PlanService {

    @Autowired
    private PlanMapper planMapper;
    @Autowired
    private DictService dictService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PlanSchedulingMeasureRelationMapper schedulingMeasureRelationMapper;
    @Autowired
    private PlanTaskRelationMapper planTaskRelationMapper;
    @Autowired
    private PlanRecordMapper planRecordMapper;
    @Autowired
    private PlanRecordService planRecordService;
    @Resource
    private ProjectsService projectsService;

    @Resource
    private PlanSchedulingMeasureRelationMapper planSchedulingMeasureRelationMapper;

    @Resource
    private CompositeService compositeService;
    @Resource
    private CompositePartnerFactory compositePartnerFactory;

    @Override
    public RestfulResultsV2<PlanVO> labelList(LabelListDTO dto) throws ServiceException {
        dto.isValid();
        List<Long> codes = new ArrayList<>();
        String type = dto.getCategoryCode() == 0 ? "plan_category_group" : "plan_category";
        if (dto.getFindChildren()) {
            //拿到所有分类id
            DictDto dictDto = dictService.getDictByTypeAndCode(type, dto.getCategoryCode());
            collectCodes(dictDto, codes);
        } else {
            codes.add(dto.getCategoryCode());
        }
        Page<PlanEntity> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<PlanVO> pageList = planMapper.labelPageList(page, codes, dto);
        //构建返回预案中的任务内容
        buildPlanTask(pageList.getRecords());
        //构建返回预案中的调度信息
        buildMeasures(pageList.getRecords());
        return RestfulResultsV2.ok(pageList.getRecords())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(pageList.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Report<String> saveOrUpdateLabel(LabelListDTO dto) throws ServiceException {
        dto.isValid();
        CurrentUser currentUser = permissionService.getCurrentUserWithRole();
        PlanEntity entity = new PlanEntity();
        if (Optional.ofNullable(dto.getId()).orElse(0L) > 0) {
            entity = planMapper.selectById(dto.getId());
        } else {
            entity.fillAuditFields(currentUser);
        }
        DictDto dictDto = dictService.getDictByTypeAndCode("plan_category", dto.getCategoryCode());
        if (Objects.isNull(dictDto)) {
            throw new ServiceException("指定分类不存在");
        }
        entity.setCategoryCode(dto.getCategoryCode());
        entity.setCategory(dictDto.getName());
        entity.setName(dto.getName());
        entity.setContent(dto.getContent());
        String detail = "新增预案";
        if (Objects.isNull(entity.getId())) {
            entity.setStatus(1);
            // 相同分类层级下 标签名称不能重复
            //拿到所有分类id
            List<Long> codes = new ArrayList<>();
            collectCodes(dictDto, codes);
            if (planMapper.selectCount(new QueryWrapper<PlanEntity>()
                    .eq("is_del", 0)
                    .in("category_code", codes)
                    .eq("name", dto.getName())) > 0) {
                throw new ServiceException("同分类层级下存在同名预案！");
            }
            entity.setUpdateTime(LocalDateTime.now());
            planMapper.insert(entity);
            if (!CollectionUtils.isEmpty(dto.getPlanSchedulingMeasure())) {
                insertSchedulingMeasure(dto, entity, currentUser);
            }
            if (!StringUtils.isEmpty(dto.getPlanTask())) {
                insertPlanTask(dto, entity.getId(), currentUser);
            }
        } else {
            detail = "修改预案";
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId(currentUser.getId());
            entity.setUpdateDeptId(currentUser.getDept().getId());
            planMapper.updateById(entity);
            if (!CollectionUtils.isEmpty(dto.getPlanSchedulingMeasure())) {
                schedulingMeasureRelationMapper.delete(new QueryWrapper<PlanSchedulingMeasureRelation>()
                        .eq("plan_id", entity.getId()));
                insertSchedulingMeasure(dto, entity, currentUser);
            }else {
                schedulingMeasureRelationMapper.delete(new QueryWrapper<PlanSchedulingMeasureRelation>()
                        .eq("plan_id", entity.getId()));
            }
            if (!StringUtils.isEmpty(dto.getPlanTask())) {
                planTaskRelationMapper.delete(new QueryWrapper<PlanTaskRelation>()
                        .eq("relation_id", entity.getId())
                        .eq("relation_type", 1));
                insertPlanTask(dto, entity.getId(), currentUser);
            }else {
                planTaskRelationMapper.delete(new QueryWrapper<PlanTaskRelation>()
                        .eq("relation_id", entity.getId())
                        .eq("relation_type", 1));
            }
        }
        return new Report<>("", detail + "成功", Report.RESULT.SUCCESS);
    }

    @Override
    public void editTask(LabelListDTO dto) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        PlanTaskRelation planTask = JSON.parseObject(dto.getPlanTask(), PlanTaskRelation.class);
        if (Objects.nonNull(planTask.getRelationId())) {
            PlanTaskRelation relation = new PlanTaskRelation();
            String deptIds = JSON.parseArray(planTask.getLeadDeptInfo()).stream()
                    .map(e -> ((JSONObject) e).getString("deptId"))
                    .collect(Collectors.joining(","));
            relation.setLeadDeptId(deptIds);
            relation.setContent(planTask.getContent());
            relation.setRelationId(planTask.getRelationId());
            relation.setRelationType(planTask.getRelationType());
            relation.setLeadDeptInfo(planTask.getLeadDeptInfo());
            planTask.fillAuditFields(currentUser);
            planTaskRelationMapper.insert(relation);
        } else {
            PlanTaskRelation relation = planTaskRelationMapper.selectById(planTask.getId());
            String deptIds = JSON.parseArray(planTask.getLeadDeptInfo()).stream()
                    .map(e -> ((JSONObject) e).getString("deptId"))
                    .collect(Collectors.joining(","));
            relation.setLeadDeptId(deptIds);
            relation.setContent(planTask.getContent());
            relation.setLeadDeptInfo(planTask.getLeadDeptInfo());
            relation.setUpdateDeptId(currentUser.getDept().getId());
            relation.setUpdateUserId(currentUser.getId());
            relation.setUpdateTime(LocalDateTime.now());
            planTaskRelationMapper.updateById(relation);
        }
    }

    @Override
    public void editTaskLeadDept(LabelListDTO dto) {
        PlanTaskRelation planTask = JSON.parseObject(dto.getPlanTask(), PlanTaskRelation.class);
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        PlanTaskRelation planTaskRelation = planTaskRelationMapper.selectById(planTask.getId());
        String deptIds = JSON.parseArray(planTask.getLeadDeptInfo()).stream()
                .map(e -> ((JSONObject) e).getString("deptId"))
                .collect(Collectors.joining(","));
        planTaskRelation.setLeadDeptId(deptIds);
        planTaskRelation.setLeadDeptInfo(planTask.getLeadDeptInfo());
        planTaskRelation.setUpdateDeptId(currentUser.getDept().getId());
        planTaskRelation.setUpdateUserId(currentUser.getId());
        planTaskRelation.setUpdateTime(LocalDateTime.now());
        planTaskRelationMapper.updateById(planTaskRelation);
    }

    @Override
    public void delTask(Long id) {
        planTaskRelationMapper.deleteById(id);
    }

    private void insertPlanTask(LabelListDTO dto, Long planId, CurrentUser currentUser) {
        List<PlanTaskRelation> planTaskList = JSON.parseArray(dto.getPlanTask(), PlanTaskRelation.class);
        for (PlanTaskRelation planTask : planTaskList) {
            String deptIds = JSON.parseArray(planTask.getLeadDeptInfo()).stream()
                    .map(e -> ((JSONObject) e).getString("deptId"))
                    .collect(Collectors.joining(","));
            planTask.setLeadDeptId(deptIds);
            planTask.setRelationId(planId);
            planTask.setRelationType(1);
            planTask.fillAuditFields(currentUser);
            planTaskRelationMapper.insert(planTask);
        }
    }

    private void insertSchedulingMeasure(LabelListDTO dto, PlanEntity entity, CurrentUser currentUser) {
        for (LabelListDTO.SchedulingMeasure schedulingMeasure : dto.getPlanSchedulingMeasure()) {
            PlanSchedulingMeasureRelation relation = new PlanSchedulingMeasureRelation();
            relation.setPlanId(entity.getId());
            relation.setMeasureType(schedulingMeasure.getMeasureType());
            relation.setMeasureRange(schedulingMeasure.getRange());
            relation.setUnitDeptId(schedulingMeasure.getUnitDeptId());
            relation.setCoreRadius(schedulingMeasure.getCoreRadius());
            relation.setInnerRadius(schedulingMeasure.getInnerRadius());
            relation.setOuterRadius(schedulingMeasure.getOuterRadius());
            relation.fillAuditFields(currentUser);
            schedulingMeasureRelationMapper.insert(relation);
        }
    }

    @Override
    public Report<String> removePlan(String ids) throws ServiceException {
        PreConditionCheck.checkNotEmpty(ids, "id不能为空！");
        //校验标签是否已有关联
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        List<Long> idsList = Arrays.asList(ids.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<PlanEntity> planList = planMapper.selectList(new QueryWrapper<PlanEntity>()
                .in("id", idsList));
        planList.forEach(entity -> {
            entity.setIsDel(1);
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId(currentUser.getId());
            entity.setUpdateDeptId(currentUser.getDept().getId());
            planMapper.updateById(entity);
        });
        return new Report<>("删除标签", "成功删除标签", Report.RESULT.SUCCESS, ids);
    }

    @Override
    public PlanVO checkPlanContent(Long planId, Long id) {
        PreConditionCheck.checkNotEmpty(String.valueOf(planId), "id不能为空！");
        PlanEntity entity = planMapper.selectById(planId);
        PlanVO vo = new PlanVO();
        vo.setName(entity.getName());
        vo.setContent(entity.getContent());
        vo.setCategoryCode(entity.getCategoryCode());
        vo.setCategoryName(entity.getCategory());
        //设置调度措施
        if (Objects.nonNull(id) && id.equals(-1L)) {
            setMeasures(planId, vo);
            setPlanTask(planId, vo);
        }
        //记录信息(启动记录，调度措施记录)
        QueryWrapper<PlanRecordEntity> recordWrapper = new QueryWrapper<PlanRecordEntity>().eq("plan_id", planId).orderByDesc("create_time");
        if (Objects.nonNull(id) && !id.equals(-1L)) {
            recordWrapper.eq("composite_id", id);
        }
        List<PlanRecordVO> voList = planRecordMapper.selectVoList(planId, id);
        voList = !CollectionUtils.isEmpty(voList) ? voList : new ArrayList<>();
        //设置记录信息
        setRecordInfo(vo, voList, id);
        return vo;
    }

    /**
     * 设置任务分配
     *
     * @param planId 预案id
     * @param vo     vo
     */
    private void setPlanTask(Long planId, PlanVO vo) {
        List<PlanTaskRelation> planTaskRelations = planTaskRelationMapper.selectList(new QueryWrapper<PlanTaskRelation>()
                .eq("relation_id", planId).eq("relation_type", 1).orderByAsc("create_time"));
        if (CollectionUtils.isEmpty(planTaskRelations)) {
            vo.setPlanTask(new ArrayList<>());
            return;
        }
        List<LabelListDTO.PlanTask> list = new ArrayList<>();
        for (PlanTaskRelation planTaskRelation : planTaskRelations) {
            LabelListDTO.PlanTask planTask = new LabelListDTO.PlanTask();
            planTask.setId(planTaskRelation.getId());
            planTask.setContent(planTaskRelation.getContent());
            planTask.setLeadDeptInfo(planTaskRelation.getLeadDeptInfo());
            planTask.setLeadDeptIds(planTaskRelation.getLeadDeptId());
            list.add(planTask);
        }
        vo.setPlanTask(list);
    }

    /**
     * 设置调度措施
     *
     * @param planId 预案id
     * @param vo     vo
     */
    private void setMeasures(Long planId, PlanVO vo) {
        QueryWrapper<PlanSchedulingMeasureRelation> wrapper = new QueryWrapper<PlanSchedulingMeasureRelation>()
                .eq("plan_id", planId);
        List<PlanSchedulingMeasureRelation> schedulingMeasures = schedulingMeasureRelationMapper.selectList(wrapper);
        schedulingMeasures = Objects.nonNull(schedulingMeasures) ? schedulingMeasures : new ArrayList<>();
        List<PlanSchedulingMeasureVO> list = new ArrayList<>();
        for (PlanSchedulingMeasureRelation e : schedulingMeasures) {
            PlanSchedulingMeasureVO measureVO = new PlanSchedulingMeasureVO();
            measureVO.setMeasureType(PlanSchedulingMeasureEnum.codeOf(e.getMeasureType().intValue()).getName());
            measureVO.setMeasureRange(e.getMeasureRange());
            List<DeptDto> deptList = !StringUtils.isEmpty(e.getUnitDeptId())
                    ? permissionService.getDeptByIds(Arrays.asList(e.getUnitDeptId().split(","))
                    .stream().map(t -> Long.valueOf(t)).collect(Collectors.toList())) : new ArrayList<>();
            measureVO.setDeptList(deptList);
            measureVO.setCoreRadius(e.getCoreRadius());
            measureVO.setInnerRadius(e.getInnerRadius());
            measureVO.setOuterRadius(e.getOuterRadius());
            list.add(measureVO);
        }
        vo.setSchedulingMeasures(list);
    }

    /**
     * 设置记录信息
     *
     * @param vo     参数
     * @param id     处突id
     * @param voList 记录vo
     */
    private void setRecordInfo(PlanVO vo, List<PlanRecordVO> voList, Long id) {
        List<Long> userIds = voList.stream().map(e -> e.getCreateUserId()).distinct().collect(Collectors.toList());
        List<Long> deptIds = voList.stream().map(e -> e.getCreateDeptId()).distinct().collect(Collectors.toList());
        Map<Long, String> userMap = Objects.nonNull(userIds) ? permissionService.getUserListById(userIds)
                .stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getRealName())) : new HashMap<>();
        Map<Long, String> deptMap = Objects.nonNull(deptIds) ? permissionService.getDeptByIds(deptIds)
                .stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getName())) : new HashMap<>();
        //设置记录voList
        for (PlanRecordVO recordVO : voList) {
            recordVO.setRecordType(PlanOperationEnum.codeOf(recordVO.getOperation()).getName());
            recordVO.setPersonName(userMap.get(recordVO.getCreateUserId()));
            recordVO.setDeptName(deptMap.get(recordVO.getCreateDeptId()));
        }
        //设置启用记录
        vo.setStartRecord(voList.stream().filter(e -> e.getOperation().equals(PlanConstant.START_PLAN_STATUS))
                .collect(Collectors.toList()));
        //设置调度反馈记录
        if (Objects.nonNull(id) && !id.equals(-1L)) {
            PlanRecordVO recordVO = vo.getStartRecord().stream()
                    .filter(e -> e.getOperation().equals(PlanConstant.START_PLAN_STATUS)).findFirst().orElse(null);
            vo.setStartTime(Objects.nonNull(recordVO) ? recordVO.getStartTime() : null);
            vo.setFeedBackRecord(voList.stream().filter(e -> e.getOperation().equals(PlanConstant.COMPOSITE_FEEDBACK))
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public RestfulResultsV2<PlanVO> getPlan(LabelListDTO dto) {
        Page<PlanEntity> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<PlanVO> voList = planMapper.getPlan(dto.getName(),page);
        if (CollectionUtils.isEmpty(voList.getRecords())) {
            return RestfulResultsV2.ok(new ArrayList<>());
        }
        buildPlanTask(voList.getRecords());
        buildMeasures(voList.getRecords());
        return RestfulResultsV2.ok(voList.getRecords())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(voList.getTotal());
    }

    private void buildMeasures(List<PlanVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            voList.forEach(e -> e.setSchedulingMeasures(new ArrayList<>()));
            return;
        }
        List<Long> idList = voList.stream().map(PlanVO::getId).collect(Collectors.toList());
        List<PlanSchedulingMeasureRelation> schedulingMeasures = schedulingMeasureRelationMapper
                .selectList(new QueryWrapper<PlanSchedulingMeasureRelation>()
                        .in("plan_id", idList));
        if (CollectionUtils.isEmpty(schedulingMeasures)) {
            return;
        }
        Map<Long, List<PlanSchedulingMeasureRelation>> map = schedulingMeasures.stream()
                .collect(Collectors.groupingBy(PlanSchedulingMeasureRelation::getPlanId));
        List<Long> unitDeptIdList = schedulingMeasures.stream()
                .map(PlanSchedulingMeasureRelation::getUnitDeptId)
                .filter(t -> !StringUtils.isEmpty(t))
                .flatMap(t -> Arrays.stream(t.split(",")))
                .distinct()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Map<Long, DeptDto> deptMap = permissionService.getDeptByIds(unitDeptIdList)
                .stream().collect(Collectors.toMap(DeptDto::getId, e -> e));
        for (PlanVO planVO : voList) {
            List<PlanSchedulingMeasureVO> measureList = new ArrayList<>();
            List<PlanSchedulingMeasureRelation> relations = map.getOrDefault(planVO.getId(), new ArrayList<>());
            for (PlanSchedulingMeasureRelation e : relations) {
                PlanSchedulingMeasureVO measureVO = new PlanSchedulingMeasureVO();
                measureVO.setMeasureType(PlanSchedulingMeasureEnum.codeOf(e.getMeasureType().intValue()).getName());
                measureVO.setMeasureRange(e.getMeasureRange());
                List<DeptDto> deptList = new ArrayList<>();
                if (!StringUtils.isEmpty(e.getUnitDeptId())) {
                    for (String deptIds : e.getUnitDeptId().split(",")) {
                        Long deptId = Long.valueOf(deptIds);
                        DeptDto deptDto = deptMap.get(deptId);
                        if (deptDto != null) {
                            deptList.add(deptDto);
                        }
                    }
                }
                measureVO.setDeptList(deptList);
                measureVO.setCoreRadius(e.getCoreRadius());
                measureVO.setInnerRadius(e.getInnerRadius());
                measureVO.setOuterRadius(e.getOuterRadius());
                measureList.add(measureVO);
            }
            planVO.setSchedulingMeasures(measureList);
        }


        List<PlanSchedulingMeasureVO> list = new ArrayList<>();
        for (PlanSchedulingMeasureRelation e : schedulingMeasures) {
            PlanSchedulingMeasureVO measureVO = new PlanSchedulingMeasureVO();
            measureVO.setMeasureType(PlanSchedulingMeasureEnum.codeOf(e.getMeasureType().intValue()).getName());
            measureVO.setMeasureRange(e.getMeasureRange());
            List<DeptDto> deptList = !StringUtils.isEmpty(e.getUnitDeptId())
                    ? permissionService.getDeptByIds(Arrays.asList(e.getUnitDeptId().split(","))
                    .stream().map(t -> Long.valueOf(t)).collect(Collectors.toList())) : new ArrayList<>();
            measureVO.setDeptList(deptList);
            measureVO.setCoreRadius(e.getCoreRadius());
            measureVO.setInnerRadius(e.getInnerRadius());
            measureVO.setOuterRadius(e.getOuterRadius());
            list.add(measureVO);
        }

    }


    private void buildPlanTask(List<PlanVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            voList.forEach(e -> e.setPlanTask(new ArrayList<>()));
            return;
        }
        List<Long> idList = voList.stream().map(PlanVO::getId).collect(Collectors.toList());
        List<PlanTaskRelation> taskRelationList = planTaskRelationMapper.selectList(new QueryWrapper<PlanTaskRelation>()
                .in("relation_id", idList)
                .eq("relation_type", 1));
        if (CollectionUtils.isEmpty(taskRelationList)) {
            voList.forEach(e -> e.setPlanTask(new ArrayList<>()));
            return;
        }
        // 将 taskRelationList 按 relationId 分组
        Map<Long, List<PlanTaskRelation>> map = taskRelationList
                .stream().collect(Collectors.groupingBy(PlanTaskRelation::getRelationId));
        // 遍历 voList 设置 planTask
        for (PlanVO planVO : voList) {
            List<PlanTaskRelation> planTaskRelations = map.getOrDefault(planVO.getId(), new ArrayList<>());
            List<LabelListDTO.PlanTask> list = new ArrayList<>();
            for (PlanTaskRelation planTaskRelation : planTaskRelations) {
                LabelListDTO.PlanTask planTask = new LabelListDTO.PlanTask();
                planTask.setId(planTaskRelation.getId());
                planTask.setContent(planTaskRelation.getContent());
                planTask.setLeadDeptIds(planTaskRelation.getLeadDeptId());
                planTask.setLeadDeptInfo(planTaskRelation.getLeadDeptInfo());
                list.add(planTask);
            }
            planVO.setPlanTask(list);
        }
    }

    @Override
    public Long getPlanNum(Long code) {
        return planMapper.selectCount(new QueryWrapper<PlanEntity>()
                .eq("is_del", 0)
                .in("category_code", code));
    }


    @Override
    public Report<String> enableLabel(String ids, Integer status) throws ServiceException {
        PreConditionCheck.checkNotEmpty(ids, "id不能为空！");
        PreConditionCheck.checkNotNull(status, "启用状态不能为空！");
        List<PlanEntity> planList = getPlans(ids);
        for (PlanEntity entity : planList) {
            entity.setStatus(status);
            CurrentUser currentUser = permissionService.getCurrentUserWithRole();
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateUserId(currentUser.getId());
            entity.setUpdateDeptId(currentUser.getDept().getId());
            planMapper.updateById(entity);
        }
        /*if (status.equals(1)){
            savePlanRecord(planList);
        }*/
        String detail = status == 1 ? "启用" : "禁用";
        return new Report<>("设置预案状态", "成功" + detail, Report.RESULT.SUCCESS, ids);
    }

    /**
     * 保存启用记录
     *
     * @param planList 参数
     */
    private void savePlanRecord(List<PlanEntity> planList) {
        List<PlanRecordEntity> list = new ArrayList<>();
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        for (PlanEntity planEntity : planList) {
            PlanRecordEntity e = new PlanRecordEntity();
            e.setCreateUserId(currentUser.getId());
            e.setCreateDeptId(currentUser.getDeptId());
            e.setUpdateUserId(currentUser.getId());
            e.setUpdateDeptId(currentUser.getDeptId());
            e.setCreateTime(LocalDateTime.now());
            e.setUpdateTime(LocalDateTime.now());
            e.setPlanId(planEntity.getId());
            e.setContent("启动预案：" + planEntity.getName());
            e.setOperation(PlanConstant.START_PLAN_STATUS);
            list.add(e);
        }
        planRecordService.saveBatch(list);
    }

    @Override
    public List<PlanEntity> findEnablePlanById(List<Long> ids) {
        if (null == ids || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return planMapper.selectBatchIds(ids)
                .stream()
                // 未被禁用
                .filter(plan -> Integer.valueOf(1).equals(plan.getStatus()))
                // 未被删除
                .filter(plan -> !Integer.valueOf(1).equals(plan.getIsDel()))
                .collect(Collectors.toList());
    }

    @Override
    public List<PlaPoliceVO> planUser(Long planeId, String geometry) {
        if (Objects.isNull(planeId) || StringUtils.isEmpty(geometry)) {
            return new ArrayList<>();
        }
        List<PlanSchedulingMeasureRelation> pr = planSchedulingMeasureRelationMapper.selectList(
                Wrappers.lambdaQuery(PlanSchedulingMeasureRelation.class)
                        .eq(PlanSchedulingMeasureRelation::getPlanId, planeId)
                        .in(PlanSchedulingMeasureRelation::getMeasureType, Arrays.asList(POLICE.getCode(), UNIT_DEPT.getCode()))
        );
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(pr)) {
            return new ArrayList<>();
        }
        List<PlaPoliceVO> vos = pr.stream()
                // 转换成用户
                .map(mr -> Try.of(() -> relatedUser(mr, geometry))
                        .onFailure(e -> log.error("获取预案人员失败", e))
                        .getOrElse(new ArrayList<>())
                )
                .flatMap(List::stream)
                // 根据用户id去重
                .collect(Collectors.groupingBy(UserVO::getUserId))
                .values()
                .stream()
                .map(list -> list.get(0))
                .collect(Collectors.toList());
        return vos;
    }

    @Override
    public FightComposite planCreateComposite(LabelListDTO dto) {
        CreateCompositeVO createCompositeVO = new CreateCompositeVO();
        createCompositeVO.setTitle(String.format("关于%s重大警情", TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD2)));
        createCompositeVO.setRequire(StringUtil.stripHtmlTags(dto.getContent()));
        createCompositeVO.setCompositeType(CompositeType.COMPOSITE.getCode());
        createCompositeVO.setTypeId(Long.valueOf(COMPOSITE_PLAN));
        createCompositeVO.setPlanId(dto.getId());
        createCompositeVO.setPlanLevel(dto.getLevel());
        createCompositeVO.setTasks(dto.getPlanTask());
        CompositePartnerDTO compositePartnerDTO = new CompositePartnerDTO();
        compositePartnerDTO.setCompositeType(COMPOSITE_PLAN);
        List<PlanTaskRelation> planTaskRelations = JSON.parseArray(dto.getPlanTask(), PlanTaskRelation.class);
        String leadDeptIds = planTaskRelations.stream().flatMap(relation ->
                JSON.parseArray(relation.getLeadDeptInfo()).stream()
                        .map(e -> ((JSONObject) e).getString("deptId"))
        ).collect(Collectors.joining(","));
        compositePartnerDTO.setLeadDeptIds(leadDeptIds);
        List<CompositeUserInfo> compositePartners = compositeService.getCompositePartner(compositePartnerDTO);
        createCompositeVO.setPartners(compositePartners);
        createCompositeVO.setIsClassified(true);
        createCompositeVO.setCaseTag(new ArrayList<>());
        FightComposite fightComposite = compositeService.createComposite(createCompositeVO);
        return fightComposite;
    }

    private List<PlaPoliceVO> relatedUser(PlanSchedulingMeasureRelation mr, String geometry) {
        // 警员
        if (POLICE.equals(PlanSchedulingMeasureEnum.codeOf(mr.getMeasureType().intValue()))) {
            if (StringUtils.isEmpty(geometry) || mr.getMeasureRange() <= 0.0D) {
                return new ArrayList<>();
            }
            GeometryVO geo = new GeometryVO();
            geo.setType("circle");
            JSONObject jsonObject = JSON.parseObject(geometry);
            geo.setGeometry(String.format("POINT(%s %s)", jsonObject.getString("long"), jsonObject.getString("lat")));
            var p = new GeometryVO.Properties();
            p.setRadius(mr.getMeasureRange());
            geo.setProperties(p);
            RestfulResultsV2<JingYuanVO> jhResult = projectsService.jingYuanListByArea(JSON.toJSONString(Arrays.asList(geo)));
            if (CollectionUtils.isEmpty(jhResult.getDatas())) {
                return new ArrayList<>();
            }
            List<String> datas = jhResult.getDatas().stream()
                    .map(JingYuanVO::getBh)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(datas)) {
                return new ArrayList<>();
            }
            List<String> jh = datas.stream()
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(jh)) {
                return new ArrayList<>();
            }
            List<PlaPoliceVO> userByJhs = permissionService.getUserByJhs(jh)
                    .stream()
                    .map(v -> {
                        PlaPoliceVO ppv = new PlaPoliceVO();
                        BeanUtils.copyProperties(v, ppv);
                        return ppv;
                    })
                    .collect(Collectors.toList());
            // 添加更新时间和部门简称
            if (CollectionUtils.isEmpty(userByJhs)) {
                return new ArrayList<>();
            }
            List<Long> deptId = userByJhs.stream()
                    .map(PlaPoliceVO::getDeptId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, DeptDto> dept = permissionService.getDeptByIds(deptId)
                    .stream()
                    .collect(Collectors.toMap(DeptDto::getId, d -> d));
            Map<String, String> map = jhResult.getDatas()
                    .stream()
                    .collect(Collectors.toMap(JingYuanVO::getBh, JingYuanVO::getUpdateTime, (old, n) -> n));
            for (PlaPoliceVO userByJh : userByJhs) {
                userByJh.setUpdateTime(map.get(userByJh.getPoliceCode()));
                Optional.ofNullable(dept.get(userByJh.getDeptId()))
                        .ifPresent(d -> userByJh.setDeptShortName(d.getShortName()));
            }
            // 更新人员信息
            VoParameterConstructor.of(userByJhs)
                    .singleValueMatcher(d -> jhResult.getDatas(), (u, d) -> Objects.equals(u.getPoliceCode(), d.getBh()))
                    .consumer((u, d) -> {
                        u.setCjzt(d.getCjzt());
                        u.setBbzt(d.getBbzt());
                    })
                    .build();
            return userByJhs;
        }
        return new ArrayList<>();
    }

    /**
     * 获取预案
     *
     * @param ids 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    private List<PlanEntity> getPlans(String ids) throws ServiceException {
        List<Long> idsList = Arrays.asList(ids.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
        return planMapper.selectList(new QueryWrapper<PlanEntity>()
                .in("id", idsList));
    }


    // 实际递归方法
    private static void collectCodes(DictDto dictDto, List<Long> codes) {
        if (dictDto == null) {
            return;
        }
        // 添加当前节点的code
        codes.add(dictDto.getCode());
        // 遍历子节点
        if (dictDto.getChildren() != null) {
            for (DictDto child : dictDto.getChildren()) {
                collectCodes(child, codes);
            }
        }
    }
}
