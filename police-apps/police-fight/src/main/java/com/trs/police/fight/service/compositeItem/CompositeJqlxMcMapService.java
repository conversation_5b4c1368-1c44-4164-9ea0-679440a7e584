package com.trs.police.fight.service.compositeItem;

import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.fight.domain.dto.CompositeItemSearchDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 作战警情类别名称映射服务
 *
 * <AUTHOR>
 */
@Component
public class CompositeJqlxMcMapService implements ICompositeItemSearchService {

    @Resource
    private DictService dictService;

    /**
     * 缓存
     */
    private Map<String, String> cache;

    @Override
    public CompositeItemSearchDTO searchCompositeItems(CompositeItemSearchDTO compositeItemSearchDTO) {
        // 缓存
        if (Objects.nonNull(cache) && !CollectionUtils.isEmpty(cache)) {
            compositeItemSearchDTO.setJqlxMcMap(cache);
            return compositeItemSearchDTO;
        }

        // 正常实现
        List<Dict2VO> sthyJqLabel = dictService.commonSearch("sthy_jq_label", null, null, null);
        if (CollectionUtils.isEmpty(sthyJqLabel)) {
            compositeItemSearchDTO.setJqlxMcMap(new HashMap<>());
        } else {
            Map<String, String> map = sthyJqLabel.stream()
                    .collect(Collectors.toMap(Dict2VO::getDictDesc, Dict2VO::getName));
            // 缓存起来
            if (Objects.nonNull(map) && !CollectionUtils.isEmpty(map)) {
                cache = map;
            }
            compositeItemSearchDTO.setJqlxMcMap(map);
        }
        return compositeItemSearchDTO;
    }
}
