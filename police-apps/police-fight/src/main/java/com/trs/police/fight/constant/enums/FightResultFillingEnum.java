package com.trs.police.fight.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 战果填报枚举
 *
 * <AUTHOR>
 * @since 2022/04/19
 */

public enum FightResultFillingEnum {

    /**
     * 督办
     */
    SUPERVISES(0, "督办"),

    /**
     * 贺电
     */
    CONGRATULATORY_TELEGRAM(1, "贺电"),

    /**
     * 领导批示
     */
    LEADERSHIP_INSTRUCTION(2, "领导批示"),

    /**
     * 部批集群战役
     */
    CLUSTER_BATTLE(3, "部批集群战役"),

    /**
     * 案件
     */
    CASE_EVENT(4, "案件"),

    /**
     * 嫌疑人
     */
    SUSPECT(5, "嫌疑人");


    @EnumValue
    @Getter
    private final Integer code;

    @Getter
    private final String name;

    FightResultFillingEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据枚举值的code解析枚举值
     *
     * @param code code
     * @return {@link FightResultFillingEnum}
     */
    public static FightResultFillingEnum codeOf(Long code) {
        if (code != null) {
            for (FightResultFillingEnum typeEnum : FightResultFillingEnum.values()) {
                if (code.equals(typeEnum.code)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

}
