<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.fight.mapper.FightCompositeMessageUserRelationMapper">


    <select id="findUnreadMessageListByUserIdAndCompositeIds" resultType="com.trs.police.common.core.entity.FightCompositeMessageUserRelation">
        select t1.*,t2.composite_id compositeId
        from t_fight_composite_message_user_relation t1
        join t_fight_composite_message t2 on
        t1.message_id = t2.id
        where t1.is_read = false
        and t1.user_id = #{userId}
        and t2.composite_id in
        <foreach item="item" collection="compositeIds" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>