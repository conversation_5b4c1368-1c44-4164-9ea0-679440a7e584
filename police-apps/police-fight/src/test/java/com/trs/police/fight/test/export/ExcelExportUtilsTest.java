package com.trs.police.fight.test.export;

import com.trs.police.fight.FightApp;
import com.trs.police.fight.utils.ExcelExportUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.LinkedHashSet;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @date 创建时间：2024/3/29 19:28
 * @version 1.0
 * @since 1.0
 */
@SpringBootTest(classes = FightApp.class)
public class ExcelExportUtilsTest {

    @Test
    public void testExport() {
        ExcelExportUtils.customDynamicHeader("2024年3月29日", new LinkedHashSet<>(0), "2024年3月29日", ExcelExportUtils.PERSONAL_STATISTICS_EXPORT_FILENAME, null);
    }
}
