package com.trs.police.fight.helper;

import com.trs.police.fight.FightApp;
import com.trs.police.fight.domain.entity.ClueEntity;
import com.trs.police.fight.mapper.ClueEntityMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

@SpringBootTest(classes = FightApp.class)
class CluePoolBusinessArchivesPushHelperTest {

    @Autowired
    private CluePoolBusinessArchivesPushHelper helper;

    @Autowired
    private ClueEntityMapper clueEntityMapper;

    private ClueEntity entity;

    private String operate;

    @BeforeEach
    void setUp() {
        entity = clueEntityMapper.selectById(25L);
        operate = "insert";
    }

    @AfterEach
    void destory() {
        entity = null;
    }

    @Test
    void pushBusinessArchivesMessage() {
        helper.pushBusinessArchivesMessage(operate, entity);
    }

    @Test
    void buildRecordInfo() throws IllegalAccessException {
        final Map<String, Object> map = helper.buildRecordInfo(entity);
        System.out.println(map);
    }
}