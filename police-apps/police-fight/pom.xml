<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>police-apps</artifactId>
        <groupId>com.trs</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.trs.police</groupId>
    <artifactId>police-fight</artifactId>

    <description>作战</description>

    <dependencies>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-security-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-statistic</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-datasource-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-es-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-openfeign-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <!-- excel 读写 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-log-starter</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-notice-starter</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-rabbitmq-starter</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.4</version>
        </dependency>
        <!-- 状态机 -->
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-statemachine</artifactId>
            <version>4.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.trs.web</groupId>
            <artifactId>media_base_web_builder</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.trs.sdk</groupId>
            <artifactId>oss-aws3</artifactId>
            <version>0.3.6</version>
        </dependency>
        <!--postgres-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>io.gitee.grtsc</groupId>
            <artifactId>condify4mybatis_plus</artifactId>
            <version>1.1</version>
            <scope>compile</scope>
        </dependency>

        <!--kafka-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>

        <!--导出word-->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-test</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.trs.police</groupId>
            <artifactId>police-common-redis-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <!-- build dockerfile-->
            <id>build-dockerfile</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>pl.project13.maven</groupId>
                        <artifactId>git-commit-id-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>