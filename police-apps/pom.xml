<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>police</artifactId>
        <groupId>com.trs</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>police-apps</artifactId>
    <packaging>pom</packaging>
    <description>微服务工程集合</description>
    <modules>
        <module>police-authorize</module>
        <module>police-gateway</module>
        <module>police-oss</module>
        <module>police-message</module>
        <module>police-fight</module>
        <module>police-global</module>
        <module>police-control</module>
        <module>police-permission</module>
        <module>police-approval</module>
        <module>police-log</module>
        <module>police-profile</module>
        <module>police-task-tracing</module>
        <module>police-risk</module>
        <module>police-schedule</module>
        <module>police-statistic</module>
        <module>police-search</module>
        <module>police-spacetime-collision</module>
        <module>police-api</module>
        <module>police-feedback</module>
        <module>police-ul-portal</module>
        <module>police-api-forward</module>
        <module>police-syncud</module>
        <module>police-intelligence</module>
        <module>police-fx-subject</module>
        <module>police-model-statistics</module>
        <module>police-incident-analysis</module>
        <module>police-comparison-api</module>
        <module>police-comparison-data-extract</module>
        <module>police-docsonline</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>