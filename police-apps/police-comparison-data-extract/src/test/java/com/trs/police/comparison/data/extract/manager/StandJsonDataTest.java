package com.trs.police.comparison.data.extract.manager;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import com.alibaba.fastjson2.JSONValidator;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/8/23 14:03
 */

public class StandJsonDataTest {

    public static void main(String[] args) {
        String value = "{\"alarmHumans\":\"[{\\\"humanName\\\": \\\"何敏\\\",\n" +
                "\\\"supplementalHuman\\\": null,\n" +
                "\\\"bornTime\\\": \\\"1972-12-10\\\",\n" +
                "\\\"humanId\\\": \\\"0082_NQ6CNI_Q8IIN6NNINNII66\\\",\n" +
                "\\\"listLibTags\\\": \\\"null\\\",\n" +
                "\\\"certificateNumber\\\": \\\"510502197212102220\\\"},{\\\"humanName\\\": \\\"何敏\\\",\n" +
                "\\\"supplementalHuman\\\": null,\n" +
                "\\\"bornTime\\\": \\\"1972-12-10\\\",\n" +
                "\\\"humanId\\\": \\\"0082_NQ6CNI_Q8IIN6NNINNII66\\\",\n" +
                "\\\"listLibTags\\\": \\\"null\\\",\n" +
                "\\\"certificateNumber\\\": \\\"510502197212102220\\\"}]\"}";
        String stringJsonPath = "$.alarmHumans";
        Object o = JSONPath.eval(value, stringJsonPath);
        if (o instanceof String) {
            String s = (String) o;
            if (StringUtils.isNotEmpty(s)) {
                if (JSONValidator.from(s).validate() && s.startsWith("[") && s.endsWith("]")) {
                    value = JSONPath.set(value, stringJsonPath, JSONArray.parseArray(s));
                } else if (JSONValidator.from(s).validate() && s.startsWith("{") && s.endsWith("}")) {
                    value = JSONPath.set(value, stringJsonPath, JSONObject.parseObject(s));
                }
            }
        }
        System.out.println("value is " + value + " and id number is " + JSONPath.eval(value, "$.alarmHumans[0].certificateNumber"));
    }
}
