package com.trs.police.comparison.data.extract.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    /**
     * 初始化线程池
     *
     * @return 线程池
     */
    @Bean(name = "processExecutor")
    public Executor processExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(40);
        // 设置最大线程数
        executor.setMaxPoolSize(100);
        // 设置队列长度
        executor.setQueueCapacity(100);
        // 设置线程存活时间
        executor.setKeepAliveSeconds(7200);
        // 设置线程前缀
        executor.setThreadNamePrefix("processExecutor-");
        // 设置最长等待时间
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(1800);
        // 设置丢弃策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 线程池初始化
        executor.initialize();
        return executor;
    }
}