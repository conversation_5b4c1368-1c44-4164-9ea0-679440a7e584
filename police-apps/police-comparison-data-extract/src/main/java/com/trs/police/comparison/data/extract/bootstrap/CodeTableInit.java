package com.trs.police.comparison.data.extract.bootstrap;

import com.trs.police.comparison.data.extract.constants.BackFillConstants;
import lombok.extern.slf4j.Slf4j;
import net.sf.ehcache.Cache;
import net.sf.ehcache.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.Optional;

import static com.trs.police.comparison.data.extract.util.CacheKeyUtil.generateKey;

/**
 * 初始换缓存码表
 *
 * <AUTHOR>
 * @since 2024/6/24 19:57
 */
@Slf4j
@Order(2)
@Component
public class CodeTableInit implements ApplicationRunner {


    private final DataSource dataSource;

    @Resource
    private Cache cache;


    public CodeTableInit(
            @Autowired DataSource dataSource) {
        this.dataSource = dataSource;
    }


    @Override
    public void run(ApplicationArguments args) {
        int pageSize = 1000;

        String sql = MessageFormat.format("select {0} from {1}",
                BackFillConstants.SENSING_SOURCE_ID + "," + BackFillConstants.BACK_FILL_FIELDS, BackFillConstants.INNER_TABLE_NAME);

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            conn = dataSource.getConnection();
            conn.setReadOnly(true);
            conn.setAutoCommit(false);

            ps = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setFetchSize(pageSize);

            rs = ps.executeQuery();
            String[] fieldArray = BackFillConstants.BACK_FILL_FIELDS.split(BackFillConstants.SEPARATOR);
            int totalCount = 0;
            while (rs.next()) {
                totalCount += 1;
                String code = String.valueOf(rs.getObject(BackFillConstants.SENSING_SOURCE_ID));
                for (String field : fieldArray) {
                    Element element = new Element(generateKey(code, field), rs.getObject(field));
                    cache.put(element);
                }
            }
            log.info("总共加载 {} 条 码表数据", totalCount);
        } catch (Exception e) {
            log.error("从数据库加载码表数据出错", e);
        } finally {
            closeRsAndPs(ps, rs);
            Optional.ofNullable(conn).ifPresent(connection -> {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.warn("连接关闭(归还)异常");
                }
            });
        }
    }

    private void closeRsAndPs(PreparedStatement ps, ResultSet rs) {
        closeResultSet(rs);
        closePreparedStatement(ps);
    }

    private void closeResultSet(ResultSet rs) {
        Optional.ofNullable(rs).ifPresent(resultSet -> {
            try {
                resultSet.close();
            } catch (SQLException e) {
                log.warn("关闭结果集异常");
            }
        });
    }

    private void closePreparedStatement(PreparedStatement ps) {
        Optional.ofNullable(ps).ifPresent(preparedStatement -> {
            try {
                preparedStatement.close();
            } catch (SQLException e) {
                log.warn("Statement关闭异常");
            }
        });
    }
}
