package com.trs.police.comparison.data.extract.bootstrap;

import com.trs.police.comparison.data.extract.config.ConfigInfo;
import com.trs.police.comparison.data.extract.config.properties.DataSourceProperties;
import com.trs.police.comparison.data.extract.config.properties.HadoopProperties;
import com.trs.police.comparison.data.extract.task.impl.DataPersistenceTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.security.SecurityUtil;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.iceberg.Schema;
import org.apache.iceberg.Table;
import org.apache.iceberg.catalog.TableIdentifier;
import org.apache.iceberg.hive.HiveCatalog;
import org.apache.iceberg.types.Types;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 初始化iceberg操作相关对象
 *
 * <AUTHOR>
 * @since 2024/7/15 20:15
 */
@Slf4j
@Order(3)
@Component
@ConditionalOnBean(DataPersistenceTask.class)
public class IcebergTableInit implements ApplicationRunner, ApplicationContextAware {

    @Resource
    private HadoopProperties hadoopProperties;

    @Resource
    private DataSourceProperties dataSourceProperties;

    private ConfigurableApplicationContext applicationContext;

    /**
     * 初始化schema
     */
    private final Map<String, Schema> schemaMap = new HashMap<>();

    /**
     * 初始化table
     */
    private final Map<String, Table> tableMap = new HashMap<>();


    private String databaseName;

    private final HiveCatalog catalog = new HiveCatalog();


    /**
     * 从名称获取table 对象
     *
     * @param name 名称
     * @return table对象
     */
    public Table getTableByName(String name){
        return tableMap.get(name);
    }

    /**
     * 从名称获取schema 对象
     *
     * @param name 名称
     * @return schema 对象
     */
    public Schema getSchemaByName(String name){
        return schemaMap.get(name);
    }

    @Override
    public void run(ApplicationArguments args) {
        try {
            if (StringUtils.isEmpty(dataSourceProperties.getHiveMetastoreUri()) || StringUtils.isEmpty(dataSourceProperties.getWarehouseLocation()) || StringUtils.isEmpty(dataSourceProperties.getDatabaseName())) {
                throw new NullPointerException("保存数据时 hiveMetastoreUri , warehouseLocation, databaseName 不能为空");
            }
            this.databaseName = dataSourceProperties.getDatabaseName();

            Map<String, String> properties = new HashMap<>(6);
            properties.put("type", "iceberg");
            properties.put("catalog-type", "hive");
            properties.put("uri", dataSourceProperties.getHiveMetastoreUri());
            properties.put("clients", "5");
            properties.put("property-version", "1");
            properties.put("warehouse", dataSourceProperties.getWarehouseLocation());
            properties.put("hive-conf-dir", dataSourceProperties.getHiveConfDir());
            Configuration conf = new Configuration();

            for (String dynamicConfFile : hadoopProperties.getDynamicConfFiles()) {
                conf.addResource(new Path(hadoopProperties.getConfigHome() + File.separator + dynamicConfFile));
            }
            initKerberos(conf);
            catalog.setConf(conf);
            catalog.initialize("hive", properties);

            //初始化表
            initTable();
            log.info("初始化表完成");
        } catch (Exception e) {
            log.error("初始化表失败", e);
            //初始化表失败, 整个服务直接停止运行, 因为后面数据写入一定出错
            applicationContext.close();
        }
    }

    private void initKerberos(Configuration configuration) {
        if (StringUtils.isNotBlank(hadoopProperties.getKrb5FilePath())) {
            System.setProperty("java.security.krb5.conf", hadoopProperties.getKrb5FilePath());
            log.info("设置java.security.krb5.conf为{}", hadoopProperties.getKrb5FilePath());
        }
        if (StringUtils.isNotBlank(hadoopProperties.getZookeeperServerPrincipal())) {
            System.setProperty("zookeeper.server.principal", hadoopProperties.getZookeeperServerPrincipal());
            log.info("设置zookeeper.server.principal为{}", hadoopProperties.getZookeeperServerPrincipal());
        }
        if (StringUtils.isNotBlank(hadoopProperties.getKeytabFilePath()) && StringUtils.isNotBlank(hadoopProperties.getLoginRealm())) {
            try {
                //启用kerberos认证
                SecurityUtil.setAuthenticationMethod(UserGroupInformation.AuthenticationMethod.KERBEROS, configuration);
                UserGroupInformation.setConfiguration(configuration);
                UserGroupInformation.loginUserFromKeytab(hadoopProperties.getLoginRealm(), hadoopProperties.getKeytabFilePath());
                log.info("使用loginUserFromKeytab登陆：loginRealm:{},keytabPath:{}", hadoopProperties.getLoginRealm(), hadoopProperties.getKeytabFilePath());
            } catch (IOException e) {
                throw new RuntimeException("loginUserFromKeytab失败", e);
            }
        }
    }

    /**
     *初始化iceberg操作相关对象
     */
    private void initTable() {
        List<ConfigInfo> list = dataSourceProperties.getList();
        for (ConfigInfo configInfo : list) {
            HashMap<String, String> map = configInfo.getFieldJsonPath();
            TableIdentifier tableIdentifier = TableIdentifier.of(databaseName, configInfo.getEnName());
            Schema schema = buildSchema(map.keySet());
            Table table;
            if (!catalog.tableExists(tableIdentifier)) {
                table = catalog.createTable(tableIdentifier, schema);
                log.info("创建表 {}", tableIdentifier.name());
            } else {
                table = catalog.loadTable(tableIdentifier);
            }
            tableMap.put(configInfo.getEnName(), table);
            schemaMap.put(configInfo.getEnName(), table.schema());
        }
    }

    /**
     * 构建表属性
     *
     * @param fieldSet 字段集合
     * @return schema
     */
    private Schema buildSchema(Set<String> fieldSet) {
        ArrayList<Types.NestedField> fieldList = new ArrayList<>();
        int index = 1;
        for (String key : fieldSet) {
            fieldList.add(Types.NestedField.optional(index++, key, Types.StringType.get()));
        }
        return new Schema(fieldList);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = (ConfigurableApplicationContext)applicationContext;
    }
}
