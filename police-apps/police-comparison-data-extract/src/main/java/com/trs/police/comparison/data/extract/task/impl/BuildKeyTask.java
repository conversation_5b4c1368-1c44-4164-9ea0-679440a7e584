package com.trs.police.comparison.data.extract.task.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.trs.police.comparison.data.extract.config.ConfigInfo;
import com.trs.police.comparison.data.extract.domain.ExtractErrorLog;
import com.trs.police.comparison.data.extract.monitor.ErrorDataMonitor;
import com.trs.police.comparison.data.extract.task.IDataTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.trs.police.comparison.data.extract.monitor.ErrorDataMonitor.getFullErrorMessage;

/**
 * 生成MD5主键
 *
 * <AUTHOR>
 * @since 2024/6/20 15:54
 */
@Slf4j
@Component
public class BuildKeyTask implements IDataTask {

    @Resource
    private ErrorDataMonitor monitor;

    /**
     * 执行顺序
     *
     * @return 顺序
     */
    @Override
    public int getOrder() {
        return 1;
    }

    /**
     * 处理数据
     *
     * @param list   传入的数据
     * @param config 配置
     */
    @Override
    public void processData(List<JSONObject> list, ConfigInfo config) {
        for (JSONObject data : list) {
            try {
                //获取生成主键的字段
                List<String> keyPathFields = config.getKeyFields();
                String keyFieldName = config.getKeyFieldName();
                StringBuilder keyString = new StringBuilder();
                if (keyPathFields.size() == 1) {
                    Object dataId = JSONPath.eval(data, keyPathFields.get(0));
                    if (dataId == null) {
                        log.error("配置的主键字段jsonPath未能获取到数据");
                        throw new RuntimeException("配置的主键字段jsonPath未能获取到数据");
                    }
                    data.put(keyFieldName, dataId);
                    return;
                }
                for (String keyPathField : keyPathFields) {
                    keyString.append(Optional.ofNullable(JSONPath.eval(data, keyPathField)).orElse(""));
                }
                data.put(keyFieldName, DigestUtils.md5Hex(keyString.toString()));
            } catch (Exception e) {
                log.error("生成主键出错", e);
                RuntimeException runtimeException = new RuntimeException("生成主键出错", e);
                ExtractErrorLog extractErrorLog = new ExtractErrorLog();
                extractErrorLog.setCreateTime(LocalDateTime.now());
                extractErrorLog.setErrorInfo(getFullErrorMessage(runtimeException));
                extractErrorLog.setErrorData(data.toJSONString());
                extractErrorLog.setDataSourceConfig(JSONObject.toJSONString(config));
                monitor.collectorErrorInfo(extractErrorLog);
                throw runtimeException;
            }
        }
    }

    /**
     * 处理数据
     *
     * @param list   传入的数据
     * @param config 配置
     * @throws JsonProcessingException 异常
     */
    @Override
    public void testProcessData(List<JSONObject> list, ConfigInfo config) throws JsonProcessingException {
        processData(list, config);
    }
}
