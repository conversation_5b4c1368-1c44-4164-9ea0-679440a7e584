package com.trs.police.comparison.data.extract.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/15 10:09
 */

@Data
@Component
@ConfigurationProperties(prefix = "com.trs.hadoop")
public class HadoopProperties implements Serializable {

    private static final long serialVersionUID = -5106288148962026938L;

    private String configHome;

    private String confFilePath;

    private String krb5FilePath;

    private String zookeeperServerPrincipal;

    private String keytabFilePath;

    private String loginRealm;

    private List<String> dynamicConfFiles = new ArrayList<>();


}
