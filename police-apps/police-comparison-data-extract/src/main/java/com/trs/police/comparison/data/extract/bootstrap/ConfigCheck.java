package com.trs.police.comparison.data.extract.bootstrap;

import com.trs.police.comparison.data.extract.config.ConfigInfo;
import com.trs.police.comparison.data.extract.config.properties.DataSourceProperties;
import com.trs.police.comparison.data.extract.config.properties.KafkaConsumerProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配置检查
 *
 * <AUTHOR>
 * @since 2024/7/15 21:05
 */
@Slf4j
@Order(2)
@Component
public class ConfigCheck implements ApplicationRunner, ApplicationContextAware {

    private ConfigurableApplicationContext applicationContext;

    @Resource
    private DataSourceProperties dataSourceProperties;

    @Resource
    private KafkaConsumerProperties kafkaProperties;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (!checkEnNameDuplicate()) {
            log.error("服务已停止");
            applicationContext.close();
        }
    }

    /**
     * 检查 配置的数据源英文名是否重复
     *
     * @return true 通过检查 false 不通过检查
     */
    private boolean checkEnNameDuplicate() {
        List<String> enNameList = dataSourceProperties.getList().stream().map(ConfigInfo::getEnName).collect(Collectors.toList());
        //判断数据源英文名称是否存在重复
        HashSet<String> set = new HashSet<>();
        for (String str : enNameList) {
            if (!set.add(str)) {
               log.error("配置的数据源英文名存在重复, 请调整");
               return false;
            }
        }
        return true;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = (ConfigurableApplicationContext)applicationContext;
    }
}
