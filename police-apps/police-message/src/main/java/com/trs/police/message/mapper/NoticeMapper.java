package com.trs.police.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.NoticeEntity;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.vo.CodeNameCountVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.message.NoticeVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @date 2023/3/22 11:13
 */
@Mapper
public interface NoticeMapper extends BaseMapper<NoticeEntity> {

    /**
     * 清除用户所有未读
     *
     * @param userId 用户id
     * @param deptId 部门id
     */
    @Update("update t_notice set is_read=1 where is_read=0 and   receiver->>\"$.userId\"=#{userId} and receiver->>\"$.deptId\"=#{deptId}")
    void clearUnreadNotice(@Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 单个消息已读
     *
     * @param id 消息id
     */
    @Update("update t_notice set is_read=1 where id =#{id}")
    void clearOneMessageUnread(@Param("id") Long id);

    /**
     * 获取消息列表
     *
     * @param filterParams 动态参数
     * @param searchParams 检索
     * @param sortParams   排序
     * @param toPage       分页
     * @return {@link  NoticeVO}
     */
    Page<NoticeVO> getNoticePageList(
        @Param("filterParams") List<KeyValueTypeVO> filterParams,
        @Param("searchParams") SearchParams searchParams,
        @Param("sortParams") SortParams sortParams,
        Page<Object> toPage);

    /**
     * 发送消息类型
     *
     * @param module 模块
     * @return 发送消息类型
     */
    @Select("select send_notice_type from t_operate_module where id=#{module}")
    Long getIsSendNoticeType(@Param("module") Long module);

    /**
     * 获取消息中西模块
     *
     * @return 模块信息
     */
    List<CodeNameCountVO> getNoticeModule();
    /**
     * 获取父模块name
     *
     * @param moduleId 模块id
     * @return 父模块name
     */
    @Select("select cn_name from t_operate_module where id = IFNULL((select pid from t_operate_module where id=#{moduleId}),#{moduleId})")
    String getParentModuleName(@Param("moduleId") Long moduleId);

    /**
     * 查询用户所有未读
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return 未读通知数据
     */
    List<NoticeVO> getUnreadNoticeList(@Param("userId") Long userId, @Param("deptId") Long deptId);
}
