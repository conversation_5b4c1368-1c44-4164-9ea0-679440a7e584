package com.trs.police.message.service.impl;

import com.trs.police.common.core.PhoneMessageReport;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.PhoneMessageReply;
import com.trs.police.common.core.vo.message.PhoneMessageVO;
import com.trs.police.message.domain.Message;
import com.trs.police.message.domain.MessageModule;
import com.trs.police.message.mapper.MessageMapper;
import com.trs.police.message.mapper.MessageModuleMapper;
import com.trs.police.message.properties.ScgaMsgGatewayProperties;
import com.trs.police.message.service.MsgGatewayService;
import com.wellbole.sms.client.impl.HttpPostSmsClient;
import com.wellbole.sms.client.impl.SmsFetcherImpl;
import com.wellbole.sms.client.impl.SmsSenderImpl;
import com.wellbole.sms.client.message.InboundMessage;
import com.wellbole.sms.client.message.OutboundMessage;
import com.wellbole.sms.client.result.SmsFetchResult;
import com.wellbole.sms.client.result.SmsSendResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短信网关服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MsgGatewayLongnanService implements MsgGatewayService {

    @Resource
    private ScgaMsgGatewayProperties msgGatewayProperties;

    @Resource
    private MessageMapper messageMapper;

    @Resource
    private MessageModuleMapper messageModuleMapper;

    private HttpPostSmsClient client;

    @Value("${message.msg.longnan.url:}")
    private String url;

    @Value("${message.msg.longnan.appKey:}")
    private String appKey;

    @Value("${message.msg.longnan.appSecret:}")
    private String appSecret;

    @PostConstruct
    private void init() {
        client = new HttpPostSmsClient();
        client.setServerUrl(url);
        client.setAppKey(appKey);
        client.setAppSecret(appSecret);
    }

    /**
     * 获取短信发送结果
     *
     * @param messageId message主键
     * @return 结果
     */
    @Override
    public List<PhoneMessageReport> getReport(Long messageId) {
        try {
            List<Message> messages = messageMapper.findByMessageId(messageId);
            return messages.stream().map(message ->
                    new PhoneMessageReport(message.getMessageId().toString(), message.getSendToPhone()[0], message.getState()))
                    .collect(Collectors.toList());
        } catch (Exception exception) {
            log.error("查询短信发送状态失败!", exception);
            return Collections.emptyList();
        }
    }

    /**
     * 发送短信
     *
     * @param messageVO 短信内容
     * @return t_message 表主键
     */
    @Override
    public Long sendMessage(PhoneMessageVO messageVO) {
        // 过滤出合法的手机号
        String[] legalPhone = Arrays.stream(messageVO.getPhoneNumbers())
                .filter(StringUtil::isLegalMobilePhone)
                .toArray(String[]::new);
        if (legalPhone.length == 0) {
            log.error("没有合法的手机号, 发送失败！：{}", Arrays.toString(legalPhone));
            return null;
        }

        long messageId = System.currentTimeMillis();
        SmsSenderImpl smsSender = new SmsSenderImpl();
        smsSender.setClient(client);
        MessageModule module = messageModuleMapper.selectById(messageVO.getModuleId());
        // 扩展码
        String extensionCode = "0000";
        for (String phone : legalPhone) {
            Message msg = new Message();
            msg.setSendToPhone(new String[]{phone});
            msg.setModuleId(module.getId());
            msg.setContent(messageVO.getContent());
            msg.setExtensionCode("0000");
            msg.setSendTime(LocalDateTime.now());
            msg.setMessageId(messageId);

            OutboundMessage message = new OutboundMessage();
            message.setContent(messageVO.getContent());
            message.setPhone(phone);
            message.setExtCode(extensionCode);
            SmsSendResult sendResult = smsSender.send(message);
            //发送结果检查
            if (sendResult != null && sendResult.hasError()) {
                log.error("发送失败，原因＝" + sendResult.getErrorMessage());
                msg.setState(0);
            } else {
                //发送成功时，服务器端会生成一个36字节长度的唯一标示。
                log.info("发送成功，服务器端返回的msgid＝" + sendResult.getMsgid());
                msg.setState(1);
            }
            messageMapper.insert(msg);
        }
        return messageId;
    }

    /**
     * 获取回复
     *
     * @param messageId message主键
     * @return 结果
     */
    @Override
    public List<PhoneMessageReply> getReply(Long messageId) {
//        List<Message> messages = messageMapper.findByMessageId(messageId);
//        List<PhoneMessageReply> replyList = new ArrayList<>();
//        for (Message message : messages) {
//            String extensionCode = message.getExtensionCode();
//            List<PhoneMessageReply> replies = reply(extensionCode);
//            replyList.addAll(replies);
//        }
        return new ArrayList<>();
    }

    private List<PhoneMessageReply> reply(String extensionCode){
        try {
            SmsFetcherImpl smsFetcher = new SmsFetcherImpl();
            smsFetcher.setClient(client);
            //获取上行短信，建议每间隔5秒以上获取一次，
            //太频繁会造成短信服务负载过大。
            SmsFetchResult fetchResult = smsFetcher.fetch(extensionCode);
            if (fetchResult.hasError()) {
                throw new RuntimeException(fetchResult.getErrorMessage());
            } else if (fetchResult.hasData()) {
                List<PhoneMessageReply> replyList = new ArrayList<>();
                for (InboundMessage inboundMessage : fetchResult.getInboundMessageList()) {
                    PhoneMessageReply reply = new PhoneMessageReply();
                    reply.setPhone(inboundMessage.getPhone());
                    reply.setContent(inboundMessage.getContent());
                    reply.setExtensionCode(inboundMessage.getExtCode());
                    reply.setReplyTime(TimeUtil.dateToLocalDateTime(inboundMessage.getReceiveAt()));
                    replyList.add(reply);
                }

                return replyList;
            }
            return Collections.emptyList();
        } catch (Exception exception) {
            log.error("查询短信回复失败!", exception);
            return Collections.emptyList();
        }
    }

    @Override
    public String key() {
        return "longnan";
    }

    @Override
    public String desc() {
        return "longnan";
    }
}
