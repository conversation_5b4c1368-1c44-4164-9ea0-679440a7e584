package com.trs.police.message.controller;

import com.trs.police.common.core.vo.message.BusinessArchivesMessageVO;
import com.trs.police.common.core.vo.message.KafkaMessageVO;
import com.trs.police.message.listener.KafkaMessageListener;
import com.trs.police.message.listener.businessarchives.KafkaBusinessArchivesMessageListener;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> yanghy
 * @date : 2022/9/28 15:11
 */
@RestController
public class KafkaController {

    @Resource
    private KafkaMessageListener kafkaMessageListener;

    @Resource
    private KafkaBusinessArchivesMessageListener businessArchivesMessageListener;

    /**
     * 向kafka推送消息
     *
     * @param message 消息内容
     */
    @PostMapping("public/kafka/push")
    public void pushMessageToKafka(@RequestBody KafkaMessageVO message) {
        kafkaMessageListener.pushMessageToKafka(message);
    }

    /**
     * 向kafka推送消息
     *
     * @param message 消息内容
     */
    @PostMapping("public/kafka/businessArchives/push")
    public void pushBusinessArchivesMessageToKafka(@RequestBody BusinessArchivesMessageVO message) {
        businessArchivesMessageListener.pushMessageToKafka(message);
    }
}
