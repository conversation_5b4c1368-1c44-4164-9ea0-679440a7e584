<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>My WebSocket client2</title>
</head>
<body>
Welcome client2<br/>
<input id="text" type="text"/>
<button onclick="send()">发送</button>
<button onclick="closeWebSocket()">关闭连接</button>
<div id="message">
</div>
</body>

<script type="text/javascript">
    var websocket = null;
    var host = "";
    if (window.location.protocol === 'http:') {
        host = 'ws://localhost:8888/message/websocket/fight/composite/message/2';
    } else {
        host = 'wss://localhost:8888/message/fight/websocket/composite/message/2';
    }
    //判断当前浏览器是否支持WebSocket

    if ('WebSocket' in window) {
        websocket = new WebSocket(host);
    } else if ('MozWebSocket' in window) {
        websocket = new MozWebSocket(host);
    } else {
        alert("该浏览器不支持WebSocket！");
//        return;
    }

    //连接发生错误的回调方法
    websocket.onerror = function () {
        setMessageInnerHTML("连接出错");
    };

    //连接成功建立的回调方法
    websocket.onopen = function (event) {
        console.log("连接成功");
        setMessageInnerHTML("已连接服务器！");
    }

    //接收到消息的回调方法
    websocket.onmessage = function (event) {
        console.log(event.data);
        setMessageInnerHTML(event.data);
    }

    //连接关闭的回调方法
    websocket.onclose = function () {
        setMessageInnerHTML("连接关闭");
    }

    //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = function () {
        websocket.close();
    }

    //将消息显示在网页上
    function setMessageInnerHTML(innerHTML) {
        document.getElementById('message').innerHTML += innerHTML + '<br/>';
    }

    //关闭连接
    function closeWebSocket() {
        websocket.close();
    }

    //发送消息
    function send() {
        var message = document.getElementById('text').value;
        websocket.send(message);
    }
</script>
</html>