package com.trs.police.search.panorama.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.exception.ServiceException;
import com.trs.police.search.PoliceSearchApplication;
import com.trs.police.search.domain.entity.SearchSchemaEntity;
import com.trs.police.search.mapper.SearchSchemaMapper;
import javassist.ClassClassPath;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtMethod;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SchemaDataCountTaskTest {

    @Resource
    private SchemaDataCountTask countTask;

    @Resource
    private SearchSchemaMapper schemaMapper;

    @BeforeAll
    static void all() throws Exception {
        ClassPool classPool = ClassPool.getDefault();
        classPool.insertClassPath(new ClassClassPath(PoliceSearchApplication.class));
        CtClass ctClass = classPool.get("org.elasticsearch.client.RestHighLevelClient");
        CtMethod ctMethod = ctClass.getDeclaredMethod("getVersionValidation");
        ctMethod.setBody("{System.out.print(\"修改了字节码方法\");return java.util.Optional.empty();}");
        ctClass.toClass();
    }

    @BeforeEach
    void setUp() {

    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void synchronizedSearchSchemaCount() throws ServiceException {
        countTask.synchronizedSearchSchemaCount();
    }

    @Test
    void synchronizedSchemaCount() throws ServiceException {
        final SearchSchemaEntity entity = schemaMapper.selectOne(
                new QueryWrapper<SearchSchemaEntity>().eq("en_name", "cs_pin_pai"));
        countTask.synchronizedSchemaCount(entity.getId());
    }

    @Test
    void getYesterdayIncrement() {
        String tableEnName = "dwd_hik_sydw_cyry_gx";
        String dataTimeField = "action_time";
        final Integer result = countTask.getYesterdayIncrementNew(tableEnName, dataTimeField);
        System.out.println(result);
    }
}
