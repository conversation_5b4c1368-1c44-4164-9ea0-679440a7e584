package com.trs.police.search.panorama.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.common.openfeign.starter.DTO.FindPersonByOtherArchivesDTO;
import com.trs.police.search.PoliceSearchApplication;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.dto.CarArchivesSearchDTO;
import com.trs.police.search.panorama.vo.JudgeRegistrationVO;
import com.trs.police.test.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = PoliceSearchApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ArchivesServiceImplTest extends BaseTestCase {

    @Autowired
    private ArchivesSearchServiceImpl service;

    @Test
    void tabs() {
    }

    @Test
    void export() {
    }

    @Test
    void getFetchRestfulResults() throws ServiceException {
        BaseArchivesSearchDTO dto = new BaseArchivesSearchDTO();
        dto.setPageNum(1);
        dto.setPageSize(10);
        dto.setUuid("232070c1-2b45-478d-9f1e-10a151ea754d");
        dto.setKeyword("51132319900101000X");
        dto.setSearchValue("51132319900101000X");
        dto.setNeedLog(false);
        dto.setArchivesType(ArchivesConstants.ARCHIVES_TYPE_PERSON);
        print(service.getFetchRestfulResults(dto));
    }

    @Test
    void judgeRegistration() throws ServiceException {
        BaseArchivesSearchDTO dto = new CarArchivesSearchDTO();
        dto.setArchivesType("person");
        dto.setRecordId("611608201106278000");
        final List<JudgeRegistrationVO> vo = service.judgeRegistration(dto);
        print(vo);
    }

    @Test
    void registration() {
    }

    @Test
    void detail() throws ServiceException {
        BaseArchivesSearchDTO dto = new CarArchivesSearchDTO();
        dto.setArchivesType("car");
        dto.setRecordId("川E25222;原农机号牌");
        ArchivesVO vo = service.detail(dto);
        print(vo);
        print("===============");
        dto.setRecordId("川E25222");
        service.detail(dto);
        print(vo);
    }

    @Test
    void detailJq() throws ServiceException {
        BaseArchivesSearchDTO dto = new BaseArchivesSearchDTO();
        dto.setNeedAuth(false);
        dto.setSkipFlushOnDetail(true);
        dto.setArchivesType(ArchivesEnum.BUSINESS_FOR_JQ.getType());
        dto.setRecordId("A4289972012072360164547");
        ArchivesVO vo = service.detail(dto);
        print(vo);
    }

    @Test
    void detailPerson() throws ServiceException {
        BaseArchivesSearchDTO dto = new BaseArchivesSearchDTO();
        dto.setSkipFlushOnDetail(true);
        dto.setArchivesType(ArchivesEnum.PERSON.getType());
        dto.setRecordId("51132319900101000X");
        ArchivesVO vo = service.detail(dto);
        print(vo);
    }

    @Test
    void queryGroup() throws ServiceException {
        BaseArchivesSearchDTO dto = new CarArchivesSearchDTO();
        dto.setArchivesType("car");
        dto.setFieldName("hphm");
        dto.setRecordId("川E25222");
        dto.setGroupFieldName("hpzl");
        var vo = service.getStatisticsRestfulResults(dto);
        print(JSON.toJSONString(vo.getDatas()));
    }

    @Test
    void findPersonByOtherArchives() throws ServiceException {
        FindPersonByOtherArchivesDTO dto = new FindPersonByOtherArchivesDTO();
        dto.setRecordId("13867856884");
        dto.setArchivesType("phone");
        service.findPersonByOtherArchives(dto);
    }
}