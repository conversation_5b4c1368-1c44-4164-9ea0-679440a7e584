package com.trs.police.search.domain.request;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.dto.BaseListDTO;
import lombok.Data;

/**
 * 红名单查询请求参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-21 15:13:37
 */
@Data
public class RedListQueryDTO extends BaseListDTO {

    private String searchType;

    private String keyword;

    @Override
    protected boolean checkParams() throws ServiceException {
        return false;
    }

}
