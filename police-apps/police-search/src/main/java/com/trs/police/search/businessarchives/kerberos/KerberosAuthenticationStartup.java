package com.trs.police.search.businessarchives.kerberos;

import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Kerberos 认证登录启动
 * *@author:wen.wen
 * *@create 2023-11-28 15:14
 **/
@Component
@Slf4j
public class KerberosAuthenticationStartup implements CommandLineRunner {

    private static final String ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME = "Client";

    private static final String ZOOKEEPER_SERVER_PRINCIPAL_KEY = "zookeeper.server.principal";

    private static final String ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL = "zookeeper/hadoop";

    @Override
    public void run(String... args) {
        try {
            boolean kerberosEnable = BeanFactoryHolder.getEnv().getProperty("business.archives.config.enable.kerberos", boolean.class, false);
            if (kerberosEnable) {
                final String userPrincipal = BeanFactoryHolder.getEnv().getProperty("hive.kerberos.principal");
                final String zookeeperPrincipal = BeanFactoryHolder.getEnv().getProperty("kerberos.zookeeper.principal", "zookeeper/hadoop");
                final String userKeytabPath = BeanFactoryHolder.getEnv().getProperty("kerberos.userKeytabPath", "/TRS/DATA/kerberos/user.keytab");
                final String krb5ConfPath = BeanFactoryHolder.getEnv().getProperty("kerberos.krb5ConfPath");
                final String userName = BeanFactoryHolder.getEnv().getProperty("kerberos.userName", "<EMAIL>");
                log.info("获取到的userName={},userPrincipal={},zookeeperPrincipal={},userKeytabPath={},krb5ConfPath={}", userName, userKeytabPath, zookeeperPrincipal, userKeytabPath, krb5ConfPath);
                System.setProperty(KerberosLoginUtil.JAVA_SECURITY_KRB5_CONF_KEY, krb5ConfPath);
                KerberosLoginUtil.setJaasConf(ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME, userName, userKeytabPath);
                KerberosLoginUtil.setZookeeperServerPrincipal(ZOOKEEPER_SERVER_PRINCIPAL_KEY, zookeeperPrincipal);
                Configuration conf = new Configuration();
                KerberosLoginUtil.login(userPrincipal, userKeytabPath, krb5ConfPath, conf);
                KerberosLoginUtil.processZkSsl();
            }
        } catch (Throwable e) {
            log.error("执行Kerberos登录发生异常", e);
        }
    }
}
