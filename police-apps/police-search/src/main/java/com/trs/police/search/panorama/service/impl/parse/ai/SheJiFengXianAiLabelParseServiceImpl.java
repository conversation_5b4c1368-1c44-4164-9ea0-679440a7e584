package com.trs.police.search.panorama.service.impl.parse.ai;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.service.BaseAiLabelParseService;
import com.trs.police.search.panorama.vo.AiLabelVo;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 涉及风险
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-19 15:18:24
 */
@Service
public class SheJiFengXianAiLabelParseServiceImpl extends BaseAiLabelParseService {

    @Override
    protected List<String> makeTopRight(AiLabelVo label, List<AiLabelVo> labels) {
        if (StringUtils.isEmpty(label.getRelation())) {
            return Collections.emptyList();
        }
        return Collections.singletonList(label.getRelation());
    }

    @Override
    protected List<String> makeCenter(AiLabelVo label, List<AiLabelVo> labels) {
        String fxbt = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("fxbt"))
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        String fxlb = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("fxlb"))
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        String fqsj = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("fqsj"))
                .filter(TimeUtils::isValid)
                .map(it -> TimeUtils.stringToString(it, TimeUtils.YYYYMMDD))
                .orElse("");
        StringBuilder aj = new StringBuilder(fxlb);
        if (StringUtils.isNotEmpty(fqsj)) {
            aj.append(StringUtils.SEPARATOR_OR).append(fqsj);
        }
        return Arrays.asList(fxbt, aj.toString());
    }

    @Override
    public String relationCatalog() {
        return SearchConstant.AI_LABEL_CATALOG_XGXX_SJFX;
    }

    @Override
    public ArchivesEnum archivesEnum() {
        return ArchivesEnum.PERSON;
    }
}
