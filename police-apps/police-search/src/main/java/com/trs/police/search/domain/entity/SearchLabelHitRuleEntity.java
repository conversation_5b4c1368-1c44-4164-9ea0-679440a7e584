package com.trs.police.search.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * SearchLabelEntity
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/9/27 17:28
 * @since 1.0
 */
@Data
@TableName("tb_search_label_hit_rule")
public class SearchLabelHitRuleEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField
    private String crUser;

    @TableField
    private Date crTime;

    @TableField
    private Long crDeptId;

    @TableField
    private Integer isDel = 0;

    @TableField
    private String updateUser;

    @TableField
    private Date updateTime;

    @TableField
    private Long updateDeptId;

    /**
     * 标签名称
     */
    @TableField
    private String labelName;

    /**
     * 标签所属档案类型
     */
    @TableField
    private String archivesType;

    /**
     * 命中规则说明
     */
    @TableField
    private String hitRuleDesc;

    /**
     * 详细命中规则
     */
    @TableField
    private String hitRuleDetail;

}
