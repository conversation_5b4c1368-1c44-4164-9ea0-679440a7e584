package com.trs.police.search.mgr.impl;

import com.trs.common.utils.StringUtils;
import com.trs.police.search.mgr.BaseJsonPathDataConvert;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/12 18:14
 * @since 1.0
 */
@Component
public class JsonPathDataConvertJoinList extends BaseJsonPathDataConvert {
    @Override
    public String key() {
        return "joinList";
    }

    /**
     * Applies this function to the given argument.
     *
     * @param o the function argument
     * @return the function result
     */
    @Override
    public Object apply(Object o) {
        if (o instanceof List) {
            return StringUtils.join(((List<?>) o).toArray(), StringUtils.SEPARATOR_COMMA);
        } else {
            return o;
        }
    }
}
