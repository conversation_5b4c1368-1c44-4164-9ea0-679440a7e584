package com.trs.police.search.traffic.police.constant.aspect;

import com.trs.police.search.constant.enums.SearchOperationEnum;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作记录注解
 *
 * <AUTHOR>
 * @since 2022/5/9 11:07
 **/
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OperationLog {

    /**
     * 表名
     *
     * @return 表名
     */
    String tableName() default "";

    /**
     * 操作uuid
     *
     * @return 操作uuid
     */
    String uuid();

    /**
     * 操作类型
     *
     * @return 操作类型
     */
    SearchOperationEnum operation();

    /**
     * 查询关键词
     *
     * @return 关键词
     */
    String keyword();

    /**
     * 查询总数
     *
     * @return 总数
     */
    String resultCount();

    /**
     * 描述
     *
     * @return 描述信息
     */
    String desc() default "";

}
