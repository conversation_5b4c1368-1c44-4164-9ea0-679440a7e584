package com.trs.police.search.domain.entity;

import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.annotations.TableId;
import com.trs.db.sdk.annotations.TableName;
import com.trs.db.sdk.annotations.analysis.impl.TableFieldAnalysis;
import com.trs.db.sdk.pojo.BaseRecordDO;
import lombok.Data;

import java.util.Map;


/**
 * @ClassName PersonVisitRecordEntity
 * @Description 人员走访记录表信息
 * <AUTHOR>
 * @Date 2024/2/27 16:15
 **/
@Data
@TableName("dwd_t_ps_person_visit_record")
public class PersonVisitRecordEntity extends BaseRecordDO {

    /**
     * 字段
     */
    private static Map<String, String> fieldMapping = null;

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 创建人
     */
    @TableField("cr_by")
    private String crBy;

    /**
     * 创建人姓名
     */
    @TableField("cr_by_name")
    private String crByName;

    /**
     * 创建部门
     */
    @TableField("cr_dept")
    private String crDept;

    /**
     * 创建时间
     */
    @TableField("cr_time")
    private String crTime;

    /**
     * 现住址
     */
    @TableField("current_residence")
    private String currentResidence;

    /**
     * 当前去向
     */
    @TableField
    private String destination;

    /**
     * 被走访人的身份证号码
     */
    @TableField("id_number")
    private String idNumber;

    /**
     * 是否在控
     */
    @TableField("in_control")
    private String inControl;

    /**
     * 走访情况
     */
    @TableField
    private String info;

    /**
     * 纬度
     */
    @TableField
    private Double lat;

    /**
     * 精度
     */
    @TableField
    private Double lng;

    /**
     * 走访方式
     */
    @TableField
    private String method;

    /**
     * 被走访人姓名
     */
    @TableField
    private String name;

    /**
     * 失控时间
     */
    @TableField("out_of_control_time")
    private String outOfControlTime;

    /**
     * 人员ID
     */
    @TableField("person_id")
    private String personId;

    /**
     * 走访时间
     */
    @TableField
    private String time;

    /**
     * 最后更新人
     */
    @TableField("up_by")
    private String upBy;

    /**
     * 最后更新人姓名
     */
    @TableField("up_by_name")
    private String upByName;

    /**
     * 最后更新时间
     */
    @TableField("up_time")
    private String upTime;

    /**
     * 走访工作人员
     */
    @TableField("visit_by")
    private String visitBy;

    /**
     * 走访方式
     */
    @TableField("visit_method")
    private String visitMethod;

    /**
     * 信息入库时间
     */
    @TableField
    private String xxrksj;

    /**
     * 构建字段映射关系map
     *
     * @return 关系映射map
     */
    public static Map<String, String> makeFieldMapping() {
        if (fieldMapping == null) {
            synchronized (PersonVisitRecordEntity.class) {
                if (fieldMapping == null) {
                    TableFieldAnalysis<PersonVisitRecordEntity> fieldAnalysis = new TableFieldAnalysis<>();
                    fieldMapping = fieldAnalysis.getAnnotationFieldValues(PersonVisitRecordEntity.class);
                }
            }
        }
        return fieldMapping;
    }

}
