package com.trs.police.search.traffic.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.search.traffic.police.dto.DataListDTO;
import com.trs.police.search.traffic.police.entity.TrafficJjwfjlEntity;
import com.trs.police.search.traffic.police.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName TrafficJjwfjlMapper
 * @Description 违法记录表mapper
 * <AUTHOR>
 * @Date 2023/12/20 10:31
 **/
@Mapper
public interface TrafficJjwfjlMapper extends BaseMapper<TrafficJjwfjlEntity> {

    /**
     * 根据ID获取信息
     *
     * @param dto       dto
     * @param dataLevel 数据级别
     * @param dataIds   数据id列表
     * @return 结果
     */
    List<WfjlAndCountVO> listByDataIds(
            @Param("dto") DataListDTO dto,
            @Param("dataLevel") Integer dataLevel,
            @Param("dataIds") List<Long> dataIds
    );

    /**
     * findByQxDy<BR>
     *
     * @param page          参数
     * @param dto           参数
     * @param user          参数
     * @param statusList    参数
     * @param dyList        参数
     * @param pcsList       参数
     * @param xzList        参数
     * @param areaShortCode 参数
     * @param warnConfig    处罚相关配置
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/20 18:33
     */
    IPage<DataListResult> selectData(
            Page<DataListVO> page,
            @Param("dto") DataListDTO dto,
            @Param("user") CurrentUser user,
            @Param("statusList") List<String> statusList,
            @Param("dyList") List<String> dyList,
            @Param("pcsList") List<String> pcsList,
            @Param("xzList") List<String> xzList,
            @Param("areaShortCode") String areaShortCode,
            @Param("warnConfig") WarnConfigVO warnConfig
    );

    /**
     * statisticReport<BR>
     *
     * @param type          参数
     * @param areaCode      参数
     * @param status        参数
     * @param startTime     参数
     * @param endTime       参数
     * @param warnConfig    处罚相关配置
     * @param waitedOperate 统计类型
     * @return 结果
     */
    default List<StatisticGroupVO> statisticReport(
            @Param("type") String type,
            @Param("areaCode") String areaCode,
            @Param("status") Integer status,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("warnConfig") WarnConfigVO warnConfig,
            @Param("waitedOperate") Integer waitedOperate
    ) {
        return statisticReport(type, areaCode, status, startTime, endTime, null, warnConfig, waitedOperate);
    }

    /**
     * statisticReport<BR>
     *
     * @param type          参数
     * @param areaCode      参数
     * @param status        参数
     * @param startTime     参数
     * @param endTime       参数
     * @param exportType    1:导出劝导书，2：导出告知书跟处罚书
     * @param warnConfig    处罚相关配置
     * @param waitedOperate 统计类型
     * @return 结果
     */
    default List<StatisticGroupVO> statisticReport(
            @Param("type") String type,
            @Param("areaCode") String areaCode,
            @Param("status") Integer status,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("exportType") Integer exportType,
            @Param("warnConfig") WarnConfigVO warnConfig,
            @Param("waitedOperate") Integer waitedOperate
    ) {
        return statisticReport(type, areaCode, status, startTime, endTime, exportType, null, warnConfig, waitedOperate);
    }

    /**
     * statisticReport<BR>
     *
     * @param type          参数
     * @param areaCode      参数
     * @param status        参数
     * @param startTime     参数
     * @param endTime       参数
     * @param exportType    1:导出劝导书，2：已填写处罚编号，3：即时下载违法告知书，4：即时反馈处罚结果 5：应处罚人数 6：处罚人数
     * @param overWfjsyz    是否超过违法阈值配置，true-是，false-否，null-不区分
     * @param warnConfig    处罚相关配置
     * @param waitedOperate 统计类型
     * @return 结果
     */
    List<StatisticGroupVO> statisticReport(
            @Param("type") String type,
            @Param("areaCode") String areaCode,
            @Param("status") Integer status,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("exportType") Integer exportType,
            @Param("overWfjsyz") Boolean overWfjsyz,
            @Param("warnConfig") WarnConfigVO warnConfig,
            @Param("waitedOperate") Integer waitedOperate
    );

    /**
     * statisticReport<BR>
     *
     * @param areaCode  地域编码
     * @param status    参数
     * @param startTime 参数
     * @param endTime   参数
     * @return 结果
     */
    List<TrafficJjwfjlEntity> getPcsSubStreetCode(@Param("areaCode") String areaCode,
                                                  @Param("status") Integer status,
                                                  @Param("startTime") String startTime,
                                                  @Param("endTime") String endTime);
}
