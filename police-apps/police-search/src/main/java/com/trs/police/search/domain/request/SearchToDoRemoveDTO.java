package com.trs.police.search.domain.request;

import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName ToDoRemoveDTO
 * @Description 移除代办参数
 * <AUTHOR>
 * @Date 2024/12/12 10:51
 **/
@Getter
@Setter
public class SearchToDoRemoveDTO extends BaseDTO{

    private List<Long> ids;

    private Boolean removeAll;

    @Override
    protected boolean checkParams() throws ServiceException {
        if (Objects.nonNull(getRemoveAll()) && getRemoveAll()) {
            return false;
        } else {
            if (CollectionUtils.isEmpty(getIds())) {
                throw new ServiceException("指定移除数据不能为空！");
            }
        }
        return false;
    }
}
