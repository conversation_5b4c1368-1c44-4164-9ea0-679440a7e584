package com.trs.police.search.traffic.police.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.search.traffic.police.entity.TrafficJjwfjlWfjsbEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 违法记录违法计数表mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TrafficJjwfjlWfjsbMapper extends BaseMapper<TrafficJjwfjlWfjsbEntity> {

    /**
     * 更新wfjs<BR>
     *
     * @param wfjsDataId 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/3 17:19
     */
    Integer updateWfjs(@Param("wfjsDataId") Long wfjsDataId);

    /**
     * countWfjs<BR>
     *
     * @param wfjsDataId 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/3 17:26
     */
    Integer countWfjs(@Param("wfjsDataId") Long wfjsDataId);
}
