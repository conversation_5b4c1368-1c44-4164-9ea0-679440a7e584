package com.trs.police.search.domain.request;

import com.alibaba.fastjson.JSON;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseListDTO;
import com.trs.common.utils.JsonUtils;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.utils.JsonUtil;
import lombok.Data;

import java.util.Collections;
import java.util.List;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * GetMyAramsDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/2/19 09:46
 * @since 1.0
 */
@Data
public class GetMyAramsDTO extends BaseListDTO {

    private String sortParams;

    private String sfzh;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL> 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getSfzh(), new ParamInvalidException("身份证号不能为空"));
        return true;
    }

    /**
     * makeSortParams<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/19 09:55
     */
    public List<SortParams> makeSortParams() {
        if (JsonUtils.isValidObject(getSortParams())) {
            return Collections.singletonList(JSON.parseObject(getSortParams(), SortParams.class));
        } else if (JsonUtils.isValidArray(getSortParams())) {
            return JsonUtil.parseArray(getSortParams(), SortParams.class);
        }
        return Collections.emptyList();
    }
}
