package com.trs.police.search.panorama.service.impl.parse.ai;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.vo.AiLabelVo;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;


/**
 * 车辆信息 - 相关信息/涉及案件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-18 18:27:03
 */
@Service
public class CarXiangGuanXinXiSheJiJingQingAiLabelParseServiceImpl extends SheJiJingQingAiLabelParseServiceImpl {

    @Override
    protected List<String> makeCenter(AiLabelVo label, List<AiLabelVo> labels) {
        String jjdBh = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("jjd_bh"))
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        final String jqlx = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("jqlx"))
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        final String jqlb = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("jqlb"))
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        String bjsj = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("bjsj"))
                .filter(TimeUtils::isValid)
                .map(it -> TimeUtils.stringToString(it, TimeUtils.YYYYMMDD))
                .orElse("");
        StringBuilder aj = new StringBuilder(StringUtils.showEmpty(jqlb, jqlx));
        if (StringUtils.isNotEmpty(bjsj)) {
            aj.append(StringUtils.SEPARATOR_OR).append(bjsj);
        }
        String relationObj = label.getRelationObj();
        return Arrays.asList(StringUtils.isNotEmpty(jjdBh) ? jjdBh : relationObj, aj.toString());
    }

    @Override
    public ArchivesEnum archivesEnum() {
        return ArchivesEnum.CAR;
    }
}
