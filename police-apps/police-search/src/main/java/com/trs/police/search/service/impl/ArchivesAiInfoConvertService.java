package com.trs.police.search.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.vo.AiInfoVo;
import com.trs.police.search.service.BaseAiInfoConvertService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/3/14 19:20
 * @since 1.0
 */
@Service
public class ArchivesAiInfoConvertService extends BaseAiInfoConvertService<JSONObject, ArchivesVO> {
    /**
     * convertToAiKnowledge<BR>
     *
     * @param aiInfoVo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 19:16
     */
    @Override
    public Optional<JSONObject> convertToAiKnowledge(AiInfoVo aiInfoVo) {
        return Optional.ofNullable(convertToReQueryData(aiInfoVo))
                .map(vo -> {
                    final ArchivesEnum instance = ArchivesEnum.getInstance(vo.getType());
                    if (Objects.isNull(instance) || instance == ArchivesEnum.ALL || instance == ArchivesEnum.BUSINESS) {
                        return null;
                    }
                    String desc = instance.getTableDesc();
                    var idField = StringUtils.getList(instance.getDetailField(), StringUtils.SEPARATOR_SEMICOLON)
                            .stream()
                            .findFirst()
                            .orElse(null);
                    final JSONObject json = new JSONObject();
                    json.put(instance.getDetailFieldDesc(), vo.getRecordId());
                    Optional.ofNullable(vo.getFields()).orElse(List.of())
                            .stream()
                            .filter(it -> !Objects.equals(idField, it.getFieldName()))
                            .filter(it -> Objects.nonNull(it.getValue()))
                            .forEach(it -> {
                                Object value = it.getValue();
                                if (Objects.equals(it.getFieldName(), "tags")) {
                                    final Set<String> tags = new HashSet<>(JsonUtils.parseList(
                                            JsonUtil.toJsonString(value),
                                            "$..tag_name",
                                            Object::toString
                                    ));
                                    if (!tags.isEmpty()) {
                                        json.put(it.getKey(), tags);
                                    }
                                } else {
                                    json.put(it.getKey(), value);
                                }
                            });
                    final String key = desc + (desc.contains("档案") ? "" : "档案");
                    return new JSONObject(Map.of(key, json));
                });
    }

    /**
     * convertToReQueryList<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/14 19:16
     */
    @Override
    public ArchivesVO convertToReQueryData(AiInfoVo vo) {
        return JsonUtil.parseObject(vo.getContent(), ArchivesVO.class);
    }

    @Override
    public String key() {
        return ArchivesVO.class.getName();
    }

    /**
     * convertToRedisString<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/17 10:29
     */
    @Override
    public String convertToRedisString(ArchivesVO vo) {
        return JsonUtil.toJsonString(vo);
    }

    /**
     * convertToRedisUniqueId<BR>
     *
     * @param vo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/17 10:30
     */
    @Override
    public String convertToRedisUniqueId(ArchivesVO vo) {
        return vo.makeUniqueId();
    }
}
