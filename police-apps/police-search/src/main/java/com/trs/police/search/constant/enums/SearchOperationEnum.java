package com.trs.police.search.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Objects;
import lombok.Getter;

/**
 * 搜索操作枚举
 *
 * <AUTHOR>
 */
public enum SearchOperationEnum {

    /**
     * 搜索
     */
    SEARCH(1, "搜索数据"),
    /**
     * 查看列表
     */
    VIEW_LIST(2, "浏览数据"),
    /**
     * 查看详情
     */
    VIEW_DETAIL(3, "点击查看"),
    /**
     * 导出
     */
    EXPORT(4, "导出数据");

    @Getter
    @EnumValue
    private final Integer code;

    @Getter
    @JsonValue
    private final String name;

    SearchOperationEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static SearchOperationEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (SearchOperationEnum typeEnum : SearchOperationEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
