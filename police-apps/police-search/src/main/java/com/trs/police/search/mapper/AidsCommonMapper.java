package com.trs.police.search.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/17 17:03
 * @since 1.0
 */
@Mapper
public interface AidsCommonMapper extends BaseMapper<Serializable> {

    /**
     * findPerson<BR>
     *
     * @param selectFields 参数
     * @param tableName    参数
     * @param recordId     参数
     * @param idField      参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/17 17:09
     */
    List<Map<String, Object>> findPerson(
            @Param("selectFields") String selectFields,
            @Param("tableName") String tableName,
            @Param("idField") String idField,
            @Param("recordId") String recordId
    );

    /**
     * getLastDataTime<BR>
     *
     * @param tableName 参数
     * @param fieldName 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/18 16:11
     */
    List<Map<String, Object>> getLastDataTime(
            @Param("tableName") String tableName,
            @Param("fieldName") String fieldName
    );
}
