package com.trs.police.search.controller;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.KeyTypeTreeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.permission.UnitTreeVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.search.domain.entity.SearchConfigEntity;
import com.trs.police.search.domain.vo.SearchSchemaListVO;
import com.trs.police.search.panorama.vo.TreeVO;
import com.trs.police.search.service.SearchBackstageService;
import com.trs.police.search.traffic.police.service.IDyPcsMappingService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/10 17:00
 */
@RestController
public class SearchBackstageController {

    @Resource
    private SearchBackstageService searchBackstageService;

    @Resource
    private IDyPcsMappingService iDyPcsMappingService;

    @Resource
    private PermissionService permissionService;

    /**
     * 元数据树形结构 https://yapi-192.trscd.com.cn/project/4974/interface/api/152619
     *
     * @return 树形结构
     */
    @GetMapping("/tree")
    List<KeyTypeTreeVO> getSearchTree() {
        return searchBackstageService.getSearchTree();
    }

    /**
     * 同步元数据 https://yapi-192.trscd.com.cn/project/4974/interface/api/152529
     */
    @GetMapping("/config/sync")
    void synchronizedSearchSchema() {
        searchBackstageService.synchronizedSearchSchema();
    }

    /**
     * 获取最后更新时间 https://yapi-192.trscd.com.cn/project/4974/interface/api/152538
     *
     * @return 时间
     */
    @GetMapping("/sync/time")
    String getSyncTime() {
        return searchBackstageService.getSyncTime();
    }

    /**
     * 获取最后更新时间 https://yapi-192.trscd.com.cn/project/4974/interface/api/152538
     *
     * @return 时间
     */
    @GetMapping("/catalog")
    List<KeyTypeTreeVO> getSearchCatalog() {
        return searchBackstageService.getSearchCatalog();
    }

    /**
     * 搜索配置列表 https://yapi-192.trscd.com.cn/project/4974/interface/api/152169
     *
     * @param request 请求参数
     * @return 时间
     */
    @PostMapping("/config/list")
    PageResult<SearchSchemaListVO> getSearchSchemaList(@RequestBody ListParamsRequest request) {
        return searchBackstageService.getSearchSchemaList(request);
    }

    /**
     * 字段配置列表 https://yapi-192.trscd.com.cn/project/4974/interface/api/152223
     *
     * @param request 请求参数
     * @param enName  表名
     * @return 时间
     */
    @PostMapping("/field/{enName}/config/list")
    List<SearchConfigEntity> getSearchSchemaFieldList(@PathVariable("enName") String enName,
        @RequestBody ListParamsRequest request) {
        return searchBackstageService.getSearchSchemaFieldList(enName, request);
    }

    /**
     * 更新字段配置 https://yapi-192.trscd.com.cn/project/4974/interface/api/152232
     *
     * @param entity 请求参数
     */
    @PutMapping("/field/config")
    void updateSearchField(@RequestBody SearchConfigEntity entity) {
        searchBackstageService.updateSearchField(entity);
    }

    /**
     * https://trsyapi.trscd.com.cn/project/534/interface/api/37228<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/11/2 22:13
     */
    @GetMapping(value = {
        "/getSchemaTypeTree",
        "/public/getSchemaTypeTree"
    })
    public RestfulResultsV2<TreeVO> getSchemaTypeTree() {
        return RestfulResultsV2.checkedBuild(() -> searchBackstageService.getSchemaTypeTree());
    }

    /**
     * synchronizedPcsMapping<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/26 21:58
     */
    @GetMapping(value = {
        "/config/sync/pcs",
        "/public/config/sync/pcs"
    })
    public void synchronizedPcsMapping() {
        iDyPcsMappingService.synchronizedMapping();
    }

    /**
     * xiaQuList<BR>
     *
     * @param level 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/20 15:20
     */
    @GetMapping("/public/config/xiaQuList")
    public RestfulResultsV2<UnitTreeVO> xiaQuList(Integer level) {
        return RestfulResultsV2.checkedBuild(() -> iDyPcsMappingService.xiaQuList(level));
    }
}
