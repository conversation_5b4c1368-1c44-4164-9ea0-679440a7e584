package com.trs.police.search.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.common.pojo.BaseVO;
import com.trs.common.utils.StringUtils;
import com.trs.db.sdk.pojo.RecordInfo;
import com.trs.police.search.converter.EntityConvert;
import com.trs.police.search.domain.entity.BfInfoEntity;
import com.trs.police.search.domain.entity.SearchConfigEntity;
import com.trs.police.search.domain.entity.SearchSchemaEntity;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.util.DataBaseUtils;
import io.vavr.Tuple2;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.trs.police.search.panorama.util.DataBaseUtils.putData;
import static com.trs.police.search.panorama.util.DataBaseUtils.putTimeData;
import static com.trs.police.search.panorama.util.RepositoryUtils.filterFieldByArchives;
import static com.trs.police.search.panorama.util.RepositoryUtils.filterFieldBySpecialNature;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/8/21 14:58
 * @since 1.0
 */
@Data
public class ChatMsgVo extends BaseVO {

    private String trsId;

    private String groupCreateBatId;

    private String msg;

    private String userIp;

    private String idNumber;

    private String trsSourceFrom;

    private String chatIp;

    private String groupName;

    private String groupCreateUid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date trsInTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date trsSourceTime;

    private String batId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date groupCreateTime;

    private String uid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    private String groupId;

    private String phone;

    private String name;

    private Integer isSender = 0;

    /**
     * of<BR>
     *
     * @param uid    参数
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/21 17:09
     */
    public static ChatMsgVo of(String uid, BfInfoEntity entity) {
        return Optional.ofNullable(entity)
                .map(EntityConvert.INSTANCE::entityToVo)
                .map(it -> {
                    it.setIsSender(Objects.equals(uid, it.getUid()) ? 1 : 0);
                    return it;
                }).orElse(null);
    }

    /**
     * of<BR>
     *
     * @param uid        参数
     * @param tableInfo  参数
     * @param recordInfo 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/21 15:23
     */
    public static ChatMsgVo of(String uid, Tuple2<SearchSchemaEntity, List<SearchConfigEntity>> tableInfo, RecordInfo recordInfo) {
        if (tableInfo == null || recordInfo == null || recordInfo.getFieldValueMap().isEmpty()) {
            return null;
        }
        final var map = DataBaseUtils.convertDbData(recordInfo.getFieldValueMap());
        final var groupIdField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_ID);
        final var groupNameField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_NAME);
        final var groupUserNameField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_USERNAME);
        final var groupMessageField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_MESSAGE);
        final var groupCreateUidField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_CREATE_UID);
        final var groupUserIpField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_USER_IP);
        final var groupChatIpField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_CHAT_IP);
        final var uIdField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_UID);
        final var trsInTimeField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_TRS_IN_TIME);
        final var trsSourceTimeField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_TRS_SOURCE_TIME);
        final var groupCreateTimeField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_CREATE_TIME);
        final var sendTimeField = filterFieldBySpecialNature(tableInfo._2, SearchConstant.SPECIAL_NATURE_GROUP_SEND_TIME);
        final var idCardField = filterFieldByArchives(tableInfo._2, ArchivesEnum.PERSON.getType());
        final var phoneField = filterFieldByArchives(tableInfo._2, ArchivesEnum.PHONE.getType());
        ChatMsgVo vo = new ChatMsgVo();
        Optional.ofNullable(map.get(tableInfo._1.getIdField())).ifPresent(vo::setTrsId);
        putData(map, groupMessageField, vo::setMsg);
        putData(map, groupUserIpField, vo::setUserIp);
        putData(map, idCardField, vo::setIdNumber);
        putData(map, groupChatIpField, vo::setChatIp);
        putData(map, groupNameField, vo::setGroupName);
        putData(map, groupCreateUidField, vo::setGroupCreateUid);
        putData(map, uIdField, it -> {
            vo.setUid(it);
            vo.setIsSender(Objects.equals(uid, it) ? 1 : 0);
        });
        putData(map, groupIdField, vo::setGroupId);
        putData(map, phoneField, vo::setPhone);
        putData(map, groupUserNameField, vo::setName);

        putData(map, trsInTimeField, it -> {

        });
        putTimeData(map, trsInTimeField, vo::setTrsInTime);
        putTimeData(map, trsSourceTimeField, vo::setTrsSourceTime);
        putTimeData(map, groupCreateTimeField, vo::setGroupCreateTime);
        putTimeData(map, sendTimeField, vo::setSendTime);
        return vo;
    }

    /**
     * 展示名称<BR>
     * 后端按照下列优先级顺序获取值<BR>
     * name->phone->idNumber->uid<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/21 15:04
     */
    public String getShowName() {
        if (StringUtils.isNotEmpty(getName())) {
            return getName();
        }
        if (StringUtils.isNotEmpty(getPhone())) {
            return getPhone();
        }
        if (StringUtils.isNotEmpty(getIdNumber())) {
            return getIdNumber();
        }
        return getUid();
    }

    /**
     * 是否是群主<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/21 17:10
     */
    public Boolean getIsGroupOwner() {
        return Objects.equals(getUid(), getGroupCreateUid());
    }
}
