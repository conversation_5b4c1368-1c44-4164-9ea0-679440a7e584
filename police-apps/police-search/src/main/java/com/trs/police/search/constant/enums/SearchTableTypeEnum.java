package com.trs.police.search.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 区域状态
 *
 * <AUTHOR>
 */
public enum SearchTableTypeEnum {
    /**
     * 草稿
     */
    DRAFT(1, "原始库"),
    /**
     * 待审批
     */
    PENDING(2, "主题库"),
    /**
     * 审批中
     */
    APPROVING(3, "资源库");

    @Getter
    @EnumValue
    private final Integer code;
    @JsonValue
    @Getter
    private final String name;

    SearchTableTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
