package com.trs.police.search.panorama.service.impl.parse.ai;

import com.trs.common.utils.StringUtils;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.service.BaseAiLabelParseService;
import com.trs.police.search.panorama.vo.AiLabelVo;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/2/27 10:48
 * @since 1.0
 */
@Service
public class JiaTingChengYuanAiLabelParseServiceImpl extends BaseAiLabelParseService {

    /**
     * makeTopRight<BR>
     *
     * @param label  参数
     * @param labels 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/27 10:30
     */
    @Override
    protected List<String> makeTopRight(AiLabelVo label, List<AiLabelVo> labels) {
        if (StringUtils.isEmpty(label.getRelation())) {
            return Collections.emptyList();
        }
        return Collections.singletonList(label.getRelation());
    }

    /**
     * makeCenter<BR>
     *
     * @param label  参数
     * @param labels 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/27 10:30
     */
    @Override
    protected List<String> makeCenter(AiLabelVo label, List<AiLabelVo> labels) {
        String name = Optional.ofNullable(label.getExt())
            .map(it -> it.getString("xm"))
            .filter(StringUtils::isNotEmpty)
            .orElse("");
        String relationObj = label.getRelationObj();
        return Arrays.asList(name, relationObj);
    }

    @Override
    public String relationCatalog() {
        return SearchConstant.AI_LABEL_CATALOG_JTCY;
    }

    /**
     * archivesEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 14:41
     */
    @Override
    public ArchivesEnum archivesEnum() {
        return ArchivesEnum.PERSON;
    }
}
