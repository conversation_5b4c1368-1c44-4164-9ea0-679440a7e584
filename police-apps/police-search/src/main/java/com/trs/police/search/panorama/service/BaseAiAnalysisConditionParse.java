package com.trs.police.search.panorama.service;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.db.sdk.exception.TrsCrudException;
import com.trs.db.sdk.pojo.RecordInfo;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.search.AiAnalysisVo;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.manager.IAiInfoMgr;
import com.trs.police.search.panorama.manager.impl.ArchivesListSceneManager;
import com.trs.police.search.panorama.util.ArchivesUtils;
import com.trs.police.search.panorama.util.ExpressionHelper;
import com.trs.police.search.service.BaseAiInfoConvertService;
import com.trs.web.builder.util.KeyMgrFactory;
import com.trs.web.entity.PageInfo;
import com.trs.web.entity.PageList;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.Tuple3;
import io.vavr.control.Either;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/12/26 21:02
 * @since 1.0
 */
@Getter
@Slf4j
public abstract class BaseAiAnalysisConditionParse implements IAiAnalysisConditionParse {

    @Resource
    private ISearchFoundationService searchService;

    @Resource
    private IAiInfoMgr aiInfoMgr;

    @Override
    public Tuple3<Expression, Long, String> parseCondition(BaseArchivesSearchDTO dto, AiAnalysisVo analysisVo) {
        if (!checkIsOk(analysisVo)) {
            log.warn("解析意图[{}]失败，item[{}]不满足需求", desc(), analysisVo);
            return Tuple.of(null, 0L, "命中0条档案数据。  ");
        }
        Tuple2<Expression, Long> tuple2 = doParseCondition(dto, analysisVo);
        tuple2 = tuple2.update2(findData(dto, analysisVo, tuple2));
        return Tuple.of(
                tuple2._1,
                tuple2._2,
                makeSearchMessage(dto, analysisVo, tuple2)
        );
    }

    /**
     * makeSearchMessage<BR>
     *
     * @param dto        参数
     * @param analysisVo 参数
     * @param tuple2     参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/17 14:28
     */
    public String makeSearchMessage(BaseArchivesSearchDTO dto, AiAnalysisVo analysisVo, Tuple2<Expression, Long> tuple2) {
        return String.format("命中%s条档案数据。  ", tuple2._2);
    }

    /**
     * findData<BR>
     *
     * @param dto        参数
     * @param analysisVo 参数
     * @param tuple2     参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/10 17:06
     */
    public Long findData(BaseArchivesSearchDTO dto, AiAnalysisVo analysisVo, Tuple2<Expression, Long> tuple2) {
        if (!needFindData()) {
            return tuple2._2;
        }
        String key = analysisVo.makeKey();
        final Long result;
        if (Objects.nonNull(tuple2._1)) {
            if (getAiInfoMgr().useAiAnalysisCondition(dto.getUuid(), key)) {
                log.warn("解析意图[{}]失败，item[{}]已经被解析过了", desc(), analysisVo);
                final String info = getAiInfoMgr().findAiAnalysisCondition(dto.getUuid(), key);
                if (StringUtils.isLong(info)) {
                    result = Long.parseLong(info);
                } else {
                    result = tuple2._2;
                }
            } else {
                result = Try.of(() -> {
                            final String tableName = ArchivesUtils.getTableNameByType(dto.getArchivesType());
                            Either<TrsCrudException, PageList<RecordInfo>> either = getSearchService()
                                    .makeRepository()
                                    .findPageList(tableName, tuple2._1, PageInfo.newPage(1, findDataPageSize(dto, analysisVo)));
                            ArchivesListSceneManager listSceneManager = BeanUtil.getBean(ArchivesListSceneManager.class);
                            BaseAiInfoConvertService parse = BaseAiInfoConvertService.findByClass(ArchivesVO.class)
                                    .orElseThrow(() -> new ServiceException("未找到解析器"));
                            final var data = Optional.ofNullable(listSceneManager.convertData(
                                            Tuple.of(either.getOrElseThrow(e -> new ServiceException(e)), "", 0L),
                                            dto
                                    )).map(PageList::getContents)
                                    .map(i -> parse.convertToRedisRawData(i))
                                    .orElseGet(List::of);
                            getAiInfoMgr().putInfoWithMerge(
                                    SearchConstant.AI_ANALYSIS_FOR_LIST,
                                    SearchConstant.AI_ANALYSIS_DATA_BASE_INFO,
                                    dto.getUuid(),
                                    data
                            );
                            return (long) data.size();
                        }).onFailure(e -> log.warn("解析意图[{}]失败，item[{}]塞入数据出错", desc(), analysisVo, e))
                        .getOrElse(tuple2._2);
            }
        } else {
            result = tuple2._2;
        }
        getAiInfoMgr().markUseAiAnalysisCondition(dto.getUuid(), key, Objects.toString(result));
        return result;
    }

    /**
     * findByKey<BR>
     *
     * @param key 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/26 21:02
     */
    public static Optional<BaseAiAnalysisConditionParse> findByKey(String key) {
        return Optional.ofNullable(
                Try.of(() -> KeyMgrFactory.findMgrByKey(BaseAiAnalysisConditionParse.class, key)).getOrNull()
        );
    }


    /**
     * makeRecordIdsExpression<BR>
     *
     * @param recordIds 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/2/27 18:03
     */
    protected Expression makeRecordIdsExpression(Collection<String> recordIds) {
        return ExpressionHelper.makeKeywordExpression(
                ArchivesConstants.ARCHIVES_TYPE_ALL,
                String.join(String.format(" %s ", SearchConstant.OR), recordIds)
        );
    }
}
