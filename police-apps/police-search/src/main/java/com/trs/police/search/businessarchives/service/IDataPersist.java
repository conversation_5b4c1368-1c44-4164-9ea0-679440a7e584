package com.trs.police.search.businessarchives.service;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.vo.message.BusinessArchivesMessageVO;
import com.trs.police.search.businessarchives.vo.DbInfoConfigWrapper;
import com.trs.web.builder.base.IKey;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.control.Either;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 执行数据持久化
 * *@author:wen.wen
 * *@create 2024-05-07 19:57
 **/
public interface IDataPersist extends IKey {

    /**
     * 进行数据持久化
     *
     * @param messageVOList 需要进行持久化的结构体
     * @param dbInfoConfig  dbInfoConfig
     * @throws Throwable Throwable
     */
    void insert(DbInfoConfigWrapper dbInfoConfig, List<BusinessArchivesMessageVO> messageVOList) throws Throwable;

    /**
     * 更新操作
     *
     * @param messageVOList 需要进行持久化的结构体
     * @param dbInfoConfig  dbInfoConfig
     * @throws Throwable Throwable
     */
    void update(DbInfoConfigWrapper dbInfoConfig, List<BusinessArchivesMessageVO> messageVOList) throws Throwable;


    /**
     * 更新操作
     *
     * @param messageVOList 需要进行持久化的结构体
     * @param dbInfoConfig dbInfoConfig
     * @throws Throwable Throwable
     */
    void delete(DbInfoConfigWrapper dbInfoConfig, List<BusinessArchivesMessageVO> messageVOList) throws Throwable;

    /**
     * 持久化方式 ，目前有两种：
     * 1、直接保存进数据库
     * 2、发送到mq后，后续可能会对接到数据中台，目前不做实现
     *
     * @return {@link String}
     */
    String way();

    /**
     * @return {@link String}
     */
    default String key() {
        return way();
    }

    /**
     * 通过持久化方式获取对应的持久化实现类
     *
     * @param way 持久化方式
     * @return {@link IDataPersist}
     */
    static IDataPersist getDataPersistByWay(String way) {
        PreConditionCheck.checkArgument(!StringUtils.isNullOrEmpty(way), "持久化方式名称不能为空!");
        Either<Throwable, Map<String, IDataPersist>> either = BeanFactoryHolder.getBeanOfType(IDataPersist.class);
        if (either.isRight()) {
            for (Map.Entry<String, IDataPersist> entry : either.get().entrySet()) {
                IDataPersist dataPersist = entry.getValue();
                if (Objects.equals(entry.getValue().way(), way)) {
                    return dataPersist;
                }
            }
            throw new RuntimeException(String.format("为找到持久化方式为[%s]的实现类", way));
        }

        throw new RuntimeException(either.getLeft());
    }
}
