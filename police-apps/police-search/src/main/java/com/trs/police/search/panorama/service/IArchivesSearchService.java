package com.trs.police.search.panorama.service;

import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.common.openfeign.starter.DTO.FindPersonByOtherArchivesDTO;
import com.trs.police.search.panorama.vo.JudgeRegistrationVO;
import com.trs.web.builder.base.RestfulResultsV2;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IArchivesSearchService {

    /**
     * 获取档案tab列表
     *
     * @return tab列表
     */
    List<String> tabs();

    /**
     * excel导出
     *
     * @param response 响应流
     * @param dto      相关参数
     * @throws ServiceException 相关异常
     */
    void export(HttpServletResponse response, BaseArchivesSearchDTO dto) throws ServiceException;

    /**
     * 判断是否需要进行登记备案
     *
     * @param dto 请求参数
     * @return 结果
     * @throws ServiceException 相关异常
     */
    List<JudgeRegistrationVO> judgeRegistration(BaseArchivesSearchDTO dto) throws ServiceException;

    /**
     * 登记备案
     *
     * @param dto     请求参数
     * @param content 备案内容
     * @return 结果
     * @throws ServiceException 相关异常
     */
    String registration(BaseArchivesSearchDTO dto, String content) throws ServiceException;

    /**
     * findPersonByOtherArchives<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/27 11:20
     */
    ArchivesVO findPersonByOtherArchives(FindPersonByOtherArchivesDTO dto) throws ServiceException;

    /**
     * findOtherArchivesByPerson<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/11/28 16:57
     */
    RestfulResultsV2<ArchivesVO> findOtherArchivesByPerson(FindPersonByOtherArchivesDTO dto) throws ServiceException;

}
