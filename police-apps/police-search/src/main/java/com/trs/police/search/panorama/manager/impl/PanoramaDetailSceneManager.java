package com.trs.police.search.panorama.manager.impl;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.db.sdk.pojo.RecordInfo;
import com.trs.police.search.domain.entity.SearchConfigEntity;
import com.trs.police.search.domain.entity.SearchSchemaEntity;
import com.trs.police.search.mapper.SearchConfigMapper;
import com.trs.police.search.mapper.SearchSchemaMapper;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.dto.PanoramaSearchDTO;
import com.trs.police.search.panorama.manager.BaseDetailSceneManager;
import com.trs.police.search.panorama.service.ISearchFoundationService;
import com.trs.police.search.panorama.util.PanoramaUtils;
import com.trs.police.search.panorama.vo.PanoramaVO;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.police.search.panorama.util.PanoramaUtils.checkIsOverview;

/**
 * @ClassName PanoramaDetailSceneManager
 * @Description 全景检索详情支持
 * <AUTHOR>
 * @Date 2023/9/27 19:33
 **/
@Component
public class PanoramaDetailSceneManager extends BaseDetailSceneManager<PanoramaSearchDTO, PanoramaVO> {

    @Resource
    private ISearchFoundationService searchService;

    @Resource
    private SearchConfigMapper searchConfigMapper;

    @Resource
    private SearchSchemaMapper searchSchemaMapper;

    @Override
    public Optional<PanoramaVO> findById(String recordId) throws ServiceException {
        throw new ServiceException("暂不支持");
    }

    @Override
    public Optional<PanoramaVO> findByDTO(PanoramaSearchDTO dto) throws ServiceException {
        final String idField = Optional.ofNullable(searchSchemaMapper.getByEnName(dto.getSchemaName()))
                .map(SearchSchemaEntity::getIdField)
                .filter(StringUtils::isNotEmpty)
                .orElse(SearchConstant.ES_DEFAULT_ID);
        final RecordInfo singleResult = searchService.searchPanoramaDetail(
                dto.getSchemaName(),
                StringUtils.showEmpty(dto.getFieldName(), idField),
                dto.getRecordId()
        );
        if (singleResult == null || CollectionUtils.isEmpty(singleResult.getFieldValueMap())) {
            return Optional.empty();
        }
        final List<SearchConfigEntity> configFields = searchConfigMapper.getByTableEnName(dto.getSchemaName());
        if (CollectionUtils.isEmpty(configFields)) {
            return Optional.empty();
        }
        final List<SearchConfigEntity> entities = configFields.stream()
                .filter(it -> checkIsOverview(it, dto.getReturnFields(), Optional.ofNullable(dto.getOverview()).orElse(1)))
                .collect(Collectors.toList());
        PanoramaVO vo = new PanoramaVO();
        final SearchSchemaEntity schema = searchSchemaMapper.getByEnName(dto.getSchemaName());
        vo.setSpecial(StringUtils.getList(schema.getSpecialNatures(), true));
        vo.setFields(PanoramaUtils.buildFieldResult(singleResult.getFieldValueMap(), entities));
        vo.setIdField(StringUtils.showEmpty(dto.getFieldName(), idField));
        vo.setRecordId(dto.getRecordId());
        vo.setSchemaName(dto.getSchemaName());
        return Optional.of(vo);
    }

}
