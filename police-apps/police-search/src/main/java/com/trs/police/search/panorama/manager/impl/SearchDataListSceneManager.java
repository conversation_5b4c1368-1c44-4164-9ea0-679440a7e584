package com.trs.police.search.panorama.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.search.converter.EntityConvert;
import com.trs.police.search.domain.entity.DataSearchAuthorizationInfoEntity;
import com.trs.police.search.domain.vo.DataSearchAuthorizationInfoVO;
import com.trs.police.search.mapper.DataSearchAuthorizationInfoMapper;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.context.SceneContext;
import com.trs.police.search.panorama.dto.SearchDataDTO;
import com.trs.police.search.panorama.manager.BaseListSceneManager;
import com.trs.police.search.panorama.vo.ExtPageList;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * SearchDataListSceneManager
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/10/30 14:44
 * @since 1.0
 */
@Component
@AllArgsConstructor
public class SearchDataListSceneManager extends BaseListSceneManager<SearchDataDTO, DataSearchAuthorizationInfoVO> {

    private DataSearchAuthorizationInfoMapper mapper;

    @Resource
    private PermissionService permissionService;

    /**
     * checkDTO<BR>
     *
     * @param dto 参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/9/27 18:44
     */
    @Override
    public void checkDTO(SearchDataDTO dto) throws ServiceException {
        super.checkDTO(dto);
        CurrentUser currentUser = permissionService.getCurrentUserWithRole();
        if (currentUser == null) {
            throw new ServiceException("用户未登录");
        }
        if (CollectionUtils.isEmpty(currentUser.getOperations())) {
            throw new ServiceException("用户没有对应的操作权限");
        }
        if (currentUser
                .getOperations()
                .stream()
                .noneMatch(item -> item.startsWith(SearchConstant.RIGHT_SEARCH_DATA))) {
            throw new ServiceException("用户没有对应的操作权限");
        }
    }

    /**
     * 去调用各自渠道的筛选条件
     * 由于是 不需要校验,不同的渠道，且查询方式也不一样，暂无公共部分抽取<BR>
     *
     * @param sceneContext 相关请求的上下文
     * @return 相关数据
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2021/5/13 18:05
     */
    @Override
    protected ExtPageList<DataSearchAuthorizationInfoVO> queryData(SceneContext<SearchDataDTO> sceneContext) throws ServiceException {
        SearchDataDTO dto = sceneContext.getSearchDTO();
        QueryWrapper<DataSearchAuthorizationInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_id", dto.getDataId());
        queryWrapper.eq("auth_obj_type", dto.getAuthObjType());
        List<DataSearchAuthorizationInfoVO> list = mapper.selectList(queryWrapper)
                .stream()
                .map(EntityConvert.INSTANCE::entityToVo)
                .collect(Collectors.toList());
        Integer size = list.size();
        return getDataPageList(list, 1, size, (long) size, 0L);
    }
}
