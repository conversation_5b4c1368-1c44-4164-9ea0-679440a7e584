package com.trs.police.search.panorama.service;

import com.trs.common.exception.ServiceException;
import com.trs.police.search.panorama.dto.HitRuleDetailDTO;
import com.trs.police.search.panorama.vo.LabelHitRuleDetailVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ILabelService {

    /**
     * hitRuleDetail<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 10:47
     */
    List<LabelHitRuleDetailVo> hitRuleDetail(HitRuleDetailDTO dto) throws ServiceException;
}
