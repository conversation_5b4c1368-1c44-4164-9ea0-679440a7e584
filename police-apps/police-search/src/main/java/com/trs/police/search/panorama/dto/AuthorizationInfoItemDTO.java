package com.trs.police.search.panorama.dto;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.search.panorama.constant.SearchConstant;
import lombok.Data;

import static com.trs.common.base.PreConditionCheck.*;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * AuthorizationInfoItemDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/10/30 15:22
 * @since 1.0
 */
@Data
public class AuthorizationInfoItemDTO extends SearchDataObjDTO {

    /**
     * 权限位
     */
    private String authKey;

    /**
     * 激活状态
     * 0:禁止
     * 1:允许
     */
    private Integer status;

    /**
     * of<BR>
     *
     * @param objType 参数
     * @param objId   参数
     * @param authKey 参数
     * @param status  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/3/12 12:25
     */
    public static AuthorizationInfoItemDTO of(String objType, String objId, String authKey, Integer status) {
        AuthorizationInfoItemDTO item = new AuthorizationInfoItemDTO();
        item.setObjType(objType);
        item.setObjId(objId);
        item.setAuthKey(authKey);
        item.setStatus(status);
        return item;
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        checkArgument(super.checkParams(), new ParamInvalidException("参数异常"));
        checkNotEmpty(getAuthKey(), new ParamInvalidException("权限位不能为空"));
        checkNotNull(getStatus(), new ParamInvalidException("激活状态不能为空"));
        checkArgument(
                SearchConstant.OBJ_TYPES.contains(getObjType()),
                new ParamInvalidException("非法对象类型[" + getObjType() + "]")
        );
        checkArgument(
                SearchConstant.RIGHTS.contains(getAuthKey()),
                new ParamInvalidException("非法权限位[" + getAuthKey() + "]")
        );
        return true;
    }
}
