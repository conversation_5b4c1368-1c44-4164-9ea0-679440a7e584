package com.trs.police.search.domain.request;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;
import static com.trs.common.base.PreConditionCheck.checkNotNull;

import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * RecentScreenshotsDTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/3/27 21:22
 * @since 1.0
 */
@Data
public class RecentScreenshotsDTO extends BaseDTO {

    private String archivesType;

    private String recordId;

    private Integer topN = 10;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2020-09-10 15:49
     */
    @Override
    protected boolean checkParams() throws ServiceException {
        checkNotEmpty(getArchivesType(), new ParamInvalidException("档案类型不能为空"));
        checkNotEmpty(getRecordId(), new ParamInvalidException("recordId不能为空"));
        checkNotNull(ArchivesEnum.getInstance(getArchivesType()), new ParamInvalidException("档案类型错误"));
        return true;
    }
}
