package com.trs.police.search.controller;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.openfeign.starter.vo.RenWuJiZhenFuNengVo;
import com.trs.police.search.domain.request.SearchLogRequest;
import com.trs.police.search.domain.vo.ListFilterVO;
import com.trs.police.search.domain.vo.LogDetailVO;
import com.trs.police.search.domain.vo.LogListVO;
import com.trs.police.search.domain.vo.SearchDetailVO;
import com.trs.police.search.domain.vo.SearchListVO;
import com.trs.police.search.service.LogService;
import com.trs.police.search.service.SearchService;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 搜索controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping()
public class SearchController {

    @Resource
    private SearchService searchService;
    @Resource
    private LogService logService;


    /**
     * 搜索列表 https://yapi-192.trscd.com.cn/project/4974/interface/api/152151
     *
     * @param enName  表名
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/{enName}/list")
    public PageResult<SearchListVO> getSearchList(@PathVariable("enName") String enName,
        @RequestBody ListParamsRequest request) {
        return searchService.getSearchList(enName, request, true);
    }

    /**
     * 搜索详情 https://yapi-192.trscd.com.cn/project/4974/interface/api/152502
     *
     * @param enName   表名
     * @param recordId 记录id
     * @return 结果
     */
    @PostMapping("/{enName}/{recordId}/detail")
    public List<SearchDetailVO> getSearchDetail(@PathVariable("enName") String enName,
        @PathVariable("recordId") String recordId) {
        return searchService.getSearchDetail(enName, recordId);
    }

    /**
     * 查询筛选条件 https://yapi-192.trscd.com.cn/project/4974/interface/api/152124
     *
     * @param enName  表名
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/{enName}/filters")
    public List<ListFilterVO> getSearchFilters(@PathVariable("enName") String enName,
        @RequestBody ListParamsRequest request) {
        return searchService.getSearchFilters(enName, request);
    }

    /**
     * 导出查询结果
     *
     * @param enName   表名
     * @param request  查询参数
     * @param response HttpServletResponse
     */
    @PostMapping("/{enName}/export")
    public void exportList(@PathVariable("enName") String enName, @RequestBody ListParamsRequest request,
        HttpServletResponse response) {
        try {
            searchService.exportList(enName, request, response);
        } catch (IOException e) {
            log.error("列表导出错误：", e);
            throw new TRSException("检索导出失败！");
        }
    }

    /**
     * 生成检索uuid https://yapi-192.trscd.com.cn/project/4974/interface/api/152511
     *
     * @param request 检索参数
     * @return uuid
     */
    @PostMapping("/uuid")
    public String getSearchUuid(@RequestBody ListParamsRequest request) {
        return logService.getSearchUuid(request);
    }

    /**
     * 生成检索记录 https://yapi-192.trscd.com.cn/project/4974/interface/api/152520
     *
     * @param request 参数
     */
    @PostMapping("/log")
    public void createSearchLog(@RequestBody SearchLogRequest request) {
        logService.createSearchLog(request);
    }

    /**
     * 查询日志列表 https://yapi-192.trscd.com.cn/project/4974/interface/api/152250
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/log/list")
    public PageResult<LogListVO> getLogPage(@RequestBody ListParamsRequest request) {
        return logService.getLogPage(request);
    }

    /**
     * 查询日志详情 https://yapi-192.trscd.com.cn/project/4974/interface/api/152295
     *
     * @param id 日志id
     * @return 结果
     */
    @GetMapping("/log/detail")
    public LogDetailVO getLogDetail(@RequestParam("id") Long id) {
        return logService.getLogDetail(id);
    }

    /**
     * 获取人员技侦数据
     *
     * @param idCard 身份证号
     * @param phone 手机号
     * @return 结果
     */
    @GetMapping("/public/getJiZhenByPerson")
    public String getJiZhenByPerson(@RequestParam("idCard") String idCard,
                                    @RequestParam(value = "phone", required = false) String phone) {
        JSONObject object = new JSONObject();
        try {
            RenWuJiZhenFuNengVo jz = searchService.getJiZhenByPerson(idCard, phone);
            object.put("isSuccess", true);
            object.put("data", jz);
        } catch (ServiceException e) {
            object.put("isSuccess", false);
            object.put("data", e.getMessage());
            log.error("获取人员技侦数据失败：", e);
            return object.toJSONString();
        }
        return object.toJSONString();
    }

    /**
     * 获取所有技侦数据中的特征值（计算匹配度）
     *
     * @return 结果
     */
    @GetMapping("/public/getAllTzz")
    public Map<String, List<String>> getAllTzz() {
        return searchService.getAllTzz();
    }

    /**
     * 警情分析（ai训练）
     *
     * @param file 样本
     * @return 结果
     */
    @PostMapping("/public/policeSituationAnalysis")
    public RestfulResultsV2 policeSituationAnalysis(@RequestParam("file") MultipartFile file) {
        return Try.of(() -> searchService.policeSituationAnalysis(file))
                .onFailure(e -> log.error("警情分析出错", e))
                .getOrElseGet(e -> RestfulResultsV2.error("警情分析出错：" + e.getMessage()));
    }
}
