package com.trs.police.search.panorama.service.impl.parse.ai;

import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.service.BaseAiLabelParseService;
import com.trs.police.search.panorama.vo.AiLabelVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/2/27 10:48
 * @since 1.0
 */
@Service
public class BuKongQingKuangAiLabelParseServiceImpl extends BaseAiLabelParseService {

    /**
     * makeCenter<BR>
     *
     * @param label  参数
     * @param labels 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/27 10:30
     */
    @Override
    protected List<String> makeCenter(AiLabelVo label, List<AiLabelVo> labels) {
        String dw = Optional.ofNullable(label.getExt())
                .map(it -> it.getString("bkdw"))
                .filter(StringUtils::isNotEmpty)
                .orElse("");
        List<String> line2 = new ArrayList<>(2);
        Optional.ofNullable(label.getExt())
                .map(it -> it.getString("sqrxm"))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(line2::add);
        Optional.ofNullable(label.getExt())
                .map(it -> it.getString("bksj"))
                .filter(TimeUtils::isValid)
                .map(it -> TimeUtils.stringToString(it, TimeUtils.YYYYMMDD))
                .ifPresent(line2::add);
        return Arrays.asList(dw, String.join("|", line2));
    }

    @Override
    public String relationCatalog() {
        return SearchConstant.AI_LABEL_CATALOG_BKQK;
    }

    /**
     * archivesEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/30 14:41
     */
    @Override
    public ArchivesEnum archivesEnum() {
        return ArchivesEnum.PERSON;
    }
}
