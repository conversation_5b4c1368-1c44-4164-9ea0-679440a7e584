package com.trs.police.search.panorama.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.core.vo.search.ContentParseVo;
import com.trs.police.common.core.vo.search.KeyValueTypeVoForSearch;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.common.openfeign.starter.DTO.FindPersonByOtherArchivesDTO;
import com.trs.police.common.openfeign.starter.DTO.SearchLogDTO;
import com.trs.police.search.config.SearchThreadPoolConfigV2;
import com.trs.police.search.converter.EntityConvert;
import com.trs.police.search.domain.entity.CaseSuspectEntity;
import com.trs.police.search.domain.entity.SearchRegistrationEntity;
import com.trs.police.search.domain.vo.CaseSuspectVO;
import com.trs.police.search.domain.vo.KeyAndDescVO;
import com.trs.police.search.mapper.CaseSuspectMapper;
import com.trs.police.search.mapper.SearchRegistrationMapper;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.constant.enums.OperateTypeEnum;
import com.trs.police.search.panorama.dto.PersonArchivesSearchDTO;
import com.trs.police.search.panorama.manager.BaseSceneService;
import com.trs.police.search.panorama.manager.IAiInfoMgr;
import com.trs.police.search.panorama.manager.impl.ArchivesDetailSceneManager;
import com.trs.police.search.panorama.manager.impl.ArchivesListSceneManager;
import com.trs.police.search.panorama.manager.impl.ArchivesStatisticsSceneManager;
import com.trs.police.search.panorama.service.*;
import com.trs.police.search.panorama.util.AiUtils;
import com.trs.police.search.panorama.util.ArchivesUtils;
import com.trs.police.search.panorama.util.LabelUtils;
import com.trs.police.search.panorama.util.SearchLogNewUtils;
import com.trs.police.search.panorama.vo.AiInfoVo;
import com.trs.police.search.panorama.vo.ExtPageList;
import com.trs.police.search.panorama.vo.JudgeRegistrationVO;
import com.trs.police.search.panorama.vo.SearchDataObjVO;
import com.trs.police.search.properties.SearchWarningSubscribeProperties;
import com.trs.police.search.service.BaseAiInfoConvertService;
import com.trs.web.builder.base.IKey;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.KeyMgrFactory;
import io.vavr.Tuple3;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.trs.common.base.PreConditionCheck.checkNotEmpty;

/**
 * @ClassName ArchivesServiceImpl
 * @Description 档案相关业务处理类
 * <AUTHOR>
 * @Date 2023/9/27 17:22
 **/
@Slf4j
@Service
@DependsOn("beanFactoryHolder")
public class ArchivesSearchServiceImpl
        extends BaseSceneService<BaseArchivesSearchDTO, ArchivesVO, KeyAndDescVO, ArchivesVO>
        implements IArchivesSearchService {

    @Resource
    private SearchWarningSubscribeProperties properties;

    @Resource
    private SearchRegistrationMapper searchRegistrationMapper;

    @Resource
    private CaseSuspectMapper caseSuspectMapper;

    @Resource
    private ISearchLogNewService searchLogNewService;

    @Resource
    private ISearchDataService searchDataService;

    @Resource
    private IAiInfoMgr iAiInfoMgr;

    private List<BaseMultiPlatformSearchService> searchServices = new ArrayList<>(0);

    public ArchivesSearchServiceImpl(
            ArchivesListSceneManager listSceneManager,
            ArchivesDetailSceneManager detailSceneManager,
            ArchivesStatisticsSceneManager statisticsSceneManager
    ) {
        super(detailSceneManager, listSceneManager, statisticsSceneManager);
    }

    /**
     * init<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/2/19 17:31
     */
    @PostConstruct
    public void init() {
        log.info("开始初始化");
        try {
            searchServices = KeyMgrFactory.getMgrs(
                    BaseMultiPlatformSearchService.class,
                    mgr -> {
                        BaseMultiPlatformSearchService it = (BaseMultiPlatformSearchService) mgr;
                        PreConditionCheck.checkArgument(
                                it.order() != null && it.order() >= 0,
                                "[" + it.desc() + "]实现类[" + it.key() + "]的优先级取值错误"
                        );
                        return true;
                    }
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 检查是否有优先级重复的情况
        searchServices
                .stream()
                .collect(Collectors.groupingBy(IMultiPlatformSearchService::order))
                .forEach((key, value) -> {
                    if (value.size() > 1) {
                        String msg = String.format(
                                "[%s]实现类的优先级重复，都为[%s]",
                                value
                                        .stream()
                                        .map(IKey::desc)
                                        .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA)),
                                key
                        );
                        log.error(msg);
                        throw new RuntimeException(msg);
                    }
                });
        log.info("完成初始化");
    }

    /**
     * makeMultiPlatformSearchService<BR>
     *
     * @param archiveType 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 14:06
     */
    public Optional<BaseMultiPlatformSearchService> makeMultiPlatformSearchService(String archiveType) {
        List<BaseMultiPlatformSearchService> list = searchServices
                .stream()
                // 清空后续处理器，避免出现异常
                .peek(it -> it.setNext(null))
                .filter(it -> it.supportArchiveType(archiveType))
                .collect(Collectors.toList());
        int len = list.size();
        log.info("根据[{}]查到了[{}]个处理类", archiveType, len);
        if (len > 0) {
            list.sort(Comparator.comparing(IMultiPlatformSearchService::order));
            for (int i = 1; i < len; i++) {
                list.get(i - 1).setNext(list.get(i));
            }
            return Optional.ofNullable(list.get(0));
        }
        return Optional.empty();
    }

    @Override
    public ArchivesVO detail(BaseArchivesSearchDTO dto) throws ServiceException {
        if (!dto.getSkipFlushOnDetail()) {
            CompletableFuture.runAsync(() -> {
                try {
                    asyncFlushDataOnDetail(dto);
                } catch (ServiceException e) {
                    log.error("档案详情：异步刷新详情数据失败！", e);
                    throw new RuntimeException(e);
                }
            }, SearchThreadPoolConfigV2.t);
        }
        if (StringUtils.isNotEmpty(dto.getUuid())) {
            iAiInfoMgr.clearInfo(dto.getUuid(), SearchConstant.AI_ANALYSIS_FOR_DETAIL, SearchConstant.AI);
        }
        ArchivesVO vo = super.detail(dto);
        BaseAiInfoConvertService.findByClass(ArchivesVO.class)
                .map(parse -> parse.convertToRedisRawData(vo))
                .filter(i -> StringUtils.isNotEmpty(dto.getUuid()))
                .ifPresent(info -> {
                    iAiInfoMgr.markHaveAiFlag(dto.getUuid());
                    iAiInfoMgr.putInfo(
                            SearchConstant.AI_ANALYSIS_FOR_DETAIL,
                            SearchConstant.AI_ANALYSIS_DATA_BASE_INFO,
                            dto.getUuid(),
                            info
                    );
                });
        return vo;
    }

    private void asyncFlushDataOnDetail(BaseArchivesSearchDTO dto) throws ServiceException {
        makeMultiPlatformSearchService(
                StringUtils.showEmpty(dto.getArchivesType())
        ).ifPresent(it -> {
            BaseMultiPlatformSearchService service = it;
            while (true) {
                service.flushDataOnDetail(dto.getRecordId());
                if (service.haveNext()) {
                    service = service.next();
                } else {
                    break;
                }
            }
        });
    }

    /**
     * 合并VO中的字段，slave中的合并到master中<BR>
     *
     * @param master 主数据
     * @param slave  参数
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/2 14:00
     */
    private void mergeFields(ArchivesVO master, ArchivesVO slave) {
        if (Objects.nonNull(master) && Objects.nonNull(slave)) {
            var main = new ArrayList<>(master.getFields());
            var exists = main.stream().map(KeyValueTypeVO::getKey).collect(Collectors.toSet());
            Optional.ofNullable(slave.getFields())
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(it -> it.forEach(item -> {
                        if (!exists.contains(item.getKey())) {
                            main.add(item);
                        }
                    }));
            master.setFields(main);
        }
    }

    /**
     * 获取列表查询的  RestfulResults
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     */
    @Override
    public RestfulResultsV2<ArchivesVO> getFetchRestfulResults(BaseArchivesSearchDTO dto) throws ServiceException {
        return makeMultiPlatformSearchService(
                StringUtils.showEmpty(dto.getArchivesType())
        ).map(it -> {
            Long total = 0L;
            List<ArchivesVO> data = new ArrayList<>(0);
            Long took = 0L;
            Integer resultSize = 0;
            BaseMultiPlatformSearchService service = it;
            final List<String> conditions = new ArrayList<>(0);
            Map<String, ArchivesVO> isHit = new HashMap<>(0);
            // 参数为空时，如果登录用户不为空就启动AI分析
            if (Objects.isNull(dto.getUseAiAnalysis())) {
                dto.setUseAiAnalysis(Optional.ofNullable(AuthHelper.getCurrentUser()).isPresent());
            }
            while (true) {
                log.info("[{}]开始搜索", service.desc());
                // 检索数据
                ExtPageList<ArchivesVO> pageList = service.searchData(total, resultSize, dto);
                if (pageList == null) {
                    log.info("[{}]未搜索到数据", service.desc());
                    continue;
                }
                // 如果分页数据还不足的话，就追加数据到其中
                if (resultSize < dto.getPageSize() && !pageList.isEmpty()) {
                    for (ArchivesVO content : pageList.getContents()) {
                        Set<String> keys = content.makeDetectDuplicationKeys();
                        var find = isHit.keySet()
                                .stream()
                                .filter(keys::contains)
                                .filter(StringUtils::isNotEmpty)
                                .findAny();
                        if (find.isPresent()) {
                            // 按照XMKFB-3658要求合并数据的字段列表
                            mergeFields(isHit.get(find.get()), content);
                            pageList.setTotal(Optional.ofNullable(pageList.getTotal()).orElse(1L) - 1L);
                        } else {
                            keys.forEach(key -> isHit.put(key, content));
                            data.add(content);
                        }
                    }
                    resultSize += pageList.size();
                }
                // 合并条件
                if (StringUtils.isNotEmpty(pageList.getCondition())) {
                    conditions.add(pageList.getCondition());
                } else {
                    log.info("检索器[{}]的条件为空", service.desc());
                }
                // 合并总数
                total += Optional.ofNullable(pageList.getTotal()).orElse(0L);
                // 合并总耗时
                took += pageList.getTook();
                // 如果处理器有数据就需要返回的话
                if (service.skipNextOnHit() && resultSize > 0) {
                    break;
                }
                // 如果没有后续处理器的话，就跳出循环
                if (!service.haveNext()) {
                    break;
                }
                service = service.next();
            }
            Boolean haveAi = iAiInfoMgr.haveAiFlag(dto.getUuid());
            if (haveAi && data.size() > 0) {
                final String queryText = StringUtils.showEmpty(dto.getKeyword(), dto.getWholeContent());
                if (StringUtils.isNotEmpty(queryText) && !Objects.equals("*", queryText)) {
                    iAiInfoMgr.putInfo(SearchConstant.AI_ANALYSIS_FOR_LIST, SearchConstant.AI_ANALYSIS_DATA_KEYWORD, dto.getUuid(), queryText);
                }
                List<AiInfoVo> info = BaseAiInfoConvertService.findByClass(ArchivesVO.class)
                        .map(parse -> parse.convertToRedisRawData(data))
                        .orElseGet(List::of);
                if (CollectionUtils.isNotEmpty(info) && StringUtils.isNotEmpty(dto.getUuid())) {
                    // 这里只所以不进行useAiAnalysis判断就塞入数据，是为了方便给一体化平台提供基础支持
                    // 一体化平台中意图识别是的单独调度的且不需要发送消息
                    iAiInfoMgr.putInfoWithMerge(SearchConstant.AI_ANALYSIS_FOR_LIST, SearchConstant.AI_ANALYSIS_DATA_BASE_INFO, dto.getUuid(), info);
                    final boolean useAiAnalysisCondition = iAiInfoMgr.useAiAnalysisCondition(dto.getUuid());
                    final boolean useAiAnalysis = Optional.ofNullable(dto.getUseAiAnalysis()).orElse(false);
                    if (useAiAnalysis) {
                        final CurrentUser user = AuthHelper.getCurrentUser();
                        // 如果用了条件就没必要再去走意图识别了，直接发送消息即可
                        if (useAiAnalysisCondition) {
                            SearchThreadPoolConfigV2.t.submit(() -> {
                                Random random = new Random(System.currentTimeMillis());
                                Long randomNum = random.nextInt(90) + 10L;
                                Try.run(() -> Thread.sleep(6000L + randomNum));
                                AiUtils.sendMessage(user, dto);
                            });
                        } else {
                            // 否则需要从新走意图识别
                            final List<ContentParseVo> items = ArchivesUtils.parseEntity(queryText);
                            AiUtils.aiAnalysisWithCallback(user, items, dto);
                        }
                    }
                }
            }
            return RestfulResultsV2.ok(data)
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTook(took)
                    .addCondition(String.join(StringUtils.SEPARATOR_SEMICOLON, conditions))
                    .addTotalCount(total);
        }).orElseThrow(() -> new ServiceException("主题[" + dto.getArchivesType() + "]不存在"));
    }

    @Override
    public List<String> tabs() {
        final String configTabs = properties.getSearchArchivesTabs();
        List<String> result = new ArrayList<>();
        result.add("全部");
        if (StringUtils.isNotEmpty(configTabs)) {
            result.addAll(Arrays.asList(configTabs.split("[,|;]")));
        }
        return tabFilter(result);
    }

    private List<String> tabFilter(List<String> list) {
        return list;
    }

    private List<String> getExportRoleByArchivesType(String archivesType) throws ServiceException {
        final CurrentUser user = AuthHelper.getNotNullUser();
        final List<SearchDataObjVO> list = searchDataService.getHasRightObjs(user, archivesType,
                SearchConstant.RIGHT_EXPORT, SearchConstant.OBJ_TYPE_TABLE);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(SearchDataObjVO::getObjId).collect(Collectors.toList());
    }

    @Override
    public void export(HttpServletResponse response, BaseArchivesSearchDTO dto) throws ServiceException {
        dto.isValid();
        try {
            final String archivesType = dto.getArchivesType();
            if (!ArchivesConstants.ARCHIVES_TYPE_ALL.equals(archivesType)) {
                final List<String> exportRoleList = getExportRoleByArchivesType(archivesType);
                if (!exportRoleList.contains(archivesType)) {
                    throw new ServiceException("没有导出权限！");
                }
            }
            String fileName = URLEncoder.encode(archivesType + "_", StandardCharsets.UTF_8)
                    + LocalDateTime.now().format(TimeUtil.WARNING_MESSAGE_PATTERN);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            dto.setExport(true);
            dto.setPageNum(1);
            dto.setPageSize(9999);
            final RestfulResultsV2<ArchivesVO> pageList = getFetchRestfulResults(dto);
            List<ArchivesVO> list = pageList.getDatas();
            if (list.isEmpty()) {
                throw new ServiceException("数据列表为空！没有数据可以导出！");
            }
            // 进行导出的日志记录
            final String content = SearchLogNewUtils.buildExportContent(archivesType,
                    com.trs.common.utils.StringUtils.showEmpty(dto.getSearchValue(), dto.getKeyword()));
            searchLogNewService.doLog(SearchLogNewUtils.buildLogDTO(archivesType, OperateTypeEnum.EXPORT, content));
            doExportExcel(response.getOutputStream(), fileName, doParas(list));
            response.flushBuffer();
        } catch (IOException e) {
            throw new ServiceException("Excel导出异常！");
        }
    }

    private List<Tuple3<String, List<List<String>>, List<List<Object>>>> doParas(List<ArchivesVO> list) {
        List<Tuple3<String, List<List<String>>, List<List<Object>>>> result = new ArrayList<>();
        list.stream().collect(Collectors.groupingBy(ArchivesVO::getType)).forEach((key, value) -> {
            List<List<Object>> data = value.stream()
                    .map(archivesVO -> resolveFieldValue(archivesVO.getFields()))
                    .collect(Collectors.toList());
            List<List<String>> heads = value
                    .get(0)
                    .getFields()
                    .stream()
                    .map(keyValue -> List.of(keyValue.getKey()))
                    .collect(Collectors.toList());
            result.add(new Tuple3<>(key, heads, data));
        });
        return result;
    }

    private void doExportExcel(ServletOutputStream outputStream, String fileName, List<Tuple3<String, List<List<String>>, List<List<Object>>>> list) {
        ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build();
        int index = 0;
        for (Tuple3<String, List<List<String>>, List<List<Object>>> tuple3 : list) {
            WriteSheet build = EasyExcelFactory.writerSheet(index++, buildSheetName(fileName, tuple3._1)).build();
            build.setHead(tuple3._2);
            excelWriter.write(tuple3._3, build);
        }
        excelWriter.finish();
    }

    private String buildSheetName(String fileName, String archivesType) {
        ArchivesEnum instance = ArchivesEnum.getInstance(archivesType);
        if (Objects.isNull(instance)) {
            return fileName;
        } else {
            String tableDesc = instance.getTableDesc();
            String name = tableDesc.contains("档案") ? tableDesc : tableDesc + "档案";
            return TimeUtils.dateToString(new Date(), TimeUtils.YYYYMMDD5) + name;
        }
    }

    private List<Object> resolveFieldValue(List<KeyValueTypeVoForSearch> fields) {
        return fields.stream()
                .map(value -> {
                    if (null == value.getValue()) {
                        return "--";
                    }
                    if (value.getValue() instanceof List) {
                        final List<JSONObject> list = (List<JSONObject>) value.getValue();
                        return ArchivesUtils.specialFieldParse(value.getKey(), list);
                    }
                    if (value.getValue() instanceof String) {
                        final String s = String.valueOf(value.getValue());
                        return ArchivesUtils.specialFieldParse(value.getKey(), s);
                    }
                    if (value.getValue() instanceof JSONObject) {
                        return "--";
                    }
                    return value.getValue();
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<JudgeRegistrationVO> judgeRegistration(BaseArchivesSearchDTO dto) throws ServiceException {
        checkNotEmpty(dto.getArchivesType(), "档案类型不能为空！");
        if (StringUtils.isEmpty(dto.getRecordId()) && StringUtils.isEmpty(dto.getRecordIds())) {
            throw new ServiceException("Record与recordIds不能同时为空");
        }
        final var recordIdArray = StringUtils.getList(StringUtils.showEmpty(dto.getRecordIds(), dto.getRecordId()), true);
        List<JudgeRegistrationVO> result = new ArrayList<>(recordIdArray.size());
        for (String recordId : recordIdArray) {
            JudgeRegistrationVO vo = JudgeRegistrationVO.of(dto.getArchivesType(), recordId, 0);
            if (hadRegistrationIn24H(dto.getArchivesType(), recordId)) {
                result.add(vo);
                continue;
            }
            dto.setRecordId(recordId);
            final Optional<ArchivesVO> detailOptional = getDetail(dto);
            if (detailOptional.isEmpty()) {
                result.add(vo);
                continue;
            }
            ArchivesVO detail = detailOptional.get();
            if (matchLabel(detail.getType(), detail.getFields())) {
                vo.setNeed(1);
            }
            result.add(vo);
        }
        return result;
    }

    private Optional<ArchivesVO> getDetail(BaseArchivesSearchDTO dto) {
        return Try.of(() -> detail(dto)).toJavaOptional();
    }

    private boolean matchLabel(String archivesType, List<KeyValueTypeVoForSearch> fields) throws ServiceException {
        final CurrentUser user = AuthHelper.getNotNullUser();
        final List<String> labelList = LabelUtils.getLabelFromFields(fields);
        final List<SearchDataObjVO> roleObjList = searchDataService.getHasRightObjs(
                user, archivesType,
                SearchConstant.RIGHT_REGISTRATION,
                SearchConstant.OBJ_TYPE_LABEL
        );
        if (CollectionUtils.isEmpty(roleObjList)) {
            return false;
        }
        final Set<String> roleSet = roleObjList.stream().map(SearchDataObjVO::getObjId).collect(Collectors.toSet());
        for (String label : labelList) {
            if (roleSet.contains(label)) {
                return true;
            }
        }
        return false;
    }

    private boolean hadRegistrationIn24H(String archivesType, String recordId) {
        final SimpleUserVO user = AuthHelper.getNotNullSimpleUser();
        final SearchRegistrationEntity entity = searchRegistrationMapper.selectOne(
                new QueryWrapper<SearchRegistrationEntity>()
                        .eq("archives_type", archivesType)
                        .eq("record_id", recordId)
                        .eq("user_id", user.getUserId())
                        .eq("dept_id", user.getDeptId())
                        .ge("create_time", LocalDateTime.now().minusHours(24))
        );
        return Objects.nonNull(entity);
    }

    @Override
    public String registration(BaseArchivesSearchDTO dto, String content) throws ServiceException {
        checkNotEmpty(dto.getArchivesType(), "档案类型不能为空！");
        if (StringUtils.isEmpty(dto.getRecordId()) && StringUtils.isEmpty(dto.getRecordIds())) {
            throw new ServiceException("RecordId与recordIds不能同时为空！");
        }
        checkNotEmpty(content, "备案内容不能为空！");
        final var recordIdArray = StringUtils.getList(StringUtils.showEmpty(dto.getRecordIds(), dto.getRecordId()), true);
        for (String recordId : recordIdArray) {
            if (hadRegistrationIn24H(dto.getArchivesType(), recordId)) {
                continue;
            }
            dto.setRecordId(recordId);
            ArchivesVO detail = this.detail(dto);
            final SearchRegistrationEntity entity = buildEntity(detail, content);
            searchRegistrationMapper.insert(entity);
            doRegistrationLog(detail, content);
        }
        return "SUCCESS";
    }

    /**
     * findPersonByOtherArchives<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/27 11:20
     */
    @Override
    public ArchivesVO findPersonByOtherArchives(FindPersonByOtherArchivesDTO dto) throws ServiceException {
        dto.isValid();
        PersonArchivesSearchDTO other = new PersonArchivesSearchDTO();
        other.setUuid(dto.getUuid());
        other.setNeedLog(Optional.ofNullable(AuthHelper.getCurrentUser()).isPresent());
        other.setNeedAuth(Optional.ofNullable(AuthHelper.getCurrentUser()).isPresent());
        other.setSkipFlushOnDetail(true);
        other.setArchivesType(dto.getArchivesType());
        other.setRecordId(dto.getRecordId());
        ArchivesVO vo = detail(other);
        final ArchivesEnum instance = Optional.ofNullable(ArchivesEnum.getInstance(dto.getArchivesType()))
                .orElseThrow(() -> new ParamInvalidException("档案类型不存在"));
        checkNotEmpty(
                instance.getPersonFieldName(),
                new ParamInvalidException("该档案类型暂不支持反查")
        );
        if (instance == ArchivesEnum.PERSON) {
            return vo;
        }
        String recordId = vo.getFields()
                .stream()
                .filter(it -> instance.getPersonFieldName().equals(it.getFieldName()) && Objects.nonNull(it.getValue()))
                .map(it -> it.getValue().toString())
                .filter(StringUtils::isNotEmpty)
                .findFirst()
                .orElseThrow(() -> new ServiceException("没能反查到人员证件号码"));
        log.info("根据[{}][{}]反查到了证件号码[{}]", dto.getArchivesType(), dto.getRecordId(), recordId);
        // 根据证件号码再去反查证件号码
        other.setArchivesType(ArchivesEnum.PERSON.getType());
        other.setRecordId(recordId);
        return detail(other);
    }

    /**
     * findPersonByOtherArchives<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/3/27 11:20
     */
    @Override
    public RestfulResultsV2<ArchivesVO> findOtherArchivesByPerson(FindPersonByOtherArchivesDTO dto) throws ServiceException {
        dto.isValid();
        final ArchivesEnum instance = Optional.ofNullable(ArchivesEnum.getInstance(dto.getArchivesType()))
                .filter(it -> StringUtils.isNotEmpty(it.getPersonFieldName()))
                .orElseThrow(() -> new ParamInvalidException("档案类型不存在或不支持反查"));
        dto.setFieldName(instance.getPersonFieldName());
        return getFetchRestfulResults(dto);
    }

    private SearchRegistrationEntity buildEntity(ArchivesVO record, String content) {
        final SimpleUserVO user = AuthHelper.getNotNullSimpleUser();
        SearchRegistrationEntity result = new SearchRegistrationEntity();
        result.setArchivesType(record.getType());
        result.setRecordId(ArchivesUtils.getRecordIdByType(record.getType(), record.getFields()));
        result.setContent(content);
        result.setCreateTime(LocalDateTime.now());
        result.setUserId(user.getUserId());
        result.setUsername(user.getUserName());
        result.setDeptId(user.getDeptId());
        result.setDeptName(user.getDeptName());
        return result;
    }

    /**
     * 进行备案登记的日志记录，异步，不影响备案流程
     *
     * @param record  实体对象
     * @param content 备案登记内容
     */
    private void doRegistrationLog(ArchivesVO record, String content) {
        try {
            final String type = record.getType();
            final List<KeyValueTypeVoForSearch> fields = record.getFields();
            final String operateContent = SearchLogNewUtils.buildRegistrationContent(
                    type,
                    ArchivesUtils.getRecordIdByType(type, fields),
                    fields,
                    content
            );
            final SearchLogDTO logDTO = SearchLogNewUtils.buildLogDTO(
                    type,
                    OperateTypeEnum.REGISTRATION,
                    operateContent
            );
            logDTO.setDjsy(StringUtils.showEmpty(content));
            searchLogNewService.doLog(logDTO);
        } catch (ServiceException e) {
            log.error("备案登记：档案备案登记出现异常！", e);
        }
    }

    /**
     * caseSuspect<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2023/12/4 11:22
     */
    public RestfulResultsV2<CaseSuspectVO> caseSuspect(BaseArchivesSearchDTO dto) throws ServiceException {
        checkNotEmpty(dto.getRecordId(), new ParamInvalidException("案件号不能为空"));
        IPage<CaseSuspectEntity> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        page = caseSuspectMapper.selectPage(
                page,
                new QueryWrapper<CaseSuspectEntity>()
                        .lambda()
                        .eq(CaseSuspectEntity::getRecordId, dto.getRecordId())
                        .orderByDesc(CaseSuspectEntity::getGjchcs)
        );
        RestfulResultsV2<CaseSuspectVO> results = RestfulResultsV2.ok(
                page.getRecords()
                        .stream()
                        .map(EntityConvert.INSTANCE::entityToVo)
                        .collect(Collectors.toList())
        ).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(page.getTotal());
        return results;
    }
}
