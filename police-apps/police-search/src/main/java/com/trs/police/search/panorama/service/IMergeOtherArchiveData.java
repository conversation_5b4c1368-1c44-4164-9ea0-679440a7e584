package com.trs.police.search.panorama.service;

import com.trs.common.exception.ServiceException;
import com.trs.police.search.panorama.dto.PanoramaSearchDTO;
import com.trs.web.builder.base.IKey;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/1/2 17:45
 * @since 1.0
 */
public interface IMergeOtherArchiveData extends IKey {

    /**
     * mergeOtherArchiveData<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/2 17:45
     */
    PanoramaSearchDTO mergeOtherArchiveData(PanoramaSearchDTO dto) throws ServiceException;
}
