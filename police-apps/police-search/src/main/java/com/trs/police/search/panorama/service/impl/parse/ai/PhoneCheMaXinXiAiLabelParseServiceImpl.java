package com.trs.police.search.panorama.service.impl.parse.ai;

import com.trs.common.utils.StringUtils;
import com.trs.police.search.panorama.constant.SearchConstant;
import com.trs.police.search.panorama.service.BasePhoneAiLabelParseService;
import com.trs.police.search.panorama.vo.AiLabelVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 手机档案 - 车码信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-20 10:20:25
 */
@Service
public class PhoneCheMaXinXiAiLabelParseServiceImpl extends BasePhoneAiLabelParseService {

    @Override
    protected List<String> makeCenter(AiLabelVo label, List<AiLabelVo> labels) {
        List<String> list = new ArrayList<>(3);
        list.add(label.getRelationObj());
        Optional.ofNullable(label.getExt())
                .map(it -> it.getString("kxd"))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(it -> list.add("可信度：" + it));
        Optional.ofNullable(label.getExt())
                .map(it -> it.getString("time"))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(it -> list.add("最新采集时间：" + it));
        return list;
    }

    @Override
    public String relationCatalog() {
        return SearchConstant.AI_LABEL_CATALOG_CMXX;
    }
}
