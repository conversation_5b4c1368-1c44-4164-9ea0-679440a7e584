package com.trs.police.control.service.impl.personclue;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.entity.dwd.BaseDwdEntity;
import com.trs.police.common.core.entity.dwd.DwdPqEntity;
import com.trs.police.common.core.vo.control.CareMonitorVO;
import com.trs.police.control.config.PersonClueCareMonitorConfig;
import com.trs.police.control.domain.vo.care.CareMonitorInitialeVO;
import com.trs.police.control.mapper.DwdPqMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 剽窃数据推送服务层
 *
 * <AUTHOR>
 * @date 2024/10/17
 */
@Component
public class DwdPqPushServiceImpl extends BasePersonCluePushService<DwdPqEntity> {

    @Autowired
    private DwdPqMapper dwdPqMapper;

    @Resource
    private PersonClueCareMonitorConfig config;

    /**
     * 获取处理类名称
     *
     * @return 类名称
     */
    @Override
    protected String getHandleName() {
        return "com.trs.police.control.handler.care.impl.DwdPqCareMonitorHandler";
    }

    @Override
    protected List<DwdPqEntity> getPushData(CareMonitorVO careMonitorVO) {
        QueryWrapper<DwdPqEntity> wrapper = new QueryWrapper<DwdPqEntity>()
                .isNotNull("zjhm");
        return dwdPqMapper.selectList(wrapper);
    }

    @Override
    protected List<DwdPqEntity> filterData(List<DwdPqEntity> list) {
        return list;
    }

    @Override
    protected List<CareMonitorInitialeVO> sourceToTarget(List<DwdPqEntity> pushData, CareMonitorVO careMonitorVO) {
        return pushData.stream()
                .map(entity -> baseDwdEntityToCareMonitorInitialeVO(entity, careMonitorVO))
                .collect(Collectors.toList());
    }
    @Override
    protected Map<String, BaseDwdEntity> getGroupData(List<DwdPqEntity> pushData) {
        Map<String, BaseDwdEntity> map = pushData.stream().collect(Collectors.toMap(BaseDwdEntity::getZjhm, b -> b, (a, b) -> a));
        return map;
    }


    /**
     * 获取需要命中的区域id
     *
     * @return 区域id
     */
    @Override
    protected List<Long> getHitAreaId(){
        return config.getDwdPqAreaId();
    }

    /**
     * 获取线索类型
     *
     * @return 线索类型
     */
    @Override
    protected Integer getClueType(){
        return 101;
    }

    @Override
    public String key() {
        return "dwd_pq";
    }

    @Override
    public String desc() {
        return "剽窃";
    }
}
