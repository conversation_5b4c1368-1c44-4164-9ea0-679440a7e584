package com.trs.police.control.kafka.v2.flow.processor.warning.postprocess;

import com.trs.data.exception.ProcessException;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.openfeign.starter.service.OperationLogService;
import com.trs.police.control.constant.TrackSourceFromEnum;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 云控操作日志记录行为
 * *@author:wen.wen
 * *@create 2025-01-24 13:27
 **/
@Component
@Slf4j
public class CloudControlOperationLogProcessor implements IWarningPostProcessor {

    @Resource
    private OperationLogService operationLogService;


    @Override
    public WarningMessageContext.WarningResultContext process(WarningMessageContext context, WarningMessageContext.WarningResultContext warningResultContext) throws ProcessException {
        try {
            Map<String, Object> trackDetail = context.getTrackVO().getTrackDetail();
            //如果是云控的预警，需要记录签收日志
            if (Objects.nonNull(trackDetail)) {
                if (TrackSourceFromEnum.CLOUD_CONTROL.getCode().equalsIgnoreCase(String.valueOf(trackDetail.get("trackSourceFrom")))) {
                    createOperationLog(warningResultContext.getWarningEntity().getId());
                }
            }

            return warningResultContext;
        } catch (Exception e) {
            log.error("CloudControlOperationLogProcessor 发生异常", e);
            return warningResultContext;
        }
    }

    private void createOperationLog(long id) {
        try {
            String detail = BeanFactoryHolder.getEnv().getProperty("com.trs.warning.cloud.control.detail", "四川省公安厅签收了预警指令");
            operationLogService.createOperationLog(id,
                    null,
                    OperateModule.CLOUD_CONTROL,
                    Operation.CLOUD_CONTROL_SIGN,
                    null,
                    detail);
        } catch (Exception e) {
            log.error("记录省厅签收日志发生异常", e);
        }
    }

    @Override
    public Integer order() {
        return 5;
    }
}
