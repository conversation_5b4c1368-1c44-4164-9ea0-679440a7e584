package com.trs.police.control.controller;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.MonitorListVO;
import com.trs.police.common.core.vo.control.TrackVO;
import com.trs.police.control.domain.vo.care.CareMonitorInitialeVO;
import com.trs.police.control.domain.vo.monitor.TrackStatisticsVO;
import com.trs.police.control.domain.vo.warning.WarningDetailVO;
import com.trs.police.control.service.CareMonitorOperateService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 无感布控
 *
 * @date 2024/4/22
 * @author: cy
 */
@Slf4j
@RestController
@RequestMapping({"/inductive-monitor", "/public/inductive-monitor"})
public class InductiveCareMonitorController {

    /*@Resource
    private InductiveCareMonitorService inductiveCareMonitorService;*/

    @Resource
    private CareMonitorOperateService careMonitorOperateService;


    /**
     * 无感布控
     *
     * @param voList voList
     * @return 结果
     */
    @PostMapping("/inductiveMonitor")
    public RestfulResultsV2 inductiveMonitor(@RequestBody List<CareMonitorInitialeVO> voList) {
        try {
            //inductiveCareMonitorService.inductiveMonitor(PreConditionCheck.checkNotNull(voList));
            return RestfulResultsV2.ok("无感布控发起成功");
        } catch (Exception ex) {
            log.error("发起无感布控失败，原因为:{}", ex.getMessage());
            return RestfulResultsV2.ok("无感布控发起失败");
        }
    }


    /**
     * 同步轨迹数据
     */
    @GetMapping
    public void syncData() {
        //inductiveCareMonitorService.syncData();
    }


    /**
     * 获取关注监控列表
     *
     * @param request 请求参数
     * @return 关注监控列表
     */
    @PostMapping("/monitorList")
    public PageResult<MonitorListVO> monitorList(@RequestBody ListParamsRequest request) {
        return careMonitorOperateService.monitorList(request);
    }


    /**
     * 删除监控
     *
     * @param monitorId monitorId
     */
    @GetMapping("removeMonitor")
    public void removeMonitor(Long monitorId) {
        careMonitorOperateService.removeMonitor(monitorId);
    }

    /**
     * 撤控
     *
     * @param personIds     personIds
     * @param relatedDataId relatedDataId
     * @param relatedType   relatedType
     * @return RestfulResultsV2 RestfulResultsV2
     */
    @GetMapping("cancelMonitor")
    public RestfulResultsV2<Object> cancelMonitor(String personIds, Long relatedDataId, String relatedType) {
        return careMonitorOperateService.cancelMonitor(personIds, relatedDataId, relatedType);
    }

    /**
     * 根据身份证号获取轨迹列表
     *
     * @param idNumber    身份证号
     * @param relatedDataId 关联数据id
     * @return 轨迹列表
     */
    @GetMapping("/track/getTrackListByIdNumber")
    public List<TrackVO> getTrackListByIdNumber(String idNumber, Long relatedDataId) {
        return careMonitorOperateService.getTrackListByIdNumber(idNumber, relatedDataId);
    }

    /**
     * 根据身份证号统计轨迹个数
     *
     * @param idNumber    身份证号
     * @param relatedDataId 关联数据类型
     * @param relatedType 关联数据类型
     * @return 轨迹列表
     */
    @GetMapping("/track/statisticsTrackByIdNumber")
    public List<TrackStatisticsVO> statisticsTrackByIdNumber(String idNumber, Long relatedDataId,String relatedType) {
        return careMonitorOperateService.statisticsTrackByIdNumber(idNumber, relatedDataId, relatedType);
    }

    /**
     * 预警轨迹详情 https://trsyapi.trscd.com.cn/project/432/interface/api/7876
     *
     * @param warningId 预警id
     * @return 详情
     */
    @GetMapping("/warningTrack/detail")
    public WarningDetailVO getWarningTrackDetail(String warningId) {
        return careMonitorOperateService.getWarningTrackDetail(warningId);
    }
}
