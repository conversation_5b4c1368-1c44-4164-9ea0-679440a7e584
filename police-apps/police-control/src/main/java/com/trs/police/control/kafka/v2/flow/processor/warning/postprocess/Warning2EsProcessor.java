package com.trs.police.control.kafka.v2.flow.processor.warning.postprocess;

import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.proxy.WarningOperateProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 预警保存到ES
 * *@author:wen.wen
 * *@create 2025-01-24 13:42
 **/
@Slf4j
@Component
public class Warning2EsProcessor implements IWarningPostProcessor {

    @Autowired
    private WarningOperateProxy warningOperateProxy;

    @Override
    public WarningMessageContext.WarningResultContext process(WarningMessageContext warningMessageContext, WarningMessageContext.WarningResultContext context) {
        try {
            //根据需要将预警信息存入es，以加快预警列表检索速度
            warningOperateProxy.esInsert(context.getWarningTrackEntity(), context.getWarningEntity(), context.getNotifyEntityList());
            return context;
        } catch (Exception e) {
            log.error("Warning2EsProcessor 发生异常", e);
            return context;
        }
    }

    @Override
    public Integer order() {
        return 4;
    }
}
