package com.trs.police.control.kafka.v2.flow.processor.warning.postprocess;

import com.trs.police.control.domain.entity.warning.WarningNotifyEntity;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.service.impl.WarningProcessServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * *@author:wen.wen
 * *@create 2025-01-24 13:44
 **/
@Slf4j
@Component
public class WarningUserRelationBuildProcessor implements IWarningPostProcessor {

    @Resource
    private WarningProcessServiceImpl warningProcessService;

    @Override
    public WarningMessageContext.WarningResultContext process(WarningMessageContext warningMessageContext,
                                                              WarningMessageContext.WarningResultContext context) {
        try {
            List<WarningNotifyEntity> warningNotifyEntityList = warningProcessService.
                    setWarningUserRelation(context.getWarningInfo(), warningMessageContext.getMonitorId());
            context.setNotifyEntityList(warningNotifyEntityList);
        } catch (Exception e) {
            log.error("WarningUserRelationBuildProcessor 发生异常", e);
        }
        return context;
    }

    @Override
    public Integer order() {
        return 2;
    }
}
