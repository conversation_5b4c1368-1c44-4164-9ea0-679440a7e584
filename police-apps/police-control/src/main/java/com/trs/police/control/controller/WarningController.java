package com.trs.police.control.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.request.WarningDoneRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.WcspsdkUtils;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.control.*;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.control.domain.dto.NameUniqueDTO;
import com.trs.police.control.domain.dto.WarningConfigDTO;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.domain.vo.WarningRankVO;
import com.trs.police.control.domain.vo.WarningTrackPointVO;
import com.trs.police.control.domain.vo.warning.*;
import com.trs.police.control.mapper.WarningTrackMapper;
import com.trs.police.control.service.WarningDisplayService;
import com.trs.police.control.service.WarningService;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 预警接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warning")
public class WarningController {

    @Resource
    private WarningDisplayService warningDisplayService;
    @Resource
    private WarningService warningService;

    /**
     * es同步
     *
     * @param warningId 预警id
     */
    @GetMapping("/es/sync/{warningId}")
    public void esSync(@PathVariable("warningId") Long warningId) {
        warningService.esSync(warningId);
    }

    /**
     * 预警列表查询 http://192.168.200.192:3001/project/4974/interface/api/140063
     *
     * @param request 参数
     * @return 列表
     */
    @PostMapping("/list")
    public PageResult<WarningListVO> getMyWarningList(@RequestBody ListParamsRequest request) {
        return warningDisplayService.getMyWarningList(request);
    }

    /**
     * 全部预警列表查询
     *
     * @param request 参数
     * @return 列表
     */
    @PostMapping("/list/all")
    public PageResult<WarningListVO> getAllWarningList(@RequestBody ListParamsRequest request) {
        return warningDisplayService.getAllWarningList(request);
    }


    /**
     * 全部预警列表查询
     *
     * @param request 参数
     * @return 列表
     */
    @PostMapping("/list/allPoint")
    public PageResult<WarningAllInfoVO> getAllWarningPointList(@RequestBody ListParamsRequest request) {
        return warningDisplayService.getAllWarningPointList(request);
    }


    /**
     * 查询待办列表
     *
     * @param pageParams 分页
     * @return 结果
     */
    @PostMapping("/todo")
    PageResult<TodoTaskVO> getWarningTodo(@RequestBody PageParams pageParams) {
        return warningDisplayService.getWarningTodo(pageParams);
    }

    /**
     * 预警签收 http://192.168.200.192:3001/project/4974/interface/api/140079
     *
     * @param id id
     */
    @PutMapping("/{id}/sign")
    public void signWarning(@PathVariable Long id) {
        warningService.signWarning(id);
    }

    /**
     * 预警完结 http://192.168.200.192:3001/project/4974/interface/api/140087
     *
     * @param id      id
     * @param request 内容
     */
    @PostMapping("/{id}/done")
    public void doneWarning(@PathVariable Long id, @RequestBody WarningDoneRequest request) {
        warningService.doneWarning(id, request);
    }

    /**
     * 获取预警完结 http://192.168.200.192:3001/project/4974/interface/api/140087
     *
     * @param id id
     * @return {@link  WarningDoneVO}
     */
    @GetMapping("/{id}/done")
    public WarningDoneVO getWarningDone(@PathVariable Long id) {
        return warningService.getWarningDone(id);
    }

    /**
     * 预警反馈 http://192.168.200.192:3001/project/4974/interface/api/140095
     *
     * @param id         id
     * @param feedbackVO 反馈内容
     */
    @PostMapping("/{id}/feedback")
    public void feedbackWarning(@PathVariable Long id, @RequestBody WarningFeedbackDetailVO feedbackVO) {
        warningService.feedbackWarning(id, feedbackVO);
    }

    /**
     * 预警详情-预警反馈删除
     *
     * @param id 预警反馈id
     */
    @DeleteMapping("/{id}/feedback")
    public void deleteWarningFeedback(@PathVariable Long id) {
        warningService.deleteWarningFeedback(id);
    }

    /**
     * 预警详情-预警反馈编辑
     *
     * @param feedbackVO 反馈内容
     * @param id         预警反馈id
     */
    @PutMapping("/{id}/feedback")
    public void updateWarningFeedback(@PathVariable Long id, @RequestBody WarningFeedbackDetailVO feedbackVO) {
        warningService.updateWarningFeedback(id, feedbackVO);
    }

    /**
     * 预警详情 http://192.168.200.192:3001/project/4974/interface/api/140071
     *
     * @param warningId 预警id
     * @return 详情
     */
    @GetMapping("/{warningId}/detail")
    public WarningDetailVO getWarningDetail(@PathVariable Long warningId) {
        return warningDisplayService.getWarningDetail(warningId);
    }

    /**
     * 预警活动时间、地点 http://192.168.200.192:3001/project/4974/interface/api/140183
     *
     * @param trackId 轨迹id
     * @param portType 端口类型
     * @return vo
     */
    @GetMapping("/detail/track/{trackId}/activity")
    public WarningActivityVO getWarningActivity(@PathVariable String trackId,@RequestParam(required = false) String portType) {
        return warningDisplayService.getWarningActivity(trackId,portType);
    }

    /**
     * 预警活动人员 http://192.168.200.192:3001/project/4974/interface/api/140103
     *
     * @param trackId 轨迹id
     * @param portType 端口类型
     * @return 人员
     */
    @GetMapping("/detail/track/{trackId}/person")
    public PersonCardVO getWarningPerson(@PathVariable String trackId,@RequestParam(required = false) String portType) {
        return warningDisplayService.getWarningPerson(trackId,portType);
    }

    /**
     * 预警活动照片 http://192.168.200.192:3001/project/4974/interface/api/140111
     *
     * @param trackId 轨迹id
     * @return 照片
     */
    @GetMapping("/detail/track/{trackId}/photo")
    public WarningPhotoVO getWarningPhoto(@PathVariable Long trackId) {
        return warningDisplayService.getWarningPhoto(trackId);
    }

    /**
     * 预警感知源 http://192.168.200.192:3001/project/4974/interface/api/140119
     *
     * @param trackId 轨迹id
     * @param portType 端口类型
     * @return 感知源
     */
    @GetMapping("/detail/track/{trackId}/source")
    public WarningSourceVO getWarningSource(@PathVariable String trackId,@RequestParam(required = false) String portType) {
        return warningDisplayService.getWarningSource(trackId,portType);
    }

    /**
     * 预警详情-轨迹信息 http://192.168.200.192:3001/project/4974/interface/api/140151
     *
     * @param trackId 轨迹id
     * @return 轨迹
     */
    @GetMapping("/detail/track/{trackId}/info")
    public WarningTrackVO getTrackDetail(@PathVariable Long trackId) {
        return warningDisplayService.getTrackDetail(trackId);
    }

    /**
     * 预警详情-重点区域 http://192.168.200.192:3001/project/4974/interface/api/140767
     *
     * @param trackId 轨迹id
     * @param portType 端口类型
     * @return 区域
     */
    @GetMapping("/detail/track/{trackId}/area")
    public List<WarningAreaVO> getImportantArea(@PathVariable String trackId,@RequestParam(required = false) String portType) {
        return warningDisplayService.getImportantArea(trackId,portType);
    }

    /**
     * 预警详情-群体列表 http://192.168.200.192:3001/project/4974/interface/api/140751
     *
     * @param warningId 预警id
     * @return 人员
     */
    @GetMapping("/{warningId}/detail/group/content")
    public List<GroupPersonVO> getWarningGroupPerson(@PathVariable Long warningId) {
        return warningDisplayService.getWarningGroupPersonList(warningId);
    }

    /**
     * 预警详情-模块列表 http://192.168.200.192:3001/project/4974/interface/api/140295
     *
     * @param trackId 预警id
     * @return 模块列表
     */
    @GetMapping("/detail/track/{trackId}/category")
    public List<CategoryVO> getWarningCategory(@PathVariable Long trackId) {
        return warningDisplayService.getWarningCategory(trackId);
    }

    /**
     * 查询操作按钮 http://192.168.200.192:3001/project/4974/interface/api/142865
     *
     * @param warningId 预警id
     * @return 是否可操作
     */
    @GetMapping("/{warningId}/operate")
    public List<String> getUserWarningOperate(@PathVariable("warningId") Long warningId) {
        return warningDisplayService.getUserWarningOperate(warningId, AuthHelper.getNotNullSimpleUser());
    }

    /**
     * 预警详情-常控人员 http://192.168.200.192:3001/project/4974/interface/api/142927
     *
     * @param trackId 轨迹id
     * @return 常控人员卡片
     */
    @GetMapping("/detail/track/{trackId}/regular-person")
    public PersonCardVO getWarningRegularPerson(@PathVariable Long trackId) {
        return warningDisplayService.getWarningRegularPerson(trackId);
    }

    /**
     * 预警详情-流程
     *
     * @param warningId 预警id
     * @return 预警流程
     */
    @GetMapping("/{warningId}/process")
    public WarningProcessVO getWarningProcess(@PathVariable Long warningId) {
        return warningDisplayService.getWarningProcess(warningId);
    }

    /**
     * 预警详情-预警处置 http://192.168.200.192:3001/project/4974/interface/api/146826
     *
     * @param warningId 预警id
     * @return {@link WarningFeedbackListVo}
     */
    @GetMapping("/{warningId}/feedback/list")
    public List<WarningFeedbackListVo> getFeedbackList(@PathVariable Long warningId) {
        return warningDisplayService.getFeedbackList(warningId);
    }

    /**
     * 预警详情-轨迹撒点 https://yapi-192.trscd.com.cn/project/4974/interface/api/148416
     *
     * @param warningId 预警id
     * @param portType 端口类型
     * @return {@link WarningTrackPointVO}
     */
    @GetMapping("/{warningId}/detail/track/point")
    public List<WarningTrackPointVO> getTrackPoint(@PathVariable String warningId,@RequestParam(required = false) String portType) {
        return warningDisplayService.getTrackPoint(warningId,portType);
    }

    /**
     * 预警未读数量
     * https://yapi-192.trscd.com.cn/project/4974/interface/api/149370
     *
     * @return 数量
     */
    @GetMapping("/unread/count")
    public Long countUnread() {
        return warningDisplayService.countUnread();
    }

    /**
     * 预警配置列表查询
     *
     * @return 配置列表
     */
    @GetMapping("/config/list")
    public List<WarningConfigVO> getWarningConfigList(){
        return warningDisplayService.getWarningConfigList();
    }
    /**
     * 预警配置列表新增
     *
     * @param  warningConfigDTO 配置的属性
     * @return 新增结果
     */
    @PostMapping("/config/add")
    public String addWarningConfig(@RequestBody WarningConfigDTO warningConfigDTO){
        return warningDisplayService.addWarningConfig(warningConfigDTO);
    }
    /**
     * 预警配置列表编辑
     *
     * @param  configId 配置id
     * @param  warningConfigDTO 配置的属性
     * @return 编辑结果
     */
    @PutMapping("/{configId}/config/edit")
    public String editWarningConfig(@PathVariable Long configId,@RequestBody WarningConfigDTO warningConfigDTO){
        return warningDisplayService.editWarningConfig(configId,warningConfigDTO);
    }

    /**
     * 预警配置列表删除
     *
     * @param  configId 配置id
     *
     */
    @DeleteMapping("/config/{configId}/delete")
    public void delWarningConfig(@PathVariable Long configId){
        warningDisplayService.delWarningConfig(configId);
    }

    /**
     * 预警配置列表状态开关
     *
     * @param configId 配置id
     * @param status 开关状态 0启用 1关闭
     */

    @PutMapping("/config/{configId}/{status}/status")
    public void setWarningConfigStatus(@PathVariable Long configId,@PathVariable Integer status){
        warningDisplayService.setWarningConfigStatus(configId,status);
    }

    /**
     * 未配置的英文名称信息
     *
     * @return 未配置的英文名
     */
    @GetMapping("/config/noConfig")
    public List<WarningEnNameVO> getNoConfigEnName(){
       return warningDisplayService.getNoConfigEnName();
    }

    /**
     *  未配置相关记录数信息
     *
     * @return 配置相关记录数
     */
    @GetMapping("/config/record")
    public WarningConfigRecordVO getConfigRecord(){
        return warningDisplayService.getConfigRecord();
    }

    /**
     * 特征类型与名称信息
     *
     * @return 特征类型与名称
     */
    @GetMapping("/config/identifier")
    public List<IdentifierTypeVO> getIdentifierType(){
        return warningDisplayService.getIdentifierType();
    }

    /**
     * 英文名称唯一性
     *
     * @param  nameUniqueDTO 英文名
     * @return 是否唯一
     */
    @PostMapping("/config/unique")
    public Boolean checkUnique(@RequestBody NameUniqueDTO nameUniqueDTO){
        return warningDisplayService.checkUnique(nameUniqueDTO);
    }

    /**
     * 加密历史数据
     *
     */
    @GetMapping("/encryptHistory")
    public void encryptHistory(){
        int pageNum = 1;
        int pageSize = 1000;
        while(true){
            IPage page = Page.of(pageNum, pageSize);
            WarningTrackMapper warningTrackMapper = BeanFactoryHolder.getBean(WarningTrackMapper.class).get();
            IPage<WarningTrackEntity> pageList = warningTrackMapper.selectPage(page, null);
            if(pageList.getRecords().isEmpty()){
                return;
            }
            pageList.getRecords().forEach(entity -> {
                entity.setTrackInfo(entity.getTrackDetail());
                entity.setMac(WcspsdkUtils.mac(entity.getTrackDetail()));
                warningTrackMapper.updateById(entity);
            });
            pageNum++;
        }
    }

    /**
     * 预警数统计
     *
     * @return 统计结果
     */
    @GetMapping("/statistic")
    public RestfulResultsV2<WarningRankVO> statistic() {
        return warningDisplayService.statistic();
    }
}
