package com.trs.police.control.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 布控任务人员-接收单位关联表
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_control_task_person_dept_relation")
public class ControlTaskPersonDeptRelation extends AbstractBaseEntity {
    /**
     * 布控任务id
     */
    private Long taskId;

    /**
     * 布控任务人员id
     */
    private Long taskPersonId;

    /**
     * 接收单位id
     */
    private Long receiveDeptId;

    /**
     * 下发状态
     * 1：未下发，2：已下发，3：已退回
     */
    private Integer issueStatus = 1;

    /**
     * 退回原因
     */
    private String returnReason;

    public ControlTaskPersonDeptRelation(Long taskId, Long taskPersonId, Long receiveDeptId) {
            this.taskId = taskId;
            this.taskPersonId = taskPersonId;
            this.receiveDeptId = receiveDeptId;
    }
}
