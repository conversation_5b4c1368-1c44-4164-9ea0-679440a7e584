package com.trs.police.control.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.mq.utils.CollectionUtils;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.control.domain.entity.warning.WarningNotifyEntity;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.mapper.WarningMapper;
import com.trs.police.control.mapper.WarningNotifyMapper;
import com.trs.police.control.mapper.WarningProcessMapper;
import com.trs.police.control.mapper.WarningTrackMapper;
import com.trs.police.control.service.WarningHistorySyncService;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.builder.util.NameTheadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 同步预警历史数据到ES
 * *@author:wen.wen
 * *@create 2024-12-30 15:49
 **/
@Slf4j
@Service
public class WarningHistorySyncServiceImpl implements WarningHistorySyncService {

    @Resource
    private WarningMapper warningMapper;

    @Resource
    private WarningTrackMapper warningTrackMapper;


    @Resource
    private WarningNotifyMapper warningNotifyMapper;

    @Resource
    private WarningProcessMapper warningProcessMapper;

    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(
            50,
            100,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(2000000),
            new NameTheadFactory("WarningHistorySyncServiceImpl"),
            // 并发过高时拒绝新的任务
            new ThreadPoolExecutor.AbortPolicy()
    );

    @Override
    public void syncWarningHistory2Es(String startDate, String endDate) {
        //默认必须要手动指定时间范围
        startDate = StringUtils.isEmpty(startDate) ? BeanFactoryHolder.getEnv().getProperty("warning.history.sync.startDate", "20241229") : startDate;
        endDate = StringUtils.isEmpty(endDate) ? BeanFactoryHolder.getEnv().getProperty("warning.history.sync.endDate", "20241230") : endDate;
        List<String> dateList = TimeUtils.getDateList(startDate, endDate, "yyyy-MM-dd");
        for (String date : dateList) {
            long startTime = System.currentTimeMillis();
            QueryWrapper<WarningEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.between("warning_time", date + " 00:00:00", date + " 23:59:59");
            List<WarningEntity> warningEntities = warningMapper.selectList(queryWrapper);
            for (List<WarningEntity> entities : Lists.partition(warningEntities, 200)) {
                List<CompletableFuture<Void>> completableFutures = entities.stream().map((warningEntity) -> CompletableFuture.runAsync(() ->
                        doSync(warningEntity),threadPoolExecutor)).collect(Collectors.toList());
                completableFutures.forEach(CompletableFuture::join);
            }
            long endTime = System.currentTimeMillis();
            log.info("完成日期={}的历史数据同步,共耗时{}ms,共处理:{}条数据", date, endTime - startTime, warningEntities.size());
        }
    }

    /**
     * 执行同步
     *
     * @param warningEntity WarningEntity
     */
    private void doSync(WarningEntity warningEntity) {
        try {
            List<WarningTrackEntity> warningTrackEntities = warningTrackMapper.selectList(new QueryWrapper<WarningTrackEntity>().eq("warning_id", warningEntity.getId()));
            if (!CollectionUtils.isEmpty(warningTrackEntities)) {
                List<WarningNotifyEntity> notifyEntityList = warningNotifyMapper.selectList(new QueryWrapper<WarningNotifyEntity>().eq("warning_id", warningEntity.getId()));
                WarningOperateServiceEsImpl warningOperateServiceEs = BeanUtil.getBean(WarningOperateServiceEsImpl.class);
                warningOperateServiceEs.insert(warningTrackEntities.get(0), warningEntity, notifyEntityList);
            }
        } catch (Exception e) {
            log.error("同步预警数据到ES发生异常,预警ID={}", warningEntity.getId(), e);
        }
    }

    @Override
    public void warningEsStatusSync(Long startWarningId, Long endWarningId) {
        List<WarningProcessEntity> overdues = warningProcessMapper.getSignOverdueReplyOverdueByWarningIds(startWarningId, endWarningId);
        long startTime = System.currentTimeMillis();
        for (WarningProcessEntity overdue : overdues) {
            WarningOperateServiceEsImpl warningOperateServiceEs = BeanUtil.getBean(WarningOperateServiceEsImpl.class);
            warningOperateServiceEs.updateSignOverdueAndReplyOverdue(overdue.getWarningId(), overdue.getSignOverdue(), overdue.getReplyOverdue());
        }
        log.info("完成esWarning状态同步，耗时{}ms,共处理:{}条数据", System.currentTimeMillis() - startTime, overdues.size());
    }
}
