package com.trs.police.control.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.trs.police.common.core.constant.enums.*;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.WarningModelVO;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.common.core.entity.MonitorWarningModelEntity;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.IdNameVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.CategoryVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.common.openfeign.starter.vo.UpdatePersonRequest;
import com.trs.police.control.constant.MonitorApprovalNameConstant;
import com.trs.police.control.constant.MonitorConstant;
import com.trs.police.control.constant.enums.MonitorPermissionEnum;
import com.trs.police.control.constant.enums.MonitorTypeEnum;
import com.trs.police.control.domain.builder.MonitorListParamsBuilder;
import com.trs.police.control.domain.entity.monitor.*;
import com.trs.police.control.domain.vo.CancelControlVO;
import com.trs.police.control.domain.vo.ControlPersonVO;
import com.trs.police.control.domain.vo.RenewControlVO;
import com.trs.police.control.domain.vo.TempMonitorPersonVO;
import com.trs.police.control.domain.vo.monitor.*;
import com.trs.police.control.listener.MonitorPersonImportListener;
import com.trs.police.control.mapper.*;
import com.trs.police.control.service.*;
import com.trs.police.control.service.approval.AreaMonitorService;
import com.trs.police.control.service.approval.GroupMonitorService;
import com.trs.police.control.service.approval.PersonMonitorApprovalService;
import com.trs.police.control.service.approval.RenewApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.police.common.core.utils.JsonUtil.OBJECT_MAPPER;
import static com.trs.police.common.core.utils.JsonUtil.parseJsonNode;
import static com.trs.police.control.constant.MonitorConstant.*;

/**
 * <AUTHOR>
 * @date 2022/6/2 11:03
 */
@Slf4j
@Service
@Transactional(rollbackFor = RuntimeException.class)
public class MonitorServiceImpl implements MonitorService {

    @Resource
    private MonitorMapper monitorMapper;

    @Resource
    private MonitorTargetRelationMapper monitorTargetRelationMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private MonitorWarningModelRelationMapper monitorWarningModelRelationMapper;

    @Resource
    private MonitorWarningModelMapper monitorWarningModelMapper;

    @Resource
    private MonitorListParamsBuilder paramsBuilder;

    @Resource
    private SubscribeService subscribeService;

    @Resource
    private MonitorTargetFilterParamsMapper monitorTargetFilterParamsMapper;

    @Resource
    private ProfileService profileService;

    @Resource
    private MonitorGroupPersonRelationMapper groupPersonRelationMapper;

    @Resource
    private MonitorConfigMapper monitorConfigMapper;

    @Resource
    private ControlTaskService controlTaskService;

    @Autowired
    private PersonMonitorApprovalService personMonitorApprovalService;

    @Autowired
    private GroupMonitorService groupMonitorApprovalService;

    @Autowired
    private AreaMonitorService areaMonitorApprovalService;

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private RenewApprovalService renewApprovalService;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Long initiate(MonitorPersonVO monitorPersonVO) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        //构建公共部分
        this.initiateCommon(monitorPersonVO, currentUser, Collections.emptyList());

        //构建布控人员信息
        Long monitorId = monitorPersonVO.getMonitorId();
        List<PersonVO> personList = monitorPersonVO.getMonitorPerson().stream().map(TempMonitorPersonVO::toPersonVo)
            .collect(Collectors.toList());

        //更新档案人员信息
        for (Long personId : updatePersonToProfile(currentUser, monitorId, personList)) {
            //更新布控人员信息
            MonitorTargetRelationEntity monitorTargetRelationEntity = new MonitorTargetRelationEntity(monitorId,
                personId, MonitorBaseTypeEnum.PERSON);
            monitorTargetRelationMapper.insert(monitorTargetRelationEntity);
        }

        //新建审批
        if (!Objects.equals(monitorPersonVO.getMonitorInfo().getMonitorStatus(), MonitorStatusEnum.DRAFT.getCode())) {
            personMonitorApprovalService.buildRequestAndApproval(
                    monitorPersonVO,
                    monitorId,
                    monitorPersonVO.getMonitorInfo().getMonitorTitle(),
                    monitorPersonVO.getMonitorPersonElements(), Operation.MONITOR_INITIATE
            );
        }

        //如果携带了布控任务id，则为通过布控任务发起，修改对应布控任务的人员布控状态
        if (Objects.nonNull(monitorPersonVO.getControlTaskId())) {
            controlTaskService.personControlCallback(monitorPersonVO);
        }
        return monitorId;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Long initiate(MonitorGroupVO monitorGroupVO) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        //构建公共部分
        this.initiateCommon(monitorGroupVO, currentUser,
            monitorGroupVO.getMonitorGroup().stream().map(GroupPersonVO::getGroupId).collect(Collectors.toList()));
        //构建布控群体信息
        Long monitorId = monitorGroupVO.getMonitorId();
        //构建群体和人的关系
        for (GroupPersonVO groupPerson : monitorGroupVO.getMonitorGroup()) {
            this.initiateMonitorGroup(groupPerson, monitorId);
        }
        //新建审批
        if (!Objects.equals(monitorGroupVO.getMonitorInfo().getMonitorStatus(), MonitorStatusEnum.DRAFT.getCode())) {
            groupMonitorApprovalService.buildRequestAndApproval(
                    monitorGroupVO,
                    monitorId,
                    monitorGroupVO.getMonitorInfo().getMonitorTitle(),
                    MonitorTypeEnum.GROUP,
                    Operation.MONITOR_INITIATE
            );
        }
        return monitorId;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Long initiate(MonitorAreaVO monitorAreaVO) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        //构建公共部分
        this.initiateCommon(monitorAreaVO, currentUser, monitorAreaVO.getAreaModelIds());
        //构建布控区域信息
        Long monitorId = monitorAreaVO.getMonitorId();
        for (Long modelId : monitorAreaVO.getAreaModelIds()) {
            MonitorTargetRelationEntity relation = new MonitorTargetRelationEntity(monitorId, modelId,
                MonitorBaseTypeEnum.AREA);
            monitorTargetRelationMapper.insert(relation);
        }
        insertMonitorTargetFilterParams(monitorAreaVO, monitorId);

        //新建审批
        if (!Objects.equals(monitorAreaVO.getMonitorInfo().getMonitorStatus(), MonitorStatusEnum.DRAFT.getCode())) {
            areaMonitorApprovalService.buildRequestAndApproval(
                    monitorAreaVO,
                    monitorId,
                    monitorAreaVO.getMonitorInfo().getMonitorTitle(),
                    MonitorTypeEnum.DEFAULT,
                    Operation.MONITOR_INITIATE
            );
        }

        return monitorId;
    }

    /**
     * 将人员信息同步到人员档案中
     *
     * @param currentUser 当前用户
     * @param monitorId   布控id
     * @param personList  人员信息
     * @return 人员档案id
     */
    private List<Long> updatePersonToProfile(CurrentUser currentUser, Long monitorId, List<PersonVO> personList) {
        if (!personList.isEmpty()) {
            UpdatePersonRequest request = new UpdatePersonRequest();
            request.setUpdateUser(currentUser);
            request.setMonitorId(monitorId);
            // 分批更新人员档案数据, 避免长事务
            List<List<PersonVO>> partition = Lists.partition(personList, 80);
            List<Long> personIds = new ArrayList<>();
            for (List<PersonVO> persons : partition) {
                request.setPersonList(persons);
                personIds.addAll(profileService.updatePersonList(request));
            }
            MonitorEntity monitor = monitorMapper.selectById(monitorId);
            monitor.setProfileTargetId(personIds);
            //设置人员标签
            List<Long> personLabel = personList.parallelStream()
                .map(PersonVO::getTargetType)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
            monitor.setPersonLabel(personLabel);
            monitorMapper.updateById(monitor);
            return personIds;
        }
        return Collections.emptyList();
    }

    private void initiateCommon(MonitorVO monitorVO, CurrentUser currentUser, List<Long> relateIds) {
        MonitorEntity monitorEntity = monitorVO.toMonitorEntity(new MonitorEntity());
        if (monitorVO.getMonitorType().equals(MonitorBaseTypeEnum.PERSON) && monitorVO instanceof MonitorPersonVO) {
            //如果是人员布控的话，新增布控平台字段
            MonitorPersonVO personVO = (MonitorPersonVO) monitorVO;
            monitorEntity.setMonitorPlatform(
                    Optional.ofNullable(personVO.getMonitorInfo().getMonitorPlatform())
                            .orElse(List.of(1L)));
        }

        monitorEntity.setDeleted(Boolean.FALSE);
        //设置创建人
        monitorEntity.setMonitorPersonId(currentUser.getId());
        // 保存布控平台
        if (monitorVO.getMonitorPlatform() != null) {
            monitorEntity.setMonitorPlatform(monitorVO.getMonitorPlatform());
        }
        //设置创建人部门
        monitorEntity.setMonitorPersonUnit(currentUser.getDeptId());
        monitorEntity.setMonitorUnitCode(currentUser.getDept().getCode());
        //设置附件内容
        monitorEntity.setAttachments(Objects.isNull(monitorVO.getMonitorInfo().getAttachments())
                ? null: JSONArray.toJSONString(monitorVO.getMonitorInfo().getAttachments()));
        //默认为草稿状态
        if (monitorEntity.getMonitorStatus() == null) {
            monitorEntity.setMonitorStatus(MonitorStatusEnum.DRAFT);
        }
        //更新布控-群体/区域id关联
        monitorEntity.setProfileTargetId(relateIds);
        if (monitorMapper.insert(monitorEntity) == 0) {
            throw new TRSException(FAIL_TO_ADD_MONITOR);
        }
        //设置布控id
        monitorVO.setMonitorId(monitorEntity.getId());
        //预警模型
        this.initiateWarningModel(monitorVO);
    }

    private void initiateWarningModel(MonitorVO monitorVO) {
        Long monitorId = monitorVO.getMonitorId();
        MonitorBaseTypeEnum monitorType = monitorVO.getMonitorType();

        for (WarningModelVO modelVO : monitorVO.getWarningInfo().getWarningModel()) {
            if (modelVO == null) {
                continue;
            }
            MonitorWarningModelRelationEntity entity = new MonitorWarningModelRelationEntity();
            entity.setMonitorId(monitorId);
            entity.setWarningModelId(modelVO.getId());
            if (monitorType == MonitorBaseTypeEnum.GROUP) {
                //设置人数阈值
                entity.setPersonCount((modelVO.getPersonCount() == null) ? 2 : modelVO.getPersonCount());
                entity.setTimeCount(modelVO.getTimeCount());
                //设置聚集区域id
                List<IdNameVO> aggregationArea = modelVO.getAggregationArea();
                if (Objects.nonNull(aggregationArea)) {
                    entity.setAggregationArea(aggregationArea.stream().map(IdNameVO::getId)
                        .collect(Collectors.toList()));
                }
            }
            monitorWarningModelRelationMapper.insert(entity);
        }
    }


    private void initiateMonitorGroup(GroupPersonVO groupPerson, Long monitorId) {
        Long groupId = groupPerson.getGroupId();
        //插入布控-布控对象关系表
        monitorTargetRelationMapper.insert(
            new MonitorTargetRelationEntity(monitorId, groupId, MonitorBaseTypeEnum.GROUP));
        //查询群体追加人员信息
        List<GroupPersonRelationEntity> relations = profileService.getPersonByIds(groupPerson.getMonitorPerson())
            .stream()
            .distinct()
            .map(e -> new GroupPersonRelationEntity(monitorId, groupId, e.getCertificateNumber(),
                e.getCertificateType(), Boolean.TRUE, e.getId())).collect(Collectors.toList());
        //查询已布控人员信息
        relations.addAll(profileService.getGroupInControlPerson(groupId)
            .stream()
            .map(e -> new GroupPersonRelationEntity(monitorId, groupId, e.getCertificateNumber(),
                e.getCertificateType(), Boolean.FALSE, e.getId())).collect(Collectors.toList()));
        relations.forEach(groupPersonRelationMapper::insert);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void delete(Long id) {
        MonitorEntity monitorEntity = monitorMapper.selectById(id);
        if (Objects.isNull(monitorEntity)) {
            throw new TRSException(MONITOR_NOT_EXISTS);
        }
        MonitorStatusEnum monitorStatus = monitorEntity.getMonitorStatus();
        if (monitorStatus.equals(MonitorStatusEnum.DRAFT) || monitorStatus.equals(MonitorStatusEnum.REJECT) || monitorStatus.equals(MonitorStatusEnum.EXPIRED)) {
            monitorMapper.deleteById(id);
        } else {
            throw new TRSException(CANNOT_DELETE);
        }
    }

    private void editCommon(MonitorVO monitorVO) {
        Long monitorId = monitorVO.getMonitorInfo().getId();
        MonitorEntity oldMonitorEntity = monitorMapper.selectById(monitorId);
        if (oldMonitorEntity == null) {
            throw new TRSException(MONITOR_NOT_EXISTS);
        }
        MonitorStatusEnum oldStatus = oldMonitorEntity.getMonitorStatus();
        if (Boolean.FALSE.equals(this.idEditable(oldStatus))) {
            throw new TRSException(CURRENT_MONITOR_STATUS + oldStatus + CANNOT_MODIFY);
        }
        //布控级别从red改为非red，删除执法处置
        if (oldMonitorEntity.getMonitorLevel() == MonitorLevelEnum.RED
                && MonitorLevelEnum.codeOf(monitorVO.getMonitorInfo().getMonitorLevel()) != MonitorLevelEnum.RED) {
            oldMonitorEntity.clearMonitorConfig();
        }
        //更新布控表
        MonitorEntity updateEntity = monitorVO.toMonitorEntity(oldMonitorEntity);
        updateEntity.setExpirationDate(oldMonitorEntity.getCreateTime());
        if (monitorVO.getMonitorPlatform() != null) {
            updateEntity.setMonitorPlatform(monitorVO.getMonitorPlatform());
        }
        monitorMapper.updateById(updateEntity);
        //删除原来的预警模型关联
        monitorWarningModelRelationMapper.deleteByMonitorId(monitorId);
        //新建预警模型关系
        this.initiateWarningModel(monitorVO);
        //删除原来的布控-目标关系
        monitorTargetRelationMapper.deleteByMonitorId(monitorId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void edit(MonitorPersonVO monitorPersonVO) {
        Long monitorId = monitorPersonVO.getMonitorInfo().getId();
        //修改公共部分
        this.editCommon(monitorPersonVO);

        //删除原来的被控人信息
        monitorTargetRelationMapper.deleteByMonitorId(monitorId);
        //新建布控人信息
        List<PersonVO> personList = monitorPersonVO.getMonitorPerson().stream().map(TempMonitorPersonVO::toPersonVo)
            .collect(Collectors.toList());

        //更新档案人员信息
        for (Long personId : updatePersonToProfile(AuthHelper.getNotNullUser(), monitorId, personList)) {
            MonitorTargetRelationEntity monitorTargetRelationEntity = new MonitorTargetRelationEntity(monitorId,
                personId, MonitorBaseTypeEnum.PERSON);
            monitorTargetRelationMapper.insert(monitorTargetRelationEntity);
        }

        if (MonitorStatusEnum.codeOf(monitorPersonVO.getMonitorInfo().getMonitorStatus()) == MonitorStatusEnum.PENDING) {
            //新建审批
            personMonitorApprovalService.buildRequestAndApproval(
                    monitorPersonVO,
                    monitorId,
                    monitorPersonVO.getMonitorInfo().getMonitorTitle(),
                    monitorPersonVO.getMonitorPersonElements(),
                    Operation.MONITOR_INITIATE
            );
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void edit(MonitorGroupVO monitorGroupVO) {
        //修改公共部分
        this.editCommon(monitorGroupVO);
        Long monitorId = monitorGroupVO.getMonitorInfo().getId();
        //删除原来的群体和人的关系
        groupPersonRelationMapper.deleteByMonitorId(monitorId);
        //重新构建群体和人的关系
        for (GroupPersonVO groupPerson : monitorGroupVO.getMonitorGroup()) {
            this.initiateMonitorGroup(groupPerson, monitorId);
        }
        if (!Objects.equals(monitorGroupVO.getMonitorInfo().getMonitorStatus(), MonitorStatusEnum.DRAFT.getCode())) {
            groupMonitorApprovalService.buildRequestAndApproval(
                    monitorGroupVO,
                    monitorId,
                    monitorGroupVO.getMonitorInfo().getMonitorTitle(),
                    MonitorTypeEnum.GROUP,
                    Operation.MONITOR_INITIATE
            );
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void edit(MonitorAreaVO monitorAreaVO) {
        //设置默认预警模型
        monitorAreaVO.initWarningModel();
        //修改公共部分
        this.editCommon(monitorAreaVO);
        //修改布控区域
        Long monitorId = monitorAreaVO.getMonitorInfo().getId();
        monitorTargetRelationMapper.deleteByMonitorId(monitorId);
        for (Long areaId : monitorAreaVO.getAreaModelIds().stream().distinct().collect(Collectors.toList())) {
            monitorTargetRelationMapper.insert(
                new MonitorTargetRelationEntity(monitorId, areaId, MonitorBaseTypeEnum.AREA));
        }

        monitorTargetFilterParamsMapper.deleteByMonitorId(monitorId);
        insertMonitorTargetFilterParams(monitorAreaVO, monitorId);

        if (!Objects.equals(monitorAreaVO.getMonitorInfo().getMonitorStatus(), MonitorStatusEnum.DRAFT.getCode())) {
            areaMonitorApprovalService.buildRequestAndApproval(
                    monitorAreaVO,
                    monitorId,
                    monitorAreaVO.getMonitorInfo().getMonitorTitle(),
                    MonitorTypeEnum.DEFAULT,
                    Operation.MONITOR_INITIATE
            );
        }
    }

    private void insertMonitorTargetFilterParams(MonitorAreaVO monitorAreaVO, Long monitorId) {
        final MonitorTargetFilterParamsEntity monitorTargetFilterParamsEntity = new MonitorTargetFilterParamsEntity();
        monitorTargetFilterParamsEntity.setMonitorId(monitorId);
        final ListParamsRequest listParamsRequest = monitorAreaVO.getMonitorPerson().toListParams();
        ObjectNode result = OBJECT_MAPPER.createObjectNode();
        monitorTargetFilterParamsEntity.setIdNumbers(profileService.selectPersonIdNumbers(listParamsRequest));
        listParamsRequest.getFilterParams().forEach(
            filter -> result.set(filter.getKey(), parseJsonNode(JsonUtil.toJsonString(filter.getProcessedValue()))));
        monitorTargetFilterParamsEntity.setFilterParams(result);
        monitorTargetFilterParamsMapper.insert(monitorTargetFilterParamsEntity);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void editLevel(Long id, MonitorOperationVo operation) {
        MonitorEntity monitorEntity = monitorMapper.selectById(id);
        if (monitorEntity.getMonitorStatus() != MonitorStatusEnum.MONITORING) {
            throw new TRSException(MONITOR_LEVEL_CANNOT_MODIFY);
        }
        MonitorLevelEnum levelEnum = operation.getLevel();
        if (levelEnum.equals(MonitorLevelEnum.RED)) {
            monitorEntity.copyMonitorConfigVO(operation.getMonitorConfig());
        } else if (monitorEntity.getMonitorLevel() == MonitorLevelEnum.RED) {
            monitorEntity.clearMonitorConfig();
        }
        monitorEntity.setMonitorLevel(levelEnum);

        getEditLevelApproval(monitorEntity.getId(), levelEnum, operation);
    }

    private void getEditLevelApproval(Long monitorId, MonitorLevelEnum level, MonitorOperationVo editLevelVO) {
        ApprovalRequest request = new ApprovalRequest();
        ApprovalActionVO actionVO = new ApprovalActionVO();
        request.setApprovalConfigName(MonitorApprovalNameConstant.MONITOR_EDIT_LEVEL);
        actionVO.setAction(Operation.MONITOR_CHANGE_LEVEL);
        actionVO.setService(OperateModule.MONITOR);
        actionVO.setId(monitorId);
        actionVO.setStartFormContent(JsonUtil.toJsonString(editLevelVO));
        actionVO.setRelatedData(level.getCode().toString());
        request.setApprovalActionVO(actionVO);
        request.setUser(UserDeptVO.of(AuthHelper.getCurrentUser()));

        approvalService.startApproval(request);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void cancel(Long id, CancelControlVO vo) {
        MonitorEntity monitorEntity = monitorMapper.selectById(id);
        if (monitorEntity.getMonitorStatus() != MonitorStatusEnum.MONITORING) {
            throw new TRSException(CANNOT_CANCEL_MONITOR);
        }
        monitorEntity.setMonitorStatus(MonitorStatusEnum.EXPIRED);
        monitorEntity.setRevokeReason(vo.getRevokeReason());
        monitorEntity.setRevokeTime(LocalDateTime.now());
        monitorMapper.updateById(monitorEntity);

        //撤控时撤销所有审批
        approvalService.cancelAllApprovals(OperateModule.MONITOR.getCode(), id);

        subscribeService.cancelWarningSubscribe(id, monitorEntity.getMonitorType());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void revokeApproval(Long id) {
        MonitorEntity monitorEntity = monitorMapper.selectById(id);
        if (monitorEntity.getMonitorStatus() != MonitorStatusEnum.PENDING) {
            throw new TRSException(CANNOT_CANCEL_APPLY);
        }
        monitorEntity.setMonitorStatus(MonitorStatusEnum.DRAFT);
        //将过期时间设置成创建时间
        monitorEntity.setExpirationDate(monitorEntity.getCreateTime());
        monitorMapper.updateById(monitorEntity);

        //撤控时撤销所有审批
        approvalService.cancelAllApprovals(OperateModule.MONITOR.getCode(), id);
    }


    @Override
    public void exportList(HttpServletResponse response, ExportParams vo) throws IOException {
        String fileName = this.generateFileName(vo);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        List<MonitorExportVO> exportListVos = this.createExportListVos(vo);

        EasyExcelFactory.write(response.getOutputStream(), MonitorExportVO.class)
            .sheet(fileName)
            .includeColumnFiledNames(vo.getFieldNames())
            .doWrite(exportListVos);
    }

    /**
     * 构建导出列表
     *
     * @param params 导出参数
     * @return com.trs.police.control.domain.params.monitor.MonitorListVO
     */
    private List<MonitorExportVO> createExportListVos(ExportParams params) {
        LambdaQueryWrapper<MonitorEntity> wrapper = Wrappers.lambdaQuery(MonitorEntity.class);
        List<MonitorEntity> monitorEntities;
        if (Boolean.FALSE.equals(params.getIsAll())) {
            wrapper.in(MonitorEntity::getId, params.getIds());
            monitorEntities = monitorMapper.selectList(wrapper);
        } else {
            ListParamsRequest requestParams = params.getListParamsRequest();
            paramsBuilder.build(requestParams, MonitorConstant.EXPORT_TYPE_MY.equalsIgnoreCase(params.getType()));
            monitorEntities = monitorMapper.getNoPage(
                requestParams.getSearchParams(), requestParams.getFilterParams(),
                requestParams.getSortParams());
        }
        LinkedList<MonitorExportVO> monitorExportVos = new LinkedList<>();
        for (MonitorEntity monitorEntity : monitorEntities) {
            // 导出的布控列表
            MonitorExportVO exportVO = MonitorExportVO.of(monitorEntity);
            // 人员信息分散放到多个单元格
            if (!org.springframework.util.CollectionUtils.isEmpty(exportVO.getMonitorPersonList())) {
                for (String monitorPerson : exportVO.getMonitorPersonList()) {
                    // 复制对象
                    MonitorExportVO monitorExportVO = JSONObject.parseObject(JSONObject.toJSONString(exportVO), MonitorExportVO.class);
                    monitorExportVO.setMonitorPerson(monitorPerson);
                    monitorExportVos.add(monitorExportVO);
                }
            } else {
                // 原有导出逻辑 布控对象信息放到一个单元格中
                monitorExportVos.add(exportVO);
            }
        }
        return monitorExportVos;
    }

    private String generateFileName(ExportParams vo) {
        String type = vo.getType();
        String prefix = MonitorConstant.FILE_PREFIX_ALL;
        if (MonitorConstant.EXPORT_TYPE_MY.equalsIgnoreCase(type)) {
            prefix = MonitorConstant.FILE_PREFIX_MY;
        }
        String time = DateTimeFormatter.ofPattern("yyyyMMddHHmm").format(LocalDateTime.now());
        return URLEncoder.encode(prefix + time, StandardCharsets.UTF_8);
    }

    private Boolean idEditable(MonitorStatusEnum status) {
        return status == MonitorStatusEnum.DRAFT
            || status == MonitorStatusEnum.REJECT
            || status == MonitorStatusEnum.EXPIRED;
    }

    @Override
    public MonitorPermissionEnum checkPermission(Long monitorId) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        MonitorEntity byId = monitorMapper.selectById(monitorId);
        if (byId == null) {
            throw new TRSException(MONITOR_NOT_EXISTS);
        }
        Long monitorPersonId = byId.getMonitorPersonId();
        Long monitorPersonDept = byId.getMonitorPersonUnit();
        //本人发布的布控
        if (monitorPersonDept.equals(currentUser.getDeptId()) && monitorPersonId.equals(currentUser.getId())) {
            return MonitorPermissionEnum.MINE;
        }
        DataPermissionEnum maxDataPermission = permissionService.getCurrentUserMaxDataPermission();
        //最大权限为所有 最大权限为本部门及其子部门时且布控部门是用户部门的子部门时 最大权限为本部门且布控属于该用户部门
        if (maxDataPermission == DataPermissionEnum.ALL
            || (maxDataPermission == DataPermissionEnum.DEPT_AND_CHILD
            && (permissionService.isChild(currentUser.getDeptId(), monitorPersonDept)
            || monitorPersonDept.equals(currentUser.getDeptId()))
            || (maxDataPermission == DataPermissionEnum.DEPT && monitorPersonDept.equals(currentUser.getDeptId())))) {
            return MonitorPermissionEnum.ALL;
        }
        return MonitorPermissionEnum.NO_PERMISSION;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public MonitorImportResultVO importPerson(MultipartFile file) {
        try {
            MonitorPersonImportListener listener = new MonitorPersonImportListener();
            EasyExcelFactory.read(file.getInputStream(), ImportPersonVO.class, listener).sheet().doRead();
            return listener.getResult();
        } catch (Exception e) {
            log.error("", e);
            throw new TRSException(FAIL_TO_IMPORT);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public MonitorEntity getNotDeletedById(Long monitorId) {
        MonitorEntity monitorEntity = monitorMapper.selectById(monitorId);
        if (Objects.isNull(monitorEntity)) {
            throw new TRSException(MONITOR + monitorId + NONE_EXISTENT);
        }
        return monitorEntity;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteImportantAreaModel(Long areaId) {
        monitorWarningModelMapper.deleteByAreaId(areaId);
        monitorWarningModelRelationMapper.deleteByAreaId(areaId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateAreaModel(ImportantAreaEntity area) {
        final Long type = WarningModelTypeEnum.PERSON_IMPORTANT_AREA.getCode();
        MonitorWarningModelEntity entity = monitorWarningModelMapper.selectByAreaId(type, area.getId());
        if (Objects.isNull(entity)) {
            entity = new MonitorWarningModelEntity();
            entity.setImportantAreaId(area.getId());
            entity.setType(type);
            entity.setTitle(area.getName() + MonitorConstant.MODEL_NAME_AREA);
            entity.setDetail(entity.getTitle());
            monitorWarningModelMapper.insert(entity);
        } else {
            entity.setTitle(area.getName() + MonitorConstant.MODEL_NAME_AREA);
            entity.setDetail(entity.getTitle());
            entity.setEnableStatus(Boolean.TRUE);
            monitorWarningModelMapper.updateById(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePlaceModel(DictDto category) {
        final Long type = WarningModelTypeEnum.PERSON_PLACE.getCode();
        MonitorWarningModelEntity entity = monitorWarningModelMapper.selectByPlaceId(type, category.getCode());
        if (Objects.isNull(entity)) {
            entity = new MonitorWarningModelEntity();
            entity.setPlaceCode(category.getCode());
            entity.setType(type);
            entity.setTitle(category.getName());
            entity.setDetail(category.getName() + MonitorConstant.MODEL_NAME_PLACE);
            monitorWarningModelMapper.insert(entity);
        } else {
            entity.setTitle(category.getName());
            entity.setDetail(category.getName() + MonitorConstant.MODEL_NAME_PLACE);
            monitorWarningModelMapper.updateById(entity);
        }
    }

    @Override
    public List<WarningModelVO> getAreaOfMonitor(Long monitorId) {
        List<Long> areaModelIds = monitorTargetRelationMapper.selectByMonitorId(monitorId)
            .stream()
            .map(MonitorTargetRelationEntity::getTargetId)
            .collect(Collectors.toList());
        return monitorWarningModelMapper.getByModelIds(areaModelIds);
    }

    @Override
    public void updateAfterApproval(MonitorEntity monitor) {
        Long monitorId = monitor.getId();
        //更新人员档案
        switch (monitor.getMonitorType()) {
            case GROUP:
                List<Long> additionalIds = groupPersonRelationMapper.getAdditionalByMonitorId(monitorId);
                if (additionalIds.isEmpty()) {
                    break;
                }
                profileService.setPersonsMonitorStatus(additionalIds, MonitorPersonStatusEnum.IN_CONTROL.getCode());
                break;
            case AREA:
                final MonitorTargetFilterParamsEntity filterParamsEntity = monitorTargetFilterParamsMapper.selectByMonitorId(
                    monitorId);
                List<String> idNumbers = filterParamsEntity.getIdNumbers();

                profileService.setPersonsMonitorStatusByIdNumbers(idNumbers, MonitorPersonStatusEnum.IN_CONTROL.getCode());
                break;
            case PERSON:
            default:
                break;
        }
    }

    @Override
    public List<CategoryVO> getCategory(Long id) {
        MonitorConfigEntity config = monitorConfigMapper.getCategoryByMonitorId(id);
        return Objects.nonNull(config) ? Arrays.asList(config.getCategory()) : List.of();
    }

    @Override
    public void renew(Long id, RenewControlVO vo) {
        MonitorEntity entity = monitorMapper.selectById(id);
        MonitorStatusEnum monitorStatus = entity.getMonitorStatus();
        if (monitorStatus != MonitorStatusEnum.EXPIRED) {
            throw new TRSException("布控:" + id + "不能被续控!");
        }
        entity.setMonitorStatus(MonitorStatusEnum.PENDING);
        entity.setRenewReason(vo.getRenewReason());
        entity.setRenewTime(LocalDateTime.now());
        monitorMapper.updateById(entity);
        //发起审批
        renewApprovalService.buildRequestAndApproval(vo, id, entity.getMonitorTitle(), MonitorTypeEnum.DEFAULT, Operation.MONITOR_RENEW);
    }

    @Override
    public PageResult<ControlPersonVO> getControlPersonList(PageParams pageParams) {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        List<ControlPersonVO> list = monitorMapper.getControlPersonList(currentUser.getId(), currentUser.getDeptId());
        list.forEach(vo -> {
            PersonVO personVO = profileService.findById(vo.getId());
            vo.setImgs(personVO.getImgs());
            vo.setIdNumber(personVO.getCertificateNumber());
            vo.setName(personVO.getName());
            if (personVO.getTargetType() != null && !personVO.getTargetType().isEmpty()) {
                List<CodeNameVO> labels = profileService.getLabelByIds(personVO.getTargetType())
                    .stream().map(labelVO -> new CodeNameVO(labelVO.getId(), labelVO.getName()))
                    .collect(Collectors.toList());
                vo.setPersonLabels(labels);
            }
        });
        return PageResult.of(list, pageParams.getPageNumber(), 10L, pageParams.getPageSize());
    }
}
