package com.trs.police.control.controller;

import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.CareMonitorVO;
import com.trs.police.common.core.vo.control.CategoryVO;
import com.trs.police.common.core.vo.control.WarningDoneVO;
import com.trs.police.common.core.vo.control.WarningPhotoVO;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.control.domain.dto.fkrxyj.SignWarningDTO;
import com.trs.police.common.core.request.WarningDoneRequest;
import com.trs.police.control.domain.vo.WarningTrackPointVO;
import com.trs.police.control.domain.vo.warning.*;
import com.trs.police.control.service.FkryCareMonitorService;
import com.trs.police.control.service.WarningFkrxyjService;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/26 10:11
 */
@RestController
@RequestMapping(value = {"/warning/fkrxyj", "/public/warning/fkrxyj"})
public class WarningFkrxyjController {

    @Resource
    private WarningFkrxyjService warningFkrxyjService;

    @Resource
    private FkryCareMonitorService fkryCareMonitorService;

    /**
     * @param request 参数
     * @return 列表
     */
    @PostMapping("/list")
    public PageResult<SpecialWarningListVO> getWarningList(@RequestBody ListParamsRequest request) {
        return warningFkrxyjService.getWarningList(request);
    }

    /**
     *  手动处理fk数据
     *
     * @param timeFrameHour timeFrameHour
     * @return result
     */
    @GetMapping("/dealFK4ProcessWarning")
    public String dealFK4ProcessWarning(Long timeFrameHour) {
        boolean result = warningFkrxyjService.dealFK4ProcessWarning(timeFrameHour);
        return result ? "手动处理fk数据成功！" : "手动处理fk数据失败！";
    }

    /**
     * 预警详情
     *
     * @param warningId 预警id
     * @return 详情
     */
    @GetMapping("/{warningId}/detail")
    public WarningStxfDetailVO getWarningDetail(@PathVariable Long warningId) {
        return warningFkrxyjService.getWarningDetail(warningId);
    }

    /**
     * fk预警签收
     *
     * @param id id
     */
    @PutMapping("/{id}/sign")
    public void signWarning(@PathVariable Long id) {
        warningFkrxyjService.signWarning(id);
    }

    /**
     * fk预警签收
     *
     * @param dto dto
     */
    @PostMapping("/sign/warnings")
    public void signWarnings(@RequestBody SignWarningDTO dto) {
        warningFkrxyjService.signWarnings(dto);
    }

    /**
     * fk预警反馈
     *
     * @param id         id
     * @param feedbackVO 反馈内容
     */
    @PostMapping("/{id}/feedback")
    public void feedbackWarning(@PathVariable Long id, @RequestBody WarningFeedbackDetailFkrxyjVO feedbackVO) {
        warningFkrxyjService.feedbackWarning(id, feedbackVO);
    }

    /**
     * 预警详情-fk预警反馈删除
     *
     * @param id 预警反馈id
     */
    @DeleteMapping("/{id}/feedback")
    public void deleteWarningFeedback(@PathVariable Long id) {
        warningFkrxyjService.deleteWarningFeedback(id);
    }

    /**
     * 预警详情-fk预警反馈编辑
     *
     * @param feedbackVO 反馈内容
     * @param id         预警反馈id
     */
    @PutMapping("/{id}/feedback")
    public void updateWarningFeedback(@PathVariable Long id, @RequestBody WarningFeedbackDetailFkrxyjVO feedbackVO) {
        warningFkrxyjService.updateWarningFeedback(id, feedbackVO);
    }

    /**
     * 预警详情-预警处置
     *
     * @param warningId 预警id
     * @return {@link WarningFeedbackFkrxyjListVo}
     */
    @GetMapping("/{warningId}/feedback/list")
    public List<WarningFeedbackFkrxyjListVo> getFeedbackList(@PathVariable Long warningId) {
        return warningFkrxyjService.getFeedbackList(warningId);
    }

    /**
     * FK预警完结
     *
     * @param id      id
     * @param request 内容
     */
    @PostMapping("/{id}/done")
    public void doneWarning(@PathVariable Long id, @RequestBody WarningDoneRequest request) {
        warningFkrxyjService.doneWarning(id, request);
    }

    /**
     * 获取FK完结的预警信息
     *
     * @param id id
     * @return {@link  WarningDoneVO}
     */
    @GetMapping("/{id}/done")
    public WarningDoneFkrxyjVO getWarningDone(@PathVariable Long id) {
        return warningFkrxyjService.getWarningDone(id);
    }

    /**
     * 预警详情-轨迹撒点
     *
     * @param warningId 预警id
     * @return {@link WarningTrackPointVO}
     */
    @GetMapping("/{warningId}/detail/track/point")
    public List<WarningTrackPointVO> getTrackPoint(@PathVariable Long warningId) {
        return warningFkrxyjService.getTrackPoint(warningId);
    }

    /**
     * 预警详情-流程
     *
     * @param warningId 预警id
     * @return 预警流程
     */
    @GetMapping("/{warningId}/process")
    public WarningProcessVO getWarningProcess(@PathVariable Long warningId) {
        return warningFkrxyjService.getWarningProcess(warningId);
    }

    /**
     * 预警活动时间、地点
     *
     * @param warningId 预警id
     * @return vo
     */
    @GetMapping("/detail/track/{warningId}/activity")
    public WarningActivityVO getWarningActivity(@PathVariable Long warningId) {
        return warningFkrxyjService.getWarningActivity(warningId);
    }

    /**
     * 预警活动照片
     *
     * @param warningId 预警id
     * @return 照片
     */
    @GetMapping("/detail/track/{warningId}/photo")
    public WarningPhotoVO getWarningPhoto(@PathVariable Long warningId) {
        return warningFkrxyjService.getWarningPhoto(warningId);
    }

    /**
     * 预警活动人员
     *
     * @param warningId 轨迹id
     * @return 人员
     */
    @GetMapping("/detail/track/{warningId}/person")
    public PersonCardVO getWarningPerson(@PathVariable Long warningId) {
        return warningFkrxyjService.getWarningPerson(warningId);
    }

    /**
     * 预警详情-模块列表
     *
     * @param trackId 预警id
     * @return 模块列表
     */
    @GetMapping("/detail/track/{trackId}/category")
    public List<CategoryVO> getWarningCategory(@PathVariable Long trackId) {
        return warningFkrxyjService.getWarningCategory(trackId);
    }

    /**
     * 预警详情-重点区域
     *
     * @param trackId 轨迹id
     * @return 区域
     */
    @GetMapping("/detail/track/{trackId}/area")
    public List<WarningAreaVO> getImportantArea(@PathVariable Long trackId) {
        return Collections.emptyList();
    }


    /**
     *  手动处理fk数据
     *
     * @return result
     */
    @GetMapping("/silenceWarning")
    public String silenceWarning() {
        boolean result = warningFkrxyjService.silenceWarning();
        return result ? "手动执行静默模型成功！" : "手动执行静默模型失败！";
    }

    /**
     * 历史fk预警签收
     *
     * @param createTime 创建时间
     */
    @GetMapping("/sign/history/warnings")
    public void signHistoryWarnings(String createTime) {
        warningFkrxyjService.signHistoryWarnings(createTime);
    }


    /**
     * fk人员发起关注监控
     *
     * @param careMonitorVO careMonitorVO
     * @return {@link RestfulResultsV2}<{@link String}>
     */
    @GetMapping("/fkryCareMonitor")
    public RestfulResultsV2<String> fkryCareMonitor(CareMonitorVO careMonitorVO) {
        fkryCareMonitorService.fkryCareMonitor(careMonitorVO);
        return RestfulResultsV2.ok("fk人员发起关注监控成功！");
    }
}
