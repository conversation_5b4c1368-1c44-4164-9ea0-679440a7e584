package com.trs.police.control.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.vo.profile.ListSourceVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 车辆信息
 *
 * <AUTHOR>
 * @date 2022/12/27 10:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_profile_vehicle", autoResultMap = true)
public class ProfileVehicleEntity extends AbstractBaseEntity {

    private static final long serialVersionUID = -1084261102696513928L;
    /**
     * 类型
     */
    private Long type;
    /**
     * 车牌号
     */
    private String carNumber;
    /**
     * 所属人
     */
    private String owner;
    /**
     * 来源（手动录入、自动同步）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ListSourceVO source;
    /**
     * 是否删除
     */
    private Boolean deleted;

    private String yqbkzj;

    private String zjhm;
}
