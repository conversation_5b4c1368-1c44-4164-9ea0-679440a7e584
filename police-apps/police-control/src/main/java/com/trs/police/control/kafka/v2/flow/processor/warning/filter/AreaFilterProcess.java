package com.trs.police.control.kafka.v2.flow.processor.warning.filter;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.service.CommonMapValueReader;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 地狱过滤
 */
@Component
public class AreaFilterProcess implements FilterProcess{

    private final CommonMapValueReader reader = new CommonMapValueReader();

    @Override
    public Boolean filter(WarningMessageContext context) {
        if (Objects.isNull(context.getTrackVO())) return false;
        if (Objects.isNull(context.getTrackVO().getPerson())) return false;
        if (Objects.isNull(context.getTrackVO().getTrackDetail())) return false;
        PersonVO person = context.getTrackVO().getPerson();
        Map<String, Object> trackDetail = context.getTrackVO().getTrackDetail();
        //获取人员户籍编号
        String personHjArea = StringUtils.isEmpty(person.getRegisteredResidence()) ? "" : person.getRegisteredResidence();
        //获取活动地址编号
        String hdfsddxzqhPath = BeanFactoryHolder.getEnv().getProperty("com.trs.warningConsume.areaFilter.hdfsddxzqhPath"
                ,"$.trackDetail.data.hdfsddxzqh");
        Optional<String> result = reader.readValueFromMap(hdfsddxzqhPath, trackDetail);
        String hdfsddxzqh = result.get();
        if (StringUtils.isEmpty(hdfsddxzqh)){
            return false;
        }
        String areaPrefix = BeanFactoryHolder.getEnv().getProperty("com.trs.warningConsume.areaFilter.areaPrefix"
                ,"");
        return personHjArea.startsWith(areaPrefix) || hdfsddxzqh.startsWith(areaPrefix);
    }

    @Override
    public Boolean isExecute() {
        return BeanFactoryHolder.getEnv().getProperty("com.trs.warningConsume.areaFilter.switch",
                Boolean.class,Boolean.FALSE);
    }

    /**
     * 优化过滤代码
     *
     * @return 结果
     */
    public boolean method() {
        WarningMessageContext context = new WarningMessageContext();
        return Optional.ofNullable(context.getTrackVO())
                .map(GroupWarningDTO.Track::getPerson)
                .filter(person -> !StringUtils.isEmpty(person.getRegisteredResidence()))
                .map(person -> {
                    String personHjArea = person.getRegisteredResidence();
                    Map<String, Object> trackDetail = context.getTrackVO().getTrackDetail();
                    String hdfsddxzqhPath = BeanFactoryHolder.getEnv().getProperty("com.trs.warningConsume.areaFilter.hdfsddxzqhPath", "$.trackDetail.data.hdfsddxzqh");
                    Optional<String> result = reader.readValueFromMap(hdfsddxzqhPath, trackDetail);
                    String hdfsddxzqh = result.orElse("");
                    String areaPrefix = BeanFactoryHolder.getEnv().getProperty("com.trs.warningConsume.areaFilter.areaPrefix", "");
                    return personHjArea.startsWith(areaPrefix) || hdfsddxzqh.startsWith(areaPrefix);
                })
                .orElse(false); // 如果任何一步为 null，则返回 false
    }
}
