package com.trs.police.control.domain.vo.warning;

import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorBaseTypeEnum;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.entity.MonitorWarningModelEntity;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.control.WarningSourceVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.domain.entity.warning.ControlEsWarning;
import com.trs.police.control.domain.entity.warning.InductiveControlEsWarning;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.mapper.*;
import com.trs.police.control.properties.WarningLevelDisplayProperties;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 预警详情vo
 *
 * <AUTHOR>
 */
@Data
public class WarningDetailVO implements Serializable {

    private static final long serialVersionUID = 5947464565052339424L;
    /**
     *
     */
    private String id;
    /**
     * 预警详情
     */
    private String content;
    /**
     * 预警状态
     */
    private CodeNameVO warningStatus;
    /**
     * 模型
     */
    private List<String> model;
    /**
     * 预警时间
     */
    private String warningTime;
    /**
     * 布控id
     */
    private Long monitorId;
    /**
     * 布控名称
     */
    private String monitorName;

    /**
     * 类型（人员/群体）
     */
    private MonitorBaseTypeEnum targetType;

    /**
     * 预警类型名称
     */
    private String targetTypeName;

    /**
     * 轨迹id（人员预警）
     */
    private Long trackId;
    /**
     * 管控类型
     */
    private Integer controlType;

    /**
     * 管控类型名称
     */
    private String controlTypeName;

    private CodeNameVO warningLevel;

    /**
     * 发文字号 云控only
     */
    private String fwzh;

    /**
     * 首次处置反馈时限 云控only
     */
    private String scczfksx;

    /**
     * 签收时限 云控only
     */
    private String qssx;

    /**
     * 处置措施要求 云控only
     */
    private String czcsyq;

    /**
     * 发布责任单位名称 云控only
     */
    private String fbzrdw;

    /**
     * 预警平台
     */
    private Integer warningPlatform;

    /**
     * 被布控人姓名
     */
    private String personName;

    /**
     * 身份证类型
     */
    private Integer personIdType;

    /**
     * 证件号码
     */
    private String personIdNumber;

    /**
     * 人员id
     */
    private Long personId;

    /**
     * 活动时间
     */
    private String activityTime;

    /**
     * 活动地址
     */
    private String activityAddress;

    /**
     * 活动发送单位区划名称
     */
    private String hdfsddqhmc;

    /**
     * 预警指令ID 云控only
     */
    private String ywzjid;

    /**
     * 活动发生地所属社会场所 云控only
     */
    private String hdfsddshcs;

    /**
     * 布控平台
     */
    private List<Long> monitorPlatform;

    /**
     * 处置单位名称
     */
    private String controlDeptName;
    /**
     * 处置单位名称
     */
    private String  controlDeptCode;

    /**
     * 布控人员标签
     */
    private List<String> personLabel;

    /**
     * 布控事由
     */
    private String monitorReason;

    /**
     * 感知源
     */
    private WarningSourceVO source;

    /**
     * 预警entity转换vo
     *
     * @param entity {@link WarningEntity}
     * @return {@link WarningDetailVO}
     */
    public static WarningDetailVO toDetailVO(WarningEntity entity) {
        WarningDetailVO vo = new WarningDetailVO();
        vo.setId(String.valueOf(entity.getId()));
        vo.setContent(entity.getContent());
        WarningProcessMapper warningProcessMapper = BeanUtil.getBean(WarningProcessMapper.class);
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        WarningProcessEntity process = warningProcessMapper.getUserProcessByWarningId(entity.getId(),
                currentUser.getUserId(), currentUser.getDeptId());
      if(Objects.nonNull(process)){
          vo.setWarningStatus(new CodeNameVO(process.getStatus().getCode(), process.getStatus().getName()));
      }else {
          if(Boolean.TRUE.equals(warningProcessMapper.getWarningDoneStatus(entity.getId()))){
              vo.setWarningStatus(new CodeNameVO(4, "已完结"));
          }else {
              vo.setWarningStatus(new CodeNameVO(5, "处置中"));
          }
      }

        MonitorWarningModelMapper monitorWarningModelMapper = BeanUtil.getBean(MonitorWarningModelMapper.class);
        List<String> models = monitorWarningModelMapper.selectBatchIds(entity.getModelId()).stream()
                .map(monitorWarningModelMapper::selectById)
                .filter(MonitorWarningModelEntity::getEnableStatus)
                .map(MonitorWarningModelEntity::getTitle)
                .collect(Collectors.toList());
        vo.setModel(models);
        vo.setWarningTime(entity.getWarningTime().format(TimeUtil.CN_TIME_PATTERN));
        vo.setMonitorId(entity.getMonitorId());
        vo.setControlType(entity.getControlType().getCode());


        ControlInfo c = ControlInfo.getControlInfo(entity.getMonitorId(), entity.getControlType());
        vo.setMonitorName(c.getName());
        vo.setTargetType(c.getTargetType());
        if (MonitorBaseTypeEnum.PERSON.equals(vo.getTargetType()) || MonitorBaseTypeEnum.AREA.equals(vo.getTargetType())) {
            WarningTrackMapper warningTrackMapper = BeanUtil.getBean(WarningTrackMapper.class);
            vo.setTrackId(warningTrackMapper.selectTrackByWarningId(entity.getId()).getId());
        }
        WarningLevelDisplayProperties displayProperties = BeanUtil.getBean(WarningLevelDisplayProperties.class);
        vo.setWarningLevel(new CodeNameVO(entity.getWarningLevel(), displayProperties.getDisplayMap().get(entity.getWarningLevel().getCode())));
        return vo;
    }

    /**
     * 预警entity转换vo
     *
     * @param entity {@link WarningEntity}
     * @return {@link WarningDetailVO}
     */
    public static WarningDetailVO esEntityToDetailVO(ControlEsWarning entity) {
        WarningDetailVO vo = new WarningDetailVO();
        vo.setId(String.valueOf(entity.getId()));
        vo.setContent(entity.getContent());
        WarningProcessMapper warningProcessMapper = BeanUtil.getBean(WarningProcessMapper.class);
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        WarningProcessEntity process = warningProcessMapper.getUserProcessByWarningId(entity.getId(),
                currentUser.getUserId(), currentUser.getDeptId());
        if (Objects.nonNull(process)) {
            vo.setWarningStatus(new CodeNameVO(process.getStatus().getCode(), process.getStatus().getName()));
        } else {
            if (Boolean.TRUE.equals(warningProcessMapper.getWarningDoneStatus(entity.getId()))) {
                vo.setWarningStatus(new CodeNameVO(4, "已完结"));
            } else {
                vo.setWarningStatus(new CodeNameVO(5, "处置中"));
            }
        }

        MonitorWarningModelMapper monitorWarningModelMapper = BeanUtil.getBean(MonitorWarningModelMapper.class);
        List<Long> modelIds = StringUtil.splitToLongList(entity.getModelId());
        List<String> models = monitorWarningModelMapper.selectBatchIds(modelIds).stream()
                .map(monitorWarningModelMapper::selectById)
                .filter(MonitorWarningModelEntity::getEnableStatus)
                .map(MonitorWarningModelEntity::getTitle)
                .collect(Collectors.toList());
        vo.setModel(models);
        vo.setWarningTime(TimeUtils.dateToString(entity.getWarningTime(), "M月d日HH:mm:ss"));
        vo.setMonitorId(entity.getMonitorId());
        vo.setControlType(entity.getControlType());


        ControlInfo c = ControlInfo.getControlInfo(entity.getMonitorId(), ControlTypeEnum.codeOf(entity.getControlType()));
        vo.setMonitorName(c.getName());
        vo.setTargetType(c.getTargetType());
        vo.setMonitorReason(c.getMonitorReason());
        if (!CollectionUtils.isEmpty(c.getPersonLabel())) {
            LabelMapper labelMapper = BeanUtil.getBean(LabelMapper.class);
            vo.setPersonLabel(labelMapper.selectBatchIds(c.getPersonLabel()).stream()
                    .map(Label::getName)
                    .collect(Collectors.toList()));
        }
        if (MonitorBaseTypeEnum.PERSON.equals(vo.getTargetType()) || MonitorBaseTypeEnum.AREA.equals(vo.getTargetType())) {
            WarningTrackMapper warningTrackMapper = BeanUtil.getBean(WarningTrackMapper.class);
            WarningTrackEntity trackEntity = warningTrackMapper.selectTrackByWarningId(entity.getId());
            vo.setTrackId(trackEntity.getId());
        }
        WarningLevelDisplayProperties displayProperties = BeanUtil.getBean(WarningLevelDisplayProperties.class);
        vo.setWarningLevel(new CodeNameVO(entity.getWarningLevel(), displayProperties.getDisplayMap().get(entity.getWarningLevel())));
        // 云控信息
        vo.setFwzh(entity.getFwzh());
        vo.setScczfksx(Objects.isNull(entity.getScczfksx()) ? null : TimeUtils.dateToString(entity.getScczfksx(), TimeUtils.YYYYMMDD_HHMMSS));
        vo.setQssx(Objects.isNull(entity.getQssx()) ? null : TimeUtils.dateToString(entity.getQssx(), TimeUtils.YYYYMMDD_HHMMSS));
        vo.setCzcsyq(StringUtil.isEmpty(entity.getCzcsyq())
                ? displayProperties.getLevelCzcsyqMap().get(entity.getWarningLevel())
                : entity.getCzcsyq());
        vo.setFbzrdw(entity.getFbzrdw());
        vo.setWarningPlatform(entity.getWarningPlatform());
        vo.setPersonName(entity.getPersonName());
        vo.setPersonIdNumber(entity.getPersonIdNumber());
        vo.setPersonIdType(entity.getPersonIdType());
        vo.setActivityTime(Objects.isNull(entity.getActivityTime()) ? null : TimeUtils.dateToString(entity.getActivityTime(), TimeUtils.YYYYMMDD_HHMMSS));
        vo.setActivityAddress(entity.getActivityAddress());
        vo.setHdfsddqhmc(entity.getHdfsddqhmc());
        vo.setMonitorPlatform(StringUtil.splitToLongList(entity.getMonitorPlatform()));
        vo.setControlDeptCode(entity.getMonitorUnitCode());
        vo.setYwzjid(entity.getYwzjid());
        vo.setHdfsddshcs(entity.getHdfsddshcs());
        vo.setPersonId(buildPersonId(entity.getPersonIdType(), entity.getPersonIdNumber()));
        return vo;
    }


    /**
     * Es预警entity转换vo
     *
     * @param entity {@link WarningEntity}
     * @return {@link WarningDetailVO}
     */
    public static WarningDetailVO esWarningToDetailVO(InductiveControlEsWarning entity) {
        WarningDetailVO vo = new WarningDetailVO();
        vo.setId(entity.getId());
        vo.setContent(entity.getWarningContent());
//        if (Objects.nonNull(entity.getMonitorStatus())){
//            vo.setWarningStatus(new CodeNameVO(entity.getMonitorStatus(), MonitorStatusEnum.codeOf(entity.getMonitorStatus()).getName()));
//        }
        vo.setModel(Collections.singletonList(entity.getWarningModelName()));
        vo.setWarningTime(TimeUtils.dateToString(entity.getWarningTime(), "M月d日HH:mm:ss"));
        //vo.setControlTypeName(entity.getControlType());
        WarningLevelDisplayProperties displayProperties = BeanUtil.getBean(WarningLevelDisplayProperties.class);
        if (Objects.nonNull(entity.getWarningLevel())){
            vo.setWarningLevel(new CodeNameVO(entity.getWarningLevel(), displayProperties.getDisplayMap().get(entity.getWarningLevel())));
        }
        vo.setPersonName(entity.getIdentifierName());
        vo.setPersonIdNumber(entity.getIdentifier());
        vo.setControlType(1);
        vo.setPersonIdType(entity.getIdentifierType());
        vo.setActivityTime(Objects.isNull(entity.getActivityTime()) ? null : TimeUtils.dateToString(entity.getActivityTime(), TimeUtils.YYYYMMDD_HHMMSS));
        vo.setActivityAddress(entity.getActivityAddress());
        vo.setTargetType(MonitorBaseTypeEnum.codeOf(entity.getWarningType()));
        if (Objects.nonNull(entity.getWarningType())){
            vo.setTargetTypeName(MonitorBaseTypeEnum.codeOf(entity.getWarningType()).getName());
        }
        return vo;
    }



    /**
     * 构建人员id
     *
     * @param idType idType
     * @param idNumber idNumber
     * @return personId
     */
    public static Long buildPersonId(Integer idType, String idNumber) {
        if(idNumber!=null){
            ProfileService profileService = BeanFactoryHolder.getBean(ProfileService.class).get();
            return profileService.findIdByIdTypeAndIdNumber(idType==null?1:idType, idNumber);
        }

        return null;
    }
}
