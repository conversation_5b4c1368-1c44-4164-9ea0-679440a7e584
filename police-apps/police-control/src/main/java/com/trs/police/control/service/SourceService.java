package com.trs.police.control.service;

import com.trs.police.common.core.dto.Source;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.ModelTreeVO;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.control.domain.request.PointRequest;
import com.trs.police.control.domain.vo.basic.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @author: zhou.youpeng
 * @date: 2022/9/1 16:09
 */
public interface SourceService {

    /**
     * 更新感知元信息
     *
     * @param sourceId 感知源id
     * @param sourceVO 感知元信息
     */
    void updateById(Long sourceId, SourceVO sourceVO);

    /**
     * 获取感知源基本信息
     *
     * @param sourceId 感知源id
     * @return {@link  SourceBasicVO}
     */
    SourceBasicVO getSourceInfo(Long sourceId);

    /**
     * 获取感知源列表
     *
     * @param request 请求参数
     * @return {@link  SourceListVO}
     */
    PageResult<SourceListVO> getSourcePageList(ListParamsRequest request);

    /**
     * 统计区域内感知源数据
     *
     * @param geometryList {@link  GeometryVO}
     * @return {@link  AreaOfSourceVO}
     */
    AreaOfSourceVO areaOfSource(List<GeometryVO> geometryList);

    /**
     * 获取区域内感知源数据
     *
     * @param geometryList {@link  GeometryVO}
     * @return {@link  SourceListVO}
     */
    List<SourceListVO> getAreaOfSourceList(List<GeometryVO> geometryList);

    /**
     * 获取区域内感知源数量<BR>
     *
     * @param geometryList 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/24 14:31
     */
    Long getAreaOfSourceSize(List<GeometryVO> geometryList);

    /**
     * 获取区域内感知源数据
     *
     * @param request {@link  GeometryVO}
     * @return {@link  SourceListVO}
     */
    List<SourceListVO> getSourcePointList(PointRequest request);

    /**
     * 批量更新
     *
     * @param sourceVO {@link  SourceBatchRequestVO}
     */
    void batchUpdate(SourceBatchRequestVO sourceVO);

    /**
     * 感知源列表（部分也）
     *
     * @param request 参数
     * @return {@link SourceListVO}
     */
    List<SourceListVO> getSourceList(ListParamsRequest request);

    /**
     * 批量导出感知源
     *
     * @param response              响应体
     * @param sourceExportRequestVO 请求参数
     * @throws IOException io
     */
    void exportSource(SourceExportRequestVO sourceExportRequestVO, HttpServletResponse response) throws IOException;

    /**
     * 从moye同步感知元
     */
    void synchronizeSource();

    /**
     * 根据感知源名称搜索id
     *
     * @param name 名称
     * @return id
     */
    Long getSourceIdByName(String name);

    /**
     * 同步感知源
     *
     * @param source 感知源
     */
    void saveOrUpdateSource(List<Source> source);

    /**
     * 获取感知源类型树
     *
     * @return 感知源类型树
     */
    List<ModelTreeVO> getSourceTypeTree();

    /**
     * 根据enName获取warningSourceType
     *
     * @param enName enName
     * @return warningSourceType
     */
    WarningSourceTypeEntity getWarningSourceTypeByEnName(String enName);

    /**
     * 获取WarningSourceTypeEntity
     *
     * @return warningSourceType
     */
    List<WarningSourceTypeEntity> getCnNameByEnName();

    /**
     * 同步tw视频监控源
     */
    void syncTwVideoMonitor();

    /**
     * 根据部门获取感知源列表
     *
     * @param request 请求参数
     * @return {@link DeptVO}
     */
    List<DeptVO> listWithDept(ListParamsRequest request);
}
