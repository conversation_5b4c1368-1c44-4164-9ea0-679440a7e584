package com.trs.police.control.kafka.v2.flow.processor.warning.postprocess;

import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.mapper.WarningTrackMapper;
import com.trs.police.control.service.impl.WarningProcessServiceImpl;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 预警轨迹入轨迹库
 * *@author:wen.wen
 * *@create 2025-01-24 13:36
 **/
@Component
@Slf4j
public class WarningTrackInsertProcessor implements IWarningPostProcessor {

    @Resource
    private WarningTrackMapper warningTrackMapper;

    @Resource
    protected WarningProcessServiceImpl warningProcessServiceImpl;

    @Override
    public WarningMessageContext.WarningResultContext process(WarningMessageContext warningMessageContext, WarningMessageContext.WarningResultContext context) {
        try {
            ControlInfo controlInfo = warningMessageContext.getControlInfo();
            WarningEntity warning = context.getWarningEntity();
            WarningTrackEntity track = warningProcessServiceImpl.setTrack(warningMessageContext.getTrackVO(), warning.getId());
            track.setMonitorId(controlInfo.getId());
            track.setControlType(controlInfo.getType());
            String needReConsumer = BeanFactoryHolder.getEnv().getProperty("kafka.warning.person.needReConsumer", "false");
            // 轨迹入库排重
            if (Boolean.parseBoolean(needReConsumer)) {
                List<WarningTrackEntity> warningTrackEntities = warningTrackMapper.selectTrackForCheck(track.getPersonId(), track.getAddress(), track.getActivityTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                if (CollectionUtils.isEmpty(warningTrackEntities)) {
                    // 重跑时没有再入库
                    warningTrackMapper.insert(track);
                }
            } else {
                warningTrackMapper.insert(track);
            }
            context.setWarningTrackEntity(track);
            log.info("预警入库：{}", warning);
        } catch (Exception e) {
            log.error("WarningTrackInsertProcessor 发生异常", e);
        }
        return context;
    }

    @Override
    public Integer order() {
        return 1;
    }
}
