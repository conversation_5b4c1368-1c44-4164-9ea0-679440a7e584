package com.trs.police.control.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.Data;

/**
 * 档案配置
 *
 * <AUTHOR>
 * @date 2024/06/07
 */
@Data
@TableName("t_profile_archives_config")
public class ArchivesConfig extends AbstractBaseEntity {
    /**
     * 分类
     */
    @TableField(value = "category")
    private String category;

    /**
     * 是否启用
     */
    @TableField(value = "is_enable")
    private Long isEnable;

    /**
     * 警情类型
     */
    @TableField(value = "jq_type")
    private Long jqType;

    /**
     * 大屏名称
     */
    @TableField(value = "display_name")
    private String displayName;

    /**
     * 分数
     */
    @TableField(value = "score")
    private Float score;

    /**
     * 分类id
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * 排序
     */
    @TableField(value = "position")
    private Integer position;
}
