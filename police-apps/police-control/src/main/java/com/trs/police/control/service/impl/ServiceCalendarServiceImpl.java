package com.trs.police.control.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.utils.DateUtil;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.control.domain.dto.BatchEditCalendarDTO;
import com.trs.police.control.domain.entity.ServiceCalendarEntity;
import com.trs.police.control.domain.entity.ServiceConfigEntity;
import com.trs.police.control.domain.vo.Mgr;
import com.trs.police.control.domain.vo.ServiceCalenderListVO;
import com.trs.police.control.domain.vo.TargetCalendarVO;
import com.trs.police.control.mapper.RegularMonitorMapper;
import com.trs.police.control.mapper.ServiceCalendarMapper;
import com.trs.police.control.mapper.ServiceConfigMapper;
import com.trs.police.control.service.ServiceCalendarService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ServiceCalendarServiceImpl
 *
 * <AUTHOR>
 * @Date 2023/6/27 11:49
 */
@Service
public class ServiceCalendarServiceImpl implements ServiceCalendarService {

    @Resource
    private ServiceCalendarMapper serviceCalendarMapper;
    @Resource
    private ServiceConfigMapper serviceConfigMapper;
    @Resource
    private DictService dictService;
    @Resource
    private RegularMonitorMapper regularMonitorMapper;

    @Override
    public List<ServiceCalenderListVO> getServiceCalendar(TimeParams timeParams) {
        return serviceCalendarMapper.getServiceCalendarList(timeParams.getBeginTime(), timeParams.getEndTime()).stream()
                .map(item -> {
                    ServiceCalenderListVO vo = new ServiceCalenderListVO();
                    vo.setTime(item.getTime());
                    vo.setServiceLevel(getServiceCalendarByTime(item.getTime()));
                    vo.setMgr(item.getMgr());
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public void editServiceCalendar(ServiceCalendarEntity entity, Boolean editSingle) {
        LocalDateTime time = entity.getTime().toLocalDate().atStartOfDay();
        ServiceCalendarEntity serviceCalendar = serviceCalendarMapper.getServiceCalendar(time);
        if (Objects.isNull(serviceCalendar)) {
            doSave(time, entity);
        } else {
            if (Objects.nonNull(entity.getLevel())) {
                serviceCalendar.setLevel(entity.getLevel());
            }
            if (editSingle) {
                //XMKFB-2430 此时若参数mgr为空 即删除对应日期的敏感日信息
                serviceCalendar.setMgr(Optional.ofNullable(entity.getMgr()).orElse(""));
            } else {
                if (StringUtils.isNotEmpty(entity.getMgr())) {
                    if (StringUtils.isNotEmpty(serviceCalendar.getMgr())) {
                        serviceCalendar.setMgr(serviceCalendar.getMgr() + ";" + entity.getMgr());
                    } else {
                        serviceCalendar.setMgr(entity.getMgr());
                    }
                }
            }
            doEditServiceCalendar(serviceCalendar);
        }
    }

    private void doSave(LocalDateTime time, ServiceCalendarEntity entity) {
        entity.setTime(time);
        entity.setDoneRegularId(regularMonitorMapper.getDoneRegularIdByTime(time));
        entity.setNoNeedDoneRegularId(regularMonitorMapper.getNoNeedDoneRegularIdByTime(time));
        serviceCalendarMapper.insert(entity);
    }

    /**
     * 更新serviceCalendar
     *
     * @param entity 参数
     */
    @Override
    public void doEditServiceCalendar(ServiceCalendarEntity entity) {
        LocalDateTime time = entity.getTime().toLocalDate().atStartOfDay();
        ServiceCalendarEntity serviceCalendar = serviceCalendarMapper.getServiceCalendar(time);
        if (Objects.isNull(serviceCalendar)) {
            doSave(time, entity);
        } else {
            //先更新level，再更新doneRegularId
            serviceCalendar.setLevel(entity.getLevel());
            if (Objects.nonNull(entity.getMgr())) {
                serviceCalendar.setMgr(entity.getMgr());
            }
            serviceCalendarMapper.updateById(serviceCalendar);

            serviceCalendar.setDoneRegularId(regularMonitorMapper.getDoneRegularIdByTime(time));
            serviceCalendar.setNoNeedDoneRegularId(regularMonitorMapper.getNoNeedDoneRegularIdByTime(time));
            serviceCalendarMapper.updateById(serviceCalendar);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editServiceConfig(List<ServiceConfigEntity> entity) {
        serviceConfigMapper.delete(null);
        entity.forEach(serviceConfigMapper::insert);
    }

    @Override
    public List<ServiceConfigEntity> getServiceConfig() {
        return serviceConfigMapper.selectList(null);
    }

    @Override
    public DictDto getServiceCalendarByTime(LocalDateTime time) {
        time = time.toLocalDate().atStartOfDay();
        Long serviceCalendarLevel = serviceCalendarMapper.getServiceCalendarLevel(time);
        return dictService.getDictByTypeAndCode("service_calendar_level",
                Objects.isNull(serviceCalendarLevel) ? 4L : serviceCalendarLevel);

    }

    @Override
    public void updateTodayServiceCalendar() {
        final LocalDateTime time = LocalDateTime.now().toLocalDate().atStartOfDay();
        ServiceCalendarEntity serviceCalendar = serviceCalendarMapper.getServiceCalendar(time);
        if (Objects.isNull(serviceCalendar)) {
            serviceCalendar = new ServiceCalendarEntity();
            serviceCalendar.setTime(time);
            serviceCalendar.setLevel(4L);
        }
        this.doEditServiceCalendar(serviceCalendar);
    }

    @Override
    public String batchEditLevel(BatchEditCalendarDTO dto) {
        PreConditionCheck.checkNotNull(dto.getLevel(), "勤务等级不能为空！");
        for (String dateTime : dto.getTimes().split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON)) {
            LocalDateTime time = DateUtil.utcToLocalDateTime(Long.parseLong(dateTime));
            ServiceCalendarEntity entity = new ServiceCalendarEntity();
            entity.setTime(time);
            entity.setLevel(dto.getLevel());
            editServiceCalendar(entity, false);
        }
        return "操作成功";
    }

    @Override
    public String batchEditMinGanRi(BatchEditCalendarDTO dto) {
        PreConditionCheck.checkNotEmpty(dto.getMgr(), "敏感日不能为空！");
        for (String dateTime : dto.getTimes().split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON)) {
            LocalDateTime time = DateUtil.utcToLocalDateTime(Long.parseLong(dateTime));
            ServiceCalendarEntity entity = new ServiceCalendarEntity();
            entity.setTime(time);
            entity.setMgr(dto.getMgr());
            editServiceCalendar(entity, false);
        }
        return "操作成功";
    }

    @Override
    public TargetCalendarVO getTargetCalendar(LocalDateTime time) {
        PreConditionCheck.checkNotNull(time, "时间戳不能为空！");
        LocalDateTime dateTime = time.toLocalDate().atStartOfDay();
        ServiceCalendarEntity serviceCalendar = serviceCalendarMapper.getServiceCalendar(dateTime);
        if (Objects.isNull(serviceCalendar)) {
            serviceCalendar = new ServiceCalendarEntity();
            //此时未设置勤务日期
            serviceCalendar.setLevel(0L);
        }
        TargetCalendarVO result = new TargetCalendarVO();
        result.setLevel(serviceCalendar.getLevel());
        List<Mgr> list = new ArrayList<>(0);
        if (StringUtils.isNotEmpty(serviceCalendar.getMgr())) {
            for (String name : serviceCalendar.getMgr().split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON)) {
                Mgr mgr = new Mgr();
                mgr.setTime(time);
                mgr.setName(name);
                mgr.setIsToday(1);
                list.add(mgr);
            }
        } else {
            //XMKFB-1970   如果对应日期的勤务日期中不存在敏感日的话，就查询离他最近的敏感日
            ServiceCalendarEntity data = serviceCalendarMapper.selectRecentMgr(time);
            if (!Objects.isNull(data)) {
                for (String name : data.getMgr().split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON)) {
                    Mgr mgr = new Mgr();
                    mgr.setTime(data.getTime());
                    mgr.setName(name);
                    mgr.setIsToday(0);
                    list.add(mgr);
                }
            }
        }
        result.setMgr(list);
        return result;
    }
}
