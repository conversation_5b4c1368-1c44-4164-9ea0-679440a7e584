package com.trs.police.control.kafka.v2.flow.processor.warning.postprocess;

import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.mapper.MonitorMapper;
import com.trs.police.control.mapper.RegularMonitorMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 更新最后预警时间
 * *@author:wen.wen
 * *@create 2025-01-24 13:31
 **/
@Slf4j
@Component
public class UpdateLastWarningTimeProcessor implements IWarningPostProcessor {

    @Resource
    private MonitorMapper monitorMapper;
    @Resource
    private RegularMonitorMapper regularMonitorMapper;

    @Override
    public WarningMessageContext.WarningResultContext process(WarningMessageContext warningMessageContext, WarningMessageContext.WarningResultContext context) {
        try {
            //更新布控最后预警时间
            if (warningMessageContext.getControlInfo().getType().equals(ControlTypeEnum.MONITOR)) {
                monitorMapper.updateMonitorLastWarningTime(warningMessageContext.getMonitorId(), LocalDateTime.now());
            } else {
                regularMonitorMapper.updateRegularLastWarningTime(warningMessageContext.getMonitorId(), LocalDateTime.now());
            }

            return context;
        } catch (Exception e) {
            log.error("UpdateLastWarningTimeProcessor 发生异常", e);
            return context;
        }
    }

    @Override
    public Integer order() {
        return 3;
    }
}
