package com.trs.police.control.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.trs.police.common.core.constant.WorkRecordStatusConstant;
import com.trs.police.common.core.constant.enums.*;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.entity.ProfilePersonPoliceControlEntity;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.approval.ApprovalInfoVO;
import com.trs.police.common.core.vo.control.RegularMonitorListVO;
import com.trs.police.common.core.vo.control.WorkRecordDTO;
import com.trs.police.common.core.vo.message.ScheduleMessageVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.core.vo.profile.LabelVO;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.*;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.common.openfeign.starter.vo.PersonListVO;
import com.trs.police.common.openfeign.starter.vo.RegularMonitorInitiateVO;
import com.trs.police.control.constant.MonitorApprovalNameConstant;
import com.trs.police.control.converter.WarningConverter;
import com.trs.police.control.domain.entity.ServiceConfigEntity;
import com.trs.police.control.domain.entity.ServiceWorkRecordEntity;
import com.trs.police.control.domain.entity.fkrxyj.ProfilePerson;
import com.trs.police.control.domain.entity.monitor.RegularMonitorEntity;
import com.trs.police.control.domain.request.RegularMonitorApprovalRequest;
import com.trs.police.control.domain.vo.RegularConfigIdsAndMonitorIdVO;
import com.trs.police.control.domain.vo.TrackPointAppVO;
import com.trs.police.control.domain.vo.WorkRecordListVO;
import com.trs.police.control.domain.vo.WorkRecordVO;
import com.trs.police.control.domain.vo.monitor.MonitorWarningInfoVO;
import com.trs.police.control.domain.vo.regular.*;
import com.trs.police.control.mapper.*;
import com.trs.police.control.service.ApprovalProcessService;
import com.trs.police.control.service.RegularMonitorService;
import com.trs.police.control.service.ServiceCalendarService;
import com.trs.police.control.service.SubscribeService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.trs.police.control.constant.MonitorConstant.CANNOT_DELETE;
import static com.trs.police.control.constant.MonitorConstant.MONITOR_NOT_EXISTS;
import static com.trs.police.control.service.impl.OperationLogServiceImpl.CONTROL_REGULAR_MONITOR_LEVEL;



/**
 * <AUTHOR>
 * @Date 2023/1/5 14:08
 */
@Slf4j
@Service
public class RegularMonitorServiceImpl implements RegularMonitorService {

    @Resource
    private RegularMonitorMapper regularMonitorMapper;
    @Resource
    private ProfileService profileService;
    @Resource
    private DictService dictService;
    @Resource
    private ApprovalService approvalService;
    @Resource
    private WarningMapper warningMapper;
    @Resource
    private ApprovalProcessService approvalProcessService;
    @Resource
    private ServiceWorkRecordMapper serviceWorkRecordMapper;
    @Resource
    private ServiceConfigMapper serviceConfigMapper;
    @Resource
    private ServiceCalendarService serviceCalendarService;
    @Resource
    private ScheduleService scheduleService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private WarningConverter warningConverter;
    @Resource
    private PersonMapper personMapper;
    @Resource
    private SubscribeService subscribeService;
    @Resource
    private ProfilePersonPoliceControlMapper profilePersonPoliceControlMapper;
    @Resource
    private LabelMapper labelMapper;

    @Resource
    private Executor  monitorWarningStatisticExecutor;

    /**
     * 日期格式
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM月dd日");

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void initiate(RegularMonitorInitiateVO initiateVO) {
        CurrentUser currentUser = Objects.nonNull(initiateVO.getCurrentUser())
                ? initiateVO.getCurrentUser()
                : AuthHelper.getNotNullUser();
        List<Long> targetIds = initiateVO.getTargetIds();
        //勾选全部
        if (Boolean.TRUE.equals(initiateVO.getIsAll())) {
            targetIds = profileService.getIdsByRegularParams(initiateVO.getRequestParams());
        }
        //去除不能布控的id
        List<Long> beMonitoredIds = regularMonitorMapper.getMonitorTargetIds(currentUser.getId(),
                currentUser.getDeptId());
        targetIds.removeAll(beMonitoredIds);
        //全部已布控
        if (targetIds.isEmpty()) {
            return;
        }
        initiateVO.setTargetIds(targetIds);
        List<RegularMonitorEntity> regularMonitorEntities = this.toEntityList(initiateVO, currentUser, RegularMonitorStatusEnum.PENDING);
        // 获取人员的管控数据
        Map<Long, List<ProfilePersonPoliceControlEntity>> personControlMap = getPersonControlMap(initiateVO.getTargetIds());
        String property = BeanFactoryHolder.getEnv().getProperty("com.trs.police.control.regular.initiate.refactor", "false");
        if (Boolean.parseBoolean(property)) {
            // 如果启用重构版
            updateRegularMonitorsReFactor(regularMonitorEntities, personControlMap, currentUser);
        } else {
            updateRegularMonitors(regularMonitorEntities, personControlMap, currentUser);
        }
        //同步人员档案-常控状态
        regularMonitorMapper.setPersonsControlStatus(targetIds, 1);
        //更新已完成工作记录的常控
        serviceCalendarService.updateTodayServiceCalendar();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void initiate(Long id) {
        RegularMonitorEntity entity = regularMonitorMapper.getById(id);
        if (Objects.isNull(entity)) {
            throw new TRSException("布控:" + id + ", 不存在！");
        }
        if (entity.getStatus() != RegularMonitorStatusEnum.REVOKE
                && entity.getStatus() != RegularMonitorStatusEnum.REJECT) {
            throw new TRSException("布控:" + id + ",不能提交！");
        }
        entity.setStatus(RegularMonitorStatusEnum.PENDING);
        regularMonitorMapper.updateById(entity);
        //提交审批
        PersonVO personVO = profileService.findById(entity.getTargetId());
        createInitiateMonitorApproval(entity.getId(), personVO.getName(), null);
        //同步人员档案-常控状态
        regularMonitorMapper.setPersonsControlStatus(List.of(entity.getTargetId()), 1);
        //更新已完成工作记录的常控
        serviceCalendarService.updateTodayServiceCalendar();
    }

    /**
     * 批量更新常控信息
     *
     * @param regularMonitorEntities 常控列表
     * @param personControlMap 常控信息
     * @param currentUser 当前用户
     */
    private void updateRegularMonitorsReFactor(List<RegularMonitorEntity> regularMonitorEntities, Map<Long, List<ProfilePersonPoliceControlEntity>> personControlMap, CurrentUser currentUser) {
        if (CollectionUtils.isEmpty(regularMonitorEntities)) {
            return;
        }
        // 批量数据分片
        List<List<RegularMonitorEntity>> partition = Lists.partition(regularMonitorEntities, 50);
        // 构建审批请求参数
        List<RegularMonitorApprovalRequest> approvalRequests = new ArrayList<>();
        for (List<RegularMonitorEntity> monitorEntities : partition) {
            // 提前获取所有 targetId 集合
            List<Long> targetIds = monitorEntities.stream()
                    .map(RegularMonitorEntity::getTargetId)
                    .collect(Collectors.toList());

            // 批量获取人员名称
            List<PersonVO> personByIds = profileService.getPersonByIds(targetIds);
            Map<Long, String> personNameMap = personByIds.stream()
                    .collect(Collectors.toMap(PersonVO::getId, PersonVO::getName));

            List<RegularMonitorEntity> toInsertList = new ArrayList<>();
            List<RegularMonitorEntity> toUpdateList = new ArrayList<>();

            monitorEntities.forEach(entity -> {
                Long monitorId = regularMonitorMapper.getIdIfMonitored(entity);
                updateControlPolice(entity, personControlMap.get(entity.getTargetId()));

                if (Objects.isNull(monitorId)) {
                    // 插入新记录
                    toInsertList.add(entity);
                } else {
                    // 更新已有记录
                    entity.setId(monitorId);
                    entity.setUpdateDeptId(currentUser.getDeptId());
                    entity.setUpdateUserId(currentUser.getId());
                    entity.setDeleted(Boolean.FALSE);
                    toUpdateList.add(entity);
                }
            });
            // 批量新增
            if (!CollectionUtils.isEmpty(toInsertList)) {
                setWarningConfigIds(toInsertList);
                regularMonitorMapper.batchInsertMonitor(toInsertList);
                for (RegularMonitorEntity entity : toInsertList) {
                    // 累加审批参数
                    approvalRequests.add(new RegularMonitorApprovalRequest(entity.getId(), personNameMap.get(entity.getTargetId()), currentUser));
                }
            }
           if (!CollectionUtils.isEmpty(toUpdateList)) {
               setWarningConfigIds(toUpdateList);
               regularMonitorMapper.batchUpdateMonitor(toUpdateList);
               for (RegularMonitorEntity entity : toUpdateList) {
                   // 累加审批参数
                   approvalRequests.add(new RegularMonitorApprovalRequest(entity.getId(), personNameMap.get(entity.getTargetId()), currentUser));
               }
           }
            // 提交审批记录
            createInitiateBatchMonitorApproval(approvalRequests);
        }
    }

    /**
     * 设置常控记录的常控配置IDS
     *
     * @param list 常控记录集合
     */
    private void setWarningConfigIds(List<RegularMonitorEntity> list) {
        // 设置常控配置ID
        List<Long> regularMonitorIds = list.stream().map(RegularMonitorEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(regularMonitorIds)) {
            throw  new TRSException("设置常控配置IDS失败！未找到常控ID");
        }
        List<RegularConfigIdsAndMonitorIdVO> regularConfigIdsAndMonitorId = regularMonitorMapper.getRegularConfigIdsAndMonitorId(regularMonitorIds);
        if (CollectionUtils.isEmpty(regularConfigIdsAndMonitorId)) {
            log.warn("未找到常控配置信息");
            return;
        }
        Map<Long, List<RegularConfigIdsAndMonitorIdVO>> regularMappingConfigs = regularConfigIdsAndMonitorId.stream().collect(Collectors.groupingBy(RegularConfigIdsAndMonitorIdVO::getRegularId));
        list.forEach(entity -> {
            List<RegularConfigIdsAndMonitorIdVO> regularConfigIdsAndMonitorIds = regularMappingConfigs.get(entity.getId());
             if (!CollectionUtils.isEmpty(regularConfigIdsAndMonitorIds)) {
                 // 找到配置就设置上去
                 entity.setWarningConfigIds(regularConfigIdsAndMonitorIds.get(0).getWarningConfigIds());
            }
        });
    }

    /**
     * 批量更新常控信息
     *
     * @param regularMonitorEntities 常控列表
     * @param personControlMap 常控信息
     * @param currentUser 当前用户
     */
    private void updateRegularMonitors(List<RegularMonitorEntity> regularMonitorEntities, Map<Long, List<ProfilePersonPoliceControlEntity>> personControlMap, CurrentUser currentUser) {
        regularMonitorEntities.forEach(entity -> {
            Long monitorId = regularMonitorMapper.getIdIfMonitored(entity);
            updateControlPolice(entity, personControlMap.get(entity.getTargetId()));
            if (Objects.isNull(monitorId)) {
                //未存在已布控就插入，存在就更新
                //插入/更新 warningConfigIds 是由sql执行实现的，逻辑是:该常控的常控标签并且该常控对应的人员标签同时命中
                //t_control_regular_monitor_config 表中的person_labels和regular_labels,命中的配置ID的集合就是warningConfigIds
                regularMonitorMapper.insertMonitor(entity);
            } else {
                entity.setId(monitorId);
                entity.setUpdateDeptId(currentUser.getDeptId());
                entity.setUpdateUserId(currentUser.getId());
                entity.setDeleted(Boolean.FALSE);
                //插入/更新 warningConfigIds 是由sql执行实现的，逻辑是:该常控的常控标签并且该常控对应的人员标签同时命中
                //t_control_regular_monitor_config 表中的person_labels和regular_labels,命中的配置ID的集合就是warningConfigIds
                regularMonitorMapper.updateMonitor(entity);
            }
            //审批
            PersonVO personVO = profileService.findById(entity.getTargetId());
            Long approval = createInitiateMonitorApproval(entity.getId(), personVO.getName(), currentUser);
            if (approval == 0L) {
                // 无审批流程，事务未提交，推送审批消息查不到数据，根据返回的审批id确定是否是无审批节点，没有则更新审批状态为审批中
                UpdateWrapper<RegularMonitorEntity> updateWrapper = new UpdateWrapper<RegularMonitorEntity>()
                        .eq("id", monitorId)
                        .set("status", RegularMonitorStatusEnum.MONITORING.getCode());
                regularMonitorMapper.update(null, updateWrapper);
            }
        });
    }

    private void updateControlPolice(RegularMonitorEntity monitorEntity, List<ProfilePersonPoliceControlEntity> profilePersonPoliceControlEntities) {
        if (Objects.isNull(profilePersonPoliceControlEntities)) {
            monitorEntity.setControlPolice(Collections.emptyList());
            monitorEntity.setControlStation(Collections.emptyList());
            monitorEntity.setControlPerson(Collections.emptyList());
            return;
        }
        handleControlInfo(monitorEntity, profilePersonPoliceControlEntities);
    }

    private void handleControlInfo(RegularMonitorEntity monitorEntity, List<ProfilePersonPoliceControlEntity> profilePersonPoliceControlEntities) {
        List<String> deptCode = new ArrayList<>();
        profilePersonPoliceControlEntities.forEach(entity -> {
            deptCode.add(entity.getControlPolice());
            deptCode.add(entity.getControlStation());
        });
        Map<String, Long> deptCodeNameMap = permissionService.getDeptByCodes(deptCode).stream().collect(Collectors.toMap(DeptDto::getCode, DeptDto::getId, (m1, m2) -> m1));
        if (profilePersonPoliceControlEntities.size() <= 1) {
            ProfilePersonPoliceControlEntity controlEntity = profilePersonPoliceControlEntities.get(0);
            monitorEntity.setControlPolice(Objects.isNull(controlEntity.getControlPolice())
                    ? Collections.emptyList()
                    : Collections.singletonList(deptCodeNameMap.get(controlEntity.getControlPolice())));
            monitorEntity.setControlStation(Objects.isNull(controlEntity.getControlStation())
                    ? Collections.emptyList()
                    : Collections.singletonList(deptCodeNameMap.get(controlEntity.getControlStation())));
            monitorEntity.setControlPerson(CollectionUtils.isEmpty(controlEntity.getControlPerson()) ? Collections.emptyList() :controlEntity.getControlPerson());
        } else {
            monitorEntity.setControlPolice(profilePersonPoliceControlEntities.stream()
                    .map(entity -> deptCodeNameMap.get(entity.getControlPolice()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
            monitorEntity.setControlStation(profilePersonPoliceControlEntities.stream()
                    .map(entity -> deptCodeNameMap.get(entity.getControlStation()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
            monitorEntity.setControlPerson(profilePersonPoliceControlEntities.stream()
                    .flatMap(entity -> entity.getControlPerson().stream())
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList()));
        }
    }

    private Long createInitiateMonitorApproval(Long monitorId, String title, CurrentUser operateUser) {
        ApprovalActionVO actionVO = new ApprovalActionVO();
        actionVO.setService(OperateModule.REGULAR);
        actionVO.setId(monitorId);
        actionVO.setAction(Operation.REGULAR_INITIATE);
        actionVO.setTitle(title);
        ApprovalRequest request = new ApprovalRequest();
        request.setApprovalActionVO(actionVO);
        request.setApprovalConfigName(MonitorApprovalNameConstant.REGULAR_INITIATE);
        UserDeptVO user = Objects.nonNull(operateUser) ? UserDeptVO.of(operateUser) : UserDeptVO.of(AuthHelper.getCurrentUser());
        request.setUser(user);
        String result = approvalService.startApprovalWithResult(request);
        JSONObject object = JSONObject.parseObject(result);
        return object.getJSONObject("data").getLongValue("processId");
    }

    /**
     * 批量发起审批
     *
     * @param requestList 审批集合
     *
     */
    private void createInitiateBatchMonitorApproval(List<RegularMonitorApprovalRequest> requestList) {
        for (RegularMonitorApprovalRequest approvalRequest : requestList) {
            ApprovalActionVO actionVO = new ApprovalActionVO();
            actionVO.setService(OperateModule.REGULAR);
            actionVO.setId(approvalRequest.getMonitorId());
            actionVO.setAction(Operation.REGULAR_INITIATE);
            actionVO.setTitle(approvalRequest.getTitle());
            ApprovalRequest request = new ApprovalRequest();
            request.setApprovalActionVO(actionVO);
            request.setApprovalConfigName(MonitorApprovalNameConstant.REGULAR_INITIATE);
            UserDeptVO user = Objects.nonNull(approvalRequest.getOperateUser()) ? UserDeptVO.of(approvalRequest.getOperateUser()) : UserDeptVO.of(AuthHelper.getCurrentUser());
            request.setUser(user);

            String result = approvalService.startApprovalWithResult(request);
            JSONObject object = JSONObject.parseObject(result);
            long processId = object.getJSONObject("data").getLongValue("processId");
            if (processId == 0L) {
                // 无审批流程，事务未提交，推送审批消息查不到数据，根据返回的审批id确定是否是无审批节点，没有则更新审批状态为审批中
                UpdateWrapper<RegularMonitorEntity> updateWrapper = new UpdateWrapper<RegularMonitorEntity>()
                        .eq("id", approvalRequest.getMonitorId())
                        .set("status", RegularMonitorStatusEnum.MONITORING.getCode());
                regularMonitorMapper.update(null, updateWrapper);
            }
        }
    }

    private void getEditLevelApproval(Long monitorId, RegularEditVO editLevelVO) {
        ApprovalRequest request = new ApprovalRequest();
        ApprovalActionVO actionVO = new ApprovalActionVO();
        request.setApprovalConfigName(MonitorApprovalNameConstant.REGULAR_EDIT_LEVEL);
        actionVO.setAction(Operation.REGULAR_CHANGE_LEVEL);
        actionVO.setService(OperateModule.REGULAR);
        actionVO.setId(monitorId);
        actionVO.setStartFormContent(JsonUtil.toJsonString(editLevelVO));
        actionVO.setRelatedData(editLevelVO.getLevel().toString());
        request.setApprovalActionVO(actionVO);
        request.setUser(UserDeptVO.of(AuthHelper.getCurrentUser()));

        approvalService.startApproval(request);
    }

    private void createRevokeMonitorApproval(Long monitorId, RegularEditVO editVO) {
        ApprovalActionVO actionVO = new ApprovalActionVO();
        actionVO.setService(OperateModule.REGULAR);
        actionVO.setId(monitorId);
        actionVO.setAction(Operation.REGULAR_REVOKE);
        actionVO.setStartFormContent(JsonUtil.toJsonString(editVO));
        ApprovalRequest request = new ApprovalRequest();
        request.setApprovalActionVO(actionVO);
        request.setApprovalConfigName(MonitorApprovalNameConstant.REGULAR_REVOKE);
        request.setUser(UserDeptVO.of(AuthHelper.getCurrentUser()));
        approvalService.startApproval(request);
    }

    @Override
    public PageResult<RegularMonitorListVO> getMyList(ListParamsRequest request) {
        return this.getList(request, Boolean.TRUE);
    }

    @Override
    public PageResult<RegularMonitorListVO> getAllList(ListParamsRequest request) {
        return this.getList(request, Boolean.FALSE);
    }

    private PageResult<RegularMonitorListVO> getList(ListParamsRequest request, Boolean mine) {
        LocalDateTime currentTime = LocalDateTime.now();
        final DictDto todayLevel = serviceCalendarService.getServiceCalendarByTime(currentTime);

        //构建数据权限筛选参数
        generateFilterParams(request, mine);

        final PageParams pageParams = request.getPageParams();
        Page<RegularMonitorListVO>  curPage = request.getPageParams().toPage();
        curPage.setSearchCount(false);
        curPage.setTotal(regularMonitorMapper.getPageCount(request));

        Page<RegularMonitorListVO> page = regularMonitorMapper.getPage(request, curPage);
        Map<Long, String> deptIdAndNameMap = getDeptIdAndNameMap(page.getRecords());
        Map<Long, Long> warningCountMap = getWarningCountMap(page.getRecords());
        page.getRecords().forEach(item -> {
            processRegularVO(item, todayLevel);
            processIdValue(item, deptIdAndNameMap, warningCountMap);
        });

        return PageResult.of(page.getRecords(), pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    private Map<Long, Long> getWarningCountMap(List<RegularMonitorListVO> records) {
        List<Long> monitorIds = records.stream().map(RegularMonitorListVO::getMonitorId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(monitorIds)) {
            return Map.of();
        }
        List<IdNameCountVO> countList = warningMapper.countWarningByMonitorIds(monitorIds);
        return countList.stream().collect(Collectors.toMap(IdNameCountVO::getId, IdNameCountVO::getCount, (v1, v2) -> v1));
    }

    private void processIdValue(RegularMonitorListVO item, Map<Long, String> deptIdAndNameMap, Map<Long, Long> warningCountMap) {
        item.setControlPolice(item.getControlPoliceId().stream().map(deptIdAndNameMap::get).collect(Collectors.toList()));
        item.setControlStation(item.getControlStationId().stream().map(deptIdAndNameMap::get).collect(Collectors.toList()));
        item.setWarningCount(warningCountMap.getOrDefault(item.getMonitorId(), 0L));
    }

    private Map<Long, String> getDeptIdAndNameMap(List<RegularMonitorListVO> records) {
        Map<Long, String> map = new HashMap<>();
        if (CollectionUtils.isEmpty(records)) {
            return map;
        }
        List<Long> deptIdList = records.stream()
                .flatMap(record -> {
                    HashSet<Long> set = new HashSet<>();
                    set.addAll(record.getControlPoliceId());
                    set.addAll(record.getControlStationId());
                    return set.stream();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deptIdList)) {
            return map;
        }
        return permissionService.getDeptByIds(deptIdList).stream()
                .collect(Collectors.toMap(DeptDto::getId, DeptDto::getName, (i1, i2) -> i1));
    }

    private RegularMonitorListVO processRegularVO(RegularMonitorListVO item, DictDto todayLevel) {
        LocalDateTime endTime = LocalDateTime.now().toLocalDate().plusDays(1L).atStartOfDay();
        ServiceConfigEntity todayConfig = serviceConfigMapper.getConfigByRegularAndTime(todayLevel.getCode(),
                item.getPersonStatus().getCode(), (int) item.getLevel().getCode());
        //如果今天未命中后台的配置，则不需要工作记录
        if (Objects.isNull(todayConfig)) {
            item.setWorkStatus(WorkRecordStatusConstant.NOT_NEED_FINISHED);
        } else {
            item.setRequireCount(todayConfig.getTimes());
            LocalDateTime beginTime = endTime.minusDays(todayConfig.getDays() > 0 ? todayConfig.getDays() : 1);
            Integer completeCount = serviceWorkRecordMapper.getCountByTime(item.getId(), beginTime, endTime);
            item.setCompleteCount(completeCount);
            item.setWorkStatus(completeCount < todayConfig.getTimes() ? WorkRecordStatusConstant.UNFINISHED
                    : WorkRecordStatusConstant.FINISHED);
        }
        item.setHasRecord(serviceWorkRecordMapper.getLastRecord(item.getId()).isPresent());
        return item;
    }

    private void generateFilterParams(ListParamsRequest request, Boolean mine) {
        //构建filter参数
        List<KeyValueTypeVO> filterParams = request.getFilterParams();
        if (mine.equals(Boolean.TRUE)) {
            //是我的布控列表，只展示自己的布控
            CurrentUser currentUser = AuthHelper.getNotNullUser();
            filterParams.add(new KeyValueTypeVO("currentUser", currentUser.getId(), "long"));
            filterParams.add(new KeyValueTypeVO("currentUserDept", currentUser.getDeptId(), "long"));
        } else {
            request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        }
    }

    @Override
    public RegularDetailVO getRegularDetail(Long id) {
        RegularMonitorEntity regularMonitor = regularMonitorMapper.selectById(id);
        return toVO(regularMonitor);
    }

    private RegularDetailVO toVO(RegularMonitorEntity regularMonitor) {
        RegularDetailVO vo = new RegularDetailVO();
        PersonVO personVO = profileService.findById(regularMonitor.getTargetId());
        vo.setRegularPerson(new RegularPersonVO(personVO));
        String name = dictService.getNameByTypeAndCode("control_regular_monitor_level", regularMonitor.getLevel());
        vo.setLevel(new CodeNameVO(regularMonitor.getLevel(), name));
        vo.setComment(regularMonitor.getComment());
        List<String> labels = profileService.getLabelByIds(regularMonitor.getLabelIds()).stream()
                .map(LabelVO::getName)
                .collect(Collectors.toList());
        vo.setRegularLabels(labels);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void revoke(Long id, RegularEditVO revokeReason) {
        RegularMonitorEntity entity = regularMonitorMapper.getById(id);
        if (Objects.isNull(entity)) {
            throw new TRSException("布控:" + id + " ,不存在！");
        }
        if (entity.getStatus() != RegularMonitorStatusEnum.MONITORING) {
            throw new TRSException("布控:" + id + ",不能撤控！");
        }
        //撤销全部相关审批
        approvalService.cancelAllApprovals(OperateModule.REGULAR.getCode(), id);
        //发起撤销常控审批
        createRevokeMonitorApproval(id, revokeReason);
        // 推送第三方撤控
        subscribeService.cancelRegularWarningSubscribe(id, MonitorBaseTypeEnum.PERSON);
    }

    @Override
    public void batchRevoke(List<Long> ids, RegularEditVO revokeReason) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<RegularMonitorEntity> entities = regularMonitorMapper.selectBatchIds(ids);
        // 撤销相关审批
        entities.forEach(en -> {
            approvalService.cancelAllApprovals(OperateModule.REGULAR.getCode(), en.getId());
        });
        // 撤销常控
        handRevokePass(entities);

    }

    @Override
    public void handRevokePass(List<RegularMonitorEntity> entities) {
        for (RegularMonitorEntity entity : entities) {
            try {
                //更新档案-常控状态
                regularMonitorMapper.setPersonUnControl(entity.getTargetId());
                entity.setStatus(RegularMonitorStatusEnum.REVOKE);
                regularMonitorMapper.updateById(entity);
            } catch (Exception e) {
                log.error("测控失败", e);
            }
        }
        log.info("常控撤控成功! ");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void revokeApproval(Long id) {
        RegularMonitorEntity entity = regularMonitorMapper.getById(id);
        if (Objects.isNull(entity)) {
            throw new TRSException("布控:" + id + ",不存在！");
        }
        if (entity.getStatus() != RegularMonitorStatusEnum.PENDING) {
            throw new TRSException("布控:" + id + ",不能撤销！");
        }
        entity.setStatus(RegularMonitorStatusEnum.REVOKE);
        regularMonitorMapper.updateById(entity);
        //撤销全部相关审批
        approvalService.cancelAllApprovals(OperateModule.REGULAR.getCode(), id);
        //更新档案-常控状态
        regularMonitorMapper.setPersonUnControl(entity.getTargetId());
    }

    @Override
    public void editLevel(Long id, RegularEditVO regularEditVO) {
        RegularMonitorEntity entity = regularMonitorMapper.getById(id);
        if (Objects.isNull(entity)) {
            throw new TRSException("布控:" + id + ",不存在！");
        }
        if (entity.getStatus() != RegularMonitorStatusEnum.MONITORING) {
            throw new TRSException("布控:" + id + ",不能修改布控级别！");
        }

        getEditLevelApproval(id, regularEditVO);
        //更新已完成工作记录的常控
        serviceCalendarService.updateTodayServiceCalendar();
    }

    @Override
    public void editLevelByPersonProfile(Long personProfileId, String personLevelName) {
        List<RegularMonitorEntity> person = regularMonitorMapper.selectList(new QueryWrapper<RegularMonitorEntity>().eq("target_id", personProfileId));
        if (CollectionUtils.isEmpty(person)) {
            return;
        }
        RegularMonitorEntity targetPerson = person.get(0);
        // 查询原来的常控级别名称
        Long level = targetPerson.getLevel();
        List<DictDto> dictList = dictService.getDictListByType(CONTROL_REGULAR_MONITOR_LEVEL);
        Optional<DictDto> oldDict = dictList.stream()
                .filter(d -> Objects.equals(d.getCode(), level))
                .findAny();
        // 如果本来就相等 直接返回
        if (oldDict.isPresent() && Objects.equals(oldDict.get().getName(), personLevelName)) {
            return;
        }
        // 不相等 修改常控级别
        Optional<DictDto> newDict = dictList.stream()
                .filter(d -> Objects.equals(d.getName(), personLevelName))
                .findAny();
        if (newDict.isEmpty()) {
            // 从人员级别没有匹配到常控级别
            return;
        }
        targetPerson.setLevel(newDict.get().getCode());
        regularMonitorMapper.updateById(targetPerson);
    }

    @Override
    public RegularDetailInfoVO getDetail(Long id) {
        RegularDetailInfoVO detail = regularMonitorMapper.getDetailById(id);
        // 通过查询审批状态设置修改布控级别是否在待审批或者审批中
        List<ApprovalInfoVO> approvalInfoVOList = approvalService.getApprovals(OperateModule.REGULAR.getCode(),
                detail.getId(), null);
        detail.setApprovalInfo(approvalInfoVOList);

        //当审批状态和布控状态不统一时候，再调用一次审批通过后的更新接口
        if (Objects.nonNull(approvalInfoVOList)) {
            ApprovalInfoVO approvalInfoVO = approvalInfoVOList.stream().findFirst().orElse(null);
            if (Objects.nonNull(approvalInfoVO)
                && ApprovalStatusEnum.PASSED.getCode().equals(approvalInfoVO.getApprovalStatus())
                && RegularMonitorStatusEnum.APPROVING.equals(detail.getStatus())
                && RegularMonitorStatusEnum.PENDING.equals(detail.getStatus())) {
                approvalProcessService.changeRegularStatus(id, ApprovalStatusEnum.PASSED);
            }
        }

        //判断是否可以修改布控级别
        detail.setModifyMonitorLevelStatus(getEditLevelStatus(detail.getStatus(), approvalInfoVOList));
        detail.setRegularLabelIds(JsonUtil.toNestingList(detail.getRegularLabelIdStr(), Long.class));
        LocalDateTime currentTime = LocalDateTime.now();
        DictDto todayLevel = serviceCalendarService.getServiceCalendarByTime(currentTime);
        LocalDateTime endTime = LocalDateTime.now().toLocalDate().plusDays(1L).atStartOfDay();
        ServiceConfigEntity todayConfig = serviceConfigMapper.getConfigByRegularAndTime(todayLevel.getCode(),
                detail.getPersonStatus().getCode(), (int) detail.getLevel().getCode());
        //如果今天未命中后台的配置，则不需要工作记录
        if (Objects.nonNull(todayConfig)) {
            detail.setRequireCount(todayConfig.getTimes());
            LocalDateTime beginTime = endTime.minusDays(todayConfig.getDays() > 0 ? todayConfig.getDays() : 1);
            Integer completeCount = serviceWorkRecordMapper.getCountByTime(detail.getId(), beginTime, endTime);
            detail.setCompleteCount(completeCount);
        }
        return detail;
    }

    private Integer getEditLevelStatus(RegularMonitorStatusEnum monitorStatus, List<ApprovalInfoVO> approvalInfoList) {
        return approvalInfoList.stream()
                .findFirst()
                .map(e -> {
                    ApprovalStatusEnum approvalStatus = ApprovalStatusEnum.codeOf(e.getApprovalStatus());
                    //布控状态为布控中 且 没有其他审批正在进行中（最新一条审批状态不为待审批、审批中）
                    return monitorStatus == RegularMonitorStatusEnum.MONITORING
                            && approvalStatus != ApprovalStatusEnum.WAITING
                            && approvalStatus != ApprovalStatusEnum.IN_PROCESS ? 0 : 1;
                })
                // 没有审批信息，布控状态为布控中，可以修改
                .orElse(monitorStatus == RegularMonitorStatusEnum.MONITORING ? 0 : 1);
    }

    @Override
    public List<MonitorWarningInfoVO> getWarningInfo(Long id, ListParamsRequest paramsRequest) {
        return warningMapper.getWarningByTypeAndTargetId(ControlTypeEnum.REGULAR.getCode(), id, paramsRequest)
                .stream()
                .map(warningConverter::toMonitorWarningInfoVO)
                .collect(Collectors.toList());
    }

    @Override
    public RegularDetailVO getApprovalRegular(Long id, Long approvalId) {
        String startForm = approvalService.getStartFormContent(approvalId);
        RegularMonitorEntity monitorEntity = approvalProcessService.getLevelChangedRegular(id, startForm);
        return toVO(monitorEntity);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addWorkRecord(Long regularId, WorkRecordVO workRecordVO) {
        RegularMonitorEntity regular = regularMonitorMapper.getById(regularId);
        ServiceWorkRecordEntity entity = new ServiceWorkRecordEntity();
        BeanUtil.copyPropertiesIgnoreNull(workRecordVO, entity);
        entity.setRegularId(regularId);
        entity.setPersonId(regular.getTargetId());
        entity.setStatus(RegularPersonStatusEnum.codeOf(workRecordVO.getStatus()));
        entity.setAttachments(workRecordVO.getAttachments().toArray(new FileInfoVO[0]));
        entity.setWorkPolice(workRecordVO.getWorkPolice().toArray(new SimpleUserVO[0]));
        if (Objects.nonNull(workRecordVO.getWorkTrack())) {
            entity.setWorkTrack(workRecordVO.getWorkTrack().toArray(new TrackPointAppVO[0]));
        }
        entity.setOutOfControlTime(workRecordVO.getOutOfControlTime());
        serviceWorkRecordMapper.insert(entity);
        regularMonitorMapper.updateRegularPersonStatus(regularId, workRecordVO.getStatus());
        //更新已完成工作记录的常控
        serviceCalendarService.updateTodayServiceCalendar();
        if (RegularPersonStatusEnum.STABILITY.getCode().equals(workRecordVO.getStatus())) {
            ScheduleMessageVO message = new ScheduleMessageVO();
            message.setRelatedId(regularId);
            message.setModule(OperateModule.REGULAR);
            message.setOperation(DelayMessageTypeEnum.REGULAR_PERSON_STATUS);
            message.setTimeLimit(entity.getCreateTime().plusDays(workRecordVO.getInControlTime()));
            scheduleService.subscribeDelayJob(message);
        }
    }

    @Override
    public PageResult<WorkRecordListVO> workRecordList(ListParamsRequest request, Long regularId) {
        PageParams pageParams = request.getPageParams();
        Page<ServiceWorkRecordEntity> page = serviceWorkRecordMapper.getRegularWorkRecordList(regularId,
                request.getFilterParams(), request.getSortParams(), pageParams.toPage());
        return PageResult.of(page.getRecords().stream().map(this::entityToListVO).collect(Collectors.toList()),
                pageParams.getPageNumber(), page.getTotal(), pageParams.getPageSize());
    }

    /**
     * entity转vo
     *
     * @param entity 实体
     * @return vo
     */
    public WorkRecordListVO entityToListVO(ServiceWorkRecordEntity entity) {
        WorkRecordListVO vo = new WorkRecordListVO();
        vo.setId(entity.getId());
        vo.setDestination(entity.getDestination());
        LocalDateTime workTime = entity.getCreateTime();
        vo.setCreateTime(workTime);
        vo.setWorkPolice(
                Arrays.stream(entity.getWorkPolice()).map(SimpleUserVO::toUserString).collect(Collectors.toList()));
        vo.setStatus(entity.getStatus());
        Integer count = serviceWorkRecordMapper.getCountByTime(entity.getRegularId(),
                workTime.toLocalDate().atStartOfDay(), workTime);
        PersonVO personVO = profileService.findById(entity.getPersonId());
        vo.setTitle(vo.getCreateTime().format(DATE_FORMATTER) + personVO.getName() + "第" + (count + 1) + "次工作记录");
        vo.setServiceLevel(serviceCalendarService.getServiceCalendarByTime(entity.getCreateTime()));
        vo.setWorkMethod(dictService.getNameByTypeAndCode("service_work_method", entity.getWorkMethod()));
        return vo;
    }

    @Override
    public void updateRegularPersonStatus(Long regularId) {
        serviceWorkRecordMapper.getLastRecord(regularId).ifPresent(entity -> {
            if (RegularPersonStatusEnum.STABILITY.equals(entity.getStatus()) && entity.getCreateTime()
                    .plusDays(entity.getInControlTime()).isBefore(LocalDateTime.now())) {
                regularMonitorMapper.updateRegularPersonStatus(regularId, RegularPersonStatusEnum.IN_CONTROL.getCode());
            }
        });
    }

    @Override
    public void regularListExport(HttpServletResponse response, ExportParams params) throws IOException {
        String time = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
        String fileName = URLEncoder.encode("常控列表_" + time, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        List<RegularListExportVO> exportListVos = createExportListVos(params);

        EasyExcelFactory.write(response.getOutputStream(), RegularListExportVO.class)
                .sheet(fileName)
                .includeColumnFiledNames(params.getFieldNames())
                .doWrite(exportListVos);
    }

    private List<RegularListExportVO> createExportListVos(ExportParams params) {
        //按id导出
        if (Boolean.FALSE.equals(params.getIsAll())) {
            LocalDateTime currentTime = LocalDateTime.now();
            DictDto todayLevel = serviceCalendarService.getServiceCalendarByTime(currentTime);

            List<RegularMonitorListVO> list = regularMonitorMapper.getRegularByIds(params.getIds());
            return list.stream().map(listVO -> {
                RegularMonitorListVO newListVO = processRegularVO(listVO, todayLevel);
                return RegularListExportVO.toExportVO(newListVO);
            }).collect(Collectors.toList());
        } else {
            //导出全部
            ListParamsRequest request = params.getListParamsRequest();
            request.setPageParams(PageParams.getAll());
            PageResult<RegularMonitorListVO> page = getList(params.getListParamsRequest(), "my".equals(params.getType()));
            return page.getItems().stream().map(RegularListExportVO::toExportVO).collect(Collectors.toList());
        }
    }

    @Override
    public void delete(Long id) {
        RegularMonitorEntity monitorEntity = regularMonitorMapper.selectById(id);
        if (Objects.isNull(monitorEntity)) {
            throw new TRSException(MONITOR_NOT_EXISTS);
        }
        RegularMonitorStatusEnum monitorStatus = monitorEntity.getStatus();
        if (monitorStatus.equals(RegularMonitorStatusEnum.REVOKE)) {
            regularMonitorMapper.deleteById(id);
        } else {
            throw new TRSException(CANNOT_DELETE);
        }
    }

    @Override
    public WorkRecordVO workRecordEditInfo(Long regularId, Long recordId) {
        ServiceWorkRecordEntity entity = serviceWorkRecordMapper.selectById(recordId);
        WorkRecordVO vo = new WorkRecordVO();
        BeanUtil.copyPropertiesIgnoreNull(entity, vo);
        vo.setStatus(entity.getStatus().getCode());
        vo.setAttachments(List.of(entity.getAttachments()));
        vo.setWorkPolice(List.of(entity.getWorkPolice()));
        if (entity.getWorkTrack() != null) {
            vo.setWorkTrack(List.of(entity.getWorkTrack()));
        }
        return vo;
    }

    @Override
    public WorkRecordDetailVO workRecordDetail(Long regularId, Long recordId) {
        final ServiceWorkRecordEntity entity = serviceWorkRecordMapper.selectById(recordId);
        final DictDto todayLevel = serviceCalendarService.getServiceCalendarByTime(LocalDateTime.now());
        WorkRecordDetailVO vo = new WorkRecordDetailVO();
        vo.setRegularPerson(getPersonVO(entity.getPersonId(), regularId, todayLevel));
        vo.setStatus(entity.getStatus().getName());
        vo.setWorkMethod(dictService.getNameByTypeAndCode("service_work_method", entity.getWorkMethod()));
        LocalDateTime time = entity.getOutOfControlTime();
        vo.setOutOfControlTime(time == null ? null : time.format(TimeUtil.DEFAULT_TIME_PATTERN));
        vo.setInControlTime(entity.getInControlTime() == null ? null : entity.getInControlTime() + "天");
        vo.setTracks(getTracks(entity.getTrack(), vo.getRegularPerson()));
        vo.setAttachments(entity.getAttachments() == null ? null : List.of(entity.getAttachments()));
        vo.setWorkPolice(List.of(entity.getWorkPolice()));
        vo.setWorkDetail(entity.getWorkDetail());
        vo.setDestination(entity.getDestination());
        vo.setCreateTime(TimeUtil.getSimpleTime(entity.getCreateTime()));
        if(entity.getCreateUserId()!=null && entity.getCreateDeptId()!=null){
            SimpleUserVO simpleUserVO = permissionService.findSimpleUser(entity.getCreateUserId(), entity.getCreateDeptId());
            vo.setCreateUser(simpleUserVO.getUserName());
            vo.setCreateDept(simpleUserVO.getDeptShortName());
        }
        vo.setRecordTime(entity.getCreateTime());
        vo.setServiceLevel(todayLevel);
        Integer count = serviceWorkRecordMapper.getCountByTime(entity.getRegularId(),
                vo.getRecordTime().toLocalDate().atStartOfDay(), vo.getRecordTime());
        PersonVO personVO = profileService.findById(entity.getPersonId());
        vo.setTitle(vo.getRecordTime().format(DATE_FORMATTER) + personVO.getName() + "第" + (count + 1) + "次工作记录");
        if (entity.getWorkTrack() != null) {
            vo.setWorkTrack(List.of(entity.getWorkTrack()));
        }
        return vo;
    }

    private WorkRecordPersonVO getPersonVO(Long personId, Long regularId, DictDto todayLevel) {
        PersonCardVO person = profileService.getPersonCardById(personId);
        WorkRecordPersonVO recordVO = new WorkRecordPersonVO();
        recordVO.setId(personId);
        recordVO.setName(person.getName());
        recordVO.setIdNumber(person.getIdNumber());
        recordVO.setPersonLabel(person.getPersonLabel());
        recordVO.setImgs(person.getImgs());

        RegularMonitorEntity regular = regularMonitorMapper.selectById(regularId);
        DictDto levelDict = dictService.getDictByTypeAndCode("control_regular_monitor_level", regular.getLevel());
        recordVO.setRegularLevel(new CodeNameVO(levelDict.getCode(), levelDict.getName()));
        recordVO.setRegularStatus(new CodeNameVO(regular.getStatus().getCode(), regular.getStatus().getName()));

        RegularMonitorListVO listVO = new RegularMonitorListVO();
        listVO.setPersonStatus(regular.getPersonStatus());
        listVO.setLevel(new CodeNameVO(Integer.parseInt(regular.getLevel().toString()), ""));
        listVO.setId(regularId);
        processRegularVO(listVO, todayLevel);
        recordVO.setWorkRecord(listVO.getWorkStatus() + " (" + listVO.getCompleteCount() + "/" + listVO.getRequireCount() + ")");
        return recordVO;
    }

    private List<WorkRecordTrackVO> getTracks(Object trackIds, WorkRecordPersonVO personVO) {
        if (trackIds == null) {
            return Collections.emptyList();
        }
        List<String> idList = JsonUtil.parseArray(JsonUtil.toJsonString(trackIds), String.class);
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        }
        return profileService.getPersonTrack(idList).stream().map(track -> {
            WorkRecordTrackVO vo = new WorkRecordTrackVO();
            vo.setTrackId(track.getId());
            vo.setSourceType(track.getGzylx());
            vo.setSourceName(track.getGzymc());
            vo.setTitle(personVO.getName() + track.getTime() + "在" + track.getAddress() + "的轨迹");
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public void editWorkRecord(Long regularId, Long recordId, WorkRecordVO workRecordVO) {
        RegularMonitorEntity regular = regularMonitorMapper.getById(regularId);
        ServiceWorkRecordEntity entity = new ServiceWorkRecordEntity();
        entity.setPersonId(regular.getTargetId());
        entity.setId(recordId);
        BeanUtil.copyPropertiesIgnoreNull(workRecordVO, entity);
        entity.setRegularId(regularId);
        entity.setStatus(RegularPersonStatusEnum.codeOf(workRecordVO.getStatus()));

        entity.setAttachments(workRecordVO.getAttachments().toArray(new FileInfoVO[0]));
        if (Objects.nonNull(workRecordVO.getWorkTrack())) {
            entity.setWorkTrack(workRecordVO.getWorkTrack().toArray(new TrackPointAppVO[0]));
        }
        serviceWorkRecordMapper.updateById(entity);
        regularMonitorMapper.updateRegularPersonStatus(regularId, workRecordVO.getStatus());
        //更新已完成工作记录的常控
        serviceCalendarService.updateTodayServiceCalendar();
        if (RegularPersonStatusEnum.STABILITY.getCode().equals(workRecordVO.getStatus())) {
            ScheduleMessageVO message = new ScheduleMessageVO();
            message.setModule(OperateModule.REGULAR);
            message.setRelatedId(regularId);
            message.setOperation(DelayMessageTypeEnum.REGULAR_PERSON_STATUS);
            //entity.createTime为空,因此要重新查一次
            ServiceWorkRecordEntity serviceWorkRecordEntity = serviceWorkRecordMapper.selectById(recordId);
            message.setTimeLimit(serviceWorkRecordEntity.getCreateTime().plusDays(workRecordVO.getInControlTime()));
            scheduleService.subscribeDelayJob(message);
        }
    }

    @Override
    public void deleteWorkRecord(Long regularId, Long recordId) {
        serviceWorkRecordMapper.deleteById(recordId);
    }

    @Override
    public List<PersonListVO> getRegularLabel(List<Long> personIds) {
        List<PersonListVO> regularLabel = regularMonitorMapper.getRegularLabel(personIds);
        return regularLabel;
    }

    @Override
    public void xzRevoke(List<Long> personIds) {
        if (CollectionUtils.isEmpty(personIds)) {
            return;
        }
        UpdateWrapper<RegularMonitorEntity> wrapper = new UpdateWrapper<RegularMonitorEntity>()
                .in("target_id", personIds)
                .set("status", RegularMonitorStatusEnum.REVOKE.getCode());
        regularMonitorMapper.update(null, wrapper);
        UpdateWrapper<ProfilePerson> personWrapper = new UpdateWrapper<ProfilePerson>()
                .in("id", personIds)
                .set("control_status", 0);
        personMapper.update(null, personWrapper);
    }

    /**
     * 人档全部数据常控
     *
     * @param partitionCount 分片数
     * @param controlLevel 空值管控等级默认值
     */
    @Override
    public void allInitiate(Integer partitionCount, Long controlLevel) {
        // 捞取人员档案中所有的人
        List<RegularPersonAllInitiateVO> allTargetIdByUnMonitor = regularMonitorMapper.getAllTargetIdByUnMonitor();
        if (CollectionUtils.isEmpty(allTargetIdByUnMonitor)) {
            return;
        }
        // 获取标签
        final Map<String, Long> labelNameAndLevelMap = getLabelMap();
        // 获取人员等级码表
        final Map<Long, String> controlLevelAndNameMap = getControlLevelMap();
        // 获取常控等级码表
        final Map<String, Long> regularControlLevelAndNameMap = getRegularControlLevelMap();
        // 根据人员ID去 t_profile_person_police_control 表中捞取公安管控信息
        List<List<RegularPersonAllInitiateVO>> partition = Lists.partition(allTargetIdByUnMonitor, partitionCount == null ? 1000 : partitionCount);
        for (List<RegularPersonAllInitiateVO> partitionVos : partition) {
            List<Long> partitionIds = partitionVos.stream().map(RegularPersonAllInitiateVO::getId).collect(Collectors.toList());
            Map<Long, List<ProfilePersonPoliceControlEntity>> controlMap = getPersonControlMap(partitionIds);
            Map<String, Long> deptCodeAndIdMap = getDeptIdMap(controlMap);
            partitionVos.forEach(vo -> {
                vo.setControlLevel(Objects.nonNull(vo.getControlLevel())
                        ? vo.getControlLevel()
                        : Objects.nonNull(controlLevel)
                        ? controlLevel.intValue()
                        : 5);
                String controlLevelName = controlLevelAndNameMap.get(vo.getControlLevel().longValue());
                Long regularLevel = regularControlLevelAndNameMap.get(controlLevelName);
                Long labelId = labelNameAndLevelMap.get(controlLevelName);
                sendWarningSubscribe(vo, controlMap.get(vo.getId()), regularLevel, labelId, deptCodeAndIdMap);
            });
        }
    }

    private Map<String, Long> getRegularControlLevelMap() {
        return dictService.getDictListByTypeList(Collections.singletonList("control_regular_monitor_level")).stream()
                .collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (v1, v2) -> v1));
    }

    private Map<String, Long> getDeptIdMap(Map<Long, List<ProfilePersonPoliceControlEntity>> controlMap) {
        List<String> deptCodeList = controlMap.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream()
                        .map(ProfilePersonPoliceControlEntity::getControlStation))
                .distinct()
                .collect(Collectors.toList());
        return permissionService.getDeptByCodes(deptCodeList).stream()
                .collect(Collectors.toMap(DeptDto::getCode, DeptDto::getId, (v1, v2) -> v1));
    }

    private Map<Long, String> getControlLevelMap() {
        return dictService.getDictListByTypeList(Collections.singletonList("profile_person_control_level")).stream()
                .collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (v1, v2) -> v1));
    }

    private Map<String, Long> getLabelMap() {
        return labelMapper.selectByModule("regular").stream()
                .collect(Collectors.toMap(Label::getName, Label::getId, (v1, v2) -> v1));
    }

    private void sendWarningSubscribe(RegularPersonAllInitiateVO vo, List<ProfilePersonPoliceControlEntity> profilePersonPoliceControlEntities, Long regularLevel, Long labelId, Map<String, Long> deptCodeAndIdMap) {
        RegularMonitorEntity monitorEntity = new RegularMonitorEntity();
        monitorEntity.setTargetId(vo.getId());
        monitorEntity.setCreateTime(LocalDateTime.now());
        monitorEntity.setStatus(RegularMonitorStatusEnum.MONITORING);
        monitorEntity.setPersonStatus(RegularPersonStatusEnum.IN_CONTROL);
        monitorEntity.setLevel(regularLevel);
        monitorEntity.setLabelIds(Objects.isNull(labelId) ? Collections.emptyList() : Collections.singletonList(labelId));
        monitorEntity.setNestingLabelIds(new Long[][]{monitorEntity.getLabelIds().toArray(new Long[]{})});
        if (Objects.isNull(profilePersonPoliceControlEntities)) {
            monitorEntity.setControlPolice(Collections.emptyList());
            monitorEntity.setControlStation(Collections.emptyList());
            monitorEntity.setControlPerson(Collections.emptyList());
            subscribeService.sendWarningSubscribe(monitorEntity);
            regularMonitorMapper.insertMonitor(monitorEntity);
            return;
        }
        ProfilePersonPoliceControlEntity controlEntity = profilePersonPoliceControlEntities.get(0);
        monitorEntity.setCreateUserId(CollectionUtils.isEmpty(controlEntity.getControlPerson()) ? null : controlEntity.getControlPerson().get(0));
        monitorEntity.setCreateDeptId(deptCodeAndIdMap.get(controlEntity.getControlPolice()));
        handleControlInfo(monitorEntity, profilePersonPoliceControlEntities);
        subscribeService.sendWarningSubscribe(monitorEntity);
        regularMonitorMapper.insertMonitor(monitorEntity);
    }

    private Map<Long, List<ProfilePersonPoliceControlEntity>> getPersonControlMap(List<Long> partitionIds) {
        QueryWrapper<ProfilePersonPoliceControlEntity> wrapper = new QueryWrapper<ProfilePersonPoliceControlEntity>()
                .in("person_id", partitionIds);
        return profilePersonPoliceControlMapper.selectList(wrapper).stream()
                .collect(Collectors.groupingBy(ProfilePersonPoliceControlEntity::getPersonId));
    }

    /**
     * 根据常控ID，更新常控配置信息
     *
     * @param regularIds 常控ID
     */
    @Override
    public void updateWarningConfig(List<Long> regularIds) {
        if (CollectionUtils.isEmpty(regularIds)) {
            return;
        }
        List<RegularMonitorEntity> entities = regularMonitorMapper.selectBatchIds(regularIds);
        for (RegularMonitorEntity entity : entities) {
            regularMonitorMapper.updateMonitor(entity);
        }
    }

    /**
     * 转实体list
     *
     * @param initiateVO  initiateVO
     * @param currentUser 当前用户
     * @param status      状态
     * @return 实体list
     */
    private List<RegularMonitorEntity> toEntityList(RegularMonitorInitiateVO initiateVO, CurrentUser currentUser, RegularMonitorStatusEnum status) {
        return initiateVO.getTargetIds().stream()
                .map(targetId -> {
                    RegularMonitorEntity entity = new RegularMonitorEntity();
                    entity.setTargetId(targetId);
                    entity.setLevel(initiateVO.getLevel());
                    entity.setStatus(status);
                    entity.setLabelIds(KeyValueTypeVO.nestingListSimplification(initiateVO.getRegularLabels()).stream()
                            .map(o -> Long.parseLong(o.toString())).collect(Collectors.toList()));
                    entity.setComment(initiateVO.getComment());
                    entity.setNestingLabelIds(initiateVO.getRegularLabels());
                    entity.fillAuditFields(currentUser);
                    return entity;
                }).collect(Collectors.toList());
    }

    @Override
    public void initiateNoApproval(RegularMonitorInitiateVO initiateVO) {
        List<Long> targetIds = initiateVO.getTargetIds();
        // 去除不能布控的id
        List<Long> beMonitoredIds = regularMonitorMapper.getMonitorByTargetIds(targetIds);
        targetIds.removeAll(beMonitoredIds);
        // 全部已布控
        if (targetIds.isEmpty()) {
            return;
        }
        initiateVO.setTargetIds(targetIds);
        CurrentUser currentUser = initiateVO.getCurrentUser();
        List<RegularMonitorEntity> regularMonitorEntities = this.toEntityList(initiateVO, currentUser, RegularMonitorStatusEnum.MONITORING);
        // 获取人员的管控数据
        Map<Long, List<ProfilePersonPoliceControlEntity>> personControlMap = getPersonControlMap(initiateVO.getTargetIds());
        regularMonitorEntities.forEach(entity -> {
            Long monitorId = regularMonitorMapper.getIdIfMonitored(entity);
            updateControlPolice(entity, personControlMap.get(entity.getTargetId()));
            if (Objects.isNull(monitorId)) {
                //未存在已布控就插入，存在就更新
                //插入/更新 warningConfigIds 是由sql执行实现的，逻辑是:该常控的常控标签并且该常控对应的人员标签同时命中
                //t_control_regular_monitor_config 表中的person_labels和regular_labels,命中的配置ID的集合就是warningConfigIds
                regularMonitorMapper.insertMonitor(entity);
            } else {
                entity.setId(monitorId);
                entity.setDeleted(Boolean.FALSE);
                //插入/更新 warningConfigIds 是由sql执行实现的，逻辑是:该常控的常控标签并且该常控对应的人员标签同时命中
                //t_control_regular_monitor_config 表中的person_labels和regular_labels,命中的配置ID的集合就是warningConfigIds
                regularMonitorMapper.updateMonitor(entity);
            }
            // 发起常控订阅
            subscribeService.sendWarningSubscribe(entity);
        });
        // 同步人员档案-常控状态
        regularMonitorMapper.setPersonsControlStatus(targetIds, 1);
        // 更新已完成工作记录的常控
        serviceCalendarService.updateTodayServiceCalendar();
    }

    @Override
    public void addWorkRecordFromGx(WorkRecordDTO workRecordDTO) {
        Long regularId;
        List<RegularMonitorEntity> regularList = regularMonitorMapper.selectList(new QueryWrapper<RegularMonitorEntity>()
                .eq("target_id", workRecordDTO.getPersonId()));
        if(CollectionUtils.isEmpty(regularList)){
            return;
        }
        regularId = regularList.get(0).getId();
        ServiceWorkRecordEntity oldEntity = serviceWorkRecordMapper.selectOne(new QueryWrapper<ServiceWorkRecordEntity>()
                .eq("person_id", workRecordDTO.getPersonId())
                .eq("create_time", workRecordDTO.getCreateTime()));
        ServiceWorkRecordEntity entity = oldEntity != null ? oldEntity : new ServiceWorkRecordEntity();
        entity.setRegularId(regularId);
        entity.setPersonId(workRecordDTO.getPersonId());
        entity.setStatus(RegularPersonStatusEnum.codeOf(workRecordDTO.getStatus()));
        entity.setWorkMethod(workRecordDTO.getWorkMethod());
        entity.setWorkDetail(workRecordDTO.getWorkDetail());
        entity.setCreateTime(DateUtil.dateToLocalDateTime(workRecordDTO.getCreateTime()));
        entity.setAttachments(new FileInfoVO[0]);
        // 机器人工作管控记录题提供的数据没有处置部门，默认使用使用管控记录对应的管控民警
        List<Long> controlPerson = regularList.get(0).getControlPerson() == null ? new ArrayList<>(0) : regularList.get(0).getControlPerson();
        List<UserDto> userListById = permissionService.getUserListById(controlPerson);
        List<Long> controlStation = regularList.get(0).getControlStation() == null ? new ArrayList<>(0) : regularList.get(0).getControlStation();
        List<DeptDto> deptListById = permissionService.getDeptByIds(controlStation);
        int size = userListById.size() > userListById.size() ? userListById.size() : controlStation.size();
        List<SimpleUserVO> workPolieList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            UserDto userDto = i < userListById.size() ? userListById.get(i) : new UserDto();
            DeptDto deptDto = i < controlStation.size() ? deptListById.get(i) : new DeptDto();
            workPolieList.add(new SimpleUserVO(userDto.getId(), userDto.getRealName(), userDto.getIdNumber(), deptDto.getId(),
                    deptDto.getName(), deptDto.getShortName(), deptDto.getCode(), userDto.getTel(), userDto.getDuty(), userDto.getStatus(), deptDto.getDistrictCode(), "", null, userDto.getPoliceCode(),null));
        }
        entity.setWorkPolice(workPolieList.toArray(new SimpleUserVO[workPolieList.size()]));

        if(entity.getId()==null){
            serviceWorkRecordMapper.insert(entity);
        }else {
            serviceWorkRecordMapper.updateById(entity);
        }

        // 更新常控人员状态，否则常控常控人员状态为null，查看工作记录详细会报错
        RegularMonitorEntity regularMonitorEntity = regularList.get(0);
        Optional<ServiceWorkRecordEntity> lastRecord = serviceWorkRecordMapper.getLastRecord(regularId);
        if(lastRecord.isPresent()){
            ServiceWorkRecordEntity lastEntity = lastRecord.get();
            regularMonitorEntity.setPersonStatus(lastEntity.getStatus());
            regularMonitorMapper.updateById(regularMonitorEntity);
        }
    }
}