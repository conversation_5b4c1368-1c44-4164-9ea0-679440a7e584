package com.trs.police.control.kafka.v2.flow.processor;

import com.alibaba.fastjson.JSON;
import com.trs.data.exception.ProcessException;
import com.trs.data.processor.IDataProcessor;
import com.trs.police.common.core.dto.HitInfo;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.kafka.v2.flow.processor.warning.model.WarningModelProcessor;
import com.trs.police.control.mapper.MonitorWarningModelMapper;
import com.trs.police.control.service.impl.WarningProcessServiceImpl;
import com.trs.police.control.utils.WarningModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 预警消费类
 * *@author:wen.wen
 * *@create 2024-12-26 17:10
 **/
@Slf4j
public abstract class BasicWarningMessageProcessor implements IWarningMessageProcessor {

    @Resource
    protected MonitorWarningModelMapper monitorWarningModelMapper;

    @Resource
    protected WarningProcessServiceImpl warningProcessServiceImpl;

    @Resource
    private List<WarningModelProcessor> warningModelProcessors;

    @Override
    public WarningMessageContext process(WarningMessageContext context) throws ProcessException {
        WarningDTO dto = context.getWarningDTO();
        final HitInfo hitInfo = dto.getHitInfo();
        List<String> hitTags = hitInfo.getHitTags();
        ControlInfo controlInfo = context.getControlInfo();
        //设置一些基础需要的东西
        if (Boolean.FALSE.equals(controlInfo.getIsInMonitor())) {
            log.info("布控不存在或状态不为布控中，丢弃该条预警! 预警内容：{}", dto);
            return null;
        }
        if (Objects.isNull(context.getTrackVO().getPerson())) {
            log.info("预警轨迹找不到对应的布控人员，丢弃该条预警! 轨迹内容：{}", dto);
            return null;
        }
        //设置重点区域ID
        List<Long> areaIds = getCodeByHitTag(hitTags, "areaId");
        //设置重点场所code
        List<Long> placeCodes = getCodeByHitTag(hitTags, "areaLabel");
        context.setAreaIds(areaIds);
        context.setPlaceCodes(placeCodes);
        //设置命中的模型ID
        context.setWarningModelIds(hitModelIds(context));
        if (CollectionUtils.isEmpty(context.getWarningModelIds())) {
            log.warn("布控模型id和预警模型id无交集，丢弃该条预警! 预警内容：{}", context.getMessage());
            return null;
        }
        //增加预警模型的调用次数
        WarningModelUtil.asyncCallModalCount(context.getWarningModelIds());
        //推送到统计模块
        WarningModelUtil.pushModelStatisticsMessage(context.getWarningModelIds());
        //执行真实行为
        context = dataProcessor().process(context);
        return context;
    }

    /**
     * 根据前缀获取hitTag中的信息
     *
     * @param hitTags    命中的标签
     * @param keyPrefix key的前缀
     * @return List
     */
    private List<Long> getCodeByHitTag(List<String> hitTags, String keyPrefix) {
        if (CollectionUtils.isEmpty(hitTags)) {
            return Collections.emptyList();
        }
        try {
            return hitTags.stream().map(item -> {
                final String[] subscribeInfo = Strings.split(item, '_');
                return (subscribeInfo.length > 0 && keyPrefix.equals(subscribeInfo[0])) ? Long.parseLong(subscribeInfo[1]) : null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (RuntimeException e) {
            log.info("解析{}失败,默认处理为有轨迹即预警，tag={}", keyPrefix, hitTags);
            return Collections.emptyList();
        }
    }

    /**
     * 解析获取命中的模型
     *
     * @param context WarningMessageContext
     * @return 模型ID
     */
    public List<Long> hitModelIds(WarningMessageContext context) {
//        List<Long> areaIds = context.getAreaIds();
//        List<Long> placeCodes = context.getPlaceCodes();
//        //--加载所有预警模型，有两个来源：通用区域（anyTrackModelId，monitorAreaModelId）和中台返回区域
//        // 默认带有有轨迹即预警
//        List<Long> warningModelIds = new ArrayList<>(List.of(warningProcessServiceImpl.getAnyTrackModelId()));
//        // 重点区域预警模型
//        if (!CollectionUtils.isEmpty(areaIds)) {
//            List<Long> areaModelIds = monitorWarningModelMapper.selectModelIdsByAreaId(areaIds);
//            warningModelIds.addAll(areaModelIds);
//            warningModelIds.addAll(warningProcessServiceImpl.getMonitorAreaModelId());
//        }
//        // 场所预警模型
//        if (!CollectionUtils.isEmpty(placeCodes)) {
//            List<Long> placeModelIds = monitorWarningModelMapper.selectModelIdsByPlaceCodes(placeCodes);
//            warningModelIds.addAll(placeModelIds);
//        }
        List<Long> warningModelIds = warningModelProcessors.stream()
                .flatMap(w -> w.collect(context).stream())
                .collect(Collectors.toList());
        log.info("命中的模型id：{}", JSON.toJSONString(warningModelIds));
        List<ControlInfo.WarningInfo> warningInfo = context.getControlInfo().getWarningInfo();
        if (!CollectionUtils.isEmpty(warningInfo)) {
            List<Long> modelIds = new ArrayList<>();
            for (ControlInfo.WarningInfo info : warningInfo) {
                modelIds.addAll(CollectionUtils.isEmpty(info.getModelIds()) ? Collections.emptyList() : info.getModelIds());
            }
            log.info("管控配置的模型id：{}", JSON.toJSONString(modelIds));
            warningModelIds.retainAll(modelIds);
            log.info("交集模型id：{}", JSON.toJSONString(warningModelIds));
        }
        return warningModelIds;
    }


    /**
     * 执行操作行为 不同的预警类型有自己的实现方式
     *
     * @return IDataProcessor
     */
    public abstract IDataProcessor<WarningMessageContext, WarningMessageContext> dataProcessor();
}
