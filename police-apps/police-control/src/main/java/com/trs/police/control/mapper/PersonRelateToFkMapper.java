package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.control.domain.dto.fkrxyj.PersonRelateToFkListDTO;
import com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity;
import com.trs.police.control.domain.vo.fkrxyj.PersonRelateToFkRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> recivejt
 * @date : 2024/01/23 11:30
 */
@Mapper
public interface PersonRelateToFkMapper extends BaseMapper<PersonRelateToFkEntity> {

    /**
     * 人员列表
     *
     * @param params params
     * @param page pg
     * @return 人员列表
     */
    Page<PersonRelateToFkEntity> personList(@Param("params") ListParamsRequest params, Page<PersonRelateToFkEntity> page);

    /**
     * 查询用户是否对人员进行了常控
     *
     * @param personId 人员Id
     * @param userId   用户id
     * @return 是否常控
     */
    @Select("select count(*) > 0 from t_control_regular_monitor where target_id=#{personId} and create_user_id=#{userId} and status in (2, 3, 5) and deleted=0")
    Boolean selectIsRegularControlByUser(@Param("personId") Long personId, @Param("userId") Long userId);

    /**
     * 获取未建档人员列表
     *
     * @param dto  查询条件
     * @param page 分页参数
     * @return 结果
     */
    Page<PersonRelateToFkEntity> getNotRecordList(@Param("dto") PersonRelateToFkListDTO dto, Page<PersonRelateToFkEntity> page);

    /**
     * 获取结合
     *
     * @param dto  查询条件
     * @param page 分页参数
     * @return 结果
     */
    Page<PersonRelateToFkRecordVo> getRecordList(@Param("dto") PersonRelateToFkListDTO dto, Page<PersonRelateToFkRecordVo> page);

    /**
     * 通过ids集合获取 PersonRelateToFkRecordVo 信息
     *
     * @param ids ids集合
     * @return 返回值
     */
    List<PersonRelateToFkRecordVo> getRecordListByIds(@Param("ids") List<Long> ids);

    /**
     * 通过ids集合获取 标签名
     *
     * @param ids ids集合
     * @return 返回值
     */
    String getLabelNames(@Param("ids") List<Long> ids);

    /**
     * 通过前缀匹配所有的deptCode
     *
     * @param preFixCodes preFixCodes
     * @return 返回值
     */
    List<String> getTotalDeptCodes(@Param("preFixCodes") List<String> preFixCodes);

    /**
     * 根据身份证号获取fk人员信息
     *
     * @param idCard 身份证号
     * @return fk人员
     */
    PersonRelateToFkEntity getByIdCard(@Param("idCard") String idCard);

}
