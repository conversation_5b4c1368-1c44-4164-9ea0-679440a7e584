package com.trs.police.control.domain.vo.warning;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 其他信息
 *
 * <AUTHOR>
 */
@Data
public class CustomInfo implements Serializable {

    private static final long serialVersionUID = -5261139435147251553L;
    /**
     * 布控id
     */
    private Long monitorId;
    /**
     * 布控名称
     */
    private String monitorName;
    /**
     * 模型id
     */
    private List<Long> modelId;
    /**
     * 模型名称
     */
    private String modelName;

    @Override
    public String toString() {
        return "CustomInfo{" +
                "monitorId=" + monitorId +
                ", monitorName='" + monitorName + '\'' +
                ", modelName='" + modelName + '\'' +
                '}';
    }
}