package com.trs.police.control.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "warning.process")
@Data
public class WarningProperties {

    /**
     * 群体聚集模型id
     */
    private Long groupAggregateModelId;
    /**
     * 群体分散模型id
     */
    private Long groupSpreadModelId;
    /**
     * 签收期限（小时）
     */
    private Integer signLimit = 24;
    /**
     * 反馈期限（小时）
     */
    private Integer replyLimit = 48;
}
