package com.trs.police.control.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.trs.police.common.core.constant.DateTimeConstants;
import com.trs.police.common.core.constant.enums.DeptTypeEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.dto.Source;
import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.common.core.excpetion.ParamValidationException;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.*;
import com.trs.police.common.core.vo.GeometryVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.MoyePageResult;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.ModelTreeVO;
import com.trs.police.common.core.vo.control.SourceListVO2;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.MessageService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.control.constant.MoyeConstant;
import com.trs.police.control.domain.dto.SourceDto;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.request.PointRequest;
import com.trs.police.control.domain.vo.HaiKanCamerasDeviceInfoVO;
import com.trs.police.control.domain.vo.basic.*;
import com.trs.police.control.handler.CustomCellWriteHandler;
import com.trs.police.control.mapper.SourceMapper;
import com.trs.police.control.mapper.WarningSourceTypeMapper;
import com.trs.police.control.properties.SourceSynchronizeProperties;
import com.trs.police.control.properties.SubscribeProperties;
import com.trs.police.control.service.SourceService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @author: zhou.youpeng
 * @date: 2022/9/1 16:03
 */
@Slf4j
@Service
public class SourceServiceImpl implements SourceService {

    @Resource
    private SourceMapper sourceMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private SourceSynchronizeProperties sourceSynchronizeProperties;

    @Resource
    private PermissionService permissionService;

    @Resource
    private DictService dictService;

    OkHttpUtil okHttpUtil = OkHttpUtil.getInstance();

    @Resource
    private SubscribeProperties subscribeProperties;
    @Resource
    private WarningSourceTypeMapper warningSourceTypeMapper;

    WKTReader wktReader = new WKTReader(new GeometryFactory(new PrecisionModel(), 4326));

    Map<Long, Integer> deptTypeLengthMap = Map.of(
            0L, 2,
            1L, 4,
            2L, 6,
            3L, 8
    );

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateById(Long sourceId, SourceVO sourceVO) {
        SourceEntity sourceEntity = sourceMapper.selectById(sourceId);
        if (Objects.isNull(sourceEntity)) {
            throw new ParamValidationException("当前感知源不存在，请刷新重试！");
        }
        BeanUtil.copyPropertiesIgnoreNull(sourceVO, sourceEntity);
        sourceEntity.setNestingCategory(sourceVO.getNestingCategory());
        sourceEntity.setCategory(KeyValueTypeVO.nestingListSimplification(sourceVO.getNestingCategory()).stream().map(o -> Long.parseLong(o.toString())).collect(Collectors.toList()));
        sourceMapper.updateSourceById(sourceEntity);
    }

    @Override
    public SourceBasicVO getSourceInfo(Long sourceId) {
        SourceBasicVO sourceBasicVO = sourceMapper.getSourceBasicInfo(sourceId);
        return setDataAmount(sourceBasicVO);
    }

    /**
     * 查询今日数据量/总数据量
     *
     * @param sourceBasicVO 数据源
     * @return 结果
     */
    private SourceBasicVO setDataAmount(SourceBasicVO sourceBasicVO) {
        sourceBasicVO.setTodayDataAmount(0);
        sourceBasicVO.setTotalDataAmount(0);
        return sourceBasicVO;
    }

    @Override
    public PageResult<SourceListVO> getSourcePageList(ListParamsRequest request) {
        // 是否本地感知源
        Boolean isLocalSource = BeanFactoryHolder.getEnv().getProperty("control.source.isLocal", Boolean.class, false);
        Page<SourceListVO> pageParams = request.getPageParams().toPage();
        pageParams.setSearchCount(false);
        Optional<KeyValueTypeVO> any = request.getFilterParams()
                .stream()
                .filter(kv -> "areaRange".equals(kv.getKey()))
                .findAny();
        List<GeometryVO> geometry = any.isPresent() && Objects.nonNull(any.get().getValue())
                ? String.valueOf(any.get().getValue()).isEmpty() ? null : JSONArray.parseArray(any.get().getValue().toString(), GeometryVO.class)
                : null;
        pageParams.setTotal(sourceMapper.selectCount(
                request.getFilterParams(),
                request.getSearchParams(),
                Objects.isNull(geometry) ? null : geometry.get(0),
                isLocalSource
        ));
        Page<SourceListVO> page = sourceMapper.selectPageListByParams(
                request.getFilterParams(),
                request.getSearchParams(),
                Objects.isNull(geometry) ? null : geometry.get(0),
                isLocalSource,
                pageParams
        );
        return PageResult.of(
                page.getRecords(),
                request.getPageParams().getPageNumber(),
                page.getTotal(),
                request.getPageParams().getPageSize()
        );
    }

    @Override
    public AreaOfSourceVO areaOfSource(List<GeometryVO> geometryList) {
        List<SourceListVO> sourceList = getAreaOfSourceList(geometryList);
        AreaOfSourceVO areaOfSourceVO = new AreaOfSourceVO();
        areaOfSourceVO.setTotal(sourceList.size());
        List<AreaOfSourceVO.SourceStatistics> statisticsList = new ArrayList<>();
        sourceList.stream()
                .distinct()
                .filter(source -> StringUtils.isNotBlank(source.getTopType()))
                .collect(Collectors.groupingBy(SourceListVO::getTopType))
                .forEach((type, sourceListVO) -> {
                    AreaOfSourceVO.SourceStatistics statistics = new AreaOfSourceVO.SourceStatistics();
                    statistics.setType(type);
                    statistics.setList(sourceListVO);
                    statisticsList.add(statistics);
                });
        areaOfSourceVO.setSource(statisticsList);
        return areaOfSourceVO;
    }

    @Override
    public List<SourceListVO> getAreaOfSourceList(List<GeometryVO> geometryList) {
        // 是否本地感知源
        Boolean isLocalSource = BeanFactoryHolder.getEnv().getProperty("control.source.isLocal", Boolean.class, false);
        return geometryList.stream().map(geometry -> {
                    if (geometry.getType().equals("line")) {
                        List<Polygon> polygons = GeoUtils.lineVoToPolygonList(geometry);
                        return polygons.stream().map(polygon -> {
                            GeometryVO polygonVO = new GeometryVO();
                            polygonVO.setType("polygon");
                            polygonVO.setGeometry(polygon.toText());
                            return sourceMapper.getSourceInArea(polygonVO, isLocalSource);
                        }).flatMap(Collection::stream).collect(Collectors.toList());
                    }
                    return sourceMapper.getSourceInArea(geometry, isLocalSource);
                }).flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取区域内感知源数量<BR>
     *
     * @param geometryList 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/10/24 14:31
     */
    @Override
    public Long getAreaOfSourceSize(List<GeometryVO> geometryList) {
        // 是否本地感知源
        Boolean isLocalSource = BeanFactoryHolder.getEnv().getProperty("control.source.isLocal", Boolean.class, false);
        return geometryList.stream().mapToLong(geometry -> {
            if (geometry.getType().equals("line")) {
                List<Polygon> polygons = GeoUtils.lineVoToPolygonList(geometry);
                return polygons.stream().mapToLong(polygon -> {
                    GeometryVO polygonVO = new GeometryVO();
                    polygonVO.setType("polygon");
                    polygonVO.setGeometry(polygon.toText());
                    return sourceMapper.getSourceInAreaCount(polygonVO, isLocalSource);
                }).sum();
            }
            return sourceMapper.getSourceInAreaCount(geometry, isLocalSource);
        }).sum();
    }

    @Override
    public List<SourceListVO> getSourcePointList(PointRequest request) {
        // 是否本地感知源
        Boolean isLocalSource = BeanFactoryHolder.getEnv().getProperty("control.source.isLocal", Boolean.class, false);
        // 参数校验
        if (request == null || CollectionUtils.isEmpty(request.getGeometries())) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        final List<GeometryVO> geometries = request.getGeometries();
        //转换前端传入的半径(单位度)为单位米
        geometries.forEach(geometry -> {
            if (geometry.getType().equals("circle") && Objects.nonNull(geometry.getGeometry()) && Objects.nonNull(geometry.getProperties())) {
                String replace = geometry.getGeometry().replace("POINT(", "").replace(")", "");
                // 提取纬度
                double latitude = Double.parseDouble(replace.split(" ")[1]);
                // 根据纬度计算实际距离
                double metersPerDegree = 111319.9 * Math.cos(Math.toRadians(latitude));
                geometry.getProperties().setRadius(metersPerDegree * geometry.getProperties().getRadius());
            }
        });
        final List<KeyValueTypeVO> filterParams = request.getFilterParams();
        final SearchParams searchParams = request.getSearchParams();
        return geometries.stream().map(geometry -> {
                    if (geometry.getType().equals("line")) {
                        List<Polygon> polygons = GeoUtils.lineVoToPolygonList(geometry);
                        return polygons.stream().map(polygon -> {
                            GeometryVO polygonVO = new GeometryVO();
                            polygonVO.setType("polygon");
                            polygonVO.setGeometry(polygon.toText());
                            return sourceMapper.getSourcePointInArea(polygonVO, filterParams, searchParams, isLocalSource);
                        }).flatMap(Collection::stream).collect(Collectors.toList());
                    }
                    return sourceMapper.getSourcePointInArea(geometry, filterParams, searchParams, isLocalSource);
                }).flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchUpdate(SourceBatchRequestVO sourceVO) {
        sourceVO.setCategory(JsonUtil.toJsonString(KeyValueTypeVO.nestingListSimplification(sourceVO.getNestingCategory()).stream().map(o -> Long.parseLong(o.toString())).collect(Collectors.toList())));
        sourceMapper.batchUpdate(sourceVO);
    }

    @Override
    public List<SourceListVO> getSourceList(ListParamsRequest request) {
        // 是否本地感知源
        Boolean isLocalSource = BeanFactoryHolder.getEnv().getProperty("control.source.isLocal", Boolean.class, false);
        Optional<KeyValueTypeVO> any = request.getFilterParams()
                .stream()
                .filter(kv -> "areaRange".equals(kv.getKey()))
                .findAny();
        List<GeometryVO> geometry = any.isPresent() && Objects.nonNull(any.get().getValue())
                ? String.valueOf(any.get().getValue()).isEmpty() ? null : JSONArray.parseArray(any.get().getValue().toString(), GeometryVO.class)
                : null;
        return sourceMapper.selectListByParams(request.getFilterParams(),
                request.getSearchParams(),
                Objects.isNull(geometry) ? null : geometry.get(0),
                isLocalSource);
    }

    @Override
    public void exportSource(SourceExportRequestVO sourceExportRequestVO, HttpServletResponse response)
            throws IOException {
        final String fileName = String.format("感知源_%s.xlsx",
                LocalDateTime.now().format(DateTimeConstants.DATE_TIME_FORMATTER_EXPORT));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition",
                "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        List<SourceListVO> sourceVOList = sourceMapper.selectByExportRequest(sourceExportRequestVO);
        EasyExcelFactory.write(response.getOutputStream(), SourceListVO.class)
                .registerWriteHandler(new CustomCellWriteHandler())
                .excludeColumnFiledNames(List.of("point"))
                .sheet()
                .doWrite(sourceVOList);
    }

    private Map<String, Geometry> getGeometryMap() {
        return dictService.getDistrictByLevel(3).stream()
                .filter(e -> StringUtils.isNotBlank(e.getContour()))
                .collect(HashMap::new,
                        (map, e) -> {
                            try {
                                map.put(e.getName() + "-" + e.getCode(), wktReader.read(e.getContour()));
                            } catch (ParseException ex) {
                                ex.printStackTrace();
                            }
                        },
                        HashMap::putAll);
    }

    /**
     * 获取顶级区域地理信息
     *
     * @return 顶级区域地理信息
     */
    public Geometry getTopDistrict() {
        DistrictListDto district = dictService.getDistrictByCode(MoyeConstant.DEFAULT_TOP_DISTRICT_CODE);
        try {
            Geometry topDistrict = wktReader.read(district.getContour());
            assert Objects.nonNull(topDistrict);
            return topDistrict;
        } catch (ParseException e) {
            log.error("顶级区域地理信息获取有误！", e);
            throw new TRSException("顶级区域地理信息获取有误！");
        }
    }

    @Override
    public void synchronizeSource() {
        Geometry topDistrict = this.getTopDistrict();
        Map<String, Geometry> geometryMap = getGeometryMap();
        int pageNum = 0;
        int totalPage = 1;
        int synchronizeCount = 0;
        int pageSize = sourceSynchronizeProperties.getPageSize();
        HashMap<String, Object> body = this.generateBody();
        while (totalPage != pageNum) {
            body.put(MoyeConstant.PAGE_NUM, ++pageNum);
            Map<String, String> headersMap = subscribeProperties.getHeadersMap();
            headersMap.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
            String response = okHttpUtil.postData(subscribeProperties.getGeneralSearchUrl(),
                    JsonUtil.toJsonString(body), headersMap);
            ResponseMessage responseMessage = JsonUtil.parseObject(response, ResponseMessage.class);
            if (Objects.isNull(responseMessage)) {
                log.error("从moye同步数据源数据失败，返回值为空！");
                break;
            }
            MoyePageResult pageResult = JsonUtil.parseSpecificObject(responseMessage.getData(),
                    MoyePageResult.class);
            if (Objects.isNull(pageResult)) {
                log.error("从moye同步数据源数据失败，返回数据为空！");
                break;
            }
            if (pageNum == 1) {
                totalPage = (int) Math.ceil((double) pageResult.getTotal() / pageSize);
            }
            List<SourceDto> sourceList = JsonUtil.parseArray(JsonUtil.toJsonString(pageResult.getData()),
                            MoyeSourceVO.class)
                    .stream()
                    .map(e -> setDistrictInfo(topDistrict, geometryMap, e))
                    .collect(Collectors.toList());
            sourceMapper.synchronizeSource(sourceList);
            synchronizeCount += sourceList.size();
        }
        log.info("从moye同步数据源数据：" + synchronizeCount + "条");
    }

    @Override
    public Long getSourceIdByName(String name) {
        return sourceMapper.selectByName(name);
    }

    /**
     * 生成请求体
     *
     * @return {@link HashMap}
     */
    private HashMap<String, Object> generateBody() {
        HashMap<String, Object> body = new HashMap<>();
        body.put(MoyeConstant.DATA_EN_NAME, sourceSynchronizeProperties.getTableName());
        body.put(MoyeConstant.REQUIRED_ITEMS, Arrays.stream(MoyeSourceVO.class.getDeclaredFields()).map(Field::getName)
                .collect(Collectors.toList()));
        body.put(MoyeConstant.PAGE_SIZE, sourceSynchronizeProperties.getPageSize());
        return body;
    }

    /**
     * 将原始感知源数据转为dto，并通过坐标计算区域名称和代码
     *
     * @param topDistrict  行政区域
     * @param sourceVO     原始感知源数据
     * @param districtInfo 区域范围信息
     * @return {@link SourceDto}
     */
    private SourceDto setDistrictInfo(Geometry topDistrict, Map<String, Geometry> districtInfo, MoyeSourceVO sourceVO) {
        SourceDto sourceDto = sourceVO.toSourceDto();
        if (!checkCoordinate(sourceVO.getJdwgs84(), sourceVO.getWdwgs84())) {
            sourceDto.setIllegal();
            return sourceDto;
        }
        try {
            Geometry point = wktReader.read(
                    String.format("point(%s %s)", DecimalUtil.keepFourDecimals(sourceVO.getJdwgs84()),
                            DecimalUtil.keepFourDecimals(sourceVO.getWdwgs84())));
            if (checkInTopDistrict(topDistrict, point)) {
                for (Entry<String, Geometry> geometryEntry : districtInfo.entrySet()) {
                    Geometry geometry = geometryEntry.getValue();
                    for (int i = 0; i < geometry.getNumGeometries(); i++) {
                        Geometry geometryN = geometry.getGeometryN(i);
                        if (point.within(geometryN)) {
                            String[] split = geometryEntry.getKey().split("-");
                            sourceDto.setDistrictName(split[0]);
                            sourceDto.setDistrictCode(split[1]);
                            return sourceDto;
                        }
                    }
                }
                return sourceDto;
            }
            //点位不在顶级区域内
            sourceDto.setIsLegal(Boolean.FALSE);
        } catch (ParseException ex) {
            //点位坐标非法
            sourceDto.setIllegal();
            log.error("", ex);
        }
        return sourceDto;
    }

    private boolean checkCoordinate(String lonString, String latString) {
        try {
            if (StringUtils.isBlank(lonString) || StringUtils.isBlank(latString)) {
                return false;
            }
            double lon = Double.parseDouble(lonString);
            double lat = Double.parseDouble(latString);
            return MoyeConstant.MIN_LAT <= lon && lon <= MoyeConstant.MAX_LON
                    && MoyeConstant.MIN_LAT <= lat && lat <= MoyeConstant.MAX_LAT;
        } catch (NumberFormatException e) {
            log.error("", e);
            return false;
        }
    }

    /**
     * 判断点位是否在顶级区域内
     *
     * @param topDistrict 顶级区域
     * @param point       点位
     * @return boolean
     */
    public static boolean checkInTopDistrict(Geometry topDistrict, Geometry point) {
        return IntStream.range(0, topDistrict.getNumGeometries()).anyMatch(i -> {
            Geometry geometryN = topDistrict.getGeometryN(i);
            try {
                return point.within(geometryN);
            } catch (Exception ignore) {
                return false;
            }
        });
    }

    /**
     * 同步感知源
     *
     * @param source 感知源
     */
    @Override
    public void saveOrUpdateSource(List<Source> source) {
        try {
            if (CollectionUtils.isEmpty(source)) {
                return;
            }
            // 拆分感知源为每50条一组
            List<List<Source>> partition = Lists.partition(source, 50);
            for (List<Source> sources : partition) {
                try {
                    List<SourceDto> sourceList = new ArrayList<>();
                    List<SourceEntity> updateList = new ArrayList<>();
                    List<String> uniqueKeys = sources.stream().map(item -> item.getId() + "-" + item.getType()).collect(Collectors.toList());
                    // 批量查询感知源列表
                    List<SourceEntity> sourceEntities = sourceMapper.selectBatchByUniqueKey(uniqueKeys);
                    if (CollectionUtils.isEmpty(sourceEntities)) {
                        sourceEntities = new ArrayList<>();
                    }
                    // 按照uniqueKey分组
                    Map<String, List<SourceEntity>> map = sourceEntities.stream().filter(item -> item.getUniqueKey() != null).collect(Collectors.groupingBy(SourceEntity::getUniqueKey));
                    sources.forEach(item -> {
                        String uniqueKey = item.getId() + "-" + item.getType();
                        if (!map.containsKey(uniqueKey)) {
                            // 不存在的就新增
                            SourceDto sourceDto = createSourceDto(item, uniqueKey);
                            sourceList.add(sourceDto);
                        } else {
                            // 更新已有设备的经纬度
                            List<SourceEntity> existsEntityList = map.get(uniqueKey);
                            for (SourceEntity sourceEntity : existsEntityList) {
                                updateList.add(sourceEntity);
                                // 修改名称
                                sourceEntity.setName(item.getName());
                                // 修改地址
                                sourceEntity.setAddress(item.getAddress());
                                // 经纬度
                                String point = String.format("point(%s %s)",
                                        Objects.isNull(item.getLongitude()) ? "1" : item.getLongitude(),
                                        Objects.isNull(item.getLatitude()) ? "1" : item.getLatitude());
                                try {
                                    Geometry geometry = getTopDistrict();
                                    boolean isLegal = checkInTopDistrict(geometry, wktReader.read(point));
                                    if (!isLegal) {
                                        //经纬度反转一次
                                        String point2 = String.format("point(%s %s)",
                                                Objects.isNull(item.getLatitude()) ? "1" : item.getLatitude(),
                                                Objects.isNull(item.getLongitude()) ? "1" : item.getLongitude());
                                        isLegal = checkInTopDistrict(geometry, wktReader.read(point2));
                                    }
                                    sourceEntity.setPoint(String.format("point(%s %s)",
                                            Objects.isNull(item.getLatitude()) ? "1" : item.getLatitude(),
                                            Objects.isNull(item.getLongitude()) ? "1" : item.getLongitude()));
                                    sourceEntity.setIsLegal(isLegal);
                                } catch (ParseException e) {
                                    sourceEntity.setIsLegal(Boolean.FALSE);
                                }
                                sourceEntity.setUpdateTime(LocalDateTime.now());
                            }
                        }
                    });
                    // 新增
                    if (!sourceList.isEmpty()) {
                        log.info("新增{} 条感知源信息", sourceList.size());
                        sourceMapper.synchronizeSource(sourceList);
                    }
                    // 更新
                    if (!updateList.isEmpty()) {
                        log.info("更新{} 条感知源信息", updateList.size());
                        for (SourceEntity sourceEntity : updateList) {
                            sourceMapper.updateSourceByIdWithOutUserInfo(sourceEntity);
                        }
                    }
                } catch (Exception e) {
                    log.error("感知源数据入库出错", e);
                }
            }
        } catch (RuntimeException e) {
            log.error("同步感知源失败,SensingMessage:{}", JsonUtil.toJsonString(source), e);
        }
    }

    @NotNull
    private SourceDto createSourceDto(Source item, String uniqueKey) {
        SourceDto sourceDto = new SourceDto();
        sourceDto.setCode(item.getCode());
        sourceDto.setIsLocal(item.getIsLocal());
        sourceDto.setUniqueKey(uniqueKey);
        sourceDto.setAddress(item.getAddress());
        sourceDto.setName(StringUtils.isNotBlank(item.getName()) ? item.getName() : item.getId());
        sourceDto.setTypeCode(item.getType());
        sourceDto.setDistrictCode(sourceSynchronizeProperties.getTopDistrictCode());
        String point = String.format("point(%s %s)",
                Objects.isNull(item.getLongitude()) ? "1" : item.getLongitude(),
                Objects.isNull(item.getLatitude()) ? "1" : item.getLatitude());
        try {
            Geometry geometry = getTopDistrict();
            boolean isLegal = checkInTopDistrict(geometry, wktReader.read(point));
            if (!isLegal) {
                //经纬度反转一次
                String point2 = String.format("point(%s %s)",
                        Objects.isNull(item.getLatitude()) ? "1" : item.getLatitude(),
                        Objects.isNull(item.getLongitude()) ? "1" : item.getLongitude());
                isLegal = checkInTopDistrict(geometry, wktReader.read(point2));
            }
            sourceDto.setPoint(String.format("point(%s %s)",
                    Objects.isNull(item.getLatitude()) ? "1" : item.getLatitude(),
                    Objects.isNull(item.getLongitude()) ? "1" : item.getLongitude()));
            sourceDto.setIsLegal(isLegal);
        } catch (ParseException e) {
            sourceDto.setIsLegal(Boolean.FALSE);
        }
        return sourceDto;
    }

    /**
     * 检查经纬度是否合法
     *
     * @param lat 经度
     * @param lng 维度
     * @return 是否合法
     */
    public boolean checkPoint(Double lat, Double lng) {
        if (Objects.isNull(lat) || Objects.isNull(lng)) {
            return false;
        }
        String point = String.format("point(%s %s)", lng, lat);
        try {
            return checkInTopDistrict(getTopDistrict(), wktReader.read(point));
        } catch (ParseException e) {
            return false;
        }
    }

    @Override
    public List<ModelTreeVO> getSourceTypeTree() {
        List<DictDto> dicts = dictService.getDictListByType("control_warning_source_type");
        return dictToTreeList(dicts);
    }

    private List<ModelTreeVO> dictToTreeList(List<DictDto> types) {
        List<ModelTreeVO> result = new ArrayList<>();
        types.forEach(type -> {
            ModelTreeVO treeVO = new ModelTreeVO();
            treeVO.setName(type.getName());
            treeVO.setId("type_" + type.getCode());
            //dict tree为叶子节点，节点下挂上模型列表
            if (type.getChildren().isEmpty()) {
                List<ModelTreeVO> children = warningSourceTypeMapper.selectBySourceType(type.getCode()).stream()
                        .map(item -> {
                            ModelTreeVO child = new ModelTreeVO();
                            child.setId("source_" + item.getEnName());
                            child.setName(item.getCnName());
                            return child;
                        }).collect(Collectors.toList());
                treeVO.setChildren(children);
            } else {
                treeVO.setChildren(dictToTreeList(type.getChildren()));
            }
            result.add(treeVO);
        });
        return result;
    }

    @Override
    public WarningSourceTypeEntity getWarningSourceTypeByEnName(String enName) {
        return warningSourceTypeMapper.selectByWarningType(enName);
    }

    @Override
    public List<WarningSourceTypeEntity> getCnNameByEnName() {
        return warningSourceTypeMapper.selectAll();
    }

    @Override
    public void syncTwVideoMonitor() {
        int pageNum = 0;
        int totalPage = 1;
        int synchronizeCount = 0;
        int pageSize = 450;
        String baseUrl = BeanFactoryHolder.getEnv().getProperty("tw.video.monitor.url", "");
        String orgCode = BeanFactoryHolder.getEnv().getProperty("tw.video.monitor.orgCode", "510000000000");
        try {
            while (totalPage != pageNum) {
                pageNum++;
                // 构建GET请求URL，添加分页和组织参数
                String url = String.format("%s/points?orgCode=%s&pageNum=%d&pageSize=%d",
                        baseUrl, orgCode, pageNum, pageSize);
                log.info("同步TW视频监控列表 - 请求第{}页数据: {}", pageNum, url);
                // 发送GET请求
                String response = okHttpUtil.getData(url);
                if (StringUtils.isBlank(response)) {
                    log.error("从TW同步视频监控数据失败，返回值为空！");
                    break;
                }
                // 解析响应数据
                ResponseMessage responseMessage = JsonUtil.parseObject(response, ResponseMessage.class);
                if (Objects.isNull(responseMessage)) {
                    log.error("从TW同步视频监控数据失败，返回值解析错误！");
                    break;
                }
                MoyePageResult pageResult = JsonUtil.parseSpecificObject(responseMessage,
                        MoyePageResult.class);
                if (Objects.isNull(pageResult)) {
                    log.error("从TW同步视频监控数据失败，返回数据为空！");
                    break;
                }

                // 转换数据并保存
                if (CollectionUtils.isEmpty(pageResult.getData())) {
                    log.warn("第{}页数据为空", pageNum);
                    // 如果当前页数据为空，说明已经没有更多数据，结束循环
                    break;
                }
                // 第一页时设置一个足够大的总页数
                if (pageNum == 1) {
                    // 设置一个足够大的值，确保能获取所有数据
                    // 当遇到空页时会通过break结束循环
                    totalPage = 500;
                    log.info("TW视频监控列表第一页数据量: {}, 设置最大页数: {}",
                            pageResult.getData().size(), totalPage);
                }
                List<SourceDto> sourceList = JsonUtil.parseArray(JsonUtil.toJsonString(pageResult.getData()),
                                HaiKanCamerasDeviceInfoVO.class)
                        .stream()
                        .filter(Objects::nonNull)
                        .map(HaiKanCamerasDeviceInfoVO::toSourceDto)
                        .peek(dto -> dto.setSourceProvider("tw"))
                        .collect(Collectors.toList());
                // 处理行政区划名称
                handleDistrictName(sourceList);
                if (!sourceList.isEmpty()) {
                    sourceMapper.synchronizeSourceV2(sourceList);
                    synchronizeCount += sourceList.size();
                    log.info("成功同步第{}页数据，本页{}条", pageNum, sourceList.size());
                }
            }
            log.info("TW视频监控列表同步完成，共同步{}条数据", synchronizeCount);
        } catch (Exception e) {
            log.error("同步TW视频监控列表异常", e);
        }
    }

    @Override
    public List<DeptVO> listWithDept(ListParamsRequest request) {
        List<DeptVO> allDeptList = new ArrayList<>();
        String sourceType = null;
        Optional<KeyValueTypeVO> keyValueTypeVO = request.getFilterParams()
                .stream()
                .filter(kv -> "sourceType".equals(kv.getKey()))
                .findAny();
        if (keyValueTypeVO.isPresent()) {
            sourceType = keyValueTypeVO.get().getValue().toString();
        }
        List<Long> pids = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getSearchParams().getSearchValue())){
            pids = sourceMapper.getSourceListByDept(null, sourceType, request.getSearchParams())
                    .stream().map(SourceListVO2::getDeptId).collect(Collectors.toList());
            List<DeptVO> deptList = permissionService.getDeptByIds(pids)
                    .stream()
                    .map(DeptDto::toDeptVO)
                    .collect(Collectors.toList());
            buildSourceList(request, deptList, sourceType, allDeptList);
        }else {
            pids = getPid(request);
            for (Long pid : pids) {
                List<DeptVO> deptList = permissionService.getNewDeptByPid(pid, true);
                //组织信息只需要省、市、县、派出所
                deptList = deptList.stream()
                        .filter(dept ->
                                Set.of(DeptTypeEnum.PROVINCIAL.getCode(), DeptTypeEnum.MUNICIPAL.getCode(),
                                                DeptTypeEnum.COUNTY.getCode(),
                                                DeptTypeEnum.POLICE_STATION.getCode())
                                        .contains(dept.getDeptType().intValue()))
                        .collect(Collectors.toList());
                if (deptList.isEmpty()){
                    continue;
                }
                buildSourceList(request, deptList, sourceType, allDeptList);
            }
        }
        return allDeptList;
    }

    private void buildSourceList(ListParamsRequest request, List<DeptVO> deptList, String sourceType, List<DeptVO> allDeptList) {
        //反查当前部门下的感知源数据
        List<Long> deptIdList = deptList.stream().map(DeptVO::getDeptId).collect(Collectors.toList());
        Map<Long, List<SourceListVO2>> sourceListMap = sourceMapper.getSourceListByDept(deptIdList, sourceType, request.getSearchParams())
                .stream().collect(Collectors.groupingBy(SourceListVO2::getDeptId, Collectors.toList()));
        deptList.forEach(vo -> {
            vo.setSourceList(sourceListMap.getOrDefault(vo.getDeptId(),new ArrayList<>()));
        });
        allDeptList.addAll(deptList);
    }

    private List<Long> getPid(ListParamsRequest request) {
        List<Long> pid = new ArrayList<>();
        Optional<KeyValueTypeVO> keyValueTypeVO = request.getFilterParams()
                .stream()
                .filter(kv -> "pid".equals(kv.getKey()))
                .findAny();
        keyValueTypeVO.ifPresent(valueTypeVO -> {
            if (com.trs.common.utils.StringUtils.isNotEmpty(valueTypeVO.getValue().toString())) {
                if ("2".equals(valueTypeVO.getValue().toString())){
                    DeptDto dept = permissionService.getDeptByCode("510000000000");
                    pid.add(dept.getId());
                }else {
                    pid.add(Long.valueOf(valueTypeVO.getValue().toString()));
                }
            }
        });
        Optional<KeyValueTypeVO> keyValueTypeVo2 = request.getFilterParams()
                .stream()
                .filter(kv -> "districtCode".equals(kv.getKey()))
                .findAny();
        if (keyValueTypeVo2.isPresent() && CollectionUtils.isEmpty(pid)) {
            KeyValueTypeVO keyValueType2 = keyValueTypeVo2.get();
            //如果选择100000，则默认为省
            if ("100000".equals(keyValueType2.getValue().toString())) {
                pid.add(2L);
            }
            if (StringUtils.isNotBlank(keyValueType2.getValue().toString())) {
                List<String> districtCodeList = Arrays.stream(keyValueType2.getValue().toString()
                                .split(","))
                        .map(code -> code + "000000")
                        .collect(Collectors.toList());
                List<DeptDto> deptList = permissionService.getDeptByCodes(districtCodeList);
                deptList.forEach(dept -> pid.add(dept.getId()));

            }
        }
        return pid;
    }

    private void handleDistrictName(List<SourceDto> sourceList) {
        List<String> districtCodes = sourceList.stream()
                .map(source -> standardizeDistrictCode(source.getDistrictCode(), source.getCode()))
                .distinct()
                .collect(Collectors.toList());
        List<DeptDto> deptList = permissionService.getDeptByCodes(districtCodes);
        Map<String, String> deptMap = deptList.stream()
                .collect(Collectors.toMap(DeptDto::getCode, DeptDto::getShortName));
        Map<String, Long> deptIdMap = deptList.stream()
                .collect(Collectors.toMap(DeptDto::getCode, DeptDto::getId));
        // 设置区划名称
        sourceList.forEach(source -> {
            String standardCode = standardizeDistrictCode(source.getDistrictCode(), source.getCode());
            source.setDistrictName(deptMap.getOrDefault(standardCode, null));
            source.setDept(deptIdMap.getOrDefault(standardCode, null));

        });
    }

    /**
     * 将区划编码标准化为12位
     * 不足12位的补充0
     *
     * @param districtCode 原始区划编码
     * @param code         源编码
     * @return 标准化后的12位区划编码
     */
    private String standardizeDistrictCode(String districtCode, String code) {
        if (StringUtils.isBlank(districtCode)) {
            return null;
        }
        // 如果已经是12位，直接返回
        if (districtCode.length() == 12) {
            return districtCode;
        }
        if (districtCode.length() > 12) {
            StringBuilder sb = new StringBuilder(code.substring(0, 6));
            while (sb.length() < 12) {
                sb.append("0");
            }
            return sb.toString();
        }
        // 不足12位，补充0
        StringBuilder sb = new StringBuilder(districtCode);
        while (sb.length() < 12) {
            sb.append("0");
        }

        return sb.toString();
    }
}
