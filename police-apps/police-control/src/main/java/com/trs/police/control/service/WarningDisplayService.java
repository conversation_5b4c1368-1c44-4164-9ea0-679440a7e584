package com.trs.police.control.service;

import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.control.CategoryVO;
import com.trs.police.common.core.vo.control.WarningPhotoVO;
import com.trs.police.common.core.vo.control.WarningSourceVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.control.domain.dto.NameUniqueDTO;
import com.trs.police.control.domain.dto.WarningConfigDTO;
import com.trs.police.control.domain.vo.WarningRankVO;
import com.trs.police.control.domain.vo.WarningTrackPointVO;
import com.trs.police.control.domain.vo.warning.*;
import com.trs.web.builder.base.RestfulResultsV2;

import java.util.List;

/**
 * 预警展示
 *
 * <AUTHOR>
 */
public interface WarningDisplayService {

    /**
     * 预警列表查询
     *
     * @param request 参数
     * @return 列表
     */
    PageResult<WarningListVO> getMyWarningList(ListParamsRequest request);

    /**
     * 全部预警列表查询
     *
     * @param request 参数
     * @return 列表
     */
    PageResult<WarningListVO> getAllWarningList(ListParamsRequest request);

    /**
     * 全部预警列表坐标查询
     *
     * @param request 参数
     * @return 列表
     */
    PageResult<WarningAllInfoVO> getAllWarningPointList(ListParamsRequest request);

    /**
     * 查询待办列表
     *
     * @param pageParams 分页
     * @return 结果
     */
    PageResult<TodoTaskVO> getWarningTodo(PageParams pageParams);

    /**
     * 预警详情
     *
     * @param id 预警id
     * @return 详情
     */
    WarningDetailVO getWarningDetail(Long id);

    /**
     * 预警活动时间、地点
     *
     * @param id 预警id
     * @param portType 端口类型
     * @return vo
     */
    WarningActivityVO getWarningActivity(String id, String portType);

    /**
     * 预警活动人员
     *
     * @param id 预警id
     * @param portType 端口类型
     * @return 人员
     */
    PersonCardVO getWarningPerson(String id,String portType);

    /**
     * 预警活动照片
     *
     * @param id 预警id
     * @return 照片
     */
    WarningPhotoVO getWarningPhoto(Long id);

    /**
     * 预警感知源
     *
     * @param id 预警id
     * @param portType 端口类型
     * @return 感知源
     */
    WarningSourceVO getWarningSource(String id,String portType);

    /**
     * 预警详情-模块列表
     *
     * @param id 预警id
     * @return 模块列表
     */
    List<CategoryVO> getWarningCategory(Long id);

    /**
     * 预警详情-轨迹信息
     *
     * @param id 预警id
     * @return 轨迹
     */
    WarningTrackVO getTrackDetail(Long id);

    /**
     * 预警详情-重点区域
     *
     * @param trackId 轨迹id
     * @param portType 端口类型
     * @return 区域
     */
    List<WarningAreaVO> getImportantArea(String trackId,String portType);

    /**
     * 预警详情-群体列表
     *
     * @param id 预警id
     * @return 人员
     */
    List<GroupPersonVO> getWarningGroupPersonList(Long id);

    /**
     * 预警已读
     *
     * @param warningId id
     */
    void readWarning(Long warningId);

    /**
     * 查询用户操作按钮
     *
     * @param warningId 预警id
     * @param user 用户信息
     * @return 是否可操作
     */
    List<String> getUserWarningOperate(Long warningId, SimpleUserVO user);

    /**
     * 预警详情-常控人员
     *
     * @param id 预警id
     * @return 预警原因
     */
    PersonCardVO getWarningRegularPerson(Long id);

    /**
     * 预警详情-流程
     *
     * @param id 预警id
     * @return 预警流程
     */
    WarningProcessVO getWarningProcess(Long id);

    /**
     * 获取预警处置
     *
     * @param id 预警id
     * @return {@link  WarningFeedbackListVo}
     */
    List<WarningFeedbackListVo> getFeedbackList(Long id);

    /**
     * 获取预警轨迹点
     *
     * @param warningId 预警id
     * @param portType 端口类型
     * @return {@link WarningTrackPointVO}
     */
    List<WarningTrackPointVO> getTrackPoint(String warningId, String portType);

    /**
     * 获取预警轨迹点
     *
     * @param warningIds 预警id
     * @return {@link WarningTrackPointVO}
     */
    List<WarningTrackPointVO> getTrackPointByIds(String warningIds);

    /**
     * 预警未读数量
     *
     * @return 数量
     */
    Long countUnread();

    /**
     * 配置列表信息
     *
     * @return 列表信息
     */
    List<WarningConfigVO> getWarningConfigList();

    /**
     * 预警配置新增
     *
     * @param warningConfigDTO 配置属性参数
     * @return 新增结果
     */
    String addWarningConfig(WarningConfigDTO warningConfigDTO);

    /**
     * 预警配置编辑
     *
     * @param configId 配置id
     * @param warningConfigDTO 配置属性参数
     * @return 编辑结果
     */
    String editWarningConfig(Long configId, WarningConfigDTO warningConfigDTO);

    /**
     * 预警配置删除
     *
     * @param configId 配置id
     */
    void delWarningConfig(Long configId);

    /**
     * 设置配置状态
     *
     * @param configId 配置id
     * @param status 状态 0启用 1关闭
     */
    void setWarningConfigStatus(Long configId,Integer status);

    /**
     * 未配置英文名信息
     *
     * @return 未配置英文名
     */
    List<WarningEnNameVO> getNoConfigEnName();

    /**
     * 配置相关记录数
     *
     * @return 配置相关记录数
     */
    WarningConfigRecordVO getConfigRecord();

    /**
     * 特征类型与名称
     *
     * @return 特征类型与名称
     */
    List<IdentifierTypeVO> getIdentifierType();

    /**
     * 英文名称唯一性
     *
     * @param nameUniqueDTO 英文名
     * @return 是否唯一
     */
    Boolean checkUnique(NameUniqueDTO nameUniqueDTO);

    /**
     * 预警数统计
     *
     * @return 统计结果
     */
    RestfulResultsV2<WarningRankVO> statistic();
}
