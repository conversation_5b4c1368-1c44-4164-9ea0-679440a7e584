package com.trs.police.control;

import com.trs.police.common.security.starter.annotation.EnablePoliceCloudResourceServer;
import com.trs.police.control.utils.KerberosLoginUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnablePoliceCloudResourceServer
@SpringBootApplication
@EnableCaching
@EnableScheduling
@ComponentScan({"com.trs.police.control", "com.trs.police.common.core.ybss"})
@MapperScan({"com.trs.police.control.mapper"})
@ComponentScan("com.trs.web.builder.util")
@ComponentScan("com.trs.db.sdk")
@ComponentScan("com.trs.db.sdk.annotations.analysis.impl")
@ComponentScan("com.trs.tcache.conf")
public class ControlApp {

    /**
     * 程序入口
     *
     * @param args 启动参数
     */
    public static void main(String[] args) {
        KerberosLoginUtil.alterEsVersionValidationMethod(ControlApp.class);
        SpringApplication.run(ControlApp.class, args);
    }
}
