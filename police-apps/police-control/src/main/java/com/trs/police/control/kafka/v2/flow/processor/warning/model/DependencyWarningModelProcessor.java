package com.trs.police.control.kafka.v2.flow.processor.warning.model;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.common.core.dto.Source;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.common.core.entity.District;
import com.trs.police.common.core.entity.MonitorWarningModelEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.mapper.MonitorWarningModelMapper;
import com.trs.police.control.mapper.SourceMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.io.WKTReader;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

import static com.trs.police.control.service.impl.SourceServiceImpl.checkInTopDistrict;

/**
 * 属地内预警
 *
 * <AUTHOR>
 * @since 2025/5/29 11:13
 */
@Slf4j
@Component
public class DependencyWarningModelProcessor implements WarningModelProcessor{

    @Resource
    protected MonitorWarningModelMapper monitorWarningModelMapper;

    @Resource
    private WKTReader wktReader;

    @Resource
    protected SourceMapper sourceMapper;

    /**
     * 收集modelId
     *
     * @param context 预警信息上下文
     * @return modelId列表
     */
    @Override
    public List<Long> collect(WarningMessageContext context) {
        Environment env = BeanFactoryHolder.getEnv();
        boolean active = Boolean.parseBoolean(env.getProperty("com.trs.warningModalConfig.activeDependencyModel", "false"));
        if (active) {
            try {
                if(context == null || context.getControlInfo() == null) {
                    log.error("没有布控信息");
                    return List.of();
                }
                // 是否选中了离开属地模型
                List<ControlInfo.WarningInfo> warningInfoList = context.getControlInfo().getWarningInfo();
                if (CollectionUtils.isEmpty(warningInfoList)) {
                    log.error("没有命中的预警");
                    // 没有命中的轨迹
                    return List.of();
                }
                ControlInfo.WarningInfo warningInfo = warningInfoList.get(0);
                if (warningInfo == null) {
                    log.error("没有命中的预警");
                    return List.of();
                }
                List<Long> modelIds = warningInfo.getModelIds();
                if (CollectionUtils.isEmpty(modelIds)) {
                    log.error("布控未关联预警模型");
                    return List.of();
                }
                String modelName = env.getProperty("com.trs.warningConsume.DependencyModelName", "市内预警");
                List<MonitorWarningModelEntity> list = monitorWarningModelMapper.selectList(new QueryWrapper<MonitorWarningModelEntity>().eq("title", modelName));
                if (CollectionUtils.isEmpty(list)) {
                    log.error("未找到模型:{}", modelName);
                    return List.of();
                }
                if (!modelIds.contains(list.get(0).getId())) {
                    log.info("布控未关联市内预警模型");
                    return List.of();
                }
                //  忽略没有经纬度的轨迹
                WarningDTO warningDTO = context.getWarningDTO();
                if (warningDTO == null) {
                    log.error("预警信息为空, 忽略当前数据");
                    return List.of();
                }
                Source sensingMessage = warningDTO.getSensingMessage();
                if (sensingMessage == null) {
                    log.error("感知源信息为空, 忽略当前数据");
                    return List.of();
                }
                Double longitude = sensingMessage.getLongitude();
                Double latitude = sensingMessage.getLatitude();

                if (longitude == null || latitude == null) {
                    log.error("轨迹数据没有经纬度：{}", warningDTO);
                    return List.of();
                }
                //  查询当前环境匹配的属地
                String placeCode = env.getProperty("com.trs.warningModalConfig.leaveDependencyPlaceCode", "510300");
                District district = sourceMapper.selectGeoByCode(placeCode);
                if (district == null) {
                    log.error("未找到区域：{}, 市内预警模型无法进行", placeCode);
                    return List.of();
                }
                log.info("市内预警模型当前配置的属地为 {}", district.getName());
                String contour = district.getContour();
                String point = String.format("POINT(%s %s)", longitude, latitude);
                if (!checkInTopDistrict(wktReader.read(contour), wktReader.read(point))) {
                    return List.of();
                }
                log.info("轨迹数据在区域内：{}, 命中属地内轨迹模型 {}", warningDTO, list.get(0).getTitle());
                //不在区域内就命中模型, 返回模型id
                return List.of(list.get(0).getId());
            } catch (Exception e) {
                log.error("DependencyWarningModelProcessor.collect error: {}", e.getMessage(), e);
                return List.of();
            }
        }
        return List.of();
    }
}
