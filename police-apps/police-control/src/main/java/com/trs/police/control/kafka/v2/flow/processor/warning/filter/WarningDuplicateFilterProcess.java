package com.trs.police.control.kafka.v2.flow.processor.warning.filter;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.mapper.WarningTrackMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/12 17:57
 */
@Slf4j
@Component
public class WarningDuplicateFilterProcess implements FilterProcess {

    @Resource
    private WarningTrackMapper warningTrackMapper;

    /**
     * 过滤方法
     *
     * @param context 上下文参数
     * @return 过滤结果
     */
    @Override
    public Boolean filter(WarningMessageContext context) {
        if (context.getTrackVO() == null) {
            return false;
        }
        GroupWarningDTO.Track trackVO = context.getTrackVO();
        Long monitorId = context.getControlInfo().getId();
        List<WarningTrackEntity> warningTrackEntities = warningTrackMapper.selectTrackForCheck(
                trackVO.getIdentifier(),
                trackVO.getIdentifierType(),
                trackVO.getSensingMessage().getId() + "-" + trackVO.getSensingMessage().getType(),
                LocalDateTime.parse(trackVO.getEventTime(), TimeUtil.WARNING_MESSAGE_PATTERN).format(TimeUtil.DEFAULT_TIME_PATTERN),
                monitorId,
                trackVO.getEnName()
        );
        if (!CollectionUtils.isEmpty(warningTrackEntities)) {
            log.info("重复数据,忽略,数据 {}", JSON.toJSONString(trackVO));
        }
        return CollectionUtils.isEmpty(warningTrackEntities);
    }

    /**
     * 是否执行
     *
     * @return 是否执行
     */
    @Override
    public Boolean isExecute() {
        return BeanFactoryHolder.getEnv().getProperty("warning.track.filter.enable", Boolean.class, false);
    }
}
