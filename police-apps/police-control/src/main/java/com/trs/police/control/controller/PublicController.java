package com.trs.police.control.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.data.reader.ReaderContext;
import com.trs.mq.utils.CollectionUtils;
import com.trs.police.common.core.constant.DateTimeConstants;
import com.trs.police.common.core.constant.enums.*;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.dto.Source;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.KeyValueVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.VirtualInfoVO;
import com.trs.police.common.core.vo.control.WorkRecordDTO;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.profile.PersonVirtualIdentityVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.PersonListVO;
import com.trs.police.common.openfeign.starter.vo.RegularMonitorInitiateVO;
import com.trs.police.control.domain.builder.control.IControlInfoBuilder;
import com.trs.police.control.domain.dto.VirtualInfoDTO;
import com.trs.police.control.domain.entity.ServiceCalendarEntity;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.entity.monitor.MonitorEntity;
import com.trs.police.control.domain.entity.monitor.RegularMonitorEntity;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.domain.vo.ControlPersonVO;
import com.trs.police.control.domain.vo.RegularStatisticVO;
import com.trs.police.control.domain.vo.WarningEntityWrapperVO;
import com.trs.police.control.domain.vo.regular.RegularEditVO;
import com.trs.police.control.kafka.v2.flow.WarningMessageConsume;
import com.trs.police.control.mapper.*;
import com.trs.police.control.properties.GatheringWarningProperties;
import com.trs.police.control.proxy.WarningOperateProxy;
import com.trs.police.control.service.*;
import com.trs.police.control.service.impl.WarningProcessServiceImpl;
import com.trs.police.control.service.impl.cloud.control.PushToCloudControlServiceImpl;
import com.trs.police.control.task.InductiveMonitorSchedule;
import com.trs.police.control.task.SilenceWarningTask;
import com.trs.police.control.task.WarningSourceDistrictCleanTask;
import com.trs.police.control.task.gathering.GatheringWarningTask;
import com.trs.police.control.task.syncDevice.DaHuaDeviceSyncTask;
import com.trs.police.control.utils.VirtualIdentityQueryUtil;
import com.trs.police.control.utils.WarningModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.control.task.gathering.GatheringWarningTask.*;

/**
 * <AUTHOR>
 * @date 2022/6/1 9:59
 */
@Slf4j
@RestController
public class PublicController {

    @Resource
    private MonitorMapper monitorMapper;
    @Resource
    private MonitorService monitorService;
    @Resource
    private SubscribeService subscribeService;
    @Resource
    private WarningProcessService warningProcessService;

    @Resource
    private WarningProcessServiceImpl processService;

    @Resource
    private OperationLogService operationLogService;
    @Resource
    private RegularMonitorMapper regularMonitorMapper;
    @Resource
    private ImportantAreaMapper importantAreaMapper;
    @Resource
    private ServiceWorkRecordMapper serviceWorkRecordMapper;
    @Resource
    private InductiveMonitorSchedule inductiveMonitorSchedule;
    @Resource
    private ServiceCalendarMapper serviceCalendarMapper;
    @Resource
    private ServiceCalendarService serviceCalendarService;
    @Resource
    private VirtualIdentityQueryUtil virtualIdentityQueryUtil;
    @Resource
    private ProfileService profileService;

    @Resource
    private GatheringWarningTask gatheringWarningTask;

    @Autowired(required = false)
    private DaHuaDeviceSyncTask daHuaDeviceSyncTask;

    @Resource
    private GatheringWarningProperties properties;

    @Resource
    private WarningMessageConsume consume;
    @Resource
    private GxtjMapper gxtjMapper;
    @Resource
    private SourceMapper sourceMapper;
    @Resource
    private RegularMonitorService regularMonitorService;
    @Autowired
    private CareMonitorMapper careMonitorMapper;

    @Resource
    private WarningHistorySyncService warningHistorySyncService;

    @Resource
    private PushToCloudControlServiceImpl pushToCloudControlService;

    @Autowired
    private PersonRelate2FkService personRelate2FkService;

    @Autowired
    private WarningOperateProxy warningOperateProxy;

    @Resource
    private WarningProcessMapper warningProcessMapper;

    @Resource
    private WarningTrackMapper warningTrackMapper;;

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
    private final Pattern phoneNumberPattern = Pattern.compile(
            "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$");
    private final Pattern imsiPattern = Pattern.compile("^[\\d]{15}(?:[\\d]{2})?$");
    private final Pattern imeiPattern = Pattern.compile("^[\\d]{15}(?:[\\d]{2})?$");
    private final Pattern macPattern = Pattern.compile("^([0-9a-fA-F]{2})(([/\\s:-][0-9a-fA-F]{2}){5})$");


    /**
     * 高新统计模板
     */
    private static final String REGULAR_TEMPLATE = "'截止目前，指挥中心稳控办梳理的%s名重点人员均已在云控平台完成了建档管控工作（高级%s人，中级%s人，低级%s人）。";
    private static final String POLICE_TYPE_TEMPLATE = "%s牵头管控%s人";
    private static final String WARNING_STATISTIC_TEMPLATE = "以上人员，在%s至%s期间，共产生预警%s条，其中红色预警%s条,橙色预警%s条，黄色预警%s条，蓝色预警%s条。预警签收共计%s条，反馈%s条";
    private static final String STATION_TEMPLATE = "%s管控%s人，档案完整度为%s，产生预警%s条，签收%s条，反馈%s条";

    /**
     * 同步 dahua 设备信息
     */
    @GetMapping("/public/syncDaHuaDeviceInfo")
    public void syncDaHuaDeviceInfo() {
        if (daHuaDeviceSyncTask == null) {
            log.warn("daHuaDeviceSyncTask is null!");
            return;
        }
        daHuaDeviceSyncTask.syncDevice();
    }

    /**
     * 修复预警关系数据
     *
     * @param request 参数
     * @return msg 结果
     */
    @PostMapping("/public/repairWarningRelationData")
    public String repairWarningRelationData(ListParamsRequest request) {
        try {
            // 查询指定时段的预警数据
            final Page<WarningEntity> page = request.getPageParams().toPage();
            request.getFilterParams().add(new KeyValueTypeVO("currentUser", true, "boolean"));
            request.getFilterParams().add(new KeyValueTypeVO("excludeSubject", true, "boolean"));
            Page<WarningEntityWrapperVO> result = warningOperateProxy.getOperateService().selectPageList(request, page);
            for (WarningEntityWrapperVO record : result.getRecords()) {
                List<WarningTrackEntity> warningTrackEntities = warningTrackMapper.selectTrackListByWarningId(record.getId());
                if (CollectionUtils.isEmpty(warningTrackEntities)) {
                    continue;
                }
                // 查询布控信息
                //MonitorEntity monitorEntity = monitorMapper.selectById(record.getMonitorId());
                WarningTrackEntity track = warningTrackEntities.get(0);
                GroupWarningDTO.Track track1 = new GroupWarningDTO.Track();
                track1.setName(track.getPlace());
                Source source = new Source();
                source.setLongitude(track.getLongitude());
                source.setLatitude(track.getLatitude());
                track1.setSensingMessage(source);
                track1.setIdentifier(track.getIdentifier());
                track1.setIdentifierType(track.getIdentifierType());
                track1.setEventTime(track.getActivityTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
                ControlInfo controlInfo = IControlInfoBuilder.builder(ControlTypeEnum.MONITOR.getEnName())
                        .buildControlInfo("monitor_" + record.getMonitorId(), List.of(track1));
                // 检查数据
                List<WarningProcessEntity> userProcessByWarningIdForCheck = warningProcessMapper.getUserProcessByWarningIdForCheck(record.getId());
                if (!CollectionUtils.isEmpty(userProcessByWarningIdForCheck)) {
                    continue;
                }
                // 修补关联数据
                processService.setWarningUserRelation(controlInfo.getWarningInfo().get(0), record.getMonitorId());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "error";
        }
        return "ok";
    }

    /**
     * 手动运行聚集预警入口
     *
     * @param monitorIds 布控id集合
     * @param startTimeStr 开始时间
     * @param endTimeStr 结束时间
     */
    @GetMapping("/public/startGatheringWarning")
    public void startGatheringWarning(String monitorIds, String startTimeStr, String endTimeStr) {
        // 获取计算开始时间并转换为LocalDateTime
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = now;
        if (endTimeStr != null) {
            endTime = LocalDateTime.parse(endTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        // 计算开始时间
        LocalDateTime startTime = endTime.minusMinutes(properties.getTimeScopeMinutes());
        if (startTimeStr != null) {
            startTime = LocalDateTime.parse(startTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        // 判断开始时间是否晚于结束时间
        if (startTime.isAfter(endTime)) {
            throw new RuntimeException("开始时间不能晚于结束时间");
        }
        // 判断开始时间或结束时间是否晚于当前时间
        if (startTime.isAfter(now) || endTime.isAfter(now)){
            throw new RuntimeException("开始时间或结束时间不能晚于当前时间");
        }
        // 解析monitorIds为Long列表
        List<Long> monitorIdList = null;
        if (monitorIds != null) {
            monitorIdList = Arrays.stream(monitorIds.split(","))
                    .filter(StringUtils::isNumeric)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        // 初始化数据读取上下文参数
        ReaderContext dataObtainContext = new ReaderContext();
        dataObtainContext.addProperty(START_TIME, startTime);
        dataObtainContext.addProperty(END_TIME, endTime);
        dataObtainContext.addProperty(MONITOR_IDS, monitorIdList);
        gatheringWarningTask.doProcess(dataObtainContext);
    }

    /**
     * test
     *
     * @param id 布控id
     */
    @GetMapping("/public/subscribe/monitor/{id}")
    public void sendSubscribe(@PathVariable Long id) {
        MonitorEntity monitor = monitorMapper.selectById(id);
        subscribeService.sendWarningSubscribe(monitor);
    }

    /**
     * 更新所有布控到moye
     */
    @GetMapping("/public/subscribe/monitor/all")
    public void sendSubscribe() {
        monitorMapper.selectList(null).forEach(monitor -> {
            if (monitor.getMonitorStatus().equals(MonitorStatusEnum.MONITORING)) {
                subscribeService.sendWarningSubscribe(monitor);
            }
        });
    }

    /**
     * test
     *
     */
    @GetMapping("/public/updateInductiveMonitorStatus")
    public void updateInductiveMonitorStatus() {
        inductiveMonitorSchedule.updateInductiveMonitorStatus();
    }


    /**
     * test 取消订阅
     *
     * @param id   布控id
     * @param type 布控类型
     */
    @DeleteMapping("/public/warning/test/{id}/{type}")
    public void cancelSubscribe(@PathVariable Long id, @PathVariable("type") Integer type) {
        subscribeService.cancelWarningSubscribe(id, MonitorBaseTypeEnum.codeOf(type));
    }


    /**
     * 测试发起常控订阅
     *
     * @param id 布控id
     */
    @GetMapping("/public/subscribe/regular/{id}")
    public void sendRegularSubscribe(@PathVariable Long id) {
        RegularMonitorEntity regularMonitor = regularMonitorMapper.selectById(id);
        subscribeService.sendWarningSubscribe(regularMonitor);
    }


    /**
     * 批量发起常控订阅
     */
    @GetMapping("/public/subscribe/regular/all")
    public void sendRegularSubscribe() {
        regularMonitorMapper.selectList(null).forEach(monitor -> {
            if (monitor.getStatus().equals(RegularMonitorStatusEnum.MONITORING)) {
                subscribeService.sendWarningSubscribe(monitor);
            }
        });
    }

    /**
     * 测试发起常控订阅
     *
     * @param ids 布控id
     * @return 订阅成功失败信息
     */
    @PostMapping("/public/subscribe/batchRegular")
    public String sendRegularSubscribeBatch(String ids) {
        StringBuilder success = new StringBuilder();
        StringBuilder error = new StringBuilder();
        for (String id : ids.split(",|;")) {
            try {
                RegularMonitorEntity regularMonitor = regularMonitorMapper.selectById(Long.valueOf(id));
                subscribeService.sendWarningSubscribe(regularMonitor);
            } catch (Exception e) {
                log.error(String.format("订阅失败，订阅id: %s", id), e);
                error.append(id).append(";");
            }
            success.append("id").append(":");
        }
        return String.format("成功：%s\n失败：%s", success.toString(), error.toString());
    }

    /**
     * 更新操作记录审批id
     *
     * @param processId 审批id
     * @param actionVO  审批操作vo
     */
    @PutMapping("/public/log/update/{processId}")
    public void updateLogRelatedProcessId(@PathVariable("processId") Long processId,
                                          @RequestBody ApprovalActionVO actionVO) {
        operationLogService.updateLogRelatedProcessId(processId, actionVO);
    }

    /**
     * 查询管控重点人员 https://yapi-192.trscd.com.cn/project/4974/interface/api/143914
     *
     * @param pageParams 分页
     * @return 列表
     */
    @PostMapping("/person/list")
    public PageResult<ControlPersonVO> getControlPersonList(@RequestBody PageParams pageParams) {
        return monitorService.getControlPersonList(pageParams);
    }

    /**
     * 同步重点区域到moye
     */
    @GetMapping("/public/area/synchronize")
    public void synchronizeArea() {
        importantAreaMapper.selectAll().forEach(item -> {
            try {
                subscribeService.addImportantArea(item.getId(), item.toVO());
            } catch (Exception e) {
                log.error("创建重点区域失败", e);
            }
        });
    }


    /**
     * 更新勤务日历数据
     */
    @GetMapping("/public/service/calendar")
    public void updateServiceCalendar() {
        serviceWorkRecordMapper.getFirstRecord().ifPresent(item -> {
            LocalDateTime beginTime = item.getCreateTime().toLocalDate().atStartOfDay();
            final LocalDateTime endTime = LocalDateTime.now().toLocalDate().atStartOfDay();
            while (beginTime.isBefore(endTime)) {
                ServiceCalendarEntity serviceCalendar = serviceCalendarMapper.getServiceCalendar(beginTime);
                if (Objects.isNull(serviceCalendar)) {
                    serviceCalendar = new ServiceCalendarEntity();
                    serviceCalendar.setLevel(4L);
                    serviceCalendar.setTime(beginTime);
                }
                serviceCalendarService.doEditServiceCalendar(serviceCalendar);
                beginTime = beginTime.plusDays(1);
            }
        });
    }

    /**
     * 530获取虚拟身份信息
     *
     * @param tels 手机号
     * @return 虚拟身份信息
     */
    @PostMapping(
            value = {
                    "/virtual/info",
                    "/public/virtual/info"
            }
    )
    public List<VirtualInfoVO> getVirtualInfoList(@RequestBody List<String> tels) {
        final CurrentUser currentUser = virtualIdentityQueryUtil.getCurrentHaveAuthUser();
        if (Objects.isNull(currentUser)) {
            log.warn("未配置用户！", new Exception("who call me!"));
            return List.of();
        }
        return tels.stream().flatMap(tel -> {
            List<VirtualInfoVO> imeis = virtualIdentityQueryUtil.getImeiByPhone(tel, currentUser).stream()
                    .filter(imei -> imeiPattern.matcher(imei.getImei()).matches())
                    .map(imei -> {
                        PersonVirtualIdentityVO personVirtualIdentityVO = new PersonVirtualIdentityVO();
                        personVirtualIdentityVO.setVirtualNumber(imei.getImei());
                        personVirtualIdentityVO.setType(Long.valueOf(VirtualIdentityTypeEnum.IMEI.getCode()));
                        personVirtualIdentityVO.setTypeName(VirtualIdentityTypeEnum.IMEI.getName());
                        VirtualInfoVO virtualInfoVO = new VirtualInfoVO();
                        virtualInfoVO.setVirtualIdentity(personVirtualIdentityVO);
                        virtualInfoVO.setTel(tel);
                        virtualInfoVO.setSource("省厅");
                        return virtualInfoVO;
                    }).collect(Collectors.toList());

            List<VirtualInfoVO> imsis = virtualIdentityQueryUtil.getImsiByPhone(tel, currentUser).stream()
                    .filter(imsi -> imsiPattern.matcher(imsi.getImsi()).matches())
                    .map(imsi -> {
                        PersonVirtualIdentityVO personVirtualIdentityVO = new PersonVirtualIdentityVO();
                        personVirtualIdentityVO.setVirtualNumber(imsi.getImsi());
                        personVirtualIdentityVO.setType(Long.valueOf(VirtualIdentityTypeEnum.IMSI.getCode()));
                        personVirtualIdentityVO.setTypeName(VirtualIdentityTypeEnum.IMSI.getName());
                        VirtualInfoVO virtualInfoVO = new VirtualInfoVO();
                        virtualInfoVO.setVirtualIdentity(personVirtualIdentityVO);
                        virtualInfoVO.setTel(tel);
                        virtualInfoVO.setSource("省厅");
                        return virtualInfoVO;
                    }).collect(Collectors.toList());

            List<VirtualInfoVO> macs = virtualIdentityQueryUtil.getMacByPhone(tel, currentUser).stream()
                    .filter(mac -> macPattern.matcher(mac.getMac()).matches())
                    .map(mac -> {
                        PersonVirtualIdentityVO personVirtualIdentityVO = new PersonVirtualIdentityVO();
                        personVirtualIdentityVO.setVirtualNumber(mac.getImei());
                        personVirtualIdentityVO.setType(Long.valueOf(VirtualIdentityTypeEnum.MAC.getCode()));
                        personVirtualIdentityVO.setTypeName(VirtualIdentityTypeEnum.MAC.getName());
                        VirtualInfoVO virtualInfoVO = new VirtualInfoVO();
                        virtualInfoVO.setVirtualIdentity(personVirtualIdentityVO);
                        virtualInfoVO.setTel(tel);
                        virtualInfoVO.setSource("省厅");
                        return virtualInfoVO;
                    }).collect(Collectors.toList());

            return Stream.of(imeis, imsis, macs).flatMap(List::stream);
        }).collect(Collectors.toList());
    }

    /**
     * 530获取手机信息
     *
     * @param imsiList imsi
     * @return 手机号
     */
    @PostMapping("/virtual/getPhoneByImsi")
    public Map<String, String> getImsiPhoneMap(@RequestBody List<String> imsiList) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        Map<String, String> result = new HashMap<>(imsiList.size());
        for (String imsi : imsiList) {
            final List<VirtualInfoDTO> list = virtualIdentityQueryUtil.getPhoneByImsi(imsi, currentUser);
            final Optional<String> first = list.stream()
                    .map(VirtualInfoDTO::getPhone)
                    .filter(StringUtils::isNotEmpty)
                    .findFirst();
            first.ifPresent(s -> result.put(imsi, s));
        }
        return result;
    }

    @GetMapping("/public/gxtj")
    String statisticsWarning(@RequestParam String beginTime, @RequestParam String endTime) {
        final LocalDateTime parseBeginTime = LocalDateTime.parse(beginTime + "0000",
                DateTimeConstants.DATE_TIME_FORMATTER_EXPORT);
        final LocalDateTime parseEndTime = LocalDateTime.parse(endTime + "2359",
                DateTimeConstants.DATE_TIME_FORMATTER_EXPORT);
        List<KeyValueVO> keyValueVo = gxtjMapper.countAllRegular();
        String value = String.format(REGULAR_TEMPLATE,
                keyValueVo.stream().map(item -> Integer.valueOf(item.getValue())).reduce(0, Integer::sum),
                keyValueVo.stream().filter(item -> "1".equals(item.getKey())).map(item -> Integer.valueOf(item.getValue()))
                        .findFirst().orElse(0),
                keyValueVo.stream().filter(item -> "2".equals(item.getKey())).map(item -> Integer.valueOf(item.getValue()))
                        .findFirst().orElse(0),
                keyValueVo.stream().filter(item -> "3".equals(item.getKey())).map(item -> Integer.valueOf(item.getValue()))
                        .findFirst().orElse(0));

        StringBuilder policeTypeStatistic = new StringBuilder();

        PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        List<DeptVO> policeType = permissionService.getDeptTreeAll();
        for (DeptVO dept : policeType.get(0).getChildren()) {
            if (Objects.nonNull(dept.getDeptType()) && dept.getDeptType() > 5L) {
                policeTypeStatistic.append(String.format(POLICE_TYPE_TEMPLATE, dept.getShortName(),
                        gxtjMapper.countRegularByDeptCode(StringUtil.getPrefixCode(dept.getDeptCode()))));
                policeTypeStatistic.append("，");
            }
        }
        if (StringUtils.isNotBlank(policeTypeStatistic.toString())) {
            policeTypeStatistic.setLength(policeTypeStatistic.length() - 1);
            value = value + "其中" + policeTypeStatistic + "。";
        }
        List<KeyValueVO> warningCount = gxtjMapper.countRegularWarning(parseBeginTime, parseEndTime);
        List<KeyValueVO> warningProcessCount = gxtjMapper.countRegularWarningProcess(parseBeginTime, parseEndTime);
        String warning = String.format(WARNING_STATISTIC_TEMPLATE,
                parseBeginTime.format(DATE_FORMATTER),
                parseEndTime.format(DATE_FORMATTER),
                warningCount.stream().map(item -> Integer.valueOf(item.getValue())).reduce(0, Integer::sum),
                warningCount.stream().filter(item -> "1".equals(item.getKey()))
                        .map(item -> Integer.valueOf(item.getValue())).findFirst().orElse(0),
                warningCount.stream().filter(item -> "2".equals(item.getKey()))
                        .map(item -> Integer.valueOf(item.getValue())).findFirst().orElse(0),
                warningCount.stream().filter(item -> "3".equals(item.getKey()))
                        .map(item -> Integer.valueOf(item.getValue())).findFirst().orElse(0),
                warningCount.stream().filter(item -> "4".equals(item.getKey()))
                        .map(item -> Integer.valueOf(item.getValue())).findFirst().orElse(0),
                warningProcessCount.stream()
                        .filter(item -> Integer.parseInt(item.getKey()) >= WarningStatusEnum.SIGN_FINISH.getCode())
                        .map(item -> Integer.valueOf(item.getValue())).reduce(0, Integer::sum),
                warningProcessCount.stream()
                        .filter(item -> Integer.parseInt(item.getKey()) >= WarningStatusEnum.REPLY_FINISH.getCode())
                        .map(item -> Integer.valueOf(item.getValue())).reduce(0, Integer::sum));
        value = value + warning + "。";
        List<RegularStatisticVO> regularStatisticVo = gxtjMapper.countWarningByPoliceStation(parseBeginTime,
                parseEndTime);
        StringBuilder policeStationStatistic = new StringBuilder();
        for (RegularStatisticVO vo : regularStatisticVo) {
            Double archiveCompleteRate = profileService.getArchiveCompleteRate(vo.getDeptId(), null, null);
            policeStationStatistic.append(String.format(STATION_TEMPLATE, vo.getDeptName(), vo.getControlTotal(),
                    Objects.isNull(archiveCompleteRate) ? "0%" : String.format("%.2f", archiveCompleteRate) + "%",
                    vo.getWarningTotal(), vo.getSignTotal(), vo.getReplyTotal()));
            policeStationStatistic.append("，");
        }
        if (policeStationStatistic.length() > 0) {
            policeStationStatistic.setLength(policeStationStatistic.length() - 1);
        }
        value = value + "按派出所区分，" + policeStationStatistic + "。";
        return value;
    }

    /**
     * 更新感知元category
     */
    @GetMapping("/public/source/category")
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateWarningSourceCategory() {
        LambdaQueryWrapper<SourceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(SourceEntity::getCategory);
        wrapper.isNull(SourceEntity::getNestingCategory);
        wrapper.orderByAsc(SourceEntity::getId);
        Long total = sourceMapper.selectCount(wrapper);
        int pageNum = 0;
        int synchronizeCount = 0;
        int pageSize = 1000;
        int totalPage = (int) Math.ceil((double) total / pageSize);
        while (totalPage >= pageNum) {
            PageParams pageParams = new PageParams();
            pageParams.setPageNumber(++pageNum);
            pageParams.setPageSize(pageSize);
            Page<SourceEntity> page = sourceMapper.selectPage(pageParams.toPage(), wrapper);
            page.getRecords()
                    .parallelStream().forEach(item -> {
                        Long[][] collect = item.getCategory().stream()
                                .map(category -> sourceMapper.getDictPathByCode("control_warning_source_category", category)
                                        .toArray(new Long[0]))
                                .toArray(Long[][]::new);
                        item.setNestingCategory(collect);
                        sourceMapper.updateSourceCategory(item.getId(), JsonUtil.toJsonString(collect));
                    });
            synchronizeCount += page.getRecords().size();
            log.info("总数：" + total + ",已更新:" + synchronizeCount + "条");
        }
    }

    /**
     * 发起常控
     *
     * @param initiateVO 常控
     * @return 是否发起常控成功
     */
    @PostMapping("/public/regular/initiate")
    public Boolean initiate(@RequestBody RegularMonitorInitiateVO initiateVO) {
        try {
            regularMonitorService.initiate(initiateVO);
            return true;
        } catch (Exception e) {
            log.error("发起常控失败", e);
            return false;
        }
    }


    /**
     * 撤控
     *
     * @param id          id
     * @param reason      原因
     * @param usePersonId 是否使用的人员id
     */
    @GetMapping("/public/regular/revoke")
    public void revoke(@RequestParam("id") Long id, @RequestParam("reason") String reason, @Nullable @RequestParam("usePersonId") Integer usePersonId) {
        RegularEditVO vo = new RegularEditVO();
        vo.setReason(reason);
        if (Integer.valueOf("1").equals(usePersonId)) {
            List<RegularMonitorEntity> regularMonitorEntities = regularMonitorMapper.selectList(
                    Wrappers.lambdaQuery(RegularMonitorEntity.class)
                            .eq(RegularMonitorEntity::getTargetId, id)
            );
            for (RegularMonitorEntity entity : regularMonitorEntities) {
                try {
                    regularMonitorService.revoke(entity.getId(), vo);
                } catch (Exception e) {
                    log.error("撤销常控失败", e);
                }
            }
        } else {
            regularMonitorService.revoke(id, vo);
        }
    }

    /**
     * 撤控
     *
     * @param ids         主键
     * @param reason      原因
     * @param usePersonId 是否使用的人员id
     */
    @GetMapping("/public/regular/batchRevoke")
    public void batchRevoke(@RequestParam("id") String ids, @RequestParam("reason") String reason, @Nullable @RequestParam("usePersonId") Integer usePersonId) {
        if (StringUtils.isEmpty(ids)) {
            return;
        }
        RegularEditVO vo = new RegularEditVO();
        vo.setReason(reason);
        List<Long> idList = Stream.of(ids.split(",|;"))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (Integer.valueOf("1").equals(usePersonId)) {
            List<RegularMonitorEntity> regularMonitorEntities = regularMonitorMapper.selectList(
                    Wrappers.lambdaQuery(RegularMonitorEntity.class)
                            .in(RegularMonitorEntity::getTargetId, idList)
            );
            for (RegularMonitorEntity entity : regularMonitorEntities) {
                try {
                    regularMonitorService.batchRevoke(Arrays.asList(entity.getId()), vo);
                } catch (Exception e) {
                    log.error("撤销常控失败", e);
                }
            }
        } else {
            regularMonitorService.batchRevoke(idList, vo);
        }
    }

    /**
     * 获取到常控标签
     *
     * @param personIds 人员id
     * @return 常控标签信息
     */
    @PostMapping("/public/regular/getRegularLabel")
    public List<PersonListVO> getRegularLabel(@RequestBody List<Long> personIds) {
        if (Objects.isNull(personIds) || personIds.isEmpty()) {
            return new ArrayList<>();
        }
        return regularMonitorService.getRegularLabel(personIds);
    }

    /**
     * 根据人员id和人员级别名称修改常控级别 <br> 理论上人员级别名称和常控级别名称一样 如果不一样 则不更新
     *
     * @param personProfileId 人员档案id
     * @param personLevelName 人员界别名称
     */
    @GetMapping("/public/regular/editLevelByPersonProfile")
    public void editLevelByPersonProfile(@RequestParam("personProfileId") Long personProfileId, @RequestParam("personLevelName") String personLevelName) {
        regularMonitorService.editLevelByPersonProfile(personProfileId, personLevelName);
    }

    /**
     * 测试模型统计发消息
     *
     * @return msg
     */
    @GetMapping("/public/testModelStatistics")
    public String testModelStatistics() {
        WarningModelUtil.pushModelStatisticsMessage(Arrays.asList(7L));
        return "ok";
    }

    /**
     * 根据最新的人员标签更新常控配置ID
     *
     * @param regularIds 常控ID
     * @return msg
     */
    @PostMapping("/public/regular/updateWarningConfig")
    public String updateWarningConfig(@RequestBody List<Long> regularIds) {
        regularMonitorService.updateWarningConfig(regularIds);
        return "ok";
    }

    /**
     * 人档数据全部常控
     *
     * @param partitionCount 分片数
     * @param controlLevel   空值管控等级默认值
     * @return 执行是否成功
     */
    @GetMapping("/public/regular/allInitiate")
    public Boolean allInitiate(Integer partitionCount, Long controlLevel) {
        try {
            regularMonitorService.allInitiate(partitionCount, controlLevel);
            return true;
        } catch (Exception e) {
            log.error("人档全部数据常控失败", e);
            return false;
        }
    }

    /**
     * 无审判常控
     *
     * @param initiateVO 常控
     * @return 是否发起常控成功
     */
    @PostMapping("/public/regular/initiateNoApproval")
    public Boolean initiateNoApproval(@RequestBody RegularMonitorInitiateVO initiateVO) {
        regularMonitorService.initiateNoApproval(initiateVO);
        return true;
    }

    /**
     * 添加工作记录
     *
     * @param workRecordDTO 工作记录
     */
    @PostMapping("/public/regular/addWorkRecord")
    public void addWorkRecordFromGx(@RequestBody WorkRecordDTO workRecordDTO) {
        regularMonitorService.addWorkRecordFromGx(workRecordDTO);
    }

    /**
     * 添加工作记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @GetMapping("/public/warning/syncHistoryData2Es")
    public void syncHistoryData2Es(String startTime, String endTime) {
        warningHistorySyncService.syncWarningHistory2Es(startTime, endTime);
    }

    /**
     * 同步es状态
     *
     * @param startWarningId startWarningId
     * @param endWarningId endWarningId
     */
    @GetMapping("/public/warning/es/status/sync")
    public void warningEsStatusSync(Long startWarningId, Long endWarningId) {
        warningHistorySyncService.warningEsStatusSync(startWarningId, endWarningId);
    }

    @Resource
    private WarningSourceDistrictCleanTask warningSourceDistrictCleanTask;

    /**
     * 感知源地区行政区划清洗
     */
    @GetMapping("/public/warning/source/update")
    public void warningSourceUpdate() {
        warningSourceDistrictCleanTask.cleanDistrict();
    }

    @Resource
    private SilenceWarningTask silenceWarningTask;

    /**
     * 静默预警
     */
    @GetMapping("/public/warning/silence")
    public void silenceWarning() {
        silenceWarningTask.execute();
    }


    /**
     * 测试向云控推送轨迹
     *
     * @param trackJson 手动传入的轨迹json
     */
    @PostMapping("/public/warning/testPushTrackToCloudControl")
    public void testPushTrackToCloudControl(@RequestBody String trackJson) {
        ControlInfo controlInfo = new ControlInfo();
        GroupWarningDTO.Track trackVO = JSONObject.parseObject(trackJson, GroupWarningDTO.Track.class);
        pushToCloudControlService.produceToCloudControl(controlInfo, trackVO);
    }

    /**
     * 根据人员id添加fk人员
     *
     * @param personId 人员id
     */
    @PostMapping("/public/warning/addFkryByPersonId")
    public void addFkryByPersonId(Long personId) {
        personRelate2FkService.addByPersonId(personId);
    }
}
