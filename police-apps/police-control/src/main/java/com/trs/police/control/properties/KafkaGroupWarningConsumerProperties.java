package com.trs.police.control.properties;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * kafka 群体预警消费配置
 *
 * <AUTHOR>
 * @date 2023/06/30
 */
@Data
@Component
@ConfigurationProperties(prefix = "kafka.warning.group")
@ConditionalOnProperty(value = "kafka.warning.group.autoStartup", havingValue = "true")
public class KafkaGroupWarningConsumerProperties {

    /**
     * 拉取消息间隔 单位：秒
     */
    private Long pollDuration;

    /**
     * 每次拉取最大记录数
     */
    private int maxPollRecords = 500;

    /**
     * kafka服务器地址
     */
    private String bootStrapServers;

    /**
     * 消费者组id
     */
    private String groupId;

    /**
     * 消费的topic
     */
    private String topic;

    /**
     * 消费策略：earliest or latest
     */
    private String autoOffsetReset;

    /**
     * 是否开启Kerberos认证
     */
    private Boolean enableKerberos;

    /**
     * 其他的属性
     */
    private Map<String, String> properties;
}
