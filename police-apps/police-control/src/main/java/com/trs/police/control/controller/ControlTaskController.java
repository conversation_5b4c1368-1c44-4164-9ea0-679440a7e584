package com.trs.police.control.controller;

import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.vo.ImportResultWithIdVO;
import com.trs.police.control.aspect.OperationLog;
import com.trs.police.control.constant.enums.ControlTaskOperationEnum;
import com.trs.police.control.domain.dto.*;
import com.trs.police.control.domain.vo.ControlTaskDetailVO;
import com.trs.police.control.domain.vo.ControlTaskListVO;
import com.trs.police.control.domain.vo.ControlTaskPersonListVO;
import com.trs.police.control.domain.vo.Disposition;
import com.trs.police.control.service.ControlTaskService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 布控任务控制层
 *
 * <AUTHOR> gao.yuan
 * @date : 2024/12/25 17:09
 */
@Slf4j
@RestController
@RequestMapping("/controlTask")
public class ControlTaskController {

    @Autowired
    private ControlTaskService controlTaskService;

    /**
     * 布控任务列表检索
     *
     * @param params 分页参数
     * @param dto 其他参数
     * @param searchParams 模糊检索参数
     * @return 布控任务列表
     */
    @GetMapping(value = "/list")
    public RestfulResultsV2<ControlTaskListVO> list(PageParams params, ControlTaskListDTO dto, SearchParams searchParams) {
        try {
            return controlTaskService.list(params, dto, searchParams);
        } catch (Exception e) {
            log.error("布控任务列表检索异常：", e);
            return RestfulResultsV2.error("布控任务列表检索异常：" + e.getMessage());
        }
    }

    /**
     * 布控任务详情检索
     *
     * @param controlTaskId 布控任务id
     * @param listType 详情类型
     * @return 布控任务详情
     */
    @GetMapping(value = "/detail")
    public RestfulResultsV2<ControlTaskDetailVO> detail(Long controlTaskId, Integer listType) {
        try {
            return controlTaskService.detail(controlTaskId, listType);
        } catch (Exception e) {
            log.error("布控任务详情检索异常：", e);
            return RestfulResultsV2.error("布控任务详情检索异常：" + e.getMessage());
        }
    }

    /**
     * 布控任务详情检索-布控人员列表
     *
     * @param pageParams    分页参数
     * @param dto 检索参数
     * @return 布控任务详情-布控人员列表
     */
    @GetMapping(value = "/controlPerson/list")
    public RestfulResultsV2<ControlTaskPersonListVO> controlPersonList(PageParams pageParams, ControlTaskPersonListDTO dto) {
        try {
            return controlTaskService.controlPersonList(pageParams, dto);
        } catch (Exception e) {
            log.error("布控任务详情-布控人员列表检索异常：", e);
            return RestfulResultsV2.error("布控任务详情-布控人员列表检索异常：" + e.getMessage());
        }
    }

    /**
     * 布控任务模板下载
     *
     * @param response 响应
     */
    @GetMapping(value = "/download/template")
    public void downloadControlTaskTemplate(HttpServletResponse response) throws IOException {
        controlTaskService.downloadControlTaskTemplate(response);
    }

    /**
     * 布控任务创建
     *
     * @param dto 布控任务创建参数
     * @return 布控任务创建结果
     */
    @PostMapping(value = "/create")
    @OperationLog(operateModule = OperateModule.CONTROL_TASK, operation = Operation.CONTROL_TASK_INITIATE, newObj = "#dto")
    public RestfulResultsV2<ImportResultWithIdVO> create(ControlTaskCreateDTO dto) {
        return controlTaskService.create(dto);
    }

    /**
     * 布控任务详情检索-布控人员处置情况统计
     *
     * @param controlTaskId 布控任务id
     * @return 布控任务详情-布控人员处置情况统计
     */
    @GetMapping(value = "/controlPerson/disposition")
    public RestfulResultsV2<Disposition> controlPersonDisposition(Long controlTaskId) {
        try {
            return controlTaskService.controlPersonDisposition(controlTaskId);
        } catch (Exception e) {
            log.error("布控任务详情-布控人员处置情况统计异常：", e);
            return RestfulResultsV2.error("布控任务详情-布控人员处置情况统计异常：" + e.getMessage());
        }
    }

    /**
     * 任务人员下发
     *
     * @param taskId taskId
     * @param dtos dtos
     * @return 是否修改成功
     */
    @PostMapping("/{taskId}/person/issue")
    public RestfulResultsV2<String> taskPersonIssue(@PathVariable("taskId") Long taskId,
                                                      @RequestBody List<ControlTaskPersonIssueDTO> dtos) {
        return controlTaskService.taskPersonIssue(taskId, dtos);
    }

    /**
     * 任务人员退回
     *
     * @param taskId 任务id
     * @param dto dto
     * @return 是否下发成功
     */
    @PostMapping("/{taskId}/person/return")
    public RestfulResultsV2<String> taskPersonReturn(@PathVariable("taskId") Long taskId,
                                                     @RequestBody ControlTaskPersonReturnDTO dto) {
        return controlTaskService.taskPersonReturn(taskId, dto);
    }

    /**
     * 任务签收
     *
     * @param signDto signDto
     * @return 是否签收成功
     */
    @PostMapping("/sign")
    public RestfulResultsV2<String> taskPersonSign(@RequestBody ControlTaskSignDTO signDto) {
        return controlTaskService.taskSign(signDto);
    }

    /**
     * 任务删除
     *
     * @param taskId 任务id
     * @return 是否删除成功
     */
    @PostMapping("/{taskId}/delete")
    public RestfulResultsV2<String> taskDelete(@PathVariable("taskId") Long taskId) {
        return controlTaskService.taskDelete(taskId);
    }

    /**
     * 获取布控任务操作
     *
     * @param controlTaskId 任务id
     * @param taskPersonId 任务人员id
     * @param listType 列表类型
     * @param deptId 部门id
     * @return 是否删除成功
     */
    @GetMapping("/operationList")
    public RestfulResultsV2<ControlTaskOperationEnum> operationList(Long controlTaskId, Long taskPersonId, Integer listType, Long deptId) {
        return controlTaskService.operationList(controlTaskId, taskPersonId, listType, deptId);
    }
}
