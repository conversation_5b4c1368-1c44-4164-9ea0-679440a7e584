package com.trs.police.control.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.IdNameCountVO;
import com.trs.police.control.domain.vo.WarningEntityWrapperVO;
import com.trs.police.control.domain.vo.WarningRankVO;
import com.trs.police.control.domain.vo.warning.WarningListVO;
import com.trs.police.control.domain.vo.warning.WarningStatusVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

/**
 * 预警表(Warning)持久层
 *
 * <AUTHOR>
 * @date 2022-08-11 14:04:37
 */
@Mapper
public interface WarningMapper extends BaseMapper<WarningEntity> {

    /**
     * 更新预警状态
     *
     * @param id     预警id
     * @param status 预警状态
     */
    @Update("update t_warning set status = #{status} where id = #{id}")
    void updateWarningStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 入库排重查询
     *
     * @param warningEntity 预警信息
     * @return 结果
     */
    List<WarningEntity> selectForDuplicate(@Param("warningEntity") WarningEntity warningEntity);

    /**
     * 插入预警信息，忽略重复
     *
     * @param warningEntity 预警信息
     */
    void insertIgnoreDuplicate(@Param("warningEntity") WarningEntity warningEntity);

    /**
     * 列表查询-全部
     *
     * @param searchValue 搜索内容
     * @return {@link Long}
     */
    Set<Long> searchFullText(@Param("searchValue") String searchValue);

    /**
     * 查询预警id
     *
     * @param searchValue 查询内容
     * @return {@link Long}
     */
    Set<Long> searchWarningInfo(@Param("searchValue") String searchValue);

    /**
     * 更新过期状态
     *
     * @param id 预警id
     */
    @Update("update t_warning set is_overdue = 1 where id = #{id}")
    void updateExpireStatus(@Param("id") Long id);

    /**
     * 查询包含模型id的预警
     *
     * @param modelIds 模型id
     * @return 预警id
     */
    List<Long> getWarningIdsByModelIds(@Param("modelIds") List<Long> modelIds);

    /**
     * 根据管控类型获取预警信息
     *
     * @param type    管控类型 {@link ControlTypeEnum}
     * @param id      id
     * @param request 请求参数
     * @return 预警信息
     */
    List<WarningEntity> getWarningByTypeAndTargetId(
        @Param("type") Integer type,
        @Param("id") Long id,
        @Param("request") ListParamsRequest request);

    /**
     * 根据预警id获取预警状态
     *
     * @param warningIds 预警ID
     * @return key：预警ID  value：预警状态
     */
    List<WarningStatusVO> selectWarningStatusByWarningIds(@Param("warningIds") List<Long> warningIds);

    /**
     * 预警列表
     *
     * @param request 请求参数
     * @param page    分页参数
     * @return {@link WarningListVO}
     */
    Page<WarningEntityWrapperVO> selectPageList(@Param("request") ListParamsRequest request, Page<WarningEntity> page);

    /**
     * 查询预警未读数
     *
     * @param userId 用户id
     * @param deptId 部门id
     * @return 数量
     */
    Long countWarningUnread(@Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 获取布控的预警数量
     *
     * @param monitorIds 布控id
     * @return 布控预警数量
     */
    List<IdNameCountVO> countWarningByMonitorIds(@Param("monitorIds") List<Long> monitorIds);

    /**
     *  统计预警数
     *
     * @param rankVO 维度
     * @param warningPlatform 预警平台
     * @return 统计数量
     */
    Integer countByRank(@Param("rankVO") WarningRankVO rankVO, @Param("warningPlatform") Long warningPlatform);

    /**
     * xx
     *
     * @return xx
     */
    List<JSONObject> getWarningTrack();


}