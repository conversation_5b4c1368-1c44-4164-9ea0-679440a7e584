package com.trs.police.control.converter;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.common.core.dto.WarningSource;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.control.constant.enums.GjxxSourceEnum;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEsEntity;
import com.trs.police.control.mapper.SourceMapper;
import com.trs.police.control.mapper.WarningSourceTypeMapper;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.util.*;

/**
 * 轨迹信息转换器
 *
 * <AUTHOR>
 */
@Component
public class GjxxConverter {

    @Autowired
    private SourceMapper sourceMapper;

    @Autowired
    private WarningSourceTypeMapper warningSourceTypeMapper;

    /**
     * 根据预警轨迹信息接入
     *
     * @param warningDTO 预警轨迹信息
     * @param source 来源
     * @return 轨迹信息es实体
     */
    public WarningFkrxyjEsEntity convertWarning(WarningDTO warningDTO, GjxxSourceEnum source) {
        WarningFkrxyjEsEntity es = new WarningFkrxyjEsEntity();
        es.setCreateTime(new Date());
        es.setAlertType(null);
        es.setCaptureAddress(warningDTO.getSensingMessage().getAddress());
        es.setCaptureTime(StringUtils.isNotEmpty(warningDTO.getEventTime()) ? TimeUtils.stringToDate(warningDTO.getEventTime()) : null);
        es.setDate(es.getCaptureTime());
        es.setDeviceName(warningDTO.getSensingMessage().getName());
        // 尝试根据名字匹配到感知源
        if (StringUtils.isNotEmpty(warningDTO.getSensingMessage().getName())) {
            List<SourceEntity> s = sourceMapper.selectList(
                    Wrappers.lambdaQuery(SourceEntity.class)
                            .eq(SourceEntity::getName, warningDTO.getSensingMessage().getName())
                            .last("limit 1")
            );
            if (!CollectionUtils.isEmpty(s)) {
                es.setDeviceCode(s.get(0).getCode());
            }
        }
        es.setFacePhoto(null);
        es.setIdCard(warningDTO.getSensingMessage().getId());
        Optional.ofNullable(warningDTO.getSensingMessage().getLatitude())
                .map(String::valueOf)
                .ifPresent(es::setLatitude);
        Optional.ofNullable(warningDTO.getSensingMessage().getLongitude())
                .map(String::valueOf)
                .ifPresent(es::setLongitude);
        es.setPhoto(null);
        es.setSimilarity(null);
        es.setName(null);
        if (StringUtils.isNotEmpty(es.getLatitude()) && StringUtils.isNotEmpty(es.getLongitude())) {
            es.setLocation(String.format("%s,%s", es.getLatitude(), es.getLongitude()));
        }
        Optional<SourceEntity> sourceEntity = getByCode(es.getDeviceCode());
        WarningSource warningSource = new WarningSource();
        if (sourceEntity.isPresent() && StringUtils.isNotEmpty(es.getDeviceCode())) {
            // 感知源
            warningSource.setSourceId(sourceEntity.get().getUniqueKey());
            warningSource.setSourceCategory(sourceEntity.get().getCategory());
        }
        // 感知源类型
        warningSource.setDatasourceType(warningDTO.getEnName());
        WarningSourceTypeEntity warningSourceTypeEntity = warningSourceTypeMapper.selectOne(
                Wrappers.lambdaQuery(WarningSourceTypeEntity.class)
                        .eq(WarningSourceTypeEntity::getEnName, warningDTO.getEnName())
        );
        if (Objects.nonNull(warningSourceTypeEntity)) {
            warningSource.setSourceType(warningSourceTypeEntity.getSourceType());
        }
        es.setWarningSource(JSON.toJSONString(warningSource));
        es.setDataSource(source.getName());
        return es;
    }

    /**
     * strx轨迹信息转换
     *
     * @param entity en
     * @param source 来源
     * @return 轨迹信息es实体
     */
    public WarningFkrxyjEsEntity convertStrx(WarningFkrxyjEntity entity, GjxxSourceEnum source) {
        String enName = BeanFactoryHolder.getEnv().getProperty("control.fkry.source.name.strx", "strx");
        return convert(entity,  enName, source);
    }

    /**
     * 轨迹信息转换
     *
     * @param entity en
     * @param typeEnName 感知源类型英文名称
     * @param source 来源
     * @return 轨迹信息es实体
     */
    private WarningFkrxyjEsEntity convert(WarningFkrxyjEntity entity, String typeEnName, GjxxSourceEnum source) {
        WarningFkrxyjEsEntity esEntity = new WarningFkrxyjEsEntity();
        esEntity.setCreateTime(new Date());
        esEntity.setAlertType(entity.getAlertType());
        esEntity.setCaptureAddress(entity.getCaptureAddress());
        esEntity.setCaptureTime(
                Objects.nonNull(entity.getCaptureTime())
                        ? Date.from(entity.getCaptureTime().atZone(ZoneId.systemDefault()).toInstant())
                        : null
        );
        esEntity.setDate(StringUtils.isNotEmpty(entity.getDate()) ? TimeUtils.stringToDate(entity.getDate()) : null);
        esEntity.setDeviceName(entity.getCaptureAddress());
        esEntity.setDeviceCode(entity.getDeviceCode());
        esEntity.setFacePhoto(entity.getFacePhoto());
        esEntity.setIdCard(entity.getIdCard());
        esEntity.setLatitude(entity.getLatitude());
        esEntity.setLibName(entity.getLibName());
        esEntity.setLongitude(entity.getLongitude());
        esEntity.setPhoto(entity.getPhoto());
        esEntity.setSimilarity(StringUtils.isNotEmpty(entity.getSimilarity()) ? Double.valueOf(entity.getSimilarity()) : null);
        esEntity.setName(entity.getName());
        esEntity.setName(entity.getName());
        if (StringUtils.isNotEmpty(entity.getLatitude()) && StringUtils.isNotEmpty(entity.getLongitude())) {
            esEntity.setLocation(String.format("%s,%s", entity.getLatitude(), entity.getLongitude()));
        }

        Optional<SourceEntity> sourceEntity = getByCode(entity.getDeviceCode());
        WarningSource warningSource = new WarningSource();
        if (sourceEntity.isPresent()) {
            // 感知源
            warningSource.setSourceId(sourceEntity.get().getUniqueKey());
            warningSource.setSourceCategory(sourceEntity.get().getCategory());
        }
        // 感知源类型
        warningSource.setDatasourceType(typeEnName);
        WarningSourceTypeEntity warningSourceTypeEntity = warningSourceTypeMapper.selectOne(
                Wrappers.lambdaQuery(WarningSourceTypeEntity.class)
                        .eq(WarningSourceTypeEntity::getEnName, typeEnName)
        );
        if (Objects.nonNull(warningSourceTypeEntity)) {
            warningSource.setSourceType(warningSourceTypeEntity.getSourceType());
        }
        esEntity.setWarningSource(JSON.toJSONString(warningSource));
        esEntity.setDataSource(source.getName());
        esEntity.setRelatedId(Objects.nonNull(entity.getId()) ? entity.getId().toString() : null);
        return esEntity;
    }

    private Optional<SourceEntity> getByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return Optional.empty();
        }
        List<SourceEntity> sourceEntityList = sourceMapper.selectList(
                Wrappers.lambdaQuery(SourceEntity.class)
                        .eq(SourceEntity::getCode, code)
                        .last("limit 1")
        );
        return sourceEntityList.isEmpty() ? Optional.empty() : Optional.of(sourceEntityList.get(0));
    }
}
