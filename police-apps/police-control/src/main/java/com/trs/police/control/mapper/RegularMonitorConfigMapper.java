package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.control.domain.entity.monitor.RegularMonitorConfigEntity;
import com.trs.police.control.domain.vo.regular.RegularMonitorConfigListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6 14:11
 */
@Mapper
public interface RegularMonitorConfigMapper extends BaseMapper<RegularMonitorConfigEntity> {

    /**
     * 获取预警配置列表
     *
     * @param request 查询参数
     * @param page    分页参数
     * @return {@link RegularMonitorConfigListVO}
     */
    Page<RegularMonitorConfigListVO> getRegularConfigPageList(@Param("request") ListParamsRequest request,
                                                              Page<RegularMonitorConfigListVO> page);

    /**
     * 更新预警配置启用状态
     *
     * @param ids           预警配置id
     * @param enabledStatus 启用状态
     */
    void updateRegularConfigEnableStatus(@Param("ids") List<Long> ids, @Param("enabledStatus") Boolean enabledStatus);

    /**
     * 删除预警配置(假删除)
     *
     * @param ids 预警配置id
     */
    void deleteRegularConfig(@Param("ids") List<Long> ids);

    /**
     * 更新常控人员 当常控预警配置更新后
     */
    void updateRegularPersonWhenConfigUpdate();

    /**
     * 根据常控查询关联预警配置
     *
     * @param regularId 常控id
     * @return 模型id
     */
    @ResultMap("mybatis-plus_RegularMonitorConfigEntity")
    @Select("select * from t_control_regular_monitor_config c "
            + "where c.id member of ((select m.warning_config_ids from t_control_regular_monitor m where m.id = #{regularId}))")
    List<RegularMonitorConfigEntity> getConfigByRegularId(@Param("regularId") Long regularId);

    /**
     * 获取配置结合
     *
     * @param configIds configIds
     * @return 结果
     */
    @ResultMap("mybatis-plus_RegularMonitorConfigEntity")
    @Select("<script>SELECT * FROM t_control_regular_monitor_config c WHERE c.id IN <foreach item='item' index='index' collection='configIds' open='(' separator=',' close=')'>#{item}</foreach></script>")
    List<RegularMonitorConfigEntity> getConfigByIds(@Param("configIds") List<Long> configIds);

    /**
     * 根据预警配置id获取下面的重点区域id
     *
     * @param warningConfigId 预警配置id
     * @return {@link List}<{@link Long}>
     */
    List<Long> getAreaIdByWarningConfigId(Long warningConfigId);

    /**
     * 根据预警配置id获取下面的模型
     *
     * @param warningConfigIdList 预警配置id
     * @return {@link List}<{@link RegularMonitorConfigListVO}>
     */
    List<RegularMonitorConfigListVO> getModelByConfigByIds(@Param("ids") List<Long> warningConfigIdList);
}
