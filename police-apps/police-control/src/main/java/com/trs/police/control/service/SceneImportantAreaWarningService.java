package com.trs.police.control.service;

import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;

import java.util.List;

/**
 * 不同场景的重点区域预警实现
 *
 * <AUTHOR>
 */
public interface SceneImportantAreaWarningService {

    /**
     * 保存不同场景的预警
     *
     * @param importantAreaEntityList 预警相关重点区域列表
     * @param warning 预警信息
     * @param track 轨迹信息
     * @return 创建的预警id
     */
    Long saveSceneImportantAreaWarning(List<ImportantAreaEntity> importantAreaEntityList, WarningEntity warning, WarningTrackEntity track);

}
