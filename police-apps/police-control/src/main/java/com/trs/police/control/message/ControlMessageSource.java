package com.trs.police.control.message;

import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

/**
 * 消息输出输出通道
 *
 * <AUTHOR>
 */
public interface ControlMessageSource {

    /**
     * 推送日志数据
     *
     */
    String PROVINCE_LOG_OUT = "provinceLogOutPut";

    /**
     * 省厅日志推送
     *
     * @return channel
     */
    @Output(PROVINCE_LOG_OUT)
    MessageChannel provinceLogOutPut();
}
