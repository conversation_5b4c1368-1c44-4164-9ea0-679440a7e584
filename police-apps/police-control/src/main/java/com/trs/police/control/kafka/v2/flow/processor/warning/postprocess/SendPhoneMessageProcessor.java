package com.trs.police.control.kafka.v2.flow.processor.warning.postprocess;

import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.message.PhoneMessageVO;
import com.trs.police.common.openfeign.starter.service.MessageService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 发送消息
 * *@author:wen.wen
 * *@create 2025-01-24 13:48
 **/
@Slf4j
@Component
public class SendPhoneMessageProcessor implements IWarningPostProcessor {


    @Resource
    private PermissionService permissionService;
    @Resource
    private MessageService messageService;


    @Override
    public WarningMessageContext.WarningResultContext process(WarningMessageContext warningMessageContext, WarningMessageContext.WarningResultContext context) {
        try {
            //TODO 消息中心推消息
            ControlInfo.WarningInfo warningInfo = context.getWarningInfo();
            //短信通知
            if (warningInfo.getPhoneMessageUser() != null
                    && !warningInfo.getPhoneMessageUser().isEmpty()) {
                sendPhoneMessage(warningInfo, context.getWarningEntity());
            }
            return context;
        } catch (Exception e) {
            log.error("SendPhoneMessageProcessor 发生异常", e);
            return context;
        }
    }

    private void sendPhoneMessage(ControlInfo.WarningInfo warningInfo, WarningEntity warning) {
        try {
            PhoneMessageVO message = new PhoneMessageVO();
            message.setContent(
                    String.format("%s 您有一条布控预警信息，请及时签收",
                            TimeUtil.getSubscribeTime(warning.getWarningTime())));
            message.setPhoneNumbers(warningInfo.getPhoneMessageUser().stream()
                    .map(user -> permissionService.findSimpleUser(user.getUserId(), user.getDeptId()).getTel())
                    .filter(StringUtil::isLegalMobilePhone)
                    .toArray(String[]::new));
            message.setModuleId(2L);
            message.setRelatedId(warning.getId());
            log.info("预警短信通知：{}", JsonUtil.toJsonString(message));
            messageService.sendPhoneMessage(message);
        } catch (Exception e) {
            log.info("预警短信通知失败！", e);
        }
    }

    @Override
    public Integer order() {
        return 6;
    }
}
