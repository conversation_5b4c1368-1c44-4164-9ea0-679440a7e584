package com.trs.police.control.constant;

import static com.trs.police.common.core.constant.enums.WarningStatusEnum.*;

import com.trs.police.common.core.constant.enums.WarningStatusEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 13/4/2023 下午3:49
 */
public class WarningProcessOperationsConstant {

    private WarningProcessOperationsConstant() {
    }

    public static final String SIGN = "签收";
    public static final String FEEDBACK = "反馈";
    public static final String DONE = "处置完结";

    /**
     * 预警列表操作按钮
     *
     * @param code 状态
     * @return 按钮
     */
    public static List<String> getWarningOperationsByStatus(Integer code) {
        if (Objects.isNull(code) || Objects.isNull(WarningStatusEnum.codeOf(code))) {
            return Collections.emptyList();
        }
        switch (Objects.requireNonNull(codeOf(code))) {
            case WAITING_SIGN:
                return List.of(SIGN);
            case SIGN_FINISH:
                return List.of(FEEDBACK);
            case REPLY_FINISH:
                return Arrays.asList(FEEDBACK, DONE);
            default:
                return Collections.emptyList();
        }
    }
}
