package com.trs.police.control.domain.entity.monitor;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 布控-预警模型关系表
 *
 * <AUTHOR> yanghy
 * @date : 2022/8/31 17:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_control_monitor_warning_model_relation", autoResultMap = true)
public class MonitorWarningModelRelationEntity extends AbstractBaseEntity {

    private static final long serialVersionUID = -5295593326198849277L;
    /**
     * 布控id
     */
    private Long monitorId;
    /**
     * 模型id
     */
    private Long warningModelId;
    /**
     * 人数百分比
     */
    private Double personCount;
    /**
     *  时间阈值，单位：分钟
     */
    private Integer timeCount;
    /**
     * 聚集区域
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> aggregationArea;
}
