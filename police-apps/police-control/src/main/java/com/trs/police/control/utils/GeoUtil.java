package com.trs.police.control.utils;

import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;

/**
 * <AUTHOR> yanghy
 * @date : 2022/9/28 16:50
 */
public class GeoUtil {

    private GeoUtil(){

    }

    /**
     * wkt转坐标
     *
     * @param wkt wkt
     * @return {@link Coordinate}
     */
    public static Coordinate wktToCoordinate(String wkt) {
        WKTReader wktReader = new WKTReader();
        Geometry read;
        try {
            read = wktReader.read(wkt);
            return read.getCoordinate();
        } catch (ParseException e) {
            return null;
        }
    }
}
