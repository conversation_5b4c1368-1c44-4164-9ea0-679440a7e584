package com.trs.police.control.service;

import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.constant.enums.WarningStatusEnum;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.control.CategoryVO;
import com.trs.police.common.core.vo.control.WarningPhotoVO;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.control.domain.dto.fkrxyj.SignWarningDTO;
import com.trs.police.common.core.request.WarningDoneRequest;
import com.trs.police.control.domain.vo.WarningTrackPointVO;
import com.trs.police.control.domain.vo.warning.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/26 10:12
 */
public interface WarningFkrxyjService {

    /**
     * 获取省厅下放预警列表
     *
     * @param request 请求参数
     * @return {@link SpecialWarningListVO}
     */
    PageResult<SpecialWarningListVO> getWarningList(ListParamsRequest request);

    /**
     * 获取省厅下放预警列表
     *
     * @param ids 请求参数
     * @return {@link SpecialWarningListVO}
     */
    List<SpecialWarningListVO> getWarningListByIds(List<Long> ids);

    /**
     * 预警详情
     *
     * @param warningId 预警id
     * @return 详情
     */
    WarningStxfDetailVO getWarningDetail(Long warningId);

    /**
     * 预警轨迹撒点
     *
     * @param warningId 预警Id
     * @return 点位信息
     */
    List<WarningTrackPointVO> getTrackPoint(Long warningId);

    /**
     * 预警流程
     *
     * @param warningId 预警Id
     * @return 预警流程
     */
    WarningProcessVO getWarningProcess(Long warningId);

    /**
     * 活动详情
     *
     * @param warningId 预警id
     * @return 活动详情
     */
    WarningActivityVO getWarningActivity(Long warningId);

    /**
     * 预警活动照片
     *
     * @param warningId 预警id
     * @return 照片
     */
    WarningPhotoVO getWarningPhoto(Long warningId);

    /**
     * 预警活动人员
     *
     * @param warningId 预警Id
     * @return 预警活动人员
     */
    PersonCardVO getWarningPerson(Long warningId);

    /**
     * 预警详情-模块列表
     *
     * @param trackId 预警id
     * @return 模块列表
     */
    List<CategoryVO> getWarningCategory(Long trackId);

    /**
     * 二次处理fk预警数据
     *
     * @param timeFrameHour 时间范围
     * @return 结果
     */
    boolean dealFK4ProcessWarning(Long timeFrameHour);

    /**
     * fk预警签收
     *
     * @param id id
     */
    void signWarning(Long id);

    /**
     * 预警消息推送到mq
     *
     * @param processId 预警流程id
     * @param level     预警级别
     * @param status    预警状态
     */
    void pushWarningActionMessage(Long processId, MonitorLevelEnum level, WarningStatusEnum status);

    /**
     * fk预警反馈
     *
     * @param id         id
     * @param feedbackVO 反馈内容
     */
    void feedbackWarning(Long id, WarningFeedbackDetailFkrxyjVO feedbackVO);

    /**
     * fk删除反馈
     *
     * @param id 反馈id
     */
    void deleteWarningFeedback(Long id);

    /**
     * fk编辑反馈
     *
     * @param feedbackVO 反馈内容
     * @param id         反馈id
     */
    void updateWarningFeedback(Long id, WarningFeedbackDetailFkrxyjVO feedbackVO);

    /**
     * 预警详情-预警处置
     *
     * @param warningId 预警id
     * @return WarningFeedbackFkrxyjListVo
     */
    List<WarningFeedbackFkrxyjListVo> getFeedbackList(Long warningId);

    /**
     * 预警完结
     *
     * @param id      id
     * @param request 内容
     */
    void doneWarning(Long id, WarningDoneRequest request);

    /**
     * 获取预警完结
     *
     * @param id 预警完结
     * @return 预警完结
     */
    WarningDoneFkrxyjVO getWarningDone(Long id);

    /**
     * 静默预警
     *
     * @return 结果
     */
    boolean silenceWarning();

    /**
     *  签收多条数据
     *
     * @param dto dto
     */
    void signWarnings(SignWarningDTO dto);

    /**
     * 历史fk预警签收
     *
     * @param createTime 创建时间
     */
    void signHistoryWarnings(String createTime);
}
