package com.trs.police.control.kafka.v2.flow.processor.warning.postprocess;

import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.control.domain.entity.warning.WarningHistory;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.mapper.WarningHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * 历史数据记录
 * *@author:wen.wen
 * *@create 2025-01-24 13:51
 **/
@Slf4j
@Component
public class WarningHistoryRecordProcessor implements IWarningPostProcessor {

    @Resource
    private WarningHistoryMapper warningHistoryMapper;

    @Override
    public WarningMessageContext.WarningResultContext process(WarningMessageContext warningMessageContext, WarningMessageContext.WarningResultContext context) {
        try {
            //记录原始预警轨迹信息
            CompletableFuture.runAsync(
                    () -> warningHistoryMapper.insert(new WarningHistory(JsonUtil.toJsonString(warningMessageContext.getWarningDTO()),
                            context.getWarningEntity().getId())));
        } catch (Exception e) {
            log.error("WarningHistoryRecordProcessor 发生异常", e);
        }
        return context;
    }

    @Override
    public Integer order() {
        return 7;
    }
}
