package com.trs.police.control.service;

import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.WarningModelVO;
import com.trs.police.common.core.params.ExportParams;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.control.constant.enums.MonitorPermissionEnum;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.control.domain.entity.monitor.MonitorEntity;
import com.trs.police.control.domain.vo.CancelControlVO;
import com.trs.police.control.domain.vo.ControlPersonVO;
import com.trs.police.control.domain.vo.RenewControlVO;
import com.trs.police.control.domain.vo.monitor.*;
import com.trs.police.common.core.vo.control.CategoryVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 布控
 *
 * <AUTHOR>
 * @since 2022/6/2 11:03
 */
@Validated
public interface MonitorService {

    /**
     * 发起布控-人员
     *
     * @param monitorPersonVO 人员布控信息
     * @return id
     */
    Long initiate(MonitorPersonVO monitorPersonVO);

    /**
     * 发起布控-群体
     *
     * @param monitorGroupVO 群体布控信息
     * @return id
     */
    Long initiate(MonitorGroupVO monitorGroupVO);

    /**
     * 发起布控-区域
     *
     * @param monitorAreaVO 区域布控信息
     * @return id
     */
    Long initiate(@Valid MonitorAreaVO monitorAreaVO);

    /**
     * 逻辑删除布控
     *
     * @param id 布控id
     **/
    void delete(Long id);

    /**
     * 修改不空级别
     *
     * @param id                 布控id
     * @param monitorOperationVo 修改级别和原因
     **/
    void editLevel(Long id, MonitorOperationVo monitorOperationVo);

    /**
     * 撤销布控
     *
     * @param id 布控id
     * @param vo 原因
     **/
    void cancel(Long id, CancelControlVO vo);

    /**
     * 撤销布控申请
     *
     * @param id 布控id
     **/
    void revokeApproval(Long id);

    /**
     * 修改布控信息-人员
     *
     * @param monitorPersonVO 修改后的布控信息
     **/
    void edit(MonitorPersonVO monitorPersonVO);

    /**
     * 修改布控信息-群体
     *
     * @param monitorGroupVO 修改后的布控信息
     **/
    void edit(MonitorGroupVO monitorGroupVO);

    /**
     * 修改布控信息-区域
     *
     * @param monitorAreaVO 修改后的布控信息
     **/
    void edit(@Valid MonitorAreaVO monitorAreaVO);


    /**
     * 布控列表导出
     *
     * @param response response
     * @param vo       导出列表vo
     **/
    void exportList(HttpServletResponse response, ExportParams vo) throws IOException;

    /**
     * 返回当前用户对该布控的操作权限
     *
     * @param monitorId 布控Id
     * @return {@link MonitorPermissionEnum}
     */
    MonitorPermissionEnum checkPermission(Long monitorId);

    /**
     * 导入布控人员信息
     *
     * @param file excel文件
     * @return {@link MonitorImportResultVO}
     */
    MonitorImportResultVO importPerson(MultipartFile file);

    /**
     * 通过id获取布控，已经删除抛异常
     *
     * @param monitorId 布控id
     * @return {@link MonitorEntity}
     */
    MonitorEntity getNotDeletedById(Long monitorId);

    /**
     * 删除对应区域的预警模型
     *
     * @param areaId 区域id
     */
    void deleteImportantAreaModel(Long areaId);

    /**
     * 更新区域预警模型
     *
     * @param area 区域
     */
    void updateAreaModel(ImportantAreaEntity area);

    /**
     * 更新场所预警模型
     *
     * @param category 场所
     */
    void updatePlaceModel(DictDto category);

    /**
     * 获取区域布控的区域信息
     *
     * @param monitorId 布控Id
     * @return {@link WarningModelVO}
     */
    List<WarningModelVO> getAreaOfMonitor(Long monitorId);

    /**
     * 布控完成后，更新布控信息及更新人员档案
     *
     * @param monitor 布控
     */
    void updateAfterApproval(MonitorEntity monitor);

    /**
     * 获取展示目录
     *
     * @param id 布控id
     * @return {@link CategoryVO}
     */
    List<CategoryVO> getCategory(Long id);

    /**
     * 续控
     *
     * @param id 布控id
     * @param vo 原因
     **/
    void renew(Long id, RenewControlVO vo);

    /**
     * 查询管控重点人员
     *
     * @param pageParams 分页
     * @return 列表
     */
    PageResult<ControlPersonVO> getControlPersonList(PageParams pageParams);
}
