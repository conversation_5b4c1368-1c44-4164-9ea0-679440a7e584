package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;

import java.time.LocalDateTime;
import java.util.List;

import com.trs.police.control.domain.vo.NameAndIdNumberVO;
import com.trs.police.control.helper.WarningListVoHelper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 预警轨迹mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WarningTrackMapper extends BaseMapper<WarningTrackEntity> {

    /**
     * 查询预警轨迹（人员预警）
     *
     * @param warningId 预警id
     * @return 轨迹
     */
    @Select("select * from t_warning_track t where t.warning_id = #{warningId} limit 1")
    WarningTrackEntity selectTrackByWarningId(@Param("warningId") Long warningId);

    /**
     * 轨迹查询排重
     *
     * @param identifier 参数
     * @param identifierType 参数
     * @param sourceId 参数
     * @param activityTime 参数
     * @param monitorId 参数
     * @param datasourceType 参数
     * @return 结果
     */
    List<WarningTrackEntity> selectTrackForCheck(@Param("identifier") String identifier, @Param("identifierType") Integer identifierType,
                                                 @Param("sourceId") String sourceId, @Param("activityTime") String activityTime,
                                                 @Param("monitorId") Long monitorId,
                                                 @Param("datasourceType") String datasourceType);

    /**
     * 查询预警轨迹（群体预警）
     *
     * @param warningId 预警id
     * @return 轨迹
     */
    @Select("select * from t_warning_track t where t.warning_id = #{warningId}")
    List<WarningTrackEntity> selectTrackListByWarningId(@Param("warningId") Long warningId);

    /**
     * 查询轨迹中personId空的数据
     *
     * @return 轨迹
     */
    @Select("select * from t_warning_track where person_id is null")
    List<WarningTrackEntity> selectTrackPersonIsNull();

    /**
     * 查询预警人员
     *
     * @param warningId 预警id
     * @return 预警人员
     */
    @Select("select name from t_profile_person where id in (select person_id from t_warning_track where warning_id = #{warningId})")
    List<String> getWarningPersonName(@Param("warningId") Long warningId);

    /**
     * 查询预警人员
     *
     * @param warningId 预警id
     * @return 预警人员
     */
    @Select("select name, id_number as idNumber from t_profile_person where id in (select person_id from t_warning_track where warning_id = #{warningId})")
    List<NameAndIdNumberVO> getWarningPersonNameAndIdNumber(@Param("warningId") Long warningId);

    /**
     * 查询预警人员
     *
     * @param warningIds 预警id
     * @return 预警人员
     */
    @Select({
            "<script>",
            "select wt.warning_id as warningId, pp.name as name, pp.id_number as idNumber ",
            "from t_profile_person pp ",
            "inner join t_warning_track wt on pp.id = wt.person_id ",
            "where wt.warning_id in ",
            "<foreach collection='warningIds' item='warningId' open='(' separator=',' close=')'>",
            "#{warningId}",
            "</foreach>",
            "</script>"
    })
    List<WarningListVoHelper.ActivityPersonVO> getWarningPersonNameAndIdNumberList(@Param("warningIds") List<Long> warningIds);


    /**
     * 查询感知源类型
     *
     * @param warningId 预警id
     * @return 感知类型
     */
    @Select("SELECT DISTINCT(name) FROM t_dict t1 where type='control_warning_source_type' and code in (SELECT type FROM t_control_warning_source where unique_key in (SELECT source_id FROM t_warning_track WHERE warning_id=#{warningId}))")
    List<String> getWarningSourceType(@Param("warningId") Long warningId);

    /**
     * 查询感知源类型
     *
     * @param warningIds 预警id
     * @return 感知类型
     */
    @Select({
            "<script>",
            "SELECT DISTINCT t1.name as sourceType, wt.warning_id",
            "FROM t_dict t1",
            "JOIN t_control_warning_source cws ON t1.code = cws.type",
            "JOIN t_warning_track wt ON cws.unique_key = wt.source_id",
            "WHERE t1.type = 'control_warning_source_type'",
            "AND wt.warning_id IN",
            "<foreach collection='warningIds' item='warningId' open='(' separator=',' close=')'>",
            "#{warningId}",
            "</foreach>",
            "</script>"
    })
    List<WarningListVoHelper.SourceTypeVO> getWarningSourceTypeList(@Param("warningIds") List<Long> warningIds);

    /**
     * 获取预警数据来源
     *
     * @param warningId 预警id
     * @return 数据来源
     */
    @Select("select DISTINCT(cn_name) from t_warning_source_type where en_name in (select datasource_type from t_warning_track where warning_id = #{warningId})")
    List<String> getWarningDataSourceType(@Param("warningId") Long warningId);
    /**
     * 获取预警数据来源
     *
     * @param warningIds 预警id
     * @return 数据来源
     */
    @Select({
            "<script>",
            "SELECT DISTINCT t.cn_name as dataSourceType, wt.warning_id",
            "FROM t_warning_source_type t",
            "JOIN t_warning_track wt ON t.en_name = wt.datasource_type",
            "WHERE wt.warning_id IN",
            "<foreach collection='warningIds' item='warningId' open='(' separator=',' close=')'>",
            "#{warningId}",
            "</foreach>",
            "</script>"
    })
    List<WarningListVoHelper.DataSourceTypeVO> getWarningDataSourceTypeList(@Param("warningIds") List<Long> warningIds);
    /**
     * 查询感知源类型
     *
     * @param warningId 预警id
     * @return 感知类型
     */
    @Select("SELECT DISTINCT(name) FROM t_dict t1 where type='control_warning_source_category' and code member of  ((SELECT category FROM t_control_warning_source where unique_key in (SELECT source_id FROM t_warning_track WHERE warning_id=#{warningId})))")
    List<String> getWarningSourceCategory(@Param("warningId") Long warningId);
    /**
     * 查询感知源类型
     *
     * @param warningIds 预警id
     * @return 感知类型
     */
    @Select({
            "<script>",
            "SELECT DISTINCT t1.name as sourceCategory, wt.warning_id",
            "FROM t_dict t1",
            "JOIN t_control_warning_source cws ON t1.code MEMBER OF (cws.category)",
            "JOIN t_warning_track wt ON cws.unique_key = wt.source_id",
            "WHERE t1.type = 'control_warning_source_category'",
            "AND wt.warning_id IN",
            "<foreach collection='warningIds' item='warningId' open='(' separator=',' close=')'>",
            "#{warningId}",
            "</foreach>",
            "</script>"
    })
    List<WarningListVoHelper.SourceCategoryVO> getWarningSourceCategoryList(@Param("warningIds") List<Long> warningIds);


    /**
     * 分页查询人员轨迹列表，不同群体的轨迹算多条
     *
     * @param personIds 人员ID集合
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 轨迹列表
     */
    List<WarningTrackEntity> selectTrackListByPersonIdsAndTimeRangeWithPage(
            @Param("personIds") List<Long> personIds,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize
    );

    /**
     * 统计符合条件的轨迹总数
     *
     * @param personIds 人员ID集合
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 总数
     */
    Long countTrackListByPersonIdsAndTimeRange(
            @Param("personIds") List<Long> personIds,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 根据证件号码获取最新轨迹
     *
     * @param identifier 证件号码
     * @return 轨迹信息
     */
    @Select("SELECT * FROM t_warning_track where identifier = #{identifier} order by activity_time desc limit 1")
    WarningTrackEntity getNewestTrackByIdNumber(@Param("identifier") String identifier);

    /**
     * 根据人员档案id获取最新轨迹
     *
     * @param personId 人员档案id
     * @return 轨迹信息
     */
    @Select("SELECT * FROM t_warning_track where person_id = #{personId} order by activity_time desc limit 1")
    WarningTrackEntity getNewestTrackByPersonId(@Param("personId") Long personId);
}
