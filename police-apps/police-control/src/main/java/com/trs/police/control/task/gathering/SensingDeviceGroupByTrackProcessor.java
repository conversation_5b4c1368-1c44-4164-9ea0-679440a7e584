package com.trs.police.control.task.gathering;

import com.trs.data.exception.ProcessException;
import com.trs.data.processor.IBatchDataProcessor;
import com.trs.police.control.domain.dto.WarningTrackWithGroupId;
import com.trs.police.control.task.gathering.strategy.GroupByKeyEnum;
import com.trs.police.control.task.gathering.strategy.impl.GroupStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 按感知源分组
 *
 * <AUTHOR>
 * @since 2025/4/21 10:16
 */
@Slf4j
@Component
public class SensingDeviceGroupByTrackProcessor implements IBatchDataProcessor<GatheringTrackContext, GatheringTrackContext> {

    @Override
    public List<GatheringTrackContext> process(List<GatheringTrackContext> gatheringTrackContexts) throws ProcessException {
        if (CollectionUtils.isEmpty(gatheringTrackContexts)) {
            return List.of();
        }
        for (GatheringTrackContext gatheringTrackContext : gatheringTrackContexts) {
            List<WarningTrackWithGroupId> trackList = gatheringTrackContext.getTrackList();
            // 按照感知源分组
            Map<String, List<WarningTrackWithGroupId>> group = GroupStrategyFactory.getStrategy(GroupByKeyEnum.SENSING_DEVICE).group(trackList);
            gatheringTrackContext.setGroupTrackMap(group);
        }
        return gatheringTrackContexts;
    }
}