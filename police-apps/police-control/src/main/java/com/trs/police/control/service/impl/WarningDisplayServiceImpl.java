package com.trs.police.control.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.constant.enums.IdentifierTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.constant.enums.RegularMonitorStatusEnum;
import com.trs.police.common.core.constant.enums.WarningStatusEnum;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.dto.UserDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.mapper.CommonMapper;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.params.SearchParams;
import com.trs.police.common.core.params.SortParams;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.TodoTaskVO;
import com.trs.police.common.core.vo.control.*;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.profile.PersonCardVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.constant.WarningConfigConstant;
import com.trs.police.control.constant.enums.WarningNotifyEnum;
import com.trs.police.control.domain.dto.NameUniqueDTO;
import com.trs.police.control.domain.dto.WarningConfigDTO;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.entity.monitor.CareMonitorEntity;
import com.trs.police.control.domain.entity.monitor.MonitorEntity;
import com.trs.police.control.domain.entity.monitor.RegularMonitorConfigEntity;
import com.trs.police.control.domain.entity.monitor.RegularMonitorEntity;
import com.trs.police.control.domain.entity.warning.InductiveControlEsWarning;
import com.trs.police.control.domain.entity.warning.WarningNotifyEntity;
import com.trs.police.control.domain.entity.warning.WarningTrackEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.domain.vo.WarningEntityWrapperVO;
import com.trs.police.control.domain.vo.WarningRankVO;
import com.trs.police.control.domain.vo.WarningTrackPointVO;
import com.trs.police.control.domain.vo.warning.*;
import com.trs.police.control.domain.vo.warning.WarningDispatchVO.MonitorInfo;
import com.trs.police.control.domain.vo.warning.WarningDispatchVO.RegularInfo;
import com.trs.police.control.domain.vo.warning.WarningProcessVO.Done;
import com.trs.police.control.helper.CloudControlPushHelper;
import com.trs.police.control.helper.DeptHelper;
import com.trs.police.control.helper.WarningListVoHelper;
import com.trs.police.control.mapper.*;
import com.trs.police.control.proxy.WarningOperateProxy;
import com.trs.police.control.repository.InductiveControlEsWarningRepository;
import com.trs.police.control.service.SourceService;
import com.trs.police.control.service.WarningContentGenerator;
import com.trs.police.control.service.WarningDisplayService;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;


/**
 * 预警
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WarningDisplayServiceImpl implements WarningDisplayService {

    @Resource
    private WarningMapper warningMapper;
    @Resource
    private WarningNotifyMapper warningNotifyMapper;
    @Resource
    private WarningSourceTypeMapper warningSourceTypeMapper;
    @Resource
    private SourceMapper sourceMapper;
    @Resource
    private ImportantAreaMapper importantAreaMapper;
    @Resource
    private WarningTrackMapper warningTrackMapper;
    @Resource
    private ProfileService profileService;
    @Resource
    private MonitorMapper monitorMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RegularMonitorMapper regularMonitorMapper;
    @Resource
    private RegularMonitorConfigMapper regularMonitorConfigMapper;
    @Resource
    private WarningProcessMapper warningProcessMapper;
    @Resource
    private WarningFeedbackMapper warningFeedbackMapper;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private WarningNoconfigMapper warningNoconfigMapper;
    @Resource
    private CloudControlPushHelper cloudControlPushHelper;

    @Resource
    private SourceService sourceService;

    @Resource
    private DictService dictService;

    @Autowired
    private WarningContentGenerator contentGenerator;

    @Autowired
    private WarningOperateProxy warningOperateProxy;
    @Autowired
    private DeptHelper deptHelper;
    @Autowired
    @Qualifier("monitorWarningStatisticExecutor")
    private Executor monitorWarningStatisticExecutor;

    @Resource
    private WarningListVoHelper warningListVoHelper;

    @Resource
    private InductiveControlEsWarningRepository inductiveControlEsWarningRepository;

    @Resource
    private CareMonitorMapper careMonitorMapper;


    @Override
    public PageResult<WarningListVO> getMyWarningList(ListParamsRequest request) {
        final Page<WarningEntity> page = request.getPageParams().toPage();
        request.getFilterParams().add(new KeyValueTypeVO("currentUser", true, "boolean"));
        request.getFilterParams().add(new KeyValueTypeVO("excludeSubject", true, "boolean"));
        Page<WarningEntityWrapperVO> result = warningOperateProxy.getOperateService().selectPageList(request, page);
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        List<WarningListVO> collect = result.getRecords().parallelStream().map(entity -> {
            WarningListVO item = WarningListVO.warningEntityToMyListVO(entity, currentUser);
            item.setOperations(getUserWarningOperate(item.getId(), currentUser));
            cloudControlSet(entity, item, currentUser);
            return item;
        }).collect(Collectors.toList());
        return PageResult.of(collect, request.getPageParams().getPageNumber(), result.getTotal(),
                request.getPageParams().getPageSize());
    }

    /**
     * 翻译布控平台
     *
     * @param monitorPlatform monitorPlatform
     * @return 布控平台
     */
    private String translateMonitorPlatform(Long monitorPlatform) {
        if (monitorPlatform == 1L) {
            return "感知引擎";
        } else if (monitorPlatform == 2L) {
            return "云控";
        }
        return "";
    }

    /**
     * 云控参数设置
     *
     * @param entity  WarningEntityWrapperVO
     * @param item    WarningListVO
     * @param current SimpleUserVO
     */
    private void cloudControlSet(WarningEntityWrapperVO entity, WarningListVO item, SimpleUserVO current) {
        item.setMonitorPlatform(entity.getMonitorPlatform());
        item.setFwzh(entity.getFwzh());
        item.setScczfksx(entity.getScczfksx());
        item.setWarningPlatform(entity.getWarningPlatform());
        if (Objects.nonNull(entity.getMonitorPersonUnit())) {
            DeptDto monitorDept = permissionService.getDeptById(entity.getMonitorPersonUnit());
            if (Objects.nonNull(monitorDept)) {
                item.setDept(monitorDept.getName());
            }
        }
        LocalDateTime now = LocalDateTime.now();
        //签收超时时间
        long qsbetween = Objects.nonNull(entity.getQssx()) ? ChronoUnit.HOURS.between(now, entity.getQssx()) : 100L;
        //反馈超时时间
        long fkbetween = Objects.nonNull(entity.getScczfksx()) ? ChronoUnit.HOURS.between(now, entity.getScczfksx()) : 100L;
        WarningProcessEntity process = warningProcessMapper.getUserProcessByWarningId(entity.getId(), current.getUserId(),
                current.getDeptId());
        if (Objects.nonNull(process)) {
            switch (process.getStatus()) {
                case WAITING_SIGN:
                    item.setWarningStatus("未反馈");
                    if (qsbetween < 0) {
                        //签收已超时
                        item.setOverdueStatus(1);
                    } else if (qsbetween < 2) {
                        //签收即将超时
                        item.setOverdueStatus(2);
                    }
                    break;
                case SIGN_FINISH:
                    item.setWarningStatus("未反馈");
                    if (fkbetween < 0) {
                        //反馈已超时
                        item.setOverdueStatus(3);
                    } else if (fkbetween < 2) {
                        //反馈即将超时
                        item.setOverdueStatus(4);
                    }
                    break;
                default:
                    item.setWarningStatus("未反馈");
                    break;
                case REPLY_FINISH:
                case PROCESS_FINISH:
                    item.setWarningStatus("已反馈");
                    break;
            }
        }
//            if (Objects.nonNull(status) && Objects.nonNull(status.getCode())) {
//            WarningStatusEnum warningStatusEnum = WarningStatusEnum.codeOf((Integer) status.getCode());
//            log.info("---------warningStatusEnum: {}", warningStatusEnum);
//            if (Objects.nonNull(warningStatusEnum)) {
//                log.info("---------warningStatusEnum.getName(): {}", warningStatusEnum.getName());
//                switch (warningStatusEnum) {
//
//                }
//            }
//        }
    }

    @Override
    public PageResult<WarningListVO> getAllWarningList(ListParamsRequest request) {
        final Page<WarningEntity> page = request.getPageParams().toPage();
        request.getFilterParams().addAll(permissionService.buildParamsByPermission());
        Page<WarningEntityWrapperVO> result = warningOperateProxy.getOperateService().selectPageList(request, page);
        SimpleUserVO currentUser = AuthHelper.getNotNullSimpleUser();
        Map<String, Map<Long, String>> map = buildDictMap();
        //default or batch
        String paramImpl = BeanFactoryHolder.getEnv().getProperty("com.trs.warning.param.impl", "default");
        List<WarningListVO> collect = paramImpl.equals("batch")
                ? warningListVoHelper.buildFrom(result.getRecords(), map, currentUser)
                : result.getRecords().parallelStream().map(entity -> {
            WarningListVO item = WarningListVO.warningEntityToAllListVO(entity);
            WarningNotifyEntity notify = warningNotifyMapper.selectByWarningIdAndUser(entity.getId(),
                    currentUser.getUserId(), currentUser.getDeptId());

            if (Objects.nonNull(notify)) {
                item.setOperations(getUserWarningOperate(item.getId(), currentUser));
            }
            item.setHitSubject(entity.getHitSubject() == null ? null : new CodeNameVO(entity.getHitSubject(), map.get("hit_subject_scene").get(entity.getHitSubject())));
            item.setHitSubjectScene(entity.getHitSubjectScene() == null ? null : new CodeNameVO(entity.getHitSubjectScene(), map.get("hit_subject_scene").get(entity.getHitSubjectScene())));
            cloudControlSet(entity, item, currentUser);
            return item;
        }).collect(Collectors.toList());
        return PageResult.of(collect, request.getPageParams().getPageNumber(), result.getTotal(),
                request.getPageParams().getPageSize());
    }

    /**
     * 获取列表需要的码表map值
     *
     * @return type -> code -> name
     */
    private Map<String, Map<Long, String>> buildDictMap() {
        return dictService.getDictListByTypeList(List.of("hit_subject_scene"))
                .stream().collect(Collectors.groupingBy(DictDto::getType,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName)))));
    }

    @Override
    public PageResult<WarningAllInfoVO> getAllWarningPointList(ListParamsRequest request) {
        PageResult<WarningListVO> allWarningList = getAllWarningList(request);
        // 预警信息
        List<WarningAllInfoVO> collect = allWarningList.getItems()
                .stream()
                .map(WarningAllInfoVO::new)
                .collect(Collectors.toList());
        // 坐标信息
        String ids = allWarningList.getItems()
                .stream()
                .map(WarningListVO::getId)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        List<WarningTrackPointVO> trackPointByIds = getTrackPointByIds(ids);
        Map<Long, List<WarningTrackPointVO>> groupByWarn = trackPointByIds.stream()
                .collect(Collectors.groupingBy(WarningTrackPointVO::getWarningId));
        collect.forEach(w -> {
            w.setPoints(Optional.ofNullable(groupByWarn.get(w.getWarningListVO().getId())).orElse(new ArrayList<>()));
        });
        return PageResult.of(collect, request.getPageParams().getPageNumber(), allWarningList.getTotal(),
                request.getPageParams().getPageSize());
    }

    @Override
    public PageResult<TodoTaskVO> getWarningTodo(PageParams pageParams) {
        try {
            final ListParamsRequest request = new ListParamsRequest();
            KeyValueTypeVO warningStatus = new KeyValueTypeVO("warningStatusList", "[1,2]", "string");
            List<KeyValueTypeVO> filters = new ArrayList<>();
            filters.add(warningStatus);
            request.setFilterParams(filters);
            request.setPageParams(pageParams);
            request.setSearchParams(new SearchParams());
            request.setSortParams(new SortParams());
            PageResult<WarningListVO> list = getMyWarningList(request);
            return PageResult.of(list.getItems().stream().map(WarningListVO::of).collect(Collectors.toList()),
                    pageParams.getPageNumber(), list.getTotal(), pageParams.getPageSize());
        } catch (Exception e) {
            log.error("查询预警待办列表出错！", e);
        }
        return PageResult.empty(pageParams);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void readWarning(Long warningId) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        warningNotifyMapper.updateByWarningIdAndUser(warningId, currentUser.getId(), currentUser.getDeptId());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public WarningDetailVO getWarningDetail(Long warningId) {
        readWarning(warningId);
        return warningOperateProxy.getOperateService().getWarningDetail(warningId);
    }

    @Override
    public WarningActivityVO getWarningActivity(String trackId, String portType) {
        WarningActivityVO vo = new WarningActivityVO();
        if ("ga".equals(portType)) {
            Expression expression = Condition("_id", Operator.Equal, trackId);
            InductiveControlEsWarning esWarning = inductiveControlEsWarningRepository.findFirst(expression);
            Date date = esWarning.getWarningTime();
            if (Objects.nonNull(date)) {
                LocalDateTime activityTime = date.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
                vo.setActivityTime(activityTime);
            }
            vo.setActivityPlace(esWarning.getGzymc());
        } else {
            WarningTrackEntity track = warningTrackMapper.selectById(trackId);
            vo = WarningActivityVO.builder()
                    .activityTime(track.getActivityTime())
                    .activityPlace(track.getPlace())
                    .build();
        }
        return vo;
    }

    @Override
    public PersonCardVO getWarningPerson(String trackId, String portType) {
        PersonCardVO cardVO = null;
        if ("ga".equals(portType)) {
            cardVO = getEsPersonCardVO(trackId);
        } else {
            cardVO = getPersonCardVO(trackId);
        }
        return cardVO;
    }

    private PersonCardVO getEsPersonCardVO(String trackId) {
        Expression expression = Condition("_id", Operator.Equal, trackId);
        InductiveControlEsWarning esWarning = inductiveControlEsWarningRepository.findFirst(expression);
        CareMonitorEntity careMonitorEntity = careMonitorMapper.selectById(esWarning.getMonitorId());
        PersonCardVO cardVO = new PersonCardVO();
        cardVO.setName(careMonitorEntity.getCertificateName());
        cardVO.setIdNumber(esWarning.getIdentifier());
        cardVO.setCarNumber(careMonitorEntity.getCarNumber());
        cardVO.setTel(careMonitorEntity.getTel());
        cardVO.setImgs(List.of(FileInfoVO.idNumberToImg(esWarning.getIdentifier())));
        return cardVO;
    }

    private @NotNull PersonCardVO getPersonCardVO(String trackId) {
        WarningTrackEntity track = warningTrackMapper.selectById(trackId);
        WarningEntity warningEntity = warningMapper.selectById(track.getWarningId());

        Long groupId = warningEntity.getGroupId();
        PersonCardVO cardVO = profileService.getPersonCardById(track.getPersonId());
        if (groupId != null) {
            cardVO.setActiveLevel(profileService.getPersonActivityLevel(track.getPersonId(), groupId));
        }
        ControlInfo controlInfo = ControlInfo.getControlInfo(track.getMonitorId(), track.getControlType());
        SimpleUserVO simpleUser = permissionService.findSimpleUser(controlInfo.getCreateUser().getUserId(),
                controlInfo.getCreateUser().getDeptId());
        cardVO.setMonitorUserName(simpleUser.getUserName());
        cardVO.setMonitorDeptName(simpleUser.getDeptShortName());
        return cardVO;
    }

    @Override
    public WarningPhotoVO getWarningPhoto(Long trackId) {
        WarningTrackEntity track = warningTrackMapper.selectById(trackId);
        if (track == null || track.getPhotos() == null) {
            return new WarningPhotoVO();
        }
        List<String> photos = track.getPhotos() != null ? track.getPhotos().stream()
                .map(url -> "/oss/photo/forward?base64Url=" + Base64.getEncoder().encodeToString(url.getBytes()))
                .collect(Collectors.toList()) : Collections.emptyList();
        return new WarningPhotoVO(Objects.isNull(track.getSimilarity()) ? null : track.getSimilarity() + "%", photos);
    }

    @Override
    public WarningSourceVO getWarningSource(String trackId, String portType) {
        WarningSourceVO sourceVO = null;
        if ("ga".equals(portType)) {
            Expression expression = Condition("_id", Operator.Equal, trackId);
            InductiveControlEsWarning esWarning = inductiveControlEsWarningRepository.findFirst(expression);
            SourceEntity sourceEntity = sourceMapper.selectByUniqueKey(esWarning.getSourceId() + "-" + esWarning.getGzylx());
            if (Objects.nonNull(sourceEntity)) {
                sourceVO = sourceEntity.toVO();
            }
        } else {
            WarningTrackEntity track = warningTrackMapper.selectById(trackId);
            if (track == null) {
                return new WarningSourceVO();
            }
            SourceEntity sourceEntity = sourceMapper.selectByUniqueKey(track.getSourceId());
            if (sourceEntity == null) {
                return new WarningSourceVO();
            }
            sourceVO = sourceEntity.toVO();
        }
        return sourceVO;
    }

    @Override
    public List<CategoryVO> getWarningCategory(Long trackId) {
        WarningSourceTypeEntity entity = warningSourceTypeMapper.selectCategoryByTrackId(trackId);
        List<CategoryVO> results = Objects.isNull(entity) || entity.getProperties() == null ? new ArrayList<>() : Arrays.asList(entity.getProperties());
        return results;
    }

    @Override
    public WarningTrackVO getTrackDetail(Long trackId) {
        WarningTrackEntity track = warningTrackMapper.selectById(trackId);
        if (track.getTrackDetail() != null) {
            WarningSourceTypeEntity configEntity = warningSourceTypeMapper.selectByWarningType(
                    track.getDatasourceType());
            TrackConfig trackConfig = JsonUtil.parseObject(configEntity.getExtend(), TrackConfig.class);
            WarningTrackVO vo = new WarningTrackVO();

            Map<String, Object> map = new HashMap<>();
            Map<String, String> trackDetailMap = JsonUtil.parseMap(track.getTrackDetail(), String.class);
            trackDetailMap.forEach((key, value) -> map.put(key, value));
            map.put(WarningContentGenerator.TRACK_DETAIL, track.getTrackDetail());

            // 根据spel表达式生成对应值
            vo.setTrainNumber(contentGenerator.generateContent(trackConfig.getTrainNumber(), map));
            vo.setFrom(contentGenerator.generateContent(trackConfig.getFrom(), map));
            vo.setTo(contentGenerator.generateContent(trackConfig.getTo(), map));

            for (Map.Entry<String, String> entry : trackConfig.getInfo().entrySet()) {
                String result = contentGenerator.generateContent(entry.getValue(), map);
                entry.setValue(result);
            }

            vo.setInfo(trackConfig.getInfo());
            return vo;
        }
        return new WarningTrackVO();
    }

    @Override
    public List<WarningAreaVO> getImportantArea(String trackId, String portType) {
        if ("ga".equals(portType)){
            return new ArrayList<>();
        }else {
            WarningTrackEntity track = warningTrackMapper.selectById(trackId);
            WarningEntity warning = warningMapper.selectById(track.getWarningId());
            return Objects.nonNull(warning.getAreaId())
                    ? warning.getAreaId().stream().map(modelId -> importantAreaMapper.findWarningDetailById(modelId)).collect(Collectors.toList())
                    : Collections.emptyList();
        }
    }

    @Override
    public List<GroupPersonVO> getWarningGroupPersonList(Long warningId) {
        //TODO 群体预警某一人员有多条轨迹时，如何展示？
        Map<String, Optional<WarningTrackEntity>> tracks = warningTrackMapper.selectTrackListByWarningId(warningId)
                .stream()
                .collect(Collectors.groupingBy(
                        WarningTrackEntity::getIdentifier,
                        Collectors.maxBy(Comparator.comparing(WarningTrackEntity::getActivityTime))));

        return tracks.values().stream().map(optional -> {
            WarningTrackEntity track = optional.orElse(null);
            if (track == null) {
                return null;
            }
            PersonVO personVO = profileService.getPersonByIdNumber(track.getIdentifier());
            if (personVO == null) {
                return null;
            }
            GroupPersonVO vo = new GroupPersonVO();
            vo.setId(personVO.getId());
            vo.setName(personVO.getName());
            vo.setPhoto(!personVO.getImgs().isEmpty() ? personVO.getImgs().get(0) : null);
            vo.setTrackId(track.getId());
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<String> getUserWarningOperate(Long warningId, SimpleUserVO current) {
        List<String> result = new ArrayList<>();
        WarningProcessEntity process = warningProcessMapper.getUserProcessByWarningId(warningId, current.getUserId(),
                current.getDeptId());
        if (Objects.nonNull(process)) {
            switch (process.getStatus()) {
                case WAITING_SIGN:
                    result.add(WarningStatusEnum.WAITING_SIGN.getAction());
                    break;
                case SIGN_FINISH:
                case PROCESS_FINISH:
                    result.add(WarningStatusEnum.SIGN_FINISH.getAction());
                    break;
                case REPLY_FINISH:
                    result.add(WarningStatusEnum.SIGN_FINISH.getAction());
                    result.add(WarningStatusEnum.REPLY_FINISH.getAction());
                    break;
                default:
            }
        } else {
            return Collections.emptyList();
        }
        return result;
    }


    @Override
    public PersonCardVO getWarningRegularPerson(Long trackId) {
        WarningTrackEntity track = warningTrackMapper.selectById(trackId);
        WarningEntity warningEntity = warningMapper.selectById(track.getWarningId());

        final RegularMonitorEntity regularMonitorEntity = regularMonitorMapper.selectById(warningEntity.getMonitorId());

        PersonCardVO cardVO = profileService.getPersonCardById(regularMonitorEntity.getTargetId());

        CodeNameVO regularLevel = commonMapper.getDict(regularMonitorEntity.getLevel(),
                "control_regular_monitor_level");
        cardVO.setRegularLevel(regularLevel);
        RegularMonitorStatusEnum status = regularMonitorEntity.getStatus();
        if (Objects.nonNull(status)) {
            cardVO.setRegularStatus(new CodeNameVO(status.getCode(), status.getName()));
        }
        return cardVO;
    }

    @Override
    public WarningProcessVO getWarningProcess(Long id) {
        WarningProcessVO warningProcessVO = new WarningProcessVO();
        warningProcessVO.setDispatch(getWarningDispatchByWarningId(id));
        warningProcessVO.setDisposal(warningProcessMapper.getWarningDisposalByWarningId(id));
        warningProcessMapper.getWarningDone(id).ifPresent(process -> {
            WarningDoneVO done = process.getDone();
            warningProcessVO.setDone(new Done(TimeUtil.getSimpleTime(done.getTime()), done.getUserInfo()));
        });
        //蓝色预警，默认完结
        WarningEntity warning = warningMapper.selectById(id);
        if (warning.getWarningLevel().equals(MonitorLevelEnum.BLUE)) {
            warningProcessVO.setDone(new Done(TimeUtil.getSimpleTime(warning.getWarningTime()), new SimpleUserVO()));
        }
        return warningProcessVO;
    }

    /**
     * 获取预警推送
     *
     * @param id 预警id
     * @return 推送
     */
    WarningDispatchVO getWarningDispatchByWarningId(Long id) {
        WarningDispatchVO warningDispatchVO = new WarningDispatchVO();
        WarningEntity warning = warningMapper.selectById(id);
        warningDispatchVO.setTime(warning.getWarningTime());
        List<WarningNotifyEntity> warningNotify = warningNotifyMapper.selectByWarningId(id);
        warningDispatchVO.setNotifyPerson(
                warningNotify.stream().filter(item -> WarningNotifyEnum.NOTIFY_PERSON.equals(item.getNotifyType()))
                        .map(item -> permissionService.findSimpleUser(item.getUserId(), item.getDeptId())).collect(
                                Collectors.toList()));

        //通知部门要进行排重
        StringBuilder notifyDept = new StringBuilder();
        for (WarningNotifyEntity curWarningNotify : warningNotify) {
            if (!WarningNotifyEnum.NOTIFY_PERSON.equals(curWarningNotify.getNotifyType())) {
                DeptDto dept = permissionService.getDeptById(curWarningNotify.getDeptId());
                String appendNotifyDept = dept.getShortName()
                        + "(" + curWarningNotify.getNotifyType().getName() + ")";
                if (!notifyDept.toString().contains(appendNotifyDept)) {
                    notifyDept.append(appendNotifyDept).append("、");
                }
            }
        }
        if (notifyDept.length() > 0) {
            warningDispatchVO.setNotifyDept(notifyDept.toString());
        }

        switch (warning.getControlType()) {
            case MONITOR:
                MonitorEntity monitorEntity = monitorMapper.selectById(warning.getMonitorId());
                warningDispatchVO.setInitiator(permissionService.findSimpleUser(monitorEntity.getMonitorPersonId(),
                        monitorEntity.getMonitorPersonUnit()));
                warningDispatchVO.setMonitorInfo(
                        new MonitorInfo(monitorEntity.getId(), monitorEntity.getMonitorTitle()));
                break;
            case REGULAR:
                RegularMonitorEntity regularMonitorEntity = regularMonitorMapper.selectById(warning.getMonitorId());
                warningDispatchVO.setInitiator(permissionService.findSimpleUser(regularMonitorEntity.getCreateUserId(),
                        regularMonitorEntity.getCreateDeptId()));
                RegularInfo regularInfo = new RegularInfo();
                regularInfo.setRegularId(regularMonitorEntity.getId());
                regularInfo.setName(profileService.findById(regularMonitorEntity.getTargetId()).getName());
                regularInfo.setTitle(
                        regularMonitorConfigMapper.getConfigByRegularId(regularMonitorEntity.getId()).stream()
                                .filter(item -> {
                                    item.getWarningModel().retainAll(warning.getModelId());
                                    return !item.getWarningModel().isEmpty();
                                }).map(RegularMonitorConfigEntity::getName).collect(Collectors.joining("、")));
                warningDispatchVO.setRegularInfo(regularInfo);
                break;
            default:
        }
        return warningDispatchVO;
    }

    @Override
    public List<WarningFeedbackListVo> getFeedbackList(Long id) {
        List<WarningFeedbackListVo> feedbackList = warningFeedbackMapper.getFeedbackListByWarningId(id);
        feedbackList.forEach(f -> {
            WarningFeedbackDetailVO feedback = f.getFeedback();
            if (Objects.isNull(feedback)) {
                return;
            }
            feedback.setContentJson(feedback.getContent());
            if (JsonUtil.isValidJson(feedback.getContent())) {
                Tuple2<String, String> tuple2 = buildFeedbackContent(feedback.getContent());
                feedback.setContent(tuple2._1);
                feedback.setContentJson(tuple2._2);
            }
        });

        return feedbackList;
    }

    @Override
    public List<WarningTrackPointVO> getTrackPoint(String warningId, String portType) {
        List<WarningTrackPointVO> pointVOList = new ArrayList<>();
        if ("ga".equals(portType)) {
            Expression expression = Condition("_id", Operator.Equal, warningId);
            List<InductiveControlEsWarning> esWarningList = inductiveControlEsWarningRepository.findList(expression);
            pointVOList = esWarningList.stream()
                    .map(this::getEsPoint)
                    .collect(Collectors.toList());
        } else {
            pointVOList = warningTrackMapper.selectTrackListByWarningId(Long.valueOf(warningId)).stream()
                    .map(WarningTrackEntity::toVO).collect(Collectors.toList());
        }
        return pointVOList;
    }

    /**
     * 获取es预警轨迹点
     *
     * @param esWarning 预警
     * @return 轨迹数据
     */
    private WarningTrackPointVO getEsPoint(InductiveControlEsWarning esWarning) {
        WarningTrackPointVO vo = new WarningTrackPointVO();
        Date date = esWarning.getWarningTime();
        LocalDateTime activityTime = null;
        if (Objects.nonNull(date)) {
            activityTime = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
        }
        vo.setDateTime(activityTime);
        vo.setAddress(esWarning.getGzymc());
        if (StringUtils.isNotEmpty(esWarning.getWdwgs84()) && !"null".equals(esWarning.getWdwgs84())){
            vo.setLatitude(Double.valueOf(esWarning.getWdwgs84()));
        }
        if (StringUtils.isNotEmpty(esWarning.getJdwgs84()) && !"null".equals(esWarning.getJdwgs84())){
            vo.setLongitude(Double.valueOf(esWarning.getJdwgs84()));
        }
        CareMonitorEntity careMonitorEntity = careMonitorMapper.selectById(esWarning.getMonitorId());
        vo.setPersonName(careMonitorEntity.getCertificateName());
        vo.setPersonIdCard(esWarning.getIdentifier());
        SourceEntity sourceEntity = sourceMapper.selectByUniqueKey(esWarning.getSourceId() + "-" + esWarning.getGzylx());
        if (Objects.nonNull(sourceEntity)) {
            WarningSourceVO sourceVO = sourceEntity.toVO();
            vo.setSourceId(sourceEntity.getId());
            vo.setSourceKey(esWarning.getSourceId() + "-" + esWarning.getGzylx());
            vo.setSourceType(sourceVO.getType());
            vo.setSourceName(sourceVO.getSourceName());
            vo.setSourceTopType(sourceVO.getTopType());
        }
        return vo;
    }

    @Override
    public List<WarningTrackPointVO> getTrackPointByIds(String warningIds) {
        if (Objects.isNull(warningIds) || warningIds.isEmpty()) {
            return new ArrayList<>();
        }
        QueryWrapper<WarningTrackEntity> query = new QueryWrapper<WarningTrackEntity>()
                .in("warning_id", Stream.of(warningIds.split(",|;")).map(Long::valueOf).collect(Collectors.toList()));
        List<WarningTrackEntity> warningTrackEntities = warningTrackMapper.selectList(query);
        return warningTrackEntities.stream()
                .map(WarningTrackEntity::toVO).collect(Collectors.toList());
    }

    @Override
    public Long countUnread() {
        CurrentUser currentUser = AuthHelper.getNotNullUser();
        return warningMapper.countWarningUnread(currentUser.getId(), currentUser.getDeptId());
    }

    @Override
    public List<WarningConfigVO> getWarningConfigList() {
        return warningSourceTypeMapper.getWarningConfigList();
    }

    @Override
    public String addWarningConfig(WarningConfigDTO warningConfigDTO) {
        //配置英文名检验唯一性
        WarningSourceTypeEntity config = sourceService.getWarningSourceTypeByEnName(warningConfigDTO.getEnName());
        if (!Objects.isNull(config)) {
            throw new TRSException("配置英文名重复！");
        }
        WarningSourceTypeEntity warningSourceTypeEntity = new WarningSourceTypeEntity();
        warningSourceTypeEntity.setSourceType(warningConfigDTO.getSourceType());
        warningSourceTypeEntity.setCnName(warningConfigDTO.getCnName());
        warningSourceTypeEntity.setEnName(warningConfigDTO.getEnName());
        warningSourceTypeEntity.setContentTemplate(warningConfigDTO.getContentTemplate());
        warningSourceTypeEntity.setIdentifierType(warningConfigDTO.getIdentifierType());
        try {
            String[] imageColumns = JSON.parseObject(warningConfigDTO.getImageColumns(), String[].class);
            warningSourceTypeEntity.setImageColumns(imageColumns);
        } catch (JSONException e) {
            return "imageColumns格式异常";
        }
        warningSourceTypeEntity.setExtend(warningConfigDTO.getExtend());
        warningSourceTypeEntity.setSimilarity(warningConfigDTO.getSimilarity());
        try {
            CategoryVO[] properties = JSON.parseObject(warningConfigDTO.getProperties(), CategoryVO[].class);
            warningSourceTypeEntity.setProperties(properties);
        } catch (JSONException e) {
            return "properties格式异常";
        }
        //状态默认开启
        warningSourceTypeMapper.insert(warningSourceTypeEntity);
        return "新增成功！";
    }

    @Override
    public String editWarningConfig(Long configId, WarningConfigDTO warningConfigDTO) {
        //配置英文名检验唯一性
        WarningSourceTypeEntity config = sourceService.getWarningSourceTypeByEnName(warningConfigDTO.getEnName());
        if (!Objects.isNull(config) && !config.getId().equals(configId)) {
            throw new TRSException("配置英文名重复！");
        }
        QueryWrapper<WarningSourceTypeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", configId);
        WarningSourceTypeEntity warningSourceTypeEntity1 = warningSourceTypeMapper.selectOne(queryWrapper);
        WarningSourceTypeEntity warningSourceTypeEntity = new WarningSourceTypeEntity();
        warningSourceTypeEntity.setStatus(warningSourceTypeEntity1.getStatus());
        warningSourceTypeEntity.setId(configId);
        warningSourceTypeEntity.setSourceType(warningConfigDTO.getSourceType());
        warningSourceTypeEntity.setCnName(warningConfigDTO.getCnName());
        warningSourceTypeEntity.setEnName(warningConfigDTO.getEnName());
        warningSourceTypeEntity.setContentTemplate(warningConfigDTO.getContentTemplate());
        warningSourceTypeEntity.setIdentifierType(warningConfigDTO.getIdentifierType());
        try {
            String[] imageColumns = JSON.parseObject(warningConfigDTO.getImageColumns(), String[].class);
            warningSourceTypeEntity.setImageColumns(imageColumns);
        } catch (JSONException e) {
            return "imageColumns格式异常";
        }
        warningSourceTypeEntity.setExtend(warningConfigDTO.getExtend());
        warningSourceTypeEntity.setSimilarity(warningConfigDTO.getSimilarity());
        try {
            CategoryVO[] properties = JSON.parseObject(warningConfigDTO.getProperties(), CategoryVO[].class);
            warningSourceTypeEntity.setProperties(properties);
        } catch (JSONException e) {
            return "properties格式异常";
        }
        warningSourceTypeMapper.updateById(warningSourceTypeEntity);
        return "编辑成功";
    }

    @Override
    public void delWarningConfig(Long configId) {
        WarningSourceTypeEntity config = warningSourceTypeMapper.selectById(configId);
        if (config == null) {
            throw new TRSException("配置不存在");
        }
        QueryWrapper<WarningTrackEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("datasource_type", config.getEnName());
        List<WarningTrackEntity> track = warningTrackMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(track)) {
            warningSourceTypeMapper.deleteById(configId);
        } else {
            throw new TRSException("该配置已经产生相关数据无法删除");
        }
    }

    @Override
    public void setWarningConfigStatus(Long configId, Integer status) {
        QueryWrapper<WarningSourceTypeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", configId);
        WarningSourceTypeEntity warningSourceTypeEntity = warningSourceTypeMapper.selectOne(queryWrapper);
        if (status.equals(WarningConfigConstant.DISABLE)) {
            warningSourceTypeEntity.setStatus(WarningConfigConstant.DISABLE);
        } else if (status.equals(WarningConfigConstant.ENABLE)) {
            warningSourceTypeEntity.setStatus(WarningConfigConstant.ENABLE);
        } else {
            throw new TRSException("传入的status值非法！");
        }
        warningSourceTypeMapper.updateById(warningSourceTypeEntity);
    }

    @Override
    public List<WarningEnNameVO> getNoConfigEnName() {
        return warningNoconfigMapper.getNoConfigEnName();
    }

    @Override
    public WarningConfigRecordVO getConfigRecord() {
        WarningConfigRecordVO warningConfigRecordVO = new WarningConfigRecordVO();
        warningConfigRecordVO.setEnable(warningSourceTypeMapper.selectEnableRecord());
        warningConfigRecordVO.setConfig(warningSourceTypeMapper.selectAllRecord());
        warningConfigRecordVO.setNoConfig(warningNoconfigMapper.selectNoConfigGroupCount());
        return warningConfigRecordVO;
    }

    @Override
    public List<IdentifierTypeVO> getIdentifierType() {
        return Arrays.stream(IdentifierTypeEnum.values())
                .map(identifierType -> new IdentifierTypeVO(identifierType.getCode(), identifierType.getCnName()))
                .collect(Collectors.toList());
    }

    @Override
    public Boolean checkUnique(NameUniqueDTO nameUniqueDTO) {
        QueryWrapper<WarningSourceTypeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("en_name", nameUniqueDTO.getEnName());
        WarningSourceTypeEntity config = warningSourceTypeMapper.selectOne(queryWrapper);
        if (nameUniqueDTO.getType() == 0) {
            return config == null;
        } else {
            return config == null || config.getId().equals(nameUniqueDTO.getConfigId());
        }
    }

    @Override
    public RestfulResultsV2<WarningRankVO> statistic() {
        List<WarningRankVO> rankList = deptHelper.getRankList().stream()
                .map(WarningRankVO::getVo)
                .collect(Collectors.toList());
        CompletableFuture.allOf(rankList.stream().flatMap(rankVO -> {
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            futures.add(CompletableFuture.runAsync(() -> executeSingleStatistic(rankVO, null, rankVO::setCount),
                    monitorWarningStatisticExecutor));
            futures.add(CompletableFuture.runAsync(() -> executeSingleStatistic(rankVO, 1L, rankVO::setEngineCount),
                    monitorWarningStatisticExecutor));
            futures.add(CompletableFuture.runAsync(() -> executeSingleStatistic(rankVO, 2L, rankVO::setCloudControlCount),
                    monitorWarningStatisticExecutor));
            return futures.stream();
        }).toArray(CompletableFuture[]::new)).join();
        return RestfulResultsV2.ok(rankList);
    }

    private void executeSingleStatistic(WarningRankVO rankVO, Long warningPlatform, Consumer<Integer> consumer) {
        Integer count = warningOperateProxy.getOperateService().countByRank(rankVO, warningPlatform);
        consumer.accept(count);
    }

    private Tuple2<String, String> buildFeedbackContent(String content) {
        Map<Long, String> fxmbztMap = dictService.getDictTree("fxmbzt_type").stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName));
        Map<Long, String> czjgMap = dictService.getDictTree("czjg_type").stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName));
        WarningFeedbackHandleVO feedbackHandleVO = JSON.parseObject(content, WarningFeedbackHandleVO.class);
        String fxmbzrdwjgdm = cloudControlPushHelper.getLastElement(feedbackHandleVO.getFxmbzrdwjgdm());
        String czzrdwjgdm = cloudControlPushHelper.getLastElement(feedbackHandleVO.getCzzrdwjgdm());
        List<String> deptCodes = Arrays.asList(fxmbzrdwjgdm, czzrdwjgdm);
        Map<String, String> deptMap = permissionService.getDeptByCodes(deptCodes).stream().collect(Collectors.toMap(DeptDto::getCode, DeptDto::getName));
        Map<String, String> userMap = permissionService.getUserByIdCards(Arrays.asList(feedbackHandleVO.getCzzrmjsfzh(), feedbackHandleVO.getFxmbzrmjsfzh())).stream()
                .collect(Collectors.toMap(UserDto::getIdNumber, UserDto::getRealName));
        feedbackHandleVO.setMbfxztmw(fxmbztMap.getOrDefault(Long.valueOf(feedbackHandleVO.getMbfxzt()), ""));
        feedbackHandleVO.setFxmbzrdw(deptMap.getOrDefault(fxmbzrdwjgdm, ""));
        feedbackHandleVO.setFxmbzrmjxm(userMap.getOrDefault(feedbackHandleVO.getFxmbzrmjsfzh(), ""));
        feedbackHandleVO.setCzzrdw(deptMap.getOrDefault(czzrdwjgdm, ""));
        feedbackHandleVO.setCzzrmjxm(userMap.getOrDefault(feedbackHandleVO.getCzzrmjsfzh(), ""));
        if (StringUtils.isNotEmpty(feedbackHandleVO.getCzjg())) {
            feedbackHandleVO.setCzjgmw(czjgMap.getOrDefault(Long.valueOf(feedbackHandleVO.getCzjg()), ""));
        }
        feedbackHandleVO.setCzddqhmw(dictService.getDistrictNameByCode(cloudControlPushHelper.getLastElement(feedbackHandleVO.getCzddqh())));
        String format;
        if ("6".equals(feedbackHandleVO.getMbfxzt())) {
            format = String.format("\n发现目标状态：%s", fxmbztMap.getOrDefault(Long.valueOf(feedbackHandleVO.getMbfxzt()), ""));
        } else {
            format = String.format("\n发现目标状态：%s\n发现目标单位：%s\n发现目标民警：%s\n处置单位：%s\n处置民警：%s\n" +
                            "处置时间：%s\n处置地点：%s\n处置结果：%s\n处置经过描述：%s\n反馈联系人电话：%s",
                    feedbackHandleVO.getMbfxztmw(),
                    feedbackHandleVO.getFxmbzrdw(),
                    feedbackHandleVO.getFxmbzrmjxm(),
                    feedbackHandleVO.getCzzrdw(),
                    feedbackHandleVO.getCzzrmjxm(),
                    feedbackHandleVO.getCzsj(),
                    feedbackHandleVO.getCzddxz(),
                    feedbackHandleVO.getCzjgmw(),
                    feedbackHandleVO.getCzjgms(),
                    feedbackHandleVO.getFkrlxdh());
        }
        return Tuple.of(format, JSON.toJSONString(feedbackHandleVO));
    }

}
