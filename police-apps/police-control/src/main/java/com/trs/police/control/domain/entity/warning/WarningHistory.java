package com.trs.police.control.domain.entity.warning;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/6/27 16:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_warning_history")
public class WarningHistory {

    /**
     * 预警id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 原始数据
     */
    private Object jsonData;

    /**
     * 预警id
     */
    private Long warningId;

    public WarningHistory(Object jsonData, Long warningId) {
        this.jsonData = jsonData;
        this.warningId = warningId;
    }
}
