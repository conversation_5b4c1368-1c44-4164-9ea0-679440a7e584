package com.trs.police.control.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.control.domain.vo.warning.WarningProcessVO.Disposal;
import com.trs.police.control.helper.WarningListVoHelper;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/4/17
 */
@Mapper
public interface WarningProcessMapper extends BaseMapper<WarningProcessEntity> {

    /**
     * 预警处置流程入库排重
     *
     * @param warningProcessEntity 预警处置流程
     */
    void insertIgnoreDuplicate(@Param("warningProcessEntity") WarningProcessEntity warningProcessEntity);

    /**
     * 预警详情-预警处置
     *
     * @param warningId 预警Id
     * @return 预警处置流程
     */
    List<Disposal> getWarningDisposalByWarningId(@Param("warningId") Long warningId);

    /**
     * 预警详情-预警处置
     *
     * @param warningIds 预警Id
     * @return 预警处置流程
     */
    List<WarningListVoHelper.WarningListDisposal> getWarningDisposalByWarningIds(@Param("warningIds") List<Long> warningIds);

    /**
     * 获取预警完结
     *
     * @param warningId 预警id
     * @return 预警完结
     */
    @Select("select * from t_warning_process where warning_id=#{warningId} and done is not null")
    @ResultMap("mybatis-plus_WarningProcessEntity")
    Optional<WarningProcessEntity> getWarningDone(@Param("warningId") Long warningId);

    /**
     * 获取预警完结
     *
     * @param warningIds 预警id
     * @return 预警完结
     */
    List<WarningProcessEntity> getWarningDoneList(@Param("warningIds") List<Long> warningIds);


    /**
     * 获取用户预警预警流程
     *
     * @param warningId 预警id
     * @param userId    用户id
     * @param deptId    部门id
     * @return {@link  WarningProcessEntity}
     */
    @Select("select * from t_warning_process where id=(select process_id from t_warning_notify where warning_id=#{warningId} and user_id=#{userId} and dept_id=#{deptId})")
    @ResultMap("mybatis-plus_WarningProcessEntity")
    WarningProcessEntity getUserProcessByWarningId(@Param("warningId") Long warningId, @Param("userId") Long userId,
        @Param("deptId") Long deptId);

    /**
     * 获取用户预警预警流程
     *
     * @param warningId 预警id
     * @return {@link  WarningProcessEntity}
     */
    @Select("select * from t_warning_process where id = #{warningId}")
    List<WarningProcessEntity> getUserProcessByWarningIdForCheck(@Param("warningId") Long warningId);

    /**
     * 获取用户预警预警流程
     *
     * @param warningIds 预警id
     * @param userId     用户id
     * @param deptId     部门id
     * @return {@link  WarningProcessEntity}
     */
    @Select({"<script>",
            "select * from t_warning_process where id in (select process_id from t_warning_notify where warning_id in ",
            "<foreach collection='warningIds' item='warningId' open='(' separator=',' close=')'>",
            "#{warningId}",
            "</foreach>",
            " and user_id=#{userId} and dept_id=#{deptId})",
            "</script>"})
    @ResultMap("mybatis-plus_WarningProcessEntity")
    List<WarningProcessEntity> getUserProcessByWarningIdList(@Param("warningIds") List<Long> warningIds, @Param("userId") Long userId,
                                                   @Param("deptId") Long deptId);

    /**
     * 更新签收预期
     *
     * @param id 流程id
     */
    @Update("update t_warning_process set sign_overdue=1 where id=#{id}")
    void updateSighExpireStatus(@Param("id") Long id);

    /**
     * 更新反馈预期
     *
     * @param id 流程id
     */
    @Update("update t_warning_process set reply_overdue=1 where id=#{id}")
    void updateReplyExpireStatus(@Param("id") Long id);

    /**
     * 获取预警流程（不去重）
     *
     * @param request 请求参数
     * @param page    分页参数
     * @return {@link WarningProcessEntity}
     */
    Page<WarningProcessEntity> selectWarningProcessList(@Param("request") ListParamsRequest request,
        Page<WarningProcessEntity> page);

    /**
     * 将所有预警状态改为已完结
     *
     * @param warningId 预警Id
     */
    @Update("update t_warning_process set status=4 where warning_id=#{warningId}")
    void updateProcessDoneByWarningId(@Param("warningId") Long warningId);

    /**
     * 查询当前预警完结状态
     *
     * @param warningId 预警id
     * @return 完结状态
     */
    @Select("select if(exists (select 1 from t_warning_process where warning_id=#{warningId} and status=4), 1, 0)")
    Boolean getWarningDoneStatus(@Param("warningId") Long warningId);

    /**
     * 查询当前预警完结状态
     *
     * @param warningIds 预警id
     * @return 完结状态
     */
    List<WarningListVoHelper.WarningDoneStatusVO> getWarningDoneStatusList(@Param("warningIds") List<Long> warningIds);

    /**
     * 同步预警状态到es
     *
     * @param startWarningId startWarningId
     * @param endWarningId   endWarningId
     * @return 列表
     */
    @Select("select warning_id, max(sign_overdue) as sign_overdue, max(reply_overdue) as reply_overdue from t_warning_process where warning_id >= #{startWarningId} and warning_id <= #{endWarningId} group by warning_id")
    List<WarningProcessEntity> getSignOverdueReplyOverdueByWarningIds(@Param("startWarningId") Long startWarningId, @Param("endWarningId") Long endWarningId);
}
