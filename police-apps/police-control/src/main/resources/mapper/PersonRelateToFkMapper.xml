<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.PersonRelateToFkMapper">

    <select id="getNotRecordList"
            resultType="com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity">
        select * from t_warning_fk_person where 1 = 1
        <if test="dto.dataSource != null">
            and data_source = #{dto.dataSource}
        </if>
        <if test="dto.nation != null">
            and nation = #{dto.nation}
        </if>
        <if test="dto.personStatus != null">
            and person_status = #{dto.personStatus}
        </if>
        <if test="dto.firstIntoTimeStart != null and dto.firstIntoTimeStart != ''">
            and first_into_time >= #{dto.firstIntoTimeStart}
        </if>
        <if test="dto.firstIntoTimeEnd != null and dto.firstIntoTimeEnd != ''">
            and first_into_time &lt;= #{dto.firstIntoTimeEnd}
        </if>
        <if test="dto.onRecord != null">
            and on_record = #{dto.onRecord}
        </if>

        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(dto.searchValue)">
            <bind name="pattern" value="'%' + dto.searchValue.trim() + '%'"/>
            <choose>
                <when test=" 'name'==dto.searchField">
                    and name like #{pattern}
                </when>
                <when test=" 'id_card'==dto.searchField">
                    and id_card like #{pattern}
                </when>
                <otherwise>
                    and ( name like #{pattern} or id_card like #{pattern} )
                </otherwise>
            </choose>
        </if>

        <if test="dto.firstIntoTimeOrder != null and dto.firstIntoTimeOrder == 'desc'">
            order by first_into_time desc
        </if>
        <if test="dto.firstIntoTimeOrder != null and dto.firstIntoTimeOrder == 'asc'">
            order by first_into_time asc
        </if>

    </select>
    <select id="getRecordList" resultType="com.trs.police.control.domain.vo.fkrxyj.PersonRelateToFkRecordVo">
        SELECT
        a.*,b.control_level,b.person_label,b.registered_residence,c.control_person as
        dutyPolice,c.control_station,d.short_name as dutyPoliceStation
        FROM
        t_warning_fk_person AS a
        LEFT JOIN t_profile_person AS b ON a.id_card = b.id_number
        LEFT JOIN t_profile_person_police_control as c ON b.id = c.person_id
        LEFT JOIN t_dept as d on c.control_station = d.`code`
        where 1=1
        <if test="dto.dataSource != null">
            and a.data_source = #{dto.dataSource}
        </if>
        <if test="dto.personStatus != null">
            and a.person_status = #{dto.personStatus}
        </if>
        <if test="dto.nation != null">
            and a.nation = #{dto.nation}
        </if>
        <if test="dto.firstIntoTimeStart != null and dto.firstIntoTimeStart != ''">
            and a.first_into_time >= #{dto.firstIntoTimeStart}
        </if>
        <if test="dto.firstIntoTimeEnd != null and dto.firstIntoTimeEnd != ''">
            and a.first_into_time &lt;= #{dto.firstIntoTimeEnd}
        </if>
        <if test="dto.onRecord != null">
            and a.on_record = #{dto.onRecord}
        </if>

        <if test="dto.controlLevel != null">
            and b.control_level = #{dto.controlLevel}
        </if>

        <if test="dto.stations != null  and dto.stations.size() > 0">
            and
            (
            <foreach collection="dto.stations" item="station" open="" close="" separator="or">
                c.control_station like concat(#{station}, '%')
            </foreach>
            )
        </if>

        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(dto.searchValue)">
            <bind name="pattern" value="'%' + dto.searchValue.trim() + '%'"/>
            <choose>
                <when test=" 'name'==dto.searchField">
                    and a.name like #{pattern}
                </when>
                <when test=" 'id_card'==dto.searchField">
                    and a.id_card like #{pattern}
                </when>
                <otherwise>
                    and ( a.name like #{pattern} or a.id_card like #{pattern} )
                </otherwise>
            </choose>
        </if>

        <if test="dto.firstIntoTimeOrder != null and dto.firstIntoTimeOrder == 'desc'">
            order by a.first_into_time desc
        </if>
        <if test="dto.firstIntoTimeOrder != null and dto.firstIntoTimeOrder == 'asc'">
            order by a.first_into_time asc
        </if>

        <if test="dto.createTimeOrder != null and dto.createTimeOrder == 'desc'">
            order by b.create_time desc
        </if>
        <if test="dto.createTimeOrder != null and dto.createTimeOrder == 'asc'">
            order by b.create_time asc
        </if>

    </select>

    <select id="getRecordListByIds" resultType="com.trs.police.control.domain.vo.fkrxyj.PersonRelateToFkRecordVo">
        SELECT
        a.*,b.control_level,c.control_person,c.control_station,d.short_name as dutyPoliceStation
        FROM
        t_warning_fk_person AS a
        LEFT JOIN t_profile_person AS b ON a.id_card = b.id_number
        LEFT JOIN t_profile_person_police_control as c ON b.id = c.person_id
        LEFT JOIN t_dept as d on c.control_station = d.`code`
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and
            a.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

    </select>
    <select id="getLabelNames" resultType="java.lang.String">
        select GROUP_CONCAT(name) from t_profile_label where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getTotalDeptCodes" resultType="java.lang.String">
        select code from t_dept
        where 1=1 and
        <if test="preFixCodes != null and preFixCodes.size() > 0">
            (
            <foreach collection="preFixCodes" item="code" open="" close="" separator="or">
                code like concat(#{code}, '%')
            </foreach>
            )
        </if>
    </select>
    <select id="getByIdCard" resultType="com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity">
        select * from t_warning_fk_person where id_card = #{idCard} group by id_card;
    </select>


    <select id="personList" resultType="com.trs.police.control.domain.entity.fkrxyj.PersonRelateToFkEntity">
        <bind name="searchParams" value="params.searchParams"/>
        <bind name="filterParams" value="params.filterParams"/>
        <bind name="sortParams" value="params.sortParams"/>
        select * from t_warning_fk_person a
        <where>
            <if test="filterParams != null and filterParams.size() != 0">
                <foreach collection="filterParams" item="param">
                    <choose>
                        <when test="param.key == 'personType' ">
                            and JSON_CONTAINS(CAST(label_id AS JSON), #{param.value})
                        </when>
                        <when test="param.key == 'hjd' ">
                            and a.place_code = #{param.value}
                        </when>
                        <when test="param.key == 'lrsj' ">
                            and a.first_into_time between #{param.value.beginTime} and #{param.value.endTime}
                        </when>
                        <when test="param.key == 'gzqk'">
                            and a.work_situation = #{param.value}
                        </when>
                        <when test="param.key == 'zrpc'">
                            and a.zrpcs = #{param.value}
                        </when>
                        <when test="param.key == 'personLevel'">
                            and a.control_level = #{param.value}
                        </when>
                        <when test="param.key == 'processedControlStation'">
                            and JSON_CONTAINS(a.zrpcs, #{param.value})
                        </when>
                        <when test="param.key == 'nation'">
                            and a.nation = #{param.value}
                        </when>
                        <when test="param.key == 'warningTime'">
                            and a.last_warning_time between #{param.value.beginTime} and #{param.value.endTime}
                        </when>
                        <when test="param.key == 'xb'">
                            and a.xb = #{param.value}
                        </when>
                        <when test="param.key == 'xgjq'">
                            <if test="param.value == 0 or param.value == '0'">
                                and NOT EXISTS (select id from v_warning_fk_person_sthy fps WHERE fps.person_id = a.id)
                            </if>
                            <if test="param.value == 1 or param.value == '1'">
                                and EXISTS (select id from v_warning_fk_person_sthy fps WHERE fps.person_id = a.id)
                            </if>
                        </when>
                        <when test="param.key == 'profileRecord'">
                            <if test="param.value == 0 or param.value == '0'">
                                and on_record = 0
                            </if>
                            <if test="param.value == 1 or param.value == '1'">
                                and on_record = 1 and person_profile_id > 0
                            </if>
                        </when>
                    </choose>
                </foreach>
            </if>
            <if test="searchParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.getSearchValue() + '%'"/>
                <choose>
                    <when test='searchParams.searchField == "targetName"'>
                        AND a.name like #{pattern}
                    </when>
                    <when test='searchParams.searchField == "idcard"'>
                        AND a.id_card like #{pattern}
                    </when>
                    <otherwise>
                        AND (a.name like #{pattern} OR a.id_card like #{pattern})
                    </otherwise>
                </choose>
            </if>
        </where>
        order by a.last_warning_time desc
    </select>

</mapper>