<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.WarningTrackMapper">

    <!-- 基础SQL片段 -->
    <sql id="baseTrackListSql">
        FROM t_warning_track
        WHERE person_id IN
        <foreach collection="personIds" item="personId" open="(" separator="," close=")">
            #{personId}
        </foreach>
        AND activity_time BETWEEN #{startTime} AND #{endTime} and address is not null and address != '' and longitude != 0 and longitude != 1
    </sql>

    <!-- 统计符合条件的轨迹总数 -->
    <select id="countTrackListByPersonIdsAndTimeRange" resultType="java.lang.Long">
        SELECT COUNT(1)
        <include refid="baseTrackListSql"/>
    </select>

    <!-- 分页查询轨迹列表 -->
    <select id="selectTrackListByPersonIdsAndTimeRangeWithPage" resultType="com.trs.police.control.domain.entity.warning.WarningTrackEntity">
        SELECT
        id,
        warning_id,
        identifier,
        identifier_type,
        datasource_type,
        source_id,
        activity_time,
        place,
        address,
        longitude,
        latitude,
        photos,
        similarity,
        person_id,
        source_type,
        district,
        monitor_id,
        track_detail
        <include refid="baseTrackListSql"/>
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="selectTrackForCheck"
            resultType="com.trs.police.control.domain.entity.warning.WarningTrackEntity">
        select id from t_warning_track where identifier = #{identifier} and identifier_type = #{identifierType} and source_id = #{sourceId} and activity_time = #{activityTime} and monitor_id = #{monitorId} and datasource_type = #{datasourceType}
    </select>

</mapper>