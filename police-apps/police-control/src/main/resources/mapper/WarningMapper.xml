<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.WarningMapper">

    <resultMap id="warningListVoMap" type="com.trs.police.control.domain.vo.warning.WarningListVO">
        <id column="id" property="id"/>
        <result column="activity_address" property="activityAddress"/>
        <result column="content" property="content"/>
        <result column="handleResult" property="handleResult"/>
        <result column="warningTime" property="warningTime"/>
        <result column="activityTime" property="activityTime"/>
        <result column="controlType" property="controlType"/>
        <result column="monitorId" property="monitorId"/>
        <result column="replyOverdue" property="replyOverdue"/>
        <result column="signOverdue" property="signOverdue"/>
        <result column="sourceType" property="sourceType"/>
        <result column="activityPerson" property="activityPerson" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="model" property="model" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="notifyDept"  property="notifyDept"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="notifyPerson" property="notifyPerson"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <association column="{code=status,type=control_warning_status}" property="status"
            javaType="com.trs.police.common.core.vo.CodeNameVO" select="selectDict"/>
        <association column="{code=warningLevel,type=monitor_level}" property="warningLevel"
            javaType="com.trs.police.common.core.vo.CodeNameVO" select="selectDict"/>
    </resultMap>
    <resultMap id="warningEntity" type="com.trs.police.control.domain.vo.WarningEntityWrapperVO">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_dept_id" property="updateDeptId"/>
        <result column="warning_type" property="warningType"/>
        <result column="warning_level" property="warningLevel"/>
        <result column="content" property="content"/>
        <result column="warning_time" property="warningTime"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="group_id" property="groupId"/>
        <result column="control_type" property="controlType"/>
        <result column="activity_address" property="activityAddress"/>
        <result column="activity_time" property="activityTime"/>
        <result column="hit_subject" property="hitSubject"/>
        <result column="hit_subject_scene" property="hitSubjectScene"/>
        <result column="model_id" property="modelId"
            typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="fwzh" property="fwzh"/>
        <result column="scczfksx" property="scczfksx"/>
        <result column="warning_platform" property="warningPlatform"/>
        <result column="monitor_platform" property="monitorPlatform"
                typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <result column="monitor_person_unit" property="monitorPersonUnit"/>
        <result column="warningStatus" property="warningStatus"/>
        <result column="qssx" property="qssx"/>
        <result column="monitor_level" property="monitorLevel"/>
        <result column="regular_level" property="regularLevel"/>
    </resultMap>

        <insert id="insertIgnoreDuplicate">
            INSERT IGNORE INTO t_warning (
        monitor_id,
        content,
        warning_type,
        warning_level,
        warning_time,
        activity_address,
        create_time,
        update_time,
        group_id,
        control_type,
        fwzh,
        scczfksx,
        warning_platform,
        hit_subject,
        hit_subject_scene,
        model_id,
        person_label,
        area_id,
        place_code,
        ywzjid,
        lkzlbh,
        qssx,
        czcsyq,
        fbzrdw,
        fbzrdwjgdm,
        hdfsddqh,
        hdfsddshcs,
        hdlx,
        monitor_platform,
        monitor_person_unit
    ) VALUES (
            #{warningEntity.monitorId},
            #{warningEntity.content},
            #{warningEntity.warningType},
            #{warningEntity.warningLevel},
            #{warningEntity.warningTime},
            #{warningEntity.activityAddress},
            #{warningEntity.createTime},
            #{warningEntity.updateTime},
            #{warningEntity.groupId},
            #{warningEntity.controlType},
            #{warningEntity.fwzh},
            #{warningEntity.scczfksx},
            #{warningEntity.warningPlatform},
            #{warningEntity.hitSubject},
            #{warningEntity.hitSubjectScene},
            #{warningEntity.modelId, typeHandler=com.trs.police.common.core.handler.typehandler.JsonToLongListHandler},
            #{warningEntity.personLabel},
            #{warningEntity.areaId, typeHandler=com.trs.police.common.core.handler.typehandler.JsonToLongListHandler},
            #{warningEntity.placeCode, typeHandler=com.trs.police.common.core.handler.typehandler.JsonToLongListHandler},
            #{warningEntity.ywzjid},
            #{warningEntity.lkzlbh},
            #{warningEntity.qssx},
            #{warningEntity.czcsyq},
            #{warningEntity.fbzrdw},
            #{warningEntity.fbzrdwjgdm},
            #{warningEntity.hdfsddqh},
            #{warningEntity.hdfsddshcs},
            #{warningEntity.hdlx},
            #{warningEntity.monitorPlatform, typeHandler=com.trs.police.common.core.handler.typehandler.JsonToLongListHandler},
            #{warningEntity.monitorPersonUnit}
            )
        </insert>

    <select id="selectDict" resultType="com.trs.police.common.core.vo.CodeNameVO">
        select name, code
        from t_dict
        where type = #{type}
          and code = #{code}
    </select>
    <select id="searchFullText" resultType="java.lang.Long">
        select w.id
        from t_warning w
                 left join t_warning_track t
                           on w.id = t.warning_id
        where w.content like concat('%', #{searchValue}, '%')
           or t.identifier like concat('%', #{searchValue}, '%')
           or t.identifier in (SELECT p.id_number
                               from t_profile_person p
                               where p.name like concat('%', #{searchValue}, '%'))
    </select>

    <select id="searchWarningInfo" resultType="java.lang.Long">
        select w.id
        from t_warning w
        where w.content like concat('%', #{searchValue}, '%')
    </select>

    <select id="getWarningIdsByModelIds" resultType="java.lang.Long">
        select id
        from t_warning
        where json_contains(JSON_ARRAY
        <foreach collection="modelIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        , model_id)
    </select>

    <select id="selectWarningStatusByWarningIds" resultType="com.trs.police.control.domain.vo.warning.WarningStatusVO">
        select
            warning_id as warningId,
            status as warningStatus
        from t_warning_process
        <where>
            warning_id in
            <foreach collection="warningIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="getWarningByTypeAndTargetId" resultType="com.trs.police.common.core.entity.WarningEntity">
        select *
        from t_warning
        where control_type = #{type}
        and monitor_id = #{id}
        and content like concat('%', #{request.searchParams.searchValue}, '%')
        <foreach collection="request.filterParams" item="param">
            <choose>
                <when test="'warningTime'.equals(param.key)">
                    and warning_time between #{param.value.beginTime} and #{param.value.endTime}
                </when>
            </choose>
        </foreach>
        order by warning_time desc
    </select>
    <select id="selectPageList" resultMap="warningEntity">
        <bind name="userId" value="@com.trs.police.common.core.utils.AuthHelper@getNotNullUser().id"/>
        <bind name="deptId" value="@com.trs.police.common.core.utils.AuthHelper@getNotNullUser().dept.id"/>
        <bind name="filterParams" value="request.filterParams"/>
        <bind name="searchParams" value="request.searchParams"/>
        <bind name="sortParams" value="request.sortParams"/>
        select a.id as id,
        a.create_time as createTime,
        a.update_time as updateTime,
        a.update_user_id as updateUserId,
        a.update_dept_id as updateDeptId,
        a.warning_type,
        a.warning_level,
        a.content,
        a.warning_time,
        a.monitor_id,
        a.model_id,
        a.group_id,
        a.control_type,
        a.activity_address,
        a.activity_time,
        a.hit_subject,
        a.hit_subject_scene,
        a.fwzh,
        a.scczfksx,
        a.warning_platform,
        a.monitor_platform,
        a.monitor_person_unit,
        (select cm.monitor_level from t_warning_track wt left join t_control_monitor cm on cm.id = wt.monitor_id and wt.control_type = 1 where wt.warning_id = a.id limit 1) as monitor_level,
        (select rm.level from t_warning_track wt left join t_control_regular_monitor rm on rm.id = wt.monitor_id and wt.control_type = 2 where wt.warning_id = a.id limit 1) as regular_level
        from t_warning a
        <where>
            <if test="filterParams!=null and filterParams.size>0">
                <foreach collection="filterParams" item="param">
                    <bind name="value" value="param.getProcessedValue()"/>
                    <choose>
                        <when test="param.key == 'monitorLevel'">
                            AND a.id in (
                            select warning_id from t_warning_track wt
                            left join t_control_monitor cm on cm.id = wt.monitor_id and wt.control_type = 1
                            where cm.monitor_level = ${value}
                            )
                        </when>
                        <when test="param.key == 'regularLevel'">
                            AND a.id in (
                            select warning_id from t_warning_track wt
                            left join t_control_regular_monitor rm on rm.id = wt.monitor_id and wt.control_type = 2
                            where rm.level = ${value}
                            )
                        </when>
                        <when test="param.key == 'permissionDept'">
                            AND a.id in  (select n.warning_id from t_warning_notify n where n.warning_id=a.id and n.dept_id in
                            <foreach collection="value" open="(" close=")" separator="," item="dept">
                                #{dept}
                            </foreach>
                            )
                        </when>
                        <when test="'warningId'.equals(param.key)">
                            and a.id in
                            <foreach collection="value.split(',')" open="(" close=")" separator="," item="item">
                                #{item}
                            </foreach>
                        </when>
                        <when test="'excludeSubject'.equals(param.key)">
                            and a.hit_subject is null
                        </when>
                        <when test="param.key == 'currentUser'">
                           and a.id in  (select warning_id from t_warning_notify where warning_id=a.id  and user_id=#{userId} and dept_id=#{deptId})
                        </when>
                        <when test="'warningStatus'.equals(param.key)">
                            and a.id in  (select warning_id from t_warning_process where id= (select process_id from t_warning_notify where warning_id=a.id  and user_id=#{userId} and dept_id=#{deptId}) and status=${value})
                        </when>
                        <when test="'doneStatus'.equals(param.key)">
                             <bind name="exists" value="param.value=='done'?'exists':'not exists'"/>
                            and  ${exists} (select id from t_warning_process where warning_id=a.id and  status=4)
                        </when>
                        <when test="'overdueStatus'.equals(param.key)">
                            <bind name="exists" value="param.value=='overdue'?'exists':'not exists'"/>
                            and  ${exists} (select id from t_warning_process where warning_id=a.id and  (sign_overdue=1 or sign_overdue=1))
                        </when>
                        <when test="'warningStatusList'.equals(param.key)">
                            and a.id in  (select warning_id  from t_warning_process where id= (select process_id from t_warning_notify where warning_id=a.id  and user_id=#{userId} and dept_id=#{deptId}) and status member of('${value}') )
                        </when>
                        <when test="'warningLevel'.equals(param.key)">
                            and a.warning_level = ${value}
                        </when>
                        <!--预警级别多选版本-->
                        <when test="'multiWarningLevel'.equals(param.key)">
                            and a.warning_level in
                            <foreach collection="value" open="(" close=")" separator="," item="dept">
                                #{dept}
                            </foreach>
                        </when>
                        <when test="'controlType'.equals(param.key)">
                            and a.control_type = ${value}
                        </when>
                        <when test="'source'.equals(param.key)">
                            <bind name="type" value="@com.trs.police.control.utils.WarningSourceUtil@splitWarningSource(value)[0]"/>
                            <bind name="id" value="@com.trs.police.control.utils.WarningSourceUtil@splitWarningSource(value)[1]"/>
                          <if test="type.equals('type')">
                              AND exists (SELECT id FROM t_warning_track wt WHERE wt.warning_id=a.id
                              and (select type from t_control_warning_source ws where ws.unique_key=wt.source_id) in (with
                              recursive t1 as(
                              select id,code from t_dict where type='control_warning_source_type' and code=#{id}
                              union all
                              select t2.id,t2.code from t_dict t2 inner join t1 on t2.p_id = t1.id
                              )select code from t1))
                          </if>
                          <if test="type.equals('source')">
                              AND exists(select t.id from t_warning_track t where t.warning_id=a.id and t.datasource_type=#{id})
                          </if>
                        </when>
                        <when test="'category'.equals(param.key)">
                            AND exists (SELECT id FROM t_warning_track wt WHERE wt.warning_id=a.id
                            and JSON_OVERLAPS((select category from t_control_warning_source ws where ws.unique_key=wt.source_id),(with
                            recursive t1 as(
                            select id,code from t_dict where type='control_warning_source_category' and code=${value}
                            union all
                            select t2.id,t2.code from t_dict t2 inner join t1 on t2.p_id = t1.id
                            )select JSON_ARRAYAGG(t1.code) from t1)))
                        </when>
                        <when test="'warningTime'.equals(param.key) and param.getProcessedValue().isAll() == false">
                            AND (a.warning_time between '${value.beginTime}' AND '${value.endTime}')
                        </when>
                        <when test="'onlyUnread'.equals(param.key)">
                            AND a.id in  (select warning_id from t_warning_notify where warning_id=a.id  and user_id=#{userId} and dept_id=#{deptId} and is_read=0)
                        </when>
                        <when test="'monitorType'.equals(param.key)">
                                AND if(a.control_type=1,(select c.monitor_type from t_control_monitor c where c.id = a.monitor_id),1) in (${value})
                        </when>
                        <when test="'handle'.equals(param.key)">
                            AND (select done->'$.isHandle' from t_warning_process where warning_id = a.id and done is
                            not null)=${value}
                        </when>
                        <when test="'handleMeasure'.equals(param.key)">
                            AND (select done->'$.handleMeasure' from t_warning_process where warning_id = a.id and done is
                            not null)=${value}
                        </when>
                        <when test="'activityTime'.equals(param.key) and value.isAll() == false">
                            AND (a.activity_time between '${value.beginTime}' AND '${value.endTime}')
                        </when>
                        <when test="'notifyTarget'.equals(param.key)">
                            <bind name="deptIdList"
                                value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(value)"/>
                            AND
                            JSON_OVERLAPS(#{deptIdList}
                            ,(SELECT JSON_ARRAYAGG(dept_id) from t_warning_notify where warning_id = a.id)
                            ) > 0
                        </when>
                        <when test="'warningModel'.equals(param.key) and value.size() > 0">
                            <bind name="modelIds"
                                value="@com.trs.police.control.domain.builder.WarningListBuilder@getModelIds(value)"/>
                            AND JSON_OVERLAPS((ifnull(a.model_id,'[]')),
                            (select JSON_ARRAYAGG(id) from t_control_monitor_warning_model where id in
                            <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                                ${modelId}
                            </foreach>
                            ))>0
                        </when>
                        <when test="param.key == 'personLabel'">
                            <bind name="personLabel" value="param.getProcessedValue()"/>
                            and
                            <foreach collection="personLabel" item="item" open="(" separator="OR" close=")">
                                (JSON_OVERLAPS(a.person_label
                                ,(SELECT JSON_ARRAYAGG( l.id )
                                FROM t_profile_label l
                                WHERE CONCAT(l.path, l.id, '-')
                                LIKE CONCAT('%-', ${item}, '-%' ))
                                )
                                >0)
                            </foreach>
                        </when>
                        <when test="param.key == 'groupType'">
                            <bind name="groupLabel" value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(param.getProcessedValue())"/>
                            and exists(select * from t_profile_group gr where
                            a.group_id = gr.id and JSON_OVERLAPS(gr.group_label, #{groupLabel}))
                        </when>
                        <when test="param.key == 'areaDistrictName'">
                            <bind name="districtCode" value="param.getProcessedValue()"/>
                            and exists(select * from t_control_important_area area left join t_control_monitor_warning_model m on area.id = m.important_area_id
                            where m.id = a.model_id
                            and area.district_code in
                            <foreach collection="districtCode" item="code" open="(" separator="," close=")">
                                #{code}
                            </foreach>
                            )
                        </when>
                        <when test="param.key == 'warningPlatform'">
                            and a.warning_platform = ${value}
                        </when>
                        <when test="param.key == 'dept'">
                            <bind name="likeCode" value="value + '%'"/>
                            and monitor_unit_code like #{likeCode}
                        </when>
                        <when test="param.key == 'signOverdue'">
                            <bind name="exists" value="param.value=='1'?'exists':'not exists'"/>
                            and ${exists} (select id from t_warning_process where warning_id=a.id and sign_overdue=1)
                        </when>
                        <when test="param.key == 'replyOverdue'">
                            <bind name="exists" value="param.value=='1'?'exists':'not exists'"/>
                            and ${exists} (select id from t_warning_process where warning_id=a.id and reply_overdue=1)
                        </when>
                        <when test="param.key == 'hdlx'">
                            and a.hdlx = #{value}
                        </when>
                        <when test="param.key == 'hdfsqy'">
                            <bind name="likeCode" value="value + '%'"/>
                            and a.hdfsddshcsdm like #{likeCode}
                        </when>
                    </choose>
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                <choose>
                    <when test=" 'targetName'==searchParams.searchField">
                        and EXISTS(select id from t_profile_person where id in (select person_id from t_warning_track
                        where warning_id =a.id) and name like #{pattern} )
                    </when>
                    <when test=" 'idNumber'==searchParams.searchField">
                        and EXISTS(select id from t_profile_person where id in (select person_id from t_warning_track
                        where warning_id =a.id) and id_number like #{pattern} )
                    </when>
                    <when test=" 'address'==searchParams.searchField">
                        and a.activity_address like #{pattern}
                    </when>
                    <when test=" 'content'==searchParams.searchField">
                        and a.content like #{pattern}
                    </when>
                    <when test=" 'fwzh'==searchParams.searchField">
                        and a.fwzh like #{pattern}
                    </when>
                    <otherwise>
                        and (
                        a.activity_address like #{pattern} or a.content like #{pattern}
                        or EXISTS(select id from t_profile_person where id in (select person_id from t_warning_track
                        where warning_id =a.id) and (id_number like #{pattern} or name like #{pattern}))
                        or a.fwzh like #{pattern}
                        )
                    </otherwise>
                </choose>
            </if>
        </where>
        order by
        <if test="sortParams != null">
            <bind name="sortField" value="sortParams.sortField"/>
            <bind name="sortValue" value="sortParams.getProcessedValue()"/>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(sortField) ">
                <if test="sortField == 'warningTime' ">
                    a.warning_time
                </if>
                <if test="sortField == 'activityTime'">
                    a.activity_time
                </if>
                ${sortValue},
            </if>
        </if>
        a.warning_time desc
    </select>

    <select id="countWarningUnread" resultType="java.lang.Long">
        select count(w.id)
        from t_warning w
                 left join t_warning_notify n on w.id = n.warning_id
        where user_id = #{userId}
          and dept_id = #{deptId}
          and n.is_read = false
    </select>
    <select id="countWarningByMonitorIds" resultType="com.trs.police.common.core.vo.IdNameCountVO">
        select
            monitor_id as id,
            count(0) as `count`
        from t_warning
        where control_type = 2
        and monitor_id in
          <foreach collection="monitorIds" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
          and hit_subject is null
        group by monitor_id
    </select>
    <select id="countByRank" resultType="java.lang.Integer">
        select count(1)
        from t_warning a
        left join t_control_monitor cm on cm.id = a.monitor_id
        <where>
            <if test="warningPlatform != null">
                a.warning_platform = #{warningPlatform}
            </if>
            <if test="rankVO.code == 'top' and rankVO.key != null and rankVO.key.size() > 0">
                and cm.monitor_unit_code in
                <foreach collection="rankVO.key" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="rankVO.code != 'top' and rankVO.code != 'all' and rankVO.code != null">
                and cm.monitor_unit_code like concat(#{rankVO.code},'%')
            </if>
            <if test="rankVO.code == 'all' and rankVO.key != null and rankVO.key.size() > 0">
                and cm.monitor_unit_code like
                <foreach collection="rankVO.key" separator="," open="(" close=")" item="item">
                    concat(#{item},'%')
                </foreach>
            </if>
        </where>
    </select>

    <select id="getWarningTrack" resultType="com.alibaba.fastjson.JSONObject">
        select
            w.id,
            w.content,
            w.warning_level,
            w.warning_time,
            w.activity_time,
            w.activity_address,
            t.create_time,
            t.source_id,
            t.identifier,
            t.identifier_type,
            t.longitude,
            t.latitude,
            t.photos,
            t.similarity,
            t.track_detail,
            t.person_id,
            s.name
        from
            t_warning w
                left join t_warning_track t on
                w.id = t.warning_id
                left join t_control_warning_source s on
                t.source_id = s.unique_key
        where
            t.identifier_type = 1
    </select>
    <select id="selectForDuplicate" resultType="com.trs.police.common.core.entity.WarningEntity">
select id from t_warning where
                            monitor_id = #{warningEntity.monitorId}
                          and activity_time = #{warningEntity.activityTime}
and activity_address = #{warningEntity.activityAddress}
    </select>
</mapper>