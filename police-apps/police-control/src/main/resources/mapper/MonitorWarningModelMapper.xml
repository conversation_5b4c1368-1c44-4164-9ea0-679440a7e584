<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.control.mapper.MonitorWarningModelMapper">

    <resultMap id="WarningModelVoMap" type="com.trs.police.common.core.dto.WarningModelVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="name"/>
        <result column="icon_url" jdbcType="VARCHAR" property="iconUrl"/>
        <result column="detail" property="detail"/>
        <result column="type" property="type"/>
        <result column="important_area_id" property="areaId"/>
        <result column="place_code" property="placeCode"/>
        <result column="district_name" property="areaDistrictName"/>
        <result column="category_name" property="areaCategoryName"/>
    </resultMap>

    <resultMap id="warningModelVOMap" type="com.trs.police.common.core.dto.WarningModelVO">
        <id column="id" property="id"/>
        <result column="title" property="name"/>
        <result column="detail" property="detail"/>
        <result column="icon_url" property="iconUrl"/>
        <result column="person_count" property="personCount"/>
        <collection property="aggregationArea"
            ofType="com.trs.police.common.core.vo.IdNameVO"
            column="{ids = aggregationArea}"
            select="com.trs.police.control.mapper.ImportantAreaMapper.getByStingIds"/>
    </resultMap>

    <resultMap id="IdNameTreeVOMap" type="com.trs.police.common.core.vo.IdNameVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="name"/>
    </resultMap>

    <select id="getVoByType" resultMap="WarningModelVoMap">
        select w.*,
               (select d.name
                from t_dict d
                where d.type = 'control_warning_source_category'
                  and d.code = a.category) as category_name,
               a.district_name
        from t_control_monitor_warning_model w
                 left join t_control_important_area a on w.important_area_id = a.id
        where w.type = #{type}
          and w.enable_status = 1
    </select>

    <select id="getByType" resultMap="IdNameTreeVOMap">
        select w.id,
               w.title
        from t_control_monitor_warning_model w
        where type = #{type}
          and enable_status = 1
    </select>

    <select id="queryImportantAreaWarningModel" resultMap="WarningModelVoMap">
        <include refid="queryAreaWarningModel"/>
    </select>
    <select id="queryImportantAreaWarningModelPage" resultMap="WarningModelVoMap">
        <include refid="queryAreaWarningModel"/>
    </select>
    <sql id="queryAreaWarningModel">
        select w.*,
        (select d.name
        from t_dict d
        where d.type = 'control_warning_source_category'
        and d.code = a.category) as category_name,
        a.district_name
        from t_control_monitor_warning_model w
        join t_control_important_area a on w.important_area_id = a.id
        <where>
            w.enable_status = 1 and a.status = 5
            <foreach collection="request.filterParams" item="param">
                <if test="param.value != null and param.value != '' ">
                    <choose>
                        <when test="param.key == 'category' ">
                            and a.category = #{param.value}
                        </when>
                        <when test="param.key == 'districtCode'">
                            and a.district_code = #{param.value}
                        </when>
                        <when test="param.key == 'importAreaTag'">
                            and a.tag = #{param.value}
                        </when>
                    </choose>
                </if>
            </foreach>
            <if test="request != null and request.searchParams != null and @org.apache.commons.lang3.StringUtils@isNotBlank(request.searchParams.searchValue)">
                <bind name="pattern" value="'%' + request.searchParams.searchValue.trim() + '%'"/>
                and w.title like #{pattern}
            </if>
        </where>
    </sql>


    <select id="getIdsByType" resultType="java.lang.Long">
        select id from t_control_monitor_warning_model
        where type in
        <foreach collection="types" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
    </select>

    <select id="selectModelIdsByAreaId" resultType="java.lang.Long">
        select distinct id
        from t_control_monitor_warning_model
        <where>
            enable_status = 1
            and important_area_id in
            <foreach collection="areaIds" item="areaId" open="(" close=")" separator=",">
                #{areaId}
            </foreach>
        </where>
    </select>

    <select id="selectModelIdsByPlaceCodes" resultType="java.lang.Long">
        select distinct id
        from t_control_monitor_warning_model
        <where>
            enable_status = 1
            and place_code in
            <foreach collection="placeCodes" item="placeCode" open="(" close=")" separator=",">
                #{placeCode}
            </foreach>
        </where>
    </select>

    <select id="getByMonitorId" resultMap="warningModelVOMap">
        select m.id                                                                     as id,
               m.title                                                                  as title,
               m.detail                                                                 as detail,
               m.icon_url                                                               as icon_url,
               r.person_count                                                           as person_count,
               r.time_count                                                             as time_count,
               REPLACE(REPLACE(IFNULL(r.aggregation_area, '[-1]'), '[', '('), ']', ')') as aggregationArea
        from t_control_monitor_warning_model_relation r
                 join t_control_monitor_warning_model m on m.id = r.warning_model_id
        where r.monitor_id = #{monitorId}
    </select>
    <select id="getByModelIds" resultMap="WarningModelVoMap">
        select w.*,
        (select d.name from t_dict d where d.type = 'control_warning_source_category' and d.code = a.category) as
        category_name,
        a.district_name
        from t_control_monitor_warning_model w
        left join t_control_important_area a on w.important_area_id = a.id
        <where>
            w.id in
            <foreach collection="ids" item="model" open="(" close=")" separator=",">
                #{model}
            </foreach>
            and w.enable_status = 1
        </where>
        order by w.important_area_id desc,w.place_code desc
    </select>
    <select id="getModelList" resultType="com.trs.police.control.domain.vo.backstage.BackstageModelListVO">
        select m.id,
        m.title,
        m.enable_status as enableStatus,
        (WITH recursive t1 AS (
        SELECT id,p_id,1 as level,name FROM t_dict WHERE type = 'control_model_type' AND CODE = m.type
        UNION ALL SELECT t2.id ,t2.p_id, (t1.level+1) as level,t2.name FROM t_dict t2 INNER JOIN t1 ON t2.id = t1.p_id
        where type = 'control_model_type'
        ) SELECT group_concat(t1.name order by level desc SEPARATOR '/') FROM t1 ) as typeName,
        m.type
        from t_control_monitor_warning_model m
        <where>
            m.delete_status = 0
            <if test="filterParams != null and filterParams.size() > 0">
                <foreach collection="filterParams" item="param">
                    <if test="param.value != null ">
                        <choose>
                            <when test="param.key == 'isEnable' ">
                                and m.enable_status = #{param.value}
                            </when>
                            <when test="param.key == 'warningModel' ">
                                <bind name="value" value="param.getProcessedValue()"/>
                                <bind name="modelIds"
                                    value="@com.trs.police.control.domain.builder.WarningListBuilder@getModelIds(value)"/>
                                AND m.id in
                                <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                                    ${modelId}
                                </foreach>
                            </when>
                        </choose>
                    </if>
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                <choose>
                    <when test=" 'title'==searchParams.searchField">
                        and m.title like #{pattern}
                    </when>
                    <when test=" 'detail'==searchParams.searchField">
                        and m.detail like #{pattern}
                    </when>
                    <otherwise>
                        and (m.title like #{pattern} or m.detail like #{pattern})
                    </otherwise>
                </choose>
            </if>
        </where>
        order by m.id desc
    </select>
    <select id="getRecommendModel" resultMap="WarningModelVoMap">
        select * from t_control_monitor_warning_model
        <where>
            <foreach collection="params" item="request" open="(" separator="OR" close=")">
                <!--  标签和地区只有不为null才需要判断-->
                ( (recommend_label is null and recommend_district is not null) or (JSON_LENGTH(recommend_label)=0 or(
                <choose>
                    <when test="request.label ==null or request.label.size()==0">
                        true
                    </when>
                    <otherwise>
                        <foreach collection="request.label" item="item" open="(" separator="OR" close=")">
                            JSON_OVERLAPS(recommend_label
                            ,(select JSON_ARRAYAGG(t1.id)
                            from t_profile_label t1
                            where (select concat(t2.path, t2.id, '-') from t_profile_label t2 where t2.id=${item})
                            like CONCAT('%-', t1.id, '-%')
                            ))>0
                        </foreach>
                    </otherwise>
                </choose>
                )))

                and ((recommend_district is null and recommend_label is not null) or (JSON_LENGTH(recommend_district)=0
                or (
                JSON_OVERLAPS(recommend_district,
                (select JSON_ARRAYAGG(t1.code)
                from t_district t1
                where (select concat(t2.path, t2.code, '-') from t_district t2 where
                t2.code= SUBSTRING(#{request.idNumber},1,6))
                like CONCAT('%-', t1.code, '-%')
                ))>0
                )))
            </foreach>
            and enable_status = 1
        </where>
    </select>

    <update id="addModelCallCount">
        UPDATE t_control_monitor_warning_model set clue_num = (clue_num +1) where id = #{modelId};
    </update>

</mapper>