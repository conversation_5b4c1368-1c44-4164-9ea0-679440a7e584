<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.WarningFeedbackMapper">

    <resultMap id="feedbackListVoMap" type="com.trs.police.control.domain.vo.warning.WarningFeedbackListVo">
        <id column="id" property="id"/>
        <result column="feedback" property="feedback"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="create_time" property="time"/>
        <result column="type" property="type"
            typeHandler="com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler"/>
        <association property="userInfo"
            column="{userId=create_user_id,deptId=create_dept_id}"
            javaType="com.trs.police.common.core.vo.permission.SimpleUserVO"
            select="getSimpleUser"/>
    </resultMap>
    <select id="getSimpleUser" resultType="com.trs.police.common.core.vo.permission.SimpleUserVO">
        SELECT u.id                        as userId,
               u.real_name                 as userName,
               d.id                        as deptId,
               d.name                      as deptName,
               d.short_name                as deptShortName,
               d.code                      as deptCode,
               u.mobile                    as tel,
               u.duty                      as duty
        FROM t_user u,
             t_dept d
        where u.id = #{userId}
          and d.id = #{deptId}
    </select>

    <select id="getFeedbackListByWarningId" resultMap="feedbackListVoMap">

        select id,
               feedback,
               type,
               create_user_id,
               create_dept_id,
               create_time
        from t_warning_feedback
        where warning_id = #{id}
        order by create_time desc, id desc
    </select>
</mapper>