<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.yq.CkryMapper">

    <select id="selectYqCkryList" resultType="com.trs.police.control.domain.entity.yq.Ckry">
        select gmsfhm as gmsfhm,
        xm as xm,
        hjdxz as hjdxz,
        xb_dm as xb,
        mz as mz,
        xzxxdz as xzxxdz
        from t_alarm_ckry yqp
        <where>
            <if test="ckryId != null and ckryId != ''">
                and zdrybh = #{ckryId}
            </if>
            <if test="startTime != null and startTime != ''">
                and jlxzsj >= TO_DATE(#{startTime},'yyyy-MM-dd HH24:MI:SS')
            </if>
            <if test="endTime != null and endTime != ''">
                and jlxzsj &lt;= TO_DATE(#{endTime},'yyyy-MM-dd HH24:MI:SS')
            </if>
        </where>
        order by jlxzsj
    </select>
</mapper>