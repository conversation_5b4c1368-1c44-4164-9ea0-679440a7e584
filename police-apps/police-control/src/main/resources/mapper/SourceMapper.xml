<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.SourceMapper">
    <resultMap id="sourceBasicVoMapper" type="com.trs.police.control.domain.vo.basic.SourceBasicVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <id column="category"  property="category"/>
        <id column="type" jdbcType="VARCHAR" property="type"/>
        <id column="district" jdbcType="VARCHAR" property="district"/>
        <id column="address" jdbcType="VARCHAR" property="address"/>
        <id column="todayDataAmount" jdbcType="BIGINT" property="todayDataAmount"/>
        <id column="totalDataAmount" jdbcType="BIGINT" property="totalDataAmount"/>
        <id column="updateTime" javaType="java.time.LocalDateTime" jdbcType="TIMESTAMP" property="updateTime"/>
        <id column="is_legal" jdbcType="BIGINT" property="isLegal"/>
        <id column="top_type" jdbcType="VARCHAR" property="topType"/>
        <result column="point" jdbcType="VARCHAR" property="point"/>
        <result column="nesting_category" property="nestingCategory" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <collection property="dept"
            ofType="com.trs.police.common.core.vo.IdNameVO"
            resultMap="IdNameVO"/>
    </resultMap>
    <resultMap id="IdNameVO" type="com.trs.police.common.core.vo.IdNameVO">
        <id column="dept_id" jdbcType="BIGINT" property="id"/>
        <result column="dept_name" jdbcType="VARCHAR" property="name"/>
    </resultMap>

    <resultMap id="sourceEntityMap" type="com.trs.police.control.domain.entity.basic.SourceEntity">
        <id column="id" javaType="java.lang.Long" jdbcType="BIGINT" property="id"/>
        <id column="create_user_id" javaType="java.lang.Long" jdbcType="BIGINT" property="createUserId"/>
        <id column="create_dept_id" javaType="java.lang.Long" jdbcType="BIGINT" property="createDeptId"/>
        <id column="create_time" javaType="java.time.LocalDateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <id column="update_user_id" javaType="java.lang.Long" jdbcType="BIGINT" property="updateUserId"/>
        <id column="update_dept_id" javaType="java.lang.Long" jdbcType="BIGINT" property="updateDeptId"/>
        <id column="update_time" javaType="java.time.LocalDateTime" jdbcType="TIMESTAMP" property="updateTime"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <id column="category"  property="category" typeHandler="com.trs.police.common.core.handler.typehandler.JsonToLongListHandler"/>
        <id column="type" jdbcType="VARCHAR" property="type"/>
        <id column="district_name" jdbcType="VARCHAR" property="districtName"/>
        <id column="district_code" jdbcType="VARCHAR" property="districtCode"/>
        <id column="dept" jdbcType="BIGINT" property="dept"/>
        <id column="address" jdbcType="VARCHAR" property="address"/>
        <id column="wkt" jdbcType="VARCHAR" property="point"/>
        <id column="unique_key" jdbcType="VARCHAR" property="uniqueKey"/>
        <id column="is_legal" jdbcType="BIGINT" property="isLegal"/>
    </resultMap>

    <resultMap id="sourceListVoMapper" type="com.trs.police.control.domain.vo.basic.SourceListVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <id column="category" jdbcType="VARCHAR" property="category"/>
        <id column="categoryCode" jdbcType="BIGINT" property="categoryCode"/>
        <id column="type" jdbcType="VARCHAR" property="type"/>
        <id column="dept" jdbcType="VARCHAR" property="dept"/>
        <id column="address" jdbcType="VARCHAR" property="address"/>
        <id column="wkt" jdbcType="VARCHAR" property="point"/>
        <id column="is_legal" jdbcType="BIGINT" property="isLegal"/>
        <id column="top_type" jdbcType="VARCHAR" property="topType"/>
    </resultMap>

    <resultMap id="sourceListVo2Mapper" type="com.trs.police.common.core.vo.control.SourceListVO2">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <id column="category" jdbcType="VARCHAR" property="category"/>
        <id column="categoryCode" jdbcType="BIGINT" property="categoryCode"/>
        <id column="type" jdbcType="VARCHAR" property="type"/>
        <id column="dept" jdbcType="VARCHAR" property="dept"/>
        <id column="deptId" jdbcType="INTEGER" property="deptId"/>
        <id column="address" jdbcType="VARCHAR" property="address"/>
        <id column="wkt" jdbcType="VARCHAR" property="point"/>
        <id column="is_legal" jdbcType="BIGINT" property="isLegal"/>
        <id column="top_type" jdbcType="VARCHAR" property="topType"/>
    </resultMap>

    <sql id="sourceVoCommonField">
        a.id as id,
        a.name as name,
        a.code as code,
        (select group_concat(d.name)
        from t_dict d
        where d.type = 'control_warning_source_category'
        and d.code member of (a.category)) as category,
        (select d.name
        from t_dict d
        where d.type = 'control_warning_source_type'
        and d.code = a.type) as type,
        a.type as categoryCode,
        a.district_name as district,
        (select d.name from t_district d where d.code = SUBSTR(a.district_code, 1, 6)) as dept,
        a.dept as deptId,
        a.address as address,
        if(st_srid(a.point) = 0,ST_AsText(a.point),ST_AsText(ST_SwapXY(a.point))) as wkt,
        a.is_legal as is_legal,
        (WITH recursive t1 AS (
        SELECT id,p_id,1 as level,name 	FROM	t_dict 	WHERE	type = 'control_warning_source_type' 	AND CODE = a.type
        UNION ALL	SELECT t2.id ,t2.p_id, (t1.level+1) as level,t2.name FROM t_dict t2 INNER JOIN t1 ON t2.id = t1.p_id  where  	type = 'control_warning_source_type'
        ) SELECT name FROM t1  ORDER BY  level desc limit 1) as top_type,
        a.unique_key as uniqueKey
    </sql>

    <sql id="pageListFilter">
        <foreach collection="filterParams" item="param">
            <choose>
                <when test="param.key == 'districtCode'">
                    <bind name="districtList" value="param.getProcessedValue()"/>
                    <if test="districtList != null and districtList.size() > 0">
                        and a.district_code in (SELECT d.code
                        FROM t_district d
                        WHERE
                        <foreach collection="districtList" item="item" separator="OR">
                            <bind name="prefixCode" value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item) + '%'"/>
                            d.code LIKE #{prefixCode}
                        </foreach>)
                    </if>
                </when>
                <when test="param.key == 'createDeptCodes'">
                    <bind name="districtList" value="param.getProcessedValue()"/>
                    <if test="districtList != null and districtList.size() > 0">
                        AND(
                        <foreach collection="districtList" item="item" separator="OR">
                            a.district_code like
                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}%'
                        </foreach>)
                    </if>
                </when>
                <when test="param.key == 'deptCode'">
                    <bind name="deptCodeList"
                        value="param.getProcessedValue()"/>
                    <if test="deptCodeList != null and deptCodeList.size() > 0">
                        and a.dept  in (SELECT d.id
                        FROM t_dept d
                        WHERE
                        <foreach collection="deptCodeList" item="item" separator="OR">
                            <bind name="prefixCode" value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item) + '%'"/>
                            d.code LIKE #{prefixCode}
                        </foreach>)
                    </if>
                </when>
                <when test="param.key == 'type'">
                    <bind name="typeList"
                        value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(param.getProcessedValue())"/>
                    AND a.type in (with recursive t1 as(
                    select id,code from t_dict where type='control_warning_source_type' and code MEMBER OF(#{typeList})
                    union all
                    select t2.id,t2.code from t_dict t2 inner join t1 on t2.p_id = t1.id
                    )select code from t1)
                </when>
                <when test="param.key == 'category'">
                    <bind name="categoryList"
                        value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(param.getProcessedValue())"/>
                    AND JSON_OVERLAPS(a.category,  (with recursive t1 as(
                    select id,code from t_dict where type='control_warning_source_category' and code MEMBER OF(#{categoryList})
                    union all
                    select t2.id,t2.code from t_dict t2 inner join t1 on t2.p_id = t1.id
                    )select JSON_ARRAYAGG(code) from t1))
                </when>
                <when test="param.key=='area'">
                    and ST_CONTAINS(ST_SRID(ST_PolygonFromText(#{param.value}), ST_SRID(a.point)), a.point)
                </when>
                <when test="param.key=='havePoint'">
                    <choose>
                        <when test="param.value == '0'">
                            and (a.point = '' or a.point is null)
                        </when>
                        <otherwise>
                            and (a.point != '' and a.point is not null and st_x(a.point) > 1 and st_y(a.point) > 1)
                        </otherwise>
                    </choose>
                </when>
                <when test="param.key=='hasPointStatus'">
                    <choose>
                        <when test="param.value == 'notExist'">
                            and (a.point = '' or a.point is null or st_x(a.point) <![CDATA[ <= ]]> 1 or st_y(a.point) <![CDATA[ <= ]]> 1)
                        </when>
                        <when test="param.value == 'exist'">
                            and (a.point != '' and a.point is not null and st_x(a.point) > 1 and st_y(a.point) > 1)
                        </when>
                    </choose>
                </when>
            </choose>
        </foreach>
        <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
            <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
            AND ( a.name like #{pattern,jdbcType=VARCHAR} or a.code like #{pattern,jdbcType=VARCHAR} or a.address like
            #{pattern,jdbcType=VARCHAR})
        </if>
    </sql>

    <sql id="withGeometry">
        <if test="null != geometry ">
            <if test="'CIRCLE'.equals(geometry.type) or 'circle'.equals(geometry.type)">
                <bind name="radius" value="geometry.properties.radius"/>
                <choose>
                    <when test="'POLYGON'.equals(geometry.type) or 'polygon'.equals(geometry.type)">
                        and ST_CONTAINS(ST_SRID(ST_PolygonFromText(#{geometry.geometry}), ST_SRID(a.point)), a.point)
                    </when>
                    <when test="'CIRCLE'.equals(geometry.type) or 'circle'.equals(geometry.type)">
                        <bind name="radius" value="geometry.properties.radius"/>
                        and ST_Distance(ST_SRID(ST_GeomFromText(#{geometry.geometry}), ST_SRID(a.point)), a.point, 'metre') &lt;
                        #{radius,jdbcType=DOUBLE}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </if>
    </sql>

    <insert id="synchronizeSource">
        <foreach collection="sourceDtos" item="source">
            insert ignore into t_control_warning_source
            (unique_key, name, code, type, district_name, district_code, address, point, is_legal,update_time,create_time, is_local)
            select #{source.uniqueKey},
            #{source.name},
            #{source.code},
            (select code from t_dict where type='control_warning_source_type' and dict_desc = #{source.typeCode}),
            (select name from t_district where code = #{source.districtCode}),
            #{source.districtCode},
            #{source.address},
            st_pointfromtext(#{source.point}, 4326),
            #{source.isLegal},
            CURRENT_TIMESTAMP(),
            CURRENT_TIMESTAMP(),
            #{source.isLocal}
            from dual;
        </foreach>
    </insert>
    <insert id="insertHkCamera">
        INSERT IGNORE INTO hk_cameras
        (name,camera_index_code,place,longitude,latitude,status,status_name,point)
        VALUES
        (
        #{vo.name,jdbcType=VARCHAR},
        #{vo.cameraIndexCode,jdbcType=VARCHAR},
        #{vo.installPlace,jdbcType=VARCHAR},
        CAST(#{vo.longitude} AS DOUBLE),
        CAST(#{vo.latitude} AS DOUBLE),
        #{vo.status,jdbcType=VARCHAR},
        #{vo.statusName,jdbcType=VARCHAR},
        ST_SRID(ST_PointFromText(concat('POINT(', #{vo.longitude}, ',', #{vo.latitude}, ')'), 4326))
        )
    </insert>

    <update id="updateSourceById" parameterType="com.trs.police.control.domain.entity.basic.SourceEntity">
        <bind name="categoryIds" value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(category)"/>
        <bind name="nestCategoryIds" value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(nestingCategory)"/>
        UPDATE t_control_warning_source
        SET dept           = #{dept,jdbcType=BIGINT},
            category       = #{categoryIds},
            nesting_category  = #{nestCategoryIds},
            address        = #{address,jdbcType=VARCHAR},
            point          =(ST_SRID(ST_PointFromText(#{point,jdbcType=VARCHAR}), 4326)),
            update_user_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id},
            update_dept_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().dept.id},
            update_time= CURRENT_TIMESTAMP(),
            district_code=(SELECT code
                           from t_district
                           where LEVEL = 3
                             and ST_CONTAINS(contour, ST_SRID(ST_PointFromText(#{point,jdbcType=VARCHAR}), 4326))
            limit 1),
            district_name=(
        SELECT name
        from t_district
        where LEVEL = 3
          and ST_CONTAINS(contour
            , ST_SRID(ST_PointFromText(#{point,jdbcType=VARCHAR})
            , 4326))
            limit 1)
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateSourceByIdWithOutUserInfo" parameterType="com.trs.police.control.domain.entity.basic.SourceEntity">
        UPDATE t_control_warning_source
        SET dept           = #{dept,jdbcType=BIGINT},
        address        = #{address,jdbcType=VARCHAR},
        point          = st_pointfromtext(#{point,jdbcType=VARCHAR}, 4326),
        update_time= CURRENT_TIMESTAMP()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdate" parameterType="com.trs.police.control.domain.vo.basic.SourceBatchRequestVO">
        <bind name="categoryIds" value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(category)"/>
        <bind name="nestCategoryIds" value="@com.trs.police.common.core.utils.JsonUtil@toJsonString(nestingCategory)"/>
        UPDATE t_control_warning_source
        SET dept = #{dept,jdbcType=BIGINT},
        category       = #{categoryIds},
        nesting_category  = #{nestCategoryIds},
        update_user_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id},
        update_dept_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().dept.id},
        update_time= CURRENT_TIMESTAMP()
        <where>
            <choose>
                <when test="updateType == 'checked'">
                    and id in
                    <foreach item="item" collection="sourceIds" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="updateType == 'all'">
                    <bind name="searchParams" value="listParamsRequest.searchParams"/>
                    <bind name="filterParams" value="listParamsRequest.filterParams"/>
                    <include refid="pageListFilter">
                    </include>
                </when>
            </choose>
        </where>
    </update>
    <select id="getSourceBasicInfo" resultMap="sourceBasicVoMapper">
        select t1.id                          as id,
               t1.name                        as name,
               t1.code                        as code,
               (select group_concat(d.name)
                from t_dict d
                where d.type = 'control_warning_source_category'
                  and d.code member of(ifnull(t1.category,'[]')))       as category,
                ifnull(t1.nesting_category,'[]')           as nesting_category,
               (select d.name
                from t_dict d
                where d.type = 'control_warning_source_type'
                  and d.code = t1.type)       as type,
               t1.district_name               as district,
               t2.id                          as dept_id,
               t2.short_name                  as dept_name,
               t1.address                     as address,
               t1.update_time                 as updateTime,
               if(st_srid(t1.point) = 0,ST_AsText(t1.point),ST_AsText(ST_SwapXY(t1.point))) as point,
               t1.is_legal                    as is_legal,
               (WITH recursive t1 AS (
                   SELECT id,p_id,1 as level,name 	FROM	t_dict 	WHERE	type = 'control_warning_source_type' 	AND CODE = t1.type
                   UNION ALL	SELECT t2.id ,t2.p_id, (t1.level+1) as level,t2.name FROM t_dict t2 INNER JOIN t1 ON t2.id = t1.p_id  where  	type = 'control_warning_source_type'
               ) SELECT name FROM t1  ORDER BY  level desc limit 1) as top_type
        from t_control_warning_source t1
                 left join t_dept t2
                           on t1.dept = t2.id
        where t1.id = #{id,jdbcType=BIGINT}
    </select>


    <select id="selectCount" resultType="java.lang.Long">
        select count(1) from t_control_warning_source a
        <where>
            1=1
            <if test="isLocalSource == 1">
                and a.is_local = 1
            </if>
            <include refid="getSourcePointInAreaWhere"/>
        </where>
    </select>

    <select id="selectById" resultMap="sourceEntityMap">
        select a.*, if(st_srid(a.point) = 0,ST_AsText(a.point),ST_AsText(ST_SwapXY(a.point))) as wkt
        from t_control_warning_source a
        where id = #{id}
    </select>

    <sql id="getSourceInAreaWhere">
        <choose>
            <when test="'POLYGON'.equals(geometry.type) or 'polygon'.equals(geometry.type)">
                and ST_CONTAINS(ST_SRID(ST_PolygonFromText(#{geometry.geometry}), ST_SRID(a.point)), a.point)
            </when>
            <when test="'CIRCLE'.equals(geometry.type) or 'circle'.equals(geometry.type)">
                <bind name="radius" value="geometry.properties.radius"/>
                and ST_Distance(ST_SRID(ST_GeomFromText(#{geometry.geometry}), ST_SRID(a.point)), a.point, 'metre') &lt;
                #{radius,jdbcType=DOUBLE}
            </when>
            <otherwise>
                <!--@ignoreSql-->
            </otherwise>
        </choose>
    </sql>
    <select id="getSourceInAreaCount" resultType="java.lang.Long">
        select count(1) from t_control_warning_source a
        <where>
            1=1
            <if test="isLocalSource == 1">
                and a.is_local = 1
            </if>
            <include refid="getSourceInAreaWhere"></include>
        </where>
    </select>

    <select id="getSourceInArea" resultMap="sourceListVoMapper"
        parameterType="com.trs.police.common.core.vo.GeometryVO">
        select a.id as id,
        a.name as name,
        a.code as code,
        (select group_concat(d.name)
        from t_dict d
        where d.type = 'control_warning_source_category'
        and d.code member of (a.category)) as category,
        (WITH recursive t1 AS (
        SELECT id,p_id,1 as level,name 	FROM t_dict WHERE type = 'control_warning_source_type' AND CODE = a.type
        UNION ALL
        SELECT t2.id, t2.p_id, (t1.level+1) as level, t2.name FROM t_dict t2 INNER JOIN t1 ON t2.id = t1.p_id where type = 'control_warning_source_type'
        ) SELECT name FROM t1 ORDER BY level desc limit 1) as top_type,
        a.district_name as district,
        (select d.name
        from t_dept d
        where d.id = a.dept) as dept,
        a.address as address,
        if(st_srid(a.point) = 0,ST_AsText(a.point),ST_AsText(ST_SwapXY(a.point))) as wkt,
        a.is_legal as is_legal,
        (SELECT name FROM t_dict WHERE type = 'control_warning_source_type' AND CODE = a.type) as type
        from t_control_warning_source a
        <where>
            <if test="isLocalSource == 1">
                and a.is_local = 1
            </if>
            <include refid="getSourceInAreaWhere"/>
        </where>
    </select>

    <sql id="getSourcePointInAreaWhere">
        <if test="null != geometry ">
        <choose>
            <when test="'POLYGON'.equals(geometry.type) or 'polygon'.equals(geometry.type)">
                and ST_CONTAINS(ST_SRID(ST_PolygonFromText(#{geometry.geometry}), ST_SRID(a.point)), a.point)
            </when>
            <when test="'CIRCLE'.equals(geometry.type) or 'circle'.equals(geometry.type)">
                <bind name="radius" value="geometry.properties.radius"/>
                and ST_Distance(ST_SRID(ST_GeomFromText(#{geometry.geometry}), ST_SRID(a.point)), a.point, 'metre') &lt;
                #{radius,jdbcType=DOUBLE}
            </when>
            <otherwise>
                <!--@ignoreSql-->
            </otherwise>
        </choose>
        </if>
        <include refid="pageListFilter"/>
        <include refid="withGeometry"/>
    </sql>

    <select id="getSourcePointInAreaForCount" resultType="java.lang.Long">
        select count(1) from t_control_warning_source a
        <where>
            <include refid="getSourcePointInAreaWhere"></include>
        </where>
    </select>

    <select id="getSourcePointInArea" resultMap="sourceListVoMapper">
        select
            <include refid="sourceVoCommonField"/>
        from t_control_warning_source a
        <where>
            1=1
            <if test="isLocalSource == 1">
                and a.is_local = 1
            </if>
            <include refid="getSourcePointInAreaWhere"/>
        </where>
    </select>

    <select id="selectListByParams" resultMap="sourceListVoMapper">
        select
            <include refid="sourceVoCommonField"/>
        from t_control_warning_source a
        <where>
            1=1
            <if test="isLocalSource == 1">
                and a.is_local = 1
            </if>
            <include refid="getSourcePointInAreaWhere"/>
        </where>
    </select>

    <select id="selectPageListByParams" resultMap="sourceListVoMapper">
        select
            <include refid="sourceVoCommonField"/>
        from t_control_warning_source a
        <where>
            1=1
            <if test="isLocalSource == 1">
                and a.is_local = 1
            </if>
            <include refid="getSourcePointInAreaWhere"/>
        </where>
        order by update_time desc
    </select>

    <select id="selectByExportRequest" resultMap="sourceListVoMapper"
        parameterType="com.trs.police.control.domain.vo.basic.SourceExportRequestVO">
        select a.id as id,
        a.name as name,
        a.code as code,
        (select group_concat(d.name)
        from t_dict d
        where d.type = 'control_warning_source_category'
        and d.code member of (a.category)) as category,
        (select d.name
        from t_dict d
        where d.type = 'control_warning_source_type'
        and d.code = a.type) as type,
        a.district_name as district,
        (select d.name
        from t_dept d
        where d.id = a.dept) as dept,
        a.address as address,
        a.is_legal as is_legal,
        (WITH recursive t1 AS (
        SELECT id,p_id,1 as level,name 	FROM	t_dict 	WHERE	type = 'control_warning_source_type' 	AND CODE = a.type
        UNION ALL	SELECT t2.id ,t2.p_id, (t1.level+1) as level,t2.name FROM t_dict t2 INNER JOIN t1 ON t2.id = t1.p_id  where  	type = 'control_warning_source_type'
        ) SELECT name FROM t1  ORDER BY  level desc limit 1) as top_type
        from t_control_warning_source a
        <where>
            <choose>
                <when test="exportType == 'checked'">
                    and id in
                    <foreach item="item" collection="sourceIds" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="exportType == 'all'">
                    <bind name="searchParams" value="listParamsRequest.searchParams"/>
                    <bind name="filterParams" value="listParamsRequest.filterParams"/>
                    <include refid="pageListFilter"/>
                </when>
            </choose>
        </where>
    </select>

    <select id="selectCameraList" resultType="com.trs.police.control.domain.vo.CameraVO">
        select a.id                          as id,
               a.name                        as name,
               a.camera_index_code           as code,
               <!-- mysql中srid=0时X轴为经度，Y轴为纬度-->
               <!-- mysql中srid=4326时X轴为纬度，Y轴为经度-->
               <!-- 所以为了兼容就需要判断-->
               if(st_srid(a.point) = 0,ST_AsText(a.point),ST_AsText(ST_SwapXY(a.point))) as point,
               '视频监控'                      as type,
               '泸州市' as dept,
               a.name as address,
               a.status as isOnline
        from hk_cameras a
        <where>
            <choose>
                <when test="'POLYGON'.equals(geometry.type) or 'polygon'.equals(geometry.type)">
                    and ST_CONTAINS(ST_SRID(ST_PolygonFromText(#{geometry.geometry}), st_srid(a.point)), a.point)
                </when>
                <when test="'CIRCLE'.equals(geometry.type) or 'circle'.equals(geometry.type)">
                    <bind name="radius" value="geometry.properties.radius"/>
                    and ST_Distance(ST_SRID(ST_GeomFromText(#{geometry.geometry}), st_srid(a.point)), a.point, 'metre') &lt;
                    #{radius,jdbcType=DOUBLE}
                </when>
            </choose>
        </where>
    </select>

    <select id="selectCamera" resultType="com.trs.police.control.domain.vo.CameraVO">
        select a.id                          as id,
               a.name                        as name,
               a.camera_index_code           as code,
               if(st_srid(a.point) = 0,ST_AsText(a.point),ST_AsText(ST_SwapXY(a.point))) as point,
               '视频监控'                      as type,
               '泸州市' as dept,
               a.name as address,
               a.status as isOnline
        from hk_cameras a
        where id= #{id}
    </select>
    <select id="selectDhCamera" resultType="com.trs.police.control.domain.vo.CameraVO">
        select
            a.id as id,
            a.name as name,
            a.code as code,
            if(st_srid(a.point) = 0,
               ST_AsText(a.point),
               ST_AsText(ST_SwapXY(a.point))) as point,
            '视频监控' as type,
            '自贡市' as dept,
            a.name as address,
            a.is_legal as isOnline
        from
            t_control_warning_source a
        where id= #{id}
    </select>

    <select id="selectBatchByUniqueKey" resultType="com.trs.police.control.domain.entity.basic.SourceEntity">
        select * from t_control_warning_source where unique_key in
        <foreach item="item" collection="uniqueKeys" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="synchronizeSourceV2" resultType="java.lang.Integer">
        <foreach collection="sourceDtos" item="source">
            insert ignore into t_control_warning_source
            (unique_key, name, code, type, district_name, district_code, address, point, is_legal,update_time,create_time,source_provider,dept)
            select #{source.uniqueKey},
            #{source.name},
            #{source.code},
            2,
            #{source.districtName},
            #{source.districtCode},
            #{source.address},
            st_pointfromtext(#{source.point}, 4326),
            #{source.isLegal},
            CURRENT_TIMESTAMP(),
            CURRENT_TIMESTAMP(),
            #{source.sourceProvider},
            #{source.dept}
            from dual;
        </foreach>
    </select>
    <select id="getSourceListByDept" resultMap="sourceListVo2Mapper">
        select
        <include refid="sourceVoCommonField"/>
        from t_control_warning_source a
        <where>
            a.is_legal = 1
            <if test="sourceType != null and sourceType != ''">
                and a.source_provider = #{sourceType}
            </if>
            <if test="deptIdList != null and deptIdList.size() > 0">
                and a.dept in
                <foreach collection="deptIdList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.searchValue)">
                <bind name="pattern" value="'%' + searchParams.searchValue.trim() + '%'"/>
                AND ( a.name like #{pattern,jdbcType=VARCHAR} or a.code like #{pattern,jdbcType=VARCHAR} or a.address like
                #{pattern,jdbcType=VARCHAR})
            </if>
        </where>
    </select>
</mapper>