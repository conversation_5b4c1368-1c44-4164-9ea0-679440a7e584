<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.control.mapper.WarningProcessMapper">

    <resultMap id="disposalMap" type="com.trs.police.control.domain.vo.warning.WarningProcessVO$Disposal">
        <result column="status" property="status"/>
        <result column="sign_overdue" property="signOverdue"/>
        <result column="reply_overdue" property="replyOverdue"/>
        <result column="notifyItem" property="notifyItem"/>
        <result column="feedback" property="feedback"/>
    </resultMap>

    <resultMap id="disposalMapWithWarningId" type="com.trs.police.control.helper.WarningListVoHelper$WarningListDisposal">
        <result column="warning_id" property="warningId"/>
        <result column="status" property="status"/>
        <result column="sign_overdue" property="signOverdue"/>
        <result column="reply_overdue" property="replyOverdue"/>
        <result column="notifyItem" property="notifyItem"/>
        <result column="feedback" property="feedback"/>
    </resultMap>

    <insert id="insertIgnoreDuplicate">
                INSERT
                ignore
            INTO
            police.t_warning_process
        (
            create_time,
            sign,
            status,
            warning_id,
            done,
            sign_overdue,
            reply_overdue,
            dept_id)
        VALUES(
        #{warningProcessEntity.createTime},
                #{warningProcessEntity.sign},
                #{warningProcessEntity.status},
                #{warningProcessEntity.warningId},
                #{warningProcessEntity.done},
                #{warningProcessEntity.signOverdue},
                #{warningProcessEntity.replyOverdue},
                #{warningProcessEntity.deptId})
    </insert>


    <select id="getWarningDisposalByWarningId"
        resultMap="disposalMap">
        select t2.status                                                 as status,
               t2.sign_overdue                                           as sign_overdue,
               t2.reply_overdue                                          as reply_overdue,
               if(t1.notify_type = 1, (select real_name from t_user where id = t1.user_id),
                  (select short_name from t_dept where id = t1.dept_id)) as notifyItem,
               if(t3.feedback IS NULL, '[]', JSON_ARRAYAGG(t3.feedback ->> '$.content')) as feedback
        from t_warning_notify t1
        join t_warning_process t2 on t1.process_id = t2.id
        left join t_warning_feedback t3 on t1.process_id = t3.process_id and t3.type = 2
        where t1.warning_id = #{warningId}
        GROUP BY status, sign_overdue, reply_overdue, notifyItem
    </select>

    <select id="getWarningDoneList" resultMap="mybatis-plus_WarningProcessEntity">
        select * from t_warning_process
        <where>
            <if test="warningIds != null and warningIds.size() != 0">
                AND warning_id IN
                <foreach collection="warningIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND done is not null
        </where>
    </select>

    <select id="getWarningDisposalByWarningIds"
            resultMap="disposalMapWithWarningId">
        select t2.warning_id                                             as warning_id,
               t2.status                                                 as status,
               t2.sign_overdue                                           as sign_overdue,
               t2.reply_overdue                                          as reply_overdue,
               if(t1.notify_type = 1, (select real_name from t_user where id = t1.user_id),
                  (select short_name from t_dept where id = t1.dept_id)) as notifyItem,
               if(t3.feedback IS NULL, '[]', JSON_ARRAYAGG(t3.feedback ->> '$.content')) as feedback
        from t_warning_notify t1
                 join t_warning_process t2 on t1.process_id = t2.id
                 left join t_warning_feedback t3 on t1.process_id = t3.process_id and t3.type = 2
        <where>
            <if test="warningIds != null and warningIds.size() != 0">
                AND t1.warning_id in
                <foreach collection="warningIds" item="warningId" separator="," open="(" close=")">
                    ${warningId}
                </foreach>
            </if>
        </where>
        GROUP BY warning_id, status, sign_overdue, reply_overdue, notifyItem
    </select>

    <select id="selectWarningProcessList" resultMap="mybatis-plus_WarningProcessEntity">
        <bind name="filterParams" value="request.filterParams"/>
        <bind name="sortParams" value="request.sortParams"/>
        select p.*
        from (select * from t_warning_process p
        <where>
            <foreach collection="filterParams" item="filterParam">
                <choose>
                    <when test="filterParam.key == 'status'">
                        and p.status >= #{filterParam.value}
                    </when>
                    <when test="filterParam.key == 'handleStatus'">
                        and p.status = #{filterParam.value}
                    </when>
                    <when test="filterParam.key == 'dept'">
                        <bind name="value"
                            value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(filterParam.value) + '%'"/>
                        and exists (select 1 from t_warning_notify n where n.process_id=p.id and (select d.code from
                        t_dept d where d.id=n.dept_id) like #{value})
                    </when>
                    <when test="filterParam.key == 'permissionDept'">
                        and p.dept_id in
                        <foreach collection="filterParam.value" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <when test="filterParam.key == 'currentUser'">
                        AND exists (select * from t_warning_notify n where n.process_id=p.id and n.user_id =#{filterParam.value})
                    </when>
                    <when test="filterParam.key == 'user'">
                        AND exists (select * from t_warning_notify n where n.process_id=p.id and n.user_id =#{filterParam.value})
                    </when>
                    <when test="filterParam.key == 'deptCode'">
                        <bind name="value"
                            value="@com.trs.police.common.core.utils.StringUtil@getPrefixCode(filterParam.value) + '%'"/>
                        and exists (select 1 from t_warning_notify n where n.process_id=p.id and (select d.code from
                        t_dept d where d.id=n.dept_id) like #{value})
                    </when>
                </choose>
            </foreach>
        </where>) p
        join (select id,warning_time,activity_time from t_warning w where w.warning_type = 'person'and w.control_type = 1
        <foreach collection="filterParams" item="filterParam">
            <choose>

                <when test="filterParam.key == 'timeRange'">
                    and (w.warning_time between #{filterParam.value.beginTime} and #{filterParam.value.endTime})
                </when>
                <when test="filterParam.key == 'modelType'">
                    and #{filterParam.value} member of(w.model_id)
                </when>
                <when test="filterParam.key == 'level'">
                    and w.warning_level = #{filterParam.value}
                </when>
                <when test="filterParam.key == 'personLabel'">
                    <bind name="personLabel" value="filterParam.getProcessedValue()"/>
                    and
                    <foreach collection="personLabel" item="item" open="(" separator="OR"
                        close=")">
                        (JSON_OVERLAPS(person_label
                        ,(SELECT JSON_ARRAYAGG( l.id )
                        FROM t_profile_label l
                        WHERE CONCAT(l.path, l.id, '-')
                        LIKE CONCAT('%-', ${item}, '-%' ))
                        )
                        >0)
                    </foreach>
                </when>
                <when test="filterParam.key == 'warningModel'">
                    <bind name="modelIds"
                        value="@com.trs.police.control.domain.builder.WarningListBuilder@getModelIds(filterParam.getProcessedValue())"/>
                    AND JSON_OVERLAPS((ifnull(w.model_id,'[]')),
                    (select JSON_ARRAYAGG(id) from t_control_monitor_warning_model m where m.id in
                    <foreach collection="modelIds" item="modelId" separator="," open="(" close=")">
                        ${modelId}
                    </foreach>
                    ))>0
                </when>
            </choose>
        </foreach>
        ) w
        on p.warning_id = w.id
        <if test="sortParams != null">
            <bind name="sortField" value="sortParams.sortField"/>
            <bind name="sortValue" value="sortParams.getProcessedValue()"/>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(sortField) ">
                order by
                <choose>
                    <when test="sortField == 'warningTime' ">
                        w.warning_time ${sortValue}
                    </when>
                    <when test="sortField == 'activityTime'">
                        w.activity_time ${sortValue}
                    </when>
                    <otherwise>
                        w.warning_time desc
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="sortParams == null">
            order by w.warning_time desc
        </if>
    </select>

    <select id="getWarningDoneStatusList"
            resultType="com.trs.police.control.helper.WarningListVoHelper$WarningDoneStatusVO">
        SELECT
            warning_id as warningId,
            CASE
                WHEN status = 4 THEN 1
                ELSE 0
                END AS status
        FROM t_warning_process
        <where>
            <if test="warningIds != null and warningIds.size() != 0">
                AND warning_id in
                <foreach collection="warningIds" item="warningId" separator="," open="(" close=")">
                    ${warningId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>