package com.trs.police.control.process;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.dto.Source;
import com.trs.police.control.ControlApp;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.domain.vo.homepage.TrackVO;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.kafka.v2.flow.processor.warning.filter.WarningDuplicateFilterProcess;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/6/12 18:20
 */
@SpringBootTest(classes = ControlApp.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class WarningDuplicateFilterProcessTest {

    @Resource
    private WarningDuplicateFilterProcess warningDuplicateFilterProcess;

    @Test
    public void testFilter() {
        WarningMessageContext warningMessageContext = new WarningMessageContext();
        GroupWarningDTO.Track trackVO = new GroupWarningDTO.Track();
        ControlInfo controlInfo = new ControlInfo();
        controlInfo.setId(1L);
        warningMessageContext.setControlInfo(controlInfo);
        trackVO.setIdentifierType(1);
        trackVO.setEventTime("20230123080534");
        trackVO.setIdentifier("51343730461221000X");
        Source source = new Source();
        source.setId("1");
        source.setType("face");
        source.setAddress("随州南");
        String json = "{\"cc\":\"C6315\",\"ccrq\":\"20230123000000\",\"gpsj\":\"20230123080534\",\"cxh\":\"11\",\"cfhcz\":\"绵阳\",\"ccrxm\":\"陈玉婷\",\"ddcz\":\"青白江东\",\"zjlid\":\"f86d9f374bc4bda3acf754ae51199fe4\",\"zwh\":\"016F\",\"ccrzjhm\":\"510502199003073530\",\"data_digest\":\"143488816a21a438d0c5d42621fdffd9846ed90eda0275b580c8a13a632e0849\",\"data_id\":0,\"data_source_id\":6044}";
        Map map = JSON.parseObject(json, Map.class);
        trackVO.setTrackDetail(map);
        trackVO.setSensingMessage(source);
        warningMessageContext.setTrackVO(trackVO);
        System.out.println(warningDuplicateFilterProcess.filter(warningMessageContext));
    }

}
