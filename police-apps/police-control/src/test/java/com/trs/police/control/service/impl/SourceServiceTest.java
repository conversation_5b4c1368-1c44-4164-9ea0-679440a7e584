package com.trs.police.control.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.common.base.Option;
import com.trs.common.utils.JsonUtils;
import com.trs.police.common.core.dto.Source;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.control.ControlApp;
import com.trs.police.control.domain.entity.basic.SourceEntity;
import com.trs.police.control.domain.request.PointRequest;
import com.trs.police.control.domain.vo.basic.SourceListVO;
import com.trs.police.control.mapper.SourceMapper;
import com.trs.police.control.service.SourceService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/28 11:43
 */
@SpringBootTest(classes = ControlApp.class)
public class SourceServiceTest {

    @Resource
    private SourceService sourceService;

    @Resource
    private SourceMapper sourceMapper;

    @Test
    public void test() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        ArrayList<Source> list = new ArrayList<>();
        list.add(objectMapper.readValue("{\"id\":-1,\"name\":\"北京首都国际机场\",\"type\":9,\"address\":\"北京市北京市朝阳区首都机场路\",\"longitude\":116.5972,\"latitude\":40.0794,\"isLocal\":1}", Source.class));
        sourceService.saveOrUpdateSource(list);
    }

    @Test
    public void testUpdate() {
        SourceEntity sourceEntity = sourceMapper.selectById(7150);
        sourceEntity.setPoint("point(29.424193 104.995541)");
        sourceMapper.updateSourceByIdWithOutUserInfo(sourceEntity);
    }

    /**
     * 测试从区域获取感知源
     */
    @Test
    public void testFetchSourceByArea() {
        Option<PointRequest> request = JsonUtils.toOptionObject("{\"geometries\":[{\"type\":\"circle\",\"geometry\":\"POINT(104.060912 30.607017)\",\"properties\":{\"radius\":\"0.012322\"}},{\"type\":\"circle\",\"geometry\":\"POINT(104.063363 30.558394)\",\"properties\":{\"radius\":\"0.012239\"}}],\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":null},\"filterParams\":[]}", PointRequest.class);
        List<SourceListVO> sourcePointList = sourceService.getSourcePointList(request.get());
        System.out.println("查询区域范围内的感知源列表为： " + JsonUtils.toOptionalJson(sourcePointList).get());
    }

    /**
     * 测试查询感知源列表有轨迹
     */
    @Test
    public void testSourceListWithPointExist(){
        ListParamsRequest request = JSONObject.parseObject("{\"pageParams\":{\"pageNumber\":2,\"pageSize\":20},\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":\"\"},\"filterParams\":[{\"key\":\"hasPointStatus\",\"type\":\"string\",\"value\":\"exist\"}]}", ListParamsRequest.class);
        PageResult<SourceListVO> sourcePageList = sourceService.getSourcePageList(request);
        System.out.println("查询感知源列表为： " + JsonUtils.toOptionalJson(sourcePageList).get());
    }

    /**
     * 测试查询感知源列表无轨迹
     */
    @Test
    public void testSourceListWithPointNotExist(){
        ListParamsRequest request = JSONObject.parseObject("{\"pageParams\":{\"pageNumber\":1,\"pageSize\":20},\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":\"\"},\"filterParams\":[{\"key\":\"hasPointStatus\",\"type\":\"string\",\"value\":\"notExist\"}]}", ListParamsRequest.class);
        PageResult<SourceListVO> sourcePageList = sourceService.getSourcePageList(request);
        System.out.println("查询感知源列表为： " + JsonUtils.toOptionalJson(sourcePageList).get());
    }

    /**
     * 测试查询感知源列表
     */
    @Test
    public void testSourceListWithPointDefault(){
        ListParamsRequest request = JSONObject.parseObject("{\"pageParams\":{\"pageNumber\":2,\"pageSize\":20},\"searchParams\":{\"searchField\":\"fullText\",\"searchValue\":\"\"},\"filterParams\":[{\"key\":\"hasPointStatus\",\"type\":\"string\",\"value\":\"default\"}]}", ListParamsRequest.class);
        PageResult<SourceListVO> sourcePageList = sourceService.getSourcePageList(request);
        System.out.println("查询感知源列表为： " + JsonUtils.toOptionalJson(sourcePageList).get());
    }
}
