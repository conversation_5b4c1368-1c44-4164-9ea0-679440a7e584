package com.trs.police.control.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.constant.enums.WarningStatusEnum;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.common.core.entity.WarningProcessEntity;
import com.trs.police.common.core.utils.GeoUtils;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.OperateVO;
import com.trs.police.control.ControlApp;
import com.trs.police.control.domain.dto.ProfessionalWarningDTO;
import com.trs.police.control.mapper.WarningProcessMapper;
import com.trs.police.control.service.WarningProcessService;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.InputStream;

import static com.trs.police.common.core.utils.GeoHashUtils.getGeoHash;

@SpringBootTest(classes = ControlApp.class)
public class WarningProcessServiceTest {

    @Autowired
    private WarningProcessServiceImpl warningProcessService;

    @Resource
    private WarningProcessMapper warningProcessMapper;

    @Test
    public void testInsertIgnore() {
        WarningProcessEntity entity = new WarningProcessEntity();
        entity.setWarningId(5L);
        entity.setSign(JSONObject.parseObject("\"{\\\"user\\\": {\\\"tel\\\": \\\"13551109999\\\", \\\"duty\\\": \\\"班长\\\", \\\"deptId\\\": 2, \\\"idCard\\\": \\\"51132419881210365X\\\", \\\"status\\\": null, \\\"userId\\\": 2214, \\\"deptCode\\\": \\\"510500000000\\\", \\\"deptName\\\": \\\"四川省泸州市公安局\\\", \\\"userName\\\": \\\"管理员\\\", \\\"districtCode\\\": \\\"510500\\\", \\\"deptShortName\\\": \\\"泸州公安局\\\"}, \\\"operateTime\\\": 1729480237886}\"", OperateVO.class));
        entity.setDone(null);
        entity.setStatus(WarningStatusEnum.PROCESS_FINISH);
        entity.setSignOverdue(false);
        entity.setReplyOverdue(false);
        entity.setDeptId(28L);
        warningProcessMapper.insertIgnoreDuplicate(entity);
    }

    @Test
    public void test() throws Exception {
        InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("gjxx/gjxx.json");
        String string = IOUtils.toString(resourceAsStream);
        WarningDTO warningDTO = JSON.parseObject(string, WarningDTO.class);
        warningProcessService.receivePersonWarningMessage(warningDTO);
    }

    /**
     * 信令预警消费测试
     */
    @Test
    public void testReceiveXlWarningMessage() throws Exception {
        ProfessionalWarningDTO warningDTO = new ProfessionalWarningDTO();
        ProfessionalWarningDTO.JzsdData jzsdData = new ProfessionalWarningDTO.JzsdData();
        jzsdData.setTaskid("1");
        jzsdData.setWarningTime("2020-08-07 14:07:07");
        jzsdData.setTarget("15351241013");
        jzsdData.setHomeArea("511600");
        jzsdData.setMsisdn("13800000000");
        jzsdData.setCurArea("511600");
        jzsdData.setRelateNum("1");
        jzsdData.setRelateHomeAc("1");
        jzsdData.setLongitude("104.066667");
        jzsdData.setLatitude("30.666667");
        jzsdData.setUuid("1");
        jzsdData.setSendTime("2020-08-07 14:07:07");
        jzsdData.setResultDesc("离开四川广安(正常离开),到达四川南充,触发离开城市预警");
        jzsdData.setSubAlarmType("1");
        jzsdData.setGeoHash(getGeoHash(104.066667, 30.666667, 9));

        warningDTO.setData(JsonUtil.toJsonString(jzsdData));
        warningDTO.setJobId("1");

        warningDTO.setResId("1");
        warningDTO.setJobType("jobType");
        warningDTO.setJobName("jobName");

        warningDTO.setDistrict("511600");
        warningDTO.setPolice("");
        warningProcessService.receiveXlWarningMessage(warningDTO);
    }
}
