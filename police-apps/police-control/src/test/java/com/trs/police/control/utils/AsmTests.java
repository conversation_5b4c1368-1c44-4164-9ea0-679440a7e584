package com.trs.police.control.utils;

import com.trs.db.sdk.connection.IEsConnection;
import com.trs.db.sdk.repository.paramProperties.CommonConnectParam;
import javassist.ClassClassPath;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtMethod;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.Test;

import java.lang.reflect.Method;
import java.util.HashMap;

/**
 * *@author:wen.wen
 * *@create 2025-01-02 18:30
 **/
public class AsmTests {

    @Test
    public void test() throws Exception {
        ClassPool classPool = ClassPool.getDefault();
        classPool.insertClassPath(new ClassClassPath(AsmTests.class));
        CtClass ctClass = classPool.get("com.trs.police.control.utils.AsmTestsV2");
        // 确保类未被冻结
        if (ctClass.isFrozen()) {
            ctClass.defrost();
        }
        CtMethod ctMethod = ctClass.getDeclaredMethod("test222");
        ctMethod.setBody("{System.out.print(\"test3333\");}");
        ctClass.toClass();
        AsmTestsV2 asmTests = new AsmTestsV2();
        asmTests.test222();
    }

    @Test
    public void testV2() throws Exception {
//        ClassPool classPool = ClassPool.getDefault();
//        classPool.insertClassPath(new ClassClassPath(AsmTests.class));
//        CtClass ctClass = classPool.get("org.elasticsearch.client.RestHighLevelClient");
//        // 确保类未被冻结
//        if (ctClass.isFrozen()) {
//            ctClass.defrost();
//        }
//        CtMethod ctMethod = ctClass.getDeclaredMethod("getVersionValidation");
//        ctMethod.setBody("{System.out.print(\"修改了字节码方法\");return java.util.Optional.empty();}");
//        ctClass.toClass();
        RestClientBuilder builder = IEsConnection.getRestClientBuilder("", "", "http://192.168.210.60:39200", new CommonConnectParam(), new HashMap<>());
        RestHighLevelClient restHighLevelClient = new RestHighLevelClient(builder);
// 获取Class对象
        Class<?> clazz = restHighLevelClient.getClass();
        // 获取私有方法的Method对象
        Method method = clazz.getDeclaredMethod("getVersionValidation", Response.class);
        // 设置方法为可访问
        method.setAccessible(true);
        // 调用方法
        method.invoke(restHighLevelClient, (Response)null);
    }

    public void test222() {
        System.out.println("test222");
    }

    public static void fffffff(String[] args) {
        AsmTestsV2 v2 = new AsmTestsV2();
    }
}
