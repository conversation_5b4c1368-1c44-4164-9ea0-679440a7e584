PUT _template/bz_warning_track_template
{
  "order": 0,
  "index_patterns": [
    "bz_warning_track-*"
  ],
  "settings": {
    "index": {
      "refresh_interval": "1s",
      "number_of_shards": "3",
      "number_of_replicas": "1"
    }
  },
  "mappings": {
    "dynamic": "strict",
    "properties": {
      "ldxxbh": {
        "meta": {
            "comment" : "联动信息编号"
        },
        "type": "keyword"
      },
      "ywzjid": {
        "meta": {
            "comment" : "业务主键 ID"
        },
        "type": "keyword"
      },
      "sender_id": {
        "meta": {
            "comment" : "联动申请应用 ID"
        },
        "type": "keyword"
      },
      "service_id": {
        "meta": {
            "comment" : "联动接收应用 ID"
        },
        "type": "keyword"
        },
      "ldxx_type": {
        "meta": {
            "comment" : "信息交换类别，详见（第 3 部分：联动信息交换信息类别代码），此处为“E014”"
        },
        "type": "keyword"
      },
      "djzjhm": {
        "meta": {
            "comment" : "登记证件号码"
        },
        "type": "keyword"
      },
      "dtxxtgdwjgdm": {
        "meta": {
            "comment" : "动态信息提供公安机关机构代码"
        },
        "type": "keyword"
      },
      "hdfssj": {
        "format": "yyyy-MM-dd HH:mm:ss||yyyy/MM/dd HH:mm:ss||strict_date_optional_time||epoch_millis",
        "meta": {
            "comment" : "活动发生时间"
        },
        "type": "date"
      },
      "dtxxtgdw": {
        "meta": {
            "comment" : "动态信息提供公安机关"
        },
        "type": "keyword"
      },
      "djxm": {
        "meta": {
            "comment" : "登记姓名"
        },
        "type": "keyword"
      },
      "xxbdsj": {
        "meta": {
            "comment" : "信息比对时间"
        },
        "type": "keyword"
      },
      "bzmbhm": {
        "meta": {
            "comment" : "比中目标号码"
        },
        "type": "keyword"
      },
      "hdxgxx": {
        "meta": {
            "comment" : "活动相关信息"
        },
        "type": "keyword"
      },
      "xxbddw": {
        "meta": {
            "comment" : "信息比对单位"
        },
        "type": "keyword"
      },
      "hdfsddqh": {
        "meta": {
            "comment" : "活动发生地点区划"
        },
        "type": "keyword"
      },
      "hdfsddshcsdm": {
        "meta": {
            "comment" : "活动发生地所属社会场所代码"
        },
        "type": "keyword"
      },
      "hdfsdd_jd": {
        "meta": {
            "comment" : "活动发生地经度"
        },
        "type": "keyword"
      },
      "bzmblx": {
        "meta": {
            "comment" : "比中目标类型"
        },
        "type": "keyword"
      },
      "djcsrq": {
        "format": "yyyy-MM-dd HH:mm:ss||yyyy/MM/dd HH:mm:ss||strict_date_optional_time||epoch_millis",
        "meta": {
            "comment" : "登记出生日期"
        },
        "type": "date"
      },
      "xxbddwjgdm": {
        "meta": {
            "comment" : "信息比对单位机构代码"
        },
        "type": "keyword"
      },
      "hdfsddshcs": {
        "meta": {
            "comment" : "活动发生地所属社会场所"
        },
        "type": "keyword"
      },
      "djxb": {
        "meta": {
            "comment" : "登记性别"
        },
        "type": "keyword"
      },
      "dtgjxxbh": {
        "meta": {
            "comment" : "动态轨迹信息编号"
        },
        "type": "keyword"
      },
      "djclsbdh": {
        "meta": {
            "comment" : "登记车辆识别代号"
        },
        "type": "keyword"
      },
      "hdfsddssgajg": {
        "meta": {
            "comment" : "活动发生地所属公安机关"
        },
        "type": "keyword"
      },
      "hdfsdd_wd": {
        "meta": {
            "comment" : "活动发生地纬度"
        },
        "type": "keyword"
      },
      "djfdjh": {
        "meta": {
            "comment" : "登记发动机号"
        },
        "type": "keyword"
      },
      "djhpzl": {
        "meta": {
            "comment" : "车辆号牌种类"
        },
        "type": "keyword"
      },
      "bkdxbh": {
        "meta": {
            "comment" : "比中目标对应的布控对象编号， 可多个，半角逗号隔开，最多 25 个，与布控申请、布控指令关联"
        },
        "type": "keyword"
      },
      "hdfsddxz": {
        "meta": {
            "comment" : "活动发生地点详址"
        },
        "type": "keyword"
      },
      "djzjlx": {
        "meta": {
            "comment" : "登记证件类型"
        },
        "type": "keyword"
      },
      "djhphm": {
        "meta": {
            "comment" : "登记号牌号码"
        },
        "type": "keyword"
      },
      "dtxxlb": {
        "meta": {
            "comment" : "动态信息类别"
        },
        "type": "keyword"
      },
      "hdfsddssgajgjgdm": {
        "meta": {
            "comment" : "活动发生地所属公安机关代码"
        },
        "type": "keyword"
      },
      "sbsj": {
        "format": "yyyy-MM-dd HH:mm:ss||yyyy/MM/dd HH:mm:ss||strict_date_optional_time||epoch_millis",
        "meta": {
            "comment" : "上报时间"
        },
        "type": "date"
      },
      "hdlxdm": {
        "meta": {
            "comment" : "活动类型"
        },
        "type": "integer"
      },
      "hdlx": {
        "meta": {
            "comment" : "活动类型"
        },
        "type": "keyword"
      },
      "create_time": {
        "format": "yyyy-MM-dd HH:mm:ss||yyyy/MM/dd HH:mm:ss||strict_date_optional_time||epoch_millis",
        "meta": {
            "comment" : "创建时间"
        },
        "type": "date"
      },
      "update_time": {
        "format": "yyyy-MM-dd HH:mm:ss||yyyy/MM/dd HH:mm:ss||strict_date_optional_time||epoch_millis",
        "meta": {
            "comment" : "更新时间"
        },
        "type": "date"
      }
    }
  },
  "aliases": {}
}


PUT bz_warning_track-000001
{
  "aliases": {
    "bz_warning_track":{
      "is_write_index": true
    }
  }
}