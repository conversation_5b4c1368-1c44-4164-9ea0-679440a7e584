package com.trs.police.task.tracing.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.police.task.tracing.constant.enums.TaskMethodEnum;
import com.trs.police.task.tracing.constant.enums.TaskStatusEnum;
import com.trs.police.task.tracing.domain.entity.TaskTracing;
import com.trs.police.task.tracing.mapper.TaskTracingMapper;
import com.trs.police.task.tracing.service.TaskTracingProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:敏感挂账自动生成普通挂账定时任务
 *
 * @author: lv.bo
 * @create: 2024-03-20 15:35
 */
@Component
@Slf4j
public class SensitiveTaskAutoGenerateTask {

    /**
     * 是否开启敏感挂账定时任务自动生成普通挂账
     */
    @Value("${com.trs.sensitiveTask.enable:true}")
    private String sensitiveTaskEnable;

    @Resource
    private TaskTracingMapper taskTracingMapper;

    @Resource
    private TaskTracingProcessService taskTracingProcessService;

    /**
     * 敏感挂账自动生成普通挂账定时任务
     */
    @Scheduled(cron = "${com.trs.sensitiveTask.corn:1 0 0 * * ?}")
    public void sensitiveTaskGenerate() {
        if (!"true".equalsIgnoreCase(sensitiveTaskEnable)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        QueryWrapper<TaskTracing> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_method", TaskMethodEnum.SENSITIVE_PERIOD.getCode())
                .eq("task_status", TaskStatusEnum.DOING.getCode())
                .ge("time_limit", now)
                .le("sensitive_start_time", now);
        List<TaskTracing> taskTracings = taskTracingMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(taskTracings)){
            log.info("本次敏感挂账定时任务未查询到敏感时段的任务！");
            return;
        }
        taskTracings.forEach(taskTracing -> {
            try {
                taskTracingProcessService.generateSimpleTask(taskTracing);
            }catch (Exception e){
                log.error("敏感时段挂账任务自动生成普通任务失败,挂账id:[{}],挂账标题:[{}]", taskTracing.getId(), taskTracing.getTitle(), e);
            }

        });
    }
}
