package com.trs.police.task.tracing.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.json.serializer.SimpleDateSerializer;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.TodoTaskVO;
import lombok.Data;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 挂账列表vo
 *
 * <AUTHOR>
 * @date 2023/2/7 15:05
 */
@Data
public class TaskTracingListVO {

    /**
     * 挂账id
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 类型
     */
    private String type;
    /**
     * 挂帐盯办编码
     */
    private String code;
    /**
     * 状态
     */
    private CodeNameVO status;
    /**
     * 是否逾期
     */
    private Boolean isOverdue;

    /**
     * 时限类型
     */
    private String timeLimitType;


    /**
     * 时限-简洁时间格式
     */
    @JsonSerialize(using = SimpleDateSerializer.class, nullsUsing = SimpleDateSerializer.class)
    private LocalDateTime timeLimit;

    /**
     * 回复数量
     */
    private Long replyCount;

    /**
     * 今日回复数量
     */
    private Long todayReplyCount;

    /**
     * 未处理数量
     */
    private Long unhandledCount;

    /**
     * 发布时间-简洁时间格式
     */
    @JsonSerialize(using = SimpleDateSerializer.class, nullsUsing = SimpleDateSerializer.class)
    private LocalDateTime taskTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 可以点击的按钮
     */
    private List<String> operations = List.of();

    /**
     * 是否有未读消息（红点）
     */
    private Boolean isRead;

    /**
     * 发布单位
     */
    private String createDept;

    /**
     * 发起单位id
     */
    private Long createDeptId;

    /**
     * 责任单位
     */
    private List<String> responsibleDept;

    /**
     * 是否有领导回复
     */
    private Boolean isLeaderReply;

    private LocalDateTime updateTime;

    /**
     * 生成TodoTaskVO
     *
     * @return TodoTaskVO
     */
    public TodoTaskVO of() {
        Duration duration = Duration.between(LocalDateTime.now(), timeLimit);
        return TodoTaskVO.builder()
            .type(3)
            .id(id)
            .content(title)
            .isRead(isRead)
            .status(status)
            .overdueNotice(duration.isNegative() ? "办结已逾期" : TimeUtil.durationToString(duration) + "后办结逾期")
            .originTime(updateTime)
            .time(TimeUtil.getSimpleTime(updateTime))
            .build();
    }
}


