package com.trs.police.task.tracing.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Objects;
import lombok.Getter;

/**
 * 挂帐类型
 *
 * <AUTHOR>
 */
public enum TaskTypeEnum {

    /**
     * 领导批示
     */
    LEADER(1, "领导批示"),
    /**
     * 敏感案事件
     */
    CASE(2, "敏感案事件"),
    /**
     * 上级交办
     */
    SUPERIOR(3, "上级交办"),
    /**
     * 会议决策
     */
    MEETING(4, "会议决策"),
    /**
     * 重点工作
     */
    IMPORTANT_WORK(5, "重点工作"),
    /**
     * 重点警情
     */
    IMPORTANT_POLICE_INFO(6, "重点警情"),
    /**
     * 涉稳事件
     */
    SW_EVENT(7, "涉稳事件"),
    /**
     * 上网追逃
     */
    ONLINE_TRACKING(8, "上网追逃");

    @Getter
    @EnumValue
    private final int code;

    @Getter
    @JsonValue
    private final String name;

    TaskTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static TaskTypeEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (TaskTypeEnum typeEnum : TaskTypeEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
