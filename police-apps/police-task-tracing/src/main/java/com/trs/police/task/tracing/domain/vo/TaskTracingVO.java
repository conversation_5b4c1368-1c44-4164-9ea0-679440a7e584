package com.trs.police.task.tracing.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.trs.police.common.core.json.serializer.SimpleDateSerializer;
import com.trs.police.common.core.valication.ValidationGroups;
import com.trs.police.common.core.vo.approval.ApprovalInfoVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.task.tracing.constant.enums.*;
import com.trs.police.task.tracing.domain.entity.TaskTracing;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2023/2/7 14:16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskTracingVO {

    @NotNull(message = "挂帐id不能为空!",groups = ValidationGroups.Update.class)
    private Long id;

    @NotBlank(message = "挂帐标题不能为空!")
    private String title;
    /**
     * 挂帐类型，修改为long类型，否则每次都要加枚举类
     */
    @NotNull(message = "挂帐类型不能为空!")
    private TaskTypeEnum type;
    /**
     * 盯办类型
     */
    @NotNull(message = "挂帐类型不能为空!")
    private DbTypeEnum dbType;
    /**
     * 时限类型
     */
    @NotNull(message = "盯办时限类型不能为空!")
    private TimeLimitTypeEnum timeLimitType;

    /**
     * 挂账方式-默认单事件挂账
     */
    @NotNull(message = "挂帐方式类型不能为空!")
    private TaskMethodEnum taskMethod = TaskMethodEnum.SINGLE_EVENT;

    /**
     * 敏感时段开始时间
     */
    @JsonSerialize(using = SimpleDateSerializer.class,nullsUsing = SimpleDateSerializer.class)
    private LocalDateTime sensitiveStartTime;

    /**
     * 时限
     */
    @JsonSerialize(using = SimpleDateSerializer.class,nullsUsing = SimpleDateSerializer.class)
    private LocalDateTime timeLimit;

    /**
     * 内容
     */
    private String content;

    /**
     * 发布挂账关联类型，要情：yaoqing，指令：zhiling，线索：xiansuo，风险警情：riskJq，布控预警：bkWarning，合成作战：hcFight
     *               反恐专题：fkTopic
     */
    private String relatedType;

    /**
     * 关联title
     */
    private String relatedTitle;

    /**
     * 关联id
     */
    private Long relatedId;

    /**
     * 附件
     */
    private FileInfoVO[] attachments;
    /**
     * 责任单位
     */
    @Valid
    private SimpleDeptVO[] responsibleDepts;
    /**
     * 报送领导
     */
    @Valid
    private SimpleUserVO[] reportLeaders;

    /**
     * 单位负责人
     */
    @Valid
    private SimpleUserVO[] responsibleLeaders;

    /**
     * 编码
     */
    private String taskCode;
    /**
     * 挂帐状态
     */
    private TaskStatusEnum taskStatus;
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 是否逾期
     */
    private Boolean isOverdue;

    /**
     * 相关审批
     */
    private ApprovalInfoVO approvalInfo;
    /**
     * 挂账时间
     */
    @JsonSerialize(using = SimpleDateSerializer.class,nullsUsing = SimpleDateSerializer.class)
    private LocalDateTime taskTime;

    /**
     * 党政责任单位
     */
    private String responsibleGov = "";
    /**
     * 关联人员
     */
    private Object[] relatePerson = new Object[0];
    /**
     * 关联群体
     */
    private Object[] relateGroup = new Object[0];

    /**
     * 是否需要短信通知
     */
    private Integer isNeedSms;

    /**
     * 转实体
     *
     * @return 实体
     */
    public TaskTracing toEntity() {
        TaskTracing taskTracing = new TaskTracing();
        taskTracing.setId(id);
        taskTracing.setTitle(title);
        taskTracing.setTaskType(type);
        taskTracing.setTaskCode(taskCode);
        taskTracing.setTimeLimitType(timeLimitType);
        taskTracing.setTaskMethod(taskMethod);
        taskTracing.setSensitiveStartTime(sensitiveStartTime);
        taskTracing.setContent(content);
        taskTracing.setAttachments(attachments);
        taskTracing.setResponsibleDept(responsibleDepts);
        taskTracing.setRelatedType(relatedType);
        taskTracing.setRelatedId(relatedId);
        taskTracing.setRelatedTitle(relatedTitle);
        taskTracing.setReportLeader(reportLeaders);
        taskTracing.setResponsibleLeader(responsibleLeaders);
        taskTracing.setTaskStatus(taskStatus);
        taskTracing.setPublishTime(publishTime);
        taskTracing.setTaskTime(taskTime);
        taskTracing.setResponsibleGov(responsibleGov);
        taskTracing.setRelatePerson(relatePerson);
        taskTracing.setRelateGroup(relateGroup);
        taskTracing.setDbType(dbType);
        taskTracing.setTimeLimit(getTimeLimitByType(timeLimitType));
        return taskTracing;
    }

    /**
     * setTimeLimit
     *
     * @param timeLimitType timeLimitType
     * @return timeLimitType
     */
    public LocalDateTime getTimeLimitByType(@NotNull(message = "盯办时限类型不能为空!") TimeLimitTypeEnum timeLimitType) {
        switch (timeLimitType){
            case CUSTOM:
                LocalTime max = LocalTime.MAX.withSecond(0).withNano(0);
                LocalDateTime todayMax = LocalDateTime.of(this.getTimeLimit().toLocalDate(), max);
                return todayMax;
            case ONE_DAY:
                return taskTime.plusDays(1).minusSeconds(1);
            case THREE_DAYS:
                return taskTime.plusDays(3).minusSeconds(1);
            case SEVEN_DAYS:
                return taskTime.plusDays(7).minusSeconds(1);
            default:
                return null;
        }
    }

    /**
     * 设置盯办时限
     */
    public void resetTimeLimit(){
        if(this.getTimeLimitType() == TimeLimitTypeEnum.CUSTOM){
            LocalTime max = LocalTime.MAX.withSecond(0).withNano(0);
            LocalDateTime todayMax = LocalDateTime.of(this.getTimeLimit().toLocalDate(), max);
            this.setTimeLimit(todayMax);
        }
    }
}
