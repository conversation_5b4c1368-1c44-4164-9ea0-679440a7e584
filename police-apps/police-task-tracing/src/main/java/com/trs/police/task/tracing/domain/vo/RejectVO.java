package com.trs.police.task.tracing.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 驳回
 *
 * <AUTHOR>
 * @date 2023/02/07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RejectVO implements Serializable {

    private static final long serialVersionUID = -3913848618687456964L;

    /**
     * 用户信息
     */
    private SimpleUserVO user;
    /**
     * 驳回原因
     */
    private String content;

    /**
     * 操作时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime operateTime;

    public RejectVO(CurrentUser user,String content) {
        this.user = new SimpleUserVO(user);
        this.operateTime = LocalDateTime.now();
        this.content = content;
    }
}
