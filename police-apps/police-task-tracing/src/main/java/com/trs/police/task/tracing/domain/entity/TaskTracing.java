package com.trs.police.task.tracing.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.approval.ApprovalInfoVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleDeptVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.task.tracing.constant.enums.*;
import com.trs.police.task.tracing.domain.vo.CloseVO;
import com.trs.police.task.tracing.domain.vo.ReopenVO;
import com.trs.police.task.tracing.domain.vo.TaskTracingVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 挂帐盯办-挂帐表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "t_task_tracing", autoResultMap = true)
public class TaskTracing extends AbstractBaseEntity {

    private static final long serialVersionUID = -4152123281426258861L;

    /**
     * 标题
     */
    private String title;

    /**
     * 挂帐编号
     */
    private String taskCode;

    /**
     * 挂帐类型
     */
    private TaskTypeEnum taskType;

    /**
     * 盯办类型
     */
    private DbTypeEnum dbType;

    /**
     * 盯办时限类型 1天内 3天内 7天内 自定义
     */
    private TimeLimitTypeEnum timeLimitType;

    /**
     * 挂账方式-默认单事件挂账：1，敏感时段挂账：2
     */
    private TaskMethodEnum taskMethod;

    /**
     * 关联title
     */
    private String relatedTitle;

    /**
     * 盯办时限 （时间戳）
     */
    private LocalDateTime timeLimit;

    /**
     * 敏感时段开始时间
     */
    private LocalDateTime sensitiveStartTime;

    /**
     * 指令内容（富文本）
     */
    private String content;

    /**
     * 附件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private FileInfoVO[] attachments;

    /**
     * 责任单位
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private SimpleDeptVO[] responsibleDept;

    /**
     * 报送领导
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private SimpleUserVO[] reportLeader;

    /**
     * 单位负责人
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private SimpleUserVO[] responsibleLeader;

    /**
     * 状态 1.草稿 2.待审核 3.已驳回 4.在办中 5.已办结 6.已销账
     */
    private TaskStatusEnum taskStatus;

    /**
     * 发布时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime publishTime;
    /**
     * 销账
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private CloseVO close;

    /**
     * 重启
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ReopenVO reopen;

    /**
     * 是否逾期
     */
    private boolean isOverdue = false;

    /**
     * 短信 t_message 表主键
     */
    private Long messageId;

    /**
     * 挂账时间
     */
    private LocalDateTime taskTime;

    /**
     * 发布挂账关联类型，要情：yaoqing，指令：zhiling，线索：xiansuo，风险警情：riskJq，布控预警：bkWarning，合成作战：hcFight
     */
    private String relatedType;

    /**
     * 关联id
     */
    private Long relatedId;

    /**
     * 党政责任单位
     */
    private String responsibleGov;
    /**
     * 关联人员
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object[] relatePerson;
    /**
     * 关联群体
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object[] relateGroup;

    /**
     * 是否需要短信通知
     */
    private Integer isNeedSms;

    /**
     * 转vo
     *
     * @return TaskTracingVO
     */
    public TaskTracingVO toVO() {
        TaskTracingVO vo = new TaskTracingVO();
        vo.setId(this.getId());
        vo.setAttachments(this.attachments);
        vo.setType(this.taskType);
        vo.setContent(this.content);
        vo.setTitle(this.title);
        vo.setTimeLimitType(this.timeLimitType);
        vo.setTaskMethod(this.taskMethod);
        vo.setTimeLimit(this.timeLimit);
        vo.setSensitiveStartTime(this.sensitiveStartTime);
        vo.setReportLeaders(this.reportLeader);
        vo.setResponsibleDepts(this.responsibleDept);
        vo.setResponsibleLeaders(this.responsibleLeader);
        vo.setIsOverdue(this.isOverdue);
        vo.setRelatedType(this.relatedType);
        vo.setRelatedTitle(this.relatedTitle);
        vo.setRelatedId(this.relatedId);
        vo.setPublishTime(this.publishTime);
        vo.setTaskCode(this.taskCode);
        vo.setTaskStatus(this.taskStatus);
        vo.setTaskTime(this.taskTime);
        vo.setResponsibleGov(this.responsibleGov);
        vo.setRelatePerson(this.relatePerson);
        vo.setRelateGroup(this.relateGroup);
        // 审批信息
        ApprovalService approvalService = BeanUtil.getBean(ApprovalService.class);
        ApprovalInfoVO approval = approvalService.getApprovals(OperateModule.TASK_TRACING.getCode(),
            this.getId(), null).stream().findFirst().orElse(new ApprovalInfoVO());
        vo.setApprovalInfo(approval);
        vo.setIsNeedSms(this.isNeedSms);
        return vo;
    }

    /**
     * 是否有管理权限
     *
     * @param commander 指挥长
     * @return 是否有管理权限
     */
    public boolean superviseBy(CurrentUser commander) {
        return commander.getDeptId().equals(this.getCreateDeptId());
    }

    /**
     * 是否有编辑权限
     *
     * @param publisher 发布人
     * @return 是否有编辑权限
     */
    public boolean isPublishBy(CurrentUser publisher) {
        return publisher.getId().equals(this.getCreateUserId());
    }

    /**
     * 是否有处置权限
     *
     * @param handler 处置人
     * @return 是否有处置权限
     */
    public boolean isHandleBy(CurrentUser handler) {
        return Arrays.stream(this.responsibleDept).anyMatch(d -> d.getDeptId().equals(handler.getDeptId()));
    }
}