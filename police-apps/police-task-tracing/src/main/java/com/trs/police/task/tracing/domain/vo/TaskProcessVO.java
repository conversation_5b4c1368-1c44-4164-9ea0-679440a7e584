package com.trs.police.task.tracing.domain.vo;

import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.TimeUtil;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptActionVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 挂账盯办流程vo
 *
 * <AUTHOR>
 */
@Data
public class TaskProcessVO {

    /**
     * 发布挂账
     */
    private ProcessNodeVO publish;
    /**
     * 挂账审核
     */
    private ProcessNodeVO approval;
    /**
     * 挂账办理
     */
    private List<ProcessDeptNodeVO> process;
    /**
     * 销账
     */
    private ProcessNodeVO close;


    /**
     * 挂账办理节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessNodeVO {

        /**
         * 操作时间
         */
        private String time;
        /**
         * 用户信息
         */
        private SimpleUserVO userInfo;
        /**
         * 审批状态
         */
        private Integer status;
        /**
         * 审批意见
         */
        private String reason;
        /**
         * 附件
         */
        private List<FileInfoVO> attachments;

        /**
         * 生成节点vo
         *
         * @param time 时间字符串
         * @param userInfo 用户信息
         */
        public ProcessNodeVO(String time, SimpleUserVO userInfo) {
            this.time = time;
            this.userInfo = userInfo;
        }
    }

    /**
     * 审批信息生成vo
     *
     * @param actionVO UserDeptActionVO
     * @return ProcessNodeVO
     */
    public static ProcessNodeVO of(UserDeptActionVO actionVO) {
        if (actionVO == null) {
            return null;
        }
        PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
        CurrentUser currentUser = permissionService.findCurrentUser(actionVO.getUserInfo().getUserId(), actionVO.getUserInfo().getDeptId());
        SimpleUserVO user = new SimpleUserVO(currentUser);
        return new ProcessNodeVO(TimeUtil.getSimpleTime(actionVO.getTime()),
            user, actionVO.getStatus(), actionVO.getReason(), actionVO.getAttachments());
    }

    /**
     * 挂账办理节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessDeptNodeVO {

        /**
         * 部门名称
         */
        private String deptName;
        /**
         * 状态
         */
        private CodeNameVO status;

        /**
         * 获取状态码值
         *
         * @return code
         */
        public Integer getCode() {
            return (Integer) status.getCode();
        }
    }
}
