package com.trs.police.task.tracing.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.vo.ExportExcelVO;
import com.trs.police.task.tracing.excel.CustomMergeStrategy;
import com.trs.police.task.tracing.excel.CustomMergeWriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-04-30 10:51
 */
@Slf4j
public class ExcelExportUtil {

    /**
     *  下载档案
     *
     * @param vo 档案信息
     * @throws Exception 异常信息
     */
    public static void downloadRecordExcel(ExportExcelVO vo) throws Exception {
        if (vo == null || vo.getResponse() == null || CollectionUtils.isEmpty(vo.getData()) || StringUtil.isEmpty(vo.getExcelName())){
            throw new TRSException("必传参数为空，请重试");
        }
        if (!CollectionUtils.isEmpty(vo.getFileMap()) && StringUtil.isEmpty(vo.getZipName())){
            throw new TRSException("下载为压缩包必传参数为空，请重试");
        }
        InputStream templateInputStream = ExcelExportUtil.class.getResourceAsStream(vo.getTemplateFilePath());
        if (CollectionUtils.isEmpty(vo.getFileMap())){
            ExcelExportUtil.writeTemplateExcel(vo.getResponse(), vo.getExcelName(), templateInputStream, vo.getData(), vo.getMergeRowIndex(), vo.getMergeColIndex());
        }else {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelExportUtil.writeTemplateExcel(outputStream, vo.getExcelName(), templateInputStream, vo.getData(), vo.getMergeRowIndex(), vo.getMergeColIndex());
            Map<String, Object> fileMap = vo.getFileMap();
            fileMap.put(vo.getExcelName(), outputStream.toByteArray());
            ExcelExportUtil.exportZip(vo.getResponse(), vo.getZipName(), fileMap, vo.getExcelName());
            outputStream.close();
        }
    }
    /**
     *  根据模板导出excel
     *
     * @param response response
     * @param excelName 导出的excel名字
     * @param inputStream 模板的输入流
     * @param map 导出的数据
     * @param mergeRowIndex 从多少行开始合并单元格
     * @param mergeColIndex 需要合并单元格的列
     */
    public static void writeTemplateExcel(HttpServletResponse response, String excelName, InputStream inputStream, Map<String, Object> map, int mergeRowIndex, int[] mergeColIndex){
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inputStream)
                .registerWriteHandler(new CustomMergeWriteHandler(mergeRowIndex, mergeColIndex))
                .registerWriteHandler(new CustomMergeStrategy())
                .build()) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download;");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(excelName, "UTF-8"));
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            for (String key : map.keySet()) {
                Object value = map.get(key);
                if (value instanceof List){
                    List collection = (List) value;
                    excelWriter.fill(new FillWrapper(key, collection), fillConfig, writeSheet);
                } else {
                    excelWriter.fill(value, writeSheet);
                }
            }
        }catch (Exception e){
            throw new TRSException("使用easyexcel导出失败：" + excelName);
        }
    }

    /**
     *  根据模板导出excel
     *
     * @param outputStream outputStream
     * @param excelName 导出的excel名字
     * @param inputStream 模板的输入流
     * @param map 导出的数据
     * @param mergeRowIndex 从多少行开始合并单元格
     * @param mergeColIndex 需要合并单元格的列
     */
    public static void writeTemplateExcel(OutputStream outputStream, String excelName, InputStream inputStream, Map<String, Object> map, int mergeRowIndex, int[] mergeColIndex){
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream).withTemplate(inputStream)
                .registerWriteHandler(new CustomMergeWriteHandler(mergeRowIndex, mergeColIndex))
                .registerWriteHandler(new CustomMergeStrategy())
                .build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            for (String key : map.keySet()) {
                Object value = map.get(key);
                if (value instanceof List){
                    List collection = (List) value;
                    excelWriter.fill(new FillWrapper(key, collection), fillConfig, writeSheet);
                } else {
                    excelWriter.fill(value, writeSheet);
                }
            }
        }catch (Exception e){
            throw new TRSException("使用easyexcel导出失败：" + excelName);
        }
    }

    /**
     *   导出指定文件到压缩包
     *
     * @param response response
     * @param zipFileName  压缩包名字
     * @param excelName ec
     * @param fileMap  上传的文件 kv格式传递，比如说 file.txt -> byte[]   最后在压缩包存的文件就为 file.xls
     * @throws Exception Exception
     */
    public static void exportZip(HttpServletResponse response, String zipFileName, Map<String, Object> fileMap,
                                 String excelName) throws Exception {

        try(ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            response.reset();
            //设置格式
            response.setContentType("application/x-msdownload");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(zipFileName, "UTF-8"));
            writeZipFile(zos, fileMap,excelName);
            zos.flush();
        } catch (Throwable throwable) {
            throw new Exception("导出压缩包发生错误,请联系管理员处理!");
        }
    }

    /**
     *  将所有文件写入压缩包中
     *
     * @param zos zos
     * @param fileMap fileMap
     * @param excelName ex
     * @throws Exception Exception
     */
    private static void writeZipFile(ZipOutputStream zos, Map<String, Object> fileMap,
                                     String excelName) throws Exception {
        @SuppressWarnings("unchecked")
        HashMap<String, Object> fileZipMap = (HashMap<String, Object>) fileMap.get("files");
        for (Map.Entry<String, Object> entry : fileZipMap.entrySet()) {
            String folderName = entry.getKey();
            @SuppressWarnings("unchecked")
            Map<String, byte[]> filesInFolder = (Map<String, byte[]>) entry.getValue();

            for (Map.Entry<String, byte[]> fileEntry : filesInFolder.entrySet()) {
                String fileName = fileEntry.getKey();
                byte[] fileContent = fileEntry.getValue();

                ZipEntry zipEntry = new ZipEntry(folderName + "/" + fileName);
                zos.putNextEntry(zipEntry);
                zos.write(fileContent);
                zos.closeEntry();
            }
        }
        @SuppressWarnings("unchecked")
        byte[] excel =(byte[]) fileMap.get(excelName);
        zos.putNextEntry(new ZipEntry(excelName));
        zos.write(excel);
        zos.closeEntry();
    }

    }
