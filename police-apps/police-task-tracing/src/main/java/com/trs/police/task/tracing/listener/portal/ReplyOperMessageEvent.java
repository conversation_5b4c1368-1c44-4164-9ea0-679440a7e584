package com.trs.police.task.tracing.listener.portal;

import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.task.tracing.domain.entity.TaskTracingReply;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Description:
 *
 * <AUTHOR>
 * @date: 2024/3/21 20:45
 */
@Getter
public class ReplyOperMessageEvent extends ApplicationEvent {

    private static final long serialVersionUID = -6771700471669306037L;


    private CurrentUser currentUser;

    private TaskTracingReply taskTracingReply;

    private String operType;

    public ReplyOperMessageEvent(Object source, CurrentUser currentUser, TaskTracingReply taskTracingReply, String operType) {
        super(source);
        this.currentUser = currentUser;
        this.taskTracingReply = taskTracingReply;
        this.operType = operType;
    }
}
