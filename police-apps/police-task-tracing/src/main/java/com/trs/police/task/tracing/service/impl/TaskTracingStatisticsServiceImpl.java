package com.trs.police.task.tracing.service.impl;

import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.District;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.task.tracing.common.util.AreaCodeUtil;
import com.trs.police.task.tracing.constant.TaskTracingStatisticsConstant;
import com.trs.police.task.tracing.constant.enums.DbTypeEnum;
import com.trs.police.task.tracing.constant.enums.TaskStatusEnum;
import com.trs.police.task.tracing.domain.dto.TracingStatisticsDto;
import com.trs.police.task.tracing.domain.entity.TaskTracing;
import com.trs.police.task.tracing.domain.vo.TaskTracingStatisticsVO;
import com.trs.police.task.tracing.domain.vo.TracingListVo;
import com.trs.police.task.tracing.mapper.TaskTracingStatisticsMapper;
import com.trs.police.task.tracing.service.TaskTracingStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 挂账统计service
 */
@Service
public class TaskTracingStatisticsServiceImpl implements TaskTracingStatisticsService {

    @Autowired
    private DictService dictService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private TaskTracingStatisticsMapper taskTracingStatisticsMapper;

    private Map<Long,String> tracingTypeMap;

    private Map<Long,String> tracingStatusMap;

    /**
     * 初始化盯办状态和类型
     */
    @PostConstruct
    public void init(){
        List<DictDto> tracingType = dictService.getDictListByType("dy_task_tracing_type");
        tracingTypeMap = tracingType.stream().collect(Collectors.toMap(e->e.getCode(),e->e.getName()));
        List<DictDto> tracingStatus = dictService.getDictListByType("dy_task_tracing_status");
        tracingStatusMap = tracingStatus.stream().collect(Collectors.toMap(e->e.getCode(),e->e.getName()));
    }

    @Override
    public TaskTracingStatisticsVO typeStatistics(TracingStatisticsDto dto) {
        District district = taskTracingStatisticsMapper.selectDistrictByCode(dto.getAreaCode());
        String areaCodePrefix = Objects.nonNull(district) ? AreaCodeUtil.getAreaCodePre(district,dto.getAreaCode()) : "";
        //获取在办中的数量
        List<TracingListVo> zbzList = taskTracingStatisticsMapper.tracingTypeCount(dto, TaskStatusEnum.DOING.getCode(), areaCodePrefix);
        Map<Long,Long> zbzMap = !CollectionUtils.isEmpty(zbzList) ? zbzList.stream()
                .collect(Collectors.toMap(e->Long.valueOf(e.getKey()),e->e.getCount())) : new HashMap<>();
        //获取已办结的数量
        List<TracingListVo> ybjList = taskTracingStatisticsMapper.tracingTypeCount(dto, TaskStatusEnum.DONE.getCode(), areaCodePrefix);
        Map<Long,Long> ybjMap = !CollectionUtils.isEmpty(ybjList) ? ybjList.stream()
                .collect(Collectors.toMap(e->Long.valueOf(e.getKey()),e->e.getCount())) : new HashMap<>();
        //获取已销账的数量
        List<TracingListVo> yxzList = taskTracingStatisticsMapper.tracingTypeCount(dto, TaskStatusEnum.CLOSED.getCode(), areaCodePrefix);
        Map<Long,Long> yxzMap = !CollectionUtils.isEmpty(yxzList) ? yxzList.stream()
                .collect(Collectors.toMap(e->Long.valueOf(e.getKey()),e->e.getCount())) : new HashMap<>();
        //获取待审核的数量
        List<TracingListVo> dshList = taskTracingStatisticsMapper.tracingTypeCount(dto, TaskStatusEnum.APPROVING.getCode(), areaCodePrefix);
        Map<Long,Long> dshMap = !CollectionUtils.isEmpty(dshList) ? dshList.stream()
                .collect(Collectors.toMap(e->Long.valueOf(e.getKey()),e->e.getCount())) : new HashMap<>();
        //获取已驳回的数量
        List<TracingListVo> ybhList = taskTracingStatisticsMapper.tracingTypeCount(dto, TaskStatusEnum.REJECTED.getCode(), areaCodePrefix);
        Map<Long,Long> ybhMap = !CollectionUtils.isEmpty(ybhList) ? ybhList.stream()
                .collect(Collectors.toMap(e->Long.valueOf(e.getKey()),e->e.getCount())) : new HashMap<>();
        List<TracingListVo> list = new ArrayList<>();
        for (Long type : tracingTypeMap.keySet()) {
            TracingListVo vo = new TracingListVo();
            vo.setKey(type.toString());
            vo.setShowName(tracingTypeMap.get(type));
            Long total = 0L;
            Map<String,Long> countMap = new HashMap<>();
            countMap.put(TaskStatusEnum.DOING.getName(),Objects.nonNull(zbzMap.get(type)) ? zbzMap.get(type) : 0L);
            total += Objects.nonNull(zbzMap.get(type)) ? zbzMap.get(type) : 0L;
            countMap.put(TaskStatusEnum.DONE.getName(),Objects.nonNull(ybjMap.get(type)) ? ybjMap.get(type) : 0L);
            total += Objects.nonNull(ybjMap.get(type)) ? ybjMap.get(type) : 0L;
            countMap.put(TaskStatusEnum.CLOSED.getName(),Objects.nonNull(yxzMap.get(type)) ? yxzMap.get(type) : 0L);
            total += Objects.nonNull(yxzMap.get(type)) ? yxzMap.get(type) : 0L;
            countMap.put(TaskStatusEnum.APPROVING.getName(),Objects.nonNull(dshMap.get(type)) ? dshMap.get(type) : 0L);
            total += Objects.nonNull(dshMap.get(type)) ? dshMap.get(type) : 0L;
            countMap.put(TaskStatusEnum.REJECTED.getName(),Objects.nonNull(ybhMap.get(type)) ? ybhMap.get(type) : 0L);
            total += Objects.nonNull(ybhMap.get(type)) ? ybhMap.get(type) : 0L;
            countMap.put("全部",total);
            vo.setTracingMap(countMap);
            list.add(vo);
        }
        //设置状态，回去查询的挂账中更新时间最新的一条
        List<String> status = Arrays.asList(TaskStatusEnum.DOING.getCode(),TaskStatusEnum.DONE.getCode(),TaskStatusEnum.CLOSED.getCode(),
                TaskStatusEnum.APPROVING.getCode(),TaskStatusEnum.REJECTED.getCode())
                .stream().map(e->String.valueOf(e)).collect(Collectors.toList());
        TaskTracingStatisticsVO vo = buildReturnVo(dto, areaCodePrefix, list,status,null);
        return vo;
    }

    @Override
    public TaskTracingStatisticsVO statusStatistics(TracingStatisticsDto dto) {
        District district = taskTracingStatisticsMapper.selectDistrictByCode(dto.getAreaCode());
        //设置应该统计挂账状态
        List<String> status = Arrays.asList(TaskStatusEnum.DOING.getCode(),TaskStatusEnum.DONE.getCode(),TaskStatusEnum.CLOSED.getCode(),
                TaskStatusEnum.APPROVING.getCode(),TaskStatusEnum.REJECTED.getCode()).stream()
                .map(e->String.valueOf(e)).collect(Collectors.toList());
        //获取有效区域前缀
        String areaCodePrefix = Objects.nonNull(district) ? AreaCodeUtil.getAreaCodePre(district,dto.getAreaCode()) : "";
        //获取各状态数量
        List<TracingListVo> statusCountList = taskTracingStatisticsMapper.tracingStatusCount(dto, areaCodePrefix,status);
        Map<Long,Long> statusCount = !CollectionUtils.isEmpty(statusCountList) ? statusCountList.stream()
                .collect(Collectors.toMap(e->Long.valueOf(e.getKey()),e->e.getCount())) : new HashMap<>();
        List<TracingListVo> list = new ArrayList<>();
        for (Long statu : tracingStatusMap.keySet()) {
            //若状态中不包含此statu，则除去改statu
            if (!status.contains(statu.toString())){
                continue;
            }
            TracingListVo vo = new TracingListVo();
            vo.setKey(statu.toString());
            vo.setShowName(tracingStatusMap.get(statu));
            Map<String,Long> countMap = new HashMap<>();
            vo.setCount(Objects.nonNull(statusCount.get(statu)) ? statusCount.get(statu) : 0L);
            countMap.put("count",Objects.nonNull(statusCount.get(statu)) ? statusCount.get(statu) : 0L);
            list.add(vo);
        }
        if (Objects.nonNull(dto.getOrderKey()) && dto.getOrderKey().equals("totalCount")){
            list = listSorted(dto,list);
        }
        TaskTracingStatisticsVO vo = buildReturnVo(dto, areaCodePrefix, list,status,null);
        return vo;
    }

    @Override
    public TaskTracingStatisticsVO publishTrend(TracingStatisticsDto dto) {
        //设置应该统计挂账状态
        List<String> status = Arrays.asList(TaskStatusEnum.DOING.getCode(),TaskStatusEnum.DONE.getCode(),TaskStatusEnum.CLOSED.getCode(),
                        TaskStatusEnum.APPROVING.getCode(),TaskStatusEnum.REJECTED.getCode()).stream()
                .map(e->String.valueOf(e)).collect(Collectors.toList());
        District district = taskTracingStatisticsMapper.selectDistrictByCode(dto.getAreaCode());
        String areaCodePrefix = Objects.nonNull(district) ? AreaCodeUtil.getAreaCodePre(district,dto.getAreaCode()) : "";
        //获取日期使用情况
        List<TracingListVo> list = getDateUseInfo(dto, areaCodePrefix, status,-1);
        TaskTracingStatisticsVO vo = buildReturnVo(dto, areaCodePrefix, list, status,null);
        return vo;
    }

    @Override
    public TaskTracingStatisticsVO finishTrend(TracingStatisticsDto dto) {
        District district = taskTracingStatisticsMapper.selectDistrictByCode(dto.getAreaCode());
        String areaCodePrefix = Objects.nonNull(district) ? AreaCodeUtil.getAreaCodePre(district,dto.getAreaCode()) : "";
        List<String> status = Arrays.asList(String.valueOf(TaskStatusEnum.DONE.getCode()));
        List<TracingListVo> list = getDateUseInfo(dto, areaCodePrefix, status,TaskStatusEnum.DONE.getCode());
        TaskTracingStatisticsVO vo = buildReturnVo(dto, areaCodePrefix, list, status,null);
        return vo;
    }

    private List<TracingListVo> getDateUseInfo(TracingStatisticsDto dto, String areaCodePrefix, List<String> status,
                                               Integer statu) {
        //办结盯办，只查statu为已办结的盯办
        List<TaskTracing> jwdbList = taskTracingStatisticsMapper.tracingDateCount(dto, areaCodePrefix
                , status,DbTypeEnum.JWDB.getCode(),statu);
        List<TaskTracing> zwdbList = taskTracingStatisticsMapper.tracingDateCount(dto, areaCodePrefix
                , status,DbTypeEnum.ZWDB.getCode(),statu);
        //判断开始日期是否是同一天
        boolean result = timeRangeIsOneDay(dto);
        //获取起始截至日期之间的所有日期
        List<LocalDateTime> dateList = result ? getHourDateByDto(dto) : getDateByDto(dto);
        List<TracingListVo> list = new ArrayList<>();
        list = result ? getCountItemsByHour(list,dateList,jwdbList,zwdbList)
                : getCountItems(list,dateList,jwdbList,zwdbList);
        return list;
    }

    @Override
    public TaskTracingStatisticsVO deptPublishInfo(TracingStatisticsDto dto) {
        //设置应该统计挂账状态
        List<String> status = Arrays.asList(TaskStatusEnum.DOING.getCode(),TaskStatusEnum.DONE.getCode(),TaskStatusEnum.CLOSED.getCode(),
                        TaskStatusEnum.APPROVING.getCode(),TaskStatusEnum.REJECTED.getCode()).stream()
                .map(e->String.valueOf(e)).collect(Collectors.toList());
        //获取有效区域代码前缀
        District district = taskTracingStatisticsMapper.selectDistrictByCode(dto.getAreaCode());
        String areaCodePrefix = Objects.nonNull(district) ? AreaCodeUtil.getAreaCodePre(district,dto.getAreaCode()) : "";
        Integer areaLevel = Objects.nonNull(district) ? district.getLevel() : TaskTracingStatisticsConstant.CITY_LEVEL;
        List<Integer> deptTypes;
        //获取部门类型
        switch (areaLevel){
            case 2:
                deptTypes = Arrays.asList(TaskTracingStatisticsConstant.QX,TaskTracingStatisticsConstant.SJ);
                break;
            default:
                deptTypes = Arrays.asList(TaskTracingStatisticsConstant.POLICE);
                break;
        }
        //获取部门办结情况
        List<TracingListVo> resultList = getDeptInfo(dto,areaCodePrefix,deptTypes);
        TaskTracingStatisticsVO vo = buildReturnVo(dto, areaCodePrefix, resultList, status,deptTypes);
        return vo;
    }

    @Override
    public TaskTracingStatisticsVO deptFinishInfo(TracingStatisticsDto dto) {
        //设置筛选状态
        List<String> status = Arrays.asList(String.valueOf(TaskStatusEnum.DONE.getCode()));
        //获取有效区域前缀
        District district = taskTracingStatisticsMapper.selectDistrictByCode(dto.getAreaCode());
        String areaCodePrefix = Objects.nonNull(district) ? AreaCodeUtil.getAreaCodePre(district,dto.getAreaCode()) : "";
        Integer areaLevel = Objects.nonNull(district) ? district.getLevel() : TaskTracingStatisticsConstant.CITY_LEVEL;
        List<Integer> deptTypes;
        //获取部门类型
        switch (areaLevel){
            case 2:
                deptTypes = Arrays.asList(TaskTracingStatisticsConstant.CITY_YEWU_POLICE);
                break;
            default:
                deptTypes = Arrays.asList(TaskTracingStatisticsConstant.QX_YEWU_POLICE);
                break;
        }
        //获取部门办结情况
        List<TracingListVo> resultList = getDeptInfo(dto,areaCodePrefix,deptTypes);
        TaskTracingStatisticsVO vo = buildReturnVo(dto, areaCodePrefix, resultList, status,deptTypes);
        return vo;
    }


    /**
     * 构造返回vo
     *
     * @param dto 参数
     * @param areaCodePrefix 区域代码前缀
     * @param list 查表数据
     * @param status 状态
     * @param deptTypes 部门组织类型
     * @return 结果vo
     */
    private TaskTracingStatisticsVO buildReturnVo(TracingStatisticsDto dto, String areaCodePrefix, List<TracingListVo> list
                ,List<String> status,List<Integer> deptTypes) {
        TaskTracingStatisticsVO vo = new TaskTracingStatisticsVO();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter returnFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        vo.setTracingList(list);
        vo.setTimeRange((Objects.nonNull(dto.getStartTime()) && Objects.nonNull(dto.getStartTime()))
                ? (returnFormat.format(LocalDateTime.parse(dto.getStartTime(),fmt)) + "~" + returnFormat.format(LocalDateTime.parse(dto.getEndTime(),fmt))) : "");
        vo.setUpdateTime(taskTracingStatisticsMapper.getUpdateTime(dto, areaCodePrefix,status,deptTypes));
        return vo;
    }

    /**
     * 排序
     *
     * @param dto 参数
     * @param list 查表集合
     * @return 结果
     */
    private List<TracingListVo> listSorted(TracingStatisticsDto dto, List<TracingListVo> list) {
        list = StringUtils.isEmpty(dto.getOrderType()) ? list
                : (dto.getOrderType().equals("asc"))
                ? list.stream().sorted(Comparator.comparingLong(TracingListVo::getCount)).collect(Collectors.toList())
                : list.stream().sorted(Comparator.comparingLong(TracingListVo::getCount).reversed()).collect(Collectors.toList());
        return list;
    }

    /**
     * 判断开始日期和结束日期是否是同一天
     *
     * @param dto 参数
     * @return 结果
     */
    private boolean timeRangeIsOneDay(TracingStatisticsDto dto) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(dto.getStartTime(), formatter);
        LocalDateTime endTime = LocalDateTime.parse(dto.getEndTime(), formatter);
        return startTime.toLocalDate().equals(endTime.toLocalDate());
    }

    /**
     * 获取一天的24小时时段
     *
     * @param dto cs
     * @return jg
     */
    private List<LocalDateTime> getHourDateByDto(TracingStatisticsDto dto) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDate = LocalDateTime.parse(dto.getStartTime(), formatter);
        LocalDateTime endDate = LocalDateTime.parse(dto.getEndTime(), formatter);
        List<LocalDateTime> list = new ArrayList<>();
        while (startDate.isBefore(endDate)){
            startDate = startDate.plusHours(1);
            list.add(startDate);
        }
        return list;
    }

    /**
     * 获取开始截止日期之间的日期
     *
     * @param dto cs
     * @return jg
     */
    private List<LocalDateTime> getDateByDto(TracingStatisticsDto dto) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDate = LocalDateTime.parse(dto.getStartTime(), formatter);
        LocalDateTime endDate = LocalDateTime.parse(dto.getEndTime(), formatter);
        List<LocalDateTime> dateTimeList = new ArrayList<>();
        LocalDate currentDate = startDate.toLocalDate();
        LocalTime startTime = LocalTime.of(23,59,59); // 每天的开始时间

        while (!currentDate.isAfter(endDate.toLocalDate())) {
            LocalDateTime dateTime = LocalDateTime.of(currentDate, startTime);
            dateTimeList.add(dateTime);
            currentDate = currentDate.plusDays(1);
        }
        return dateTimeList;
    }

    private List<TracingListVo> getCountItems(List<TracingListVo> list, List<LocalDateTime> dateList, List<TaskTracing> jwdbList,
                                              List<TaskTracing> zwdbList) {
        jwdbList = CollectionUtils.isEmpty(jwdbList) ? new ArrayList<>() : jwdbList;
        DateTimeFormatter formatterDay = DateTimeFormatter.ofPattern("MM-dd");
        DateTimeFormatter formatterYear = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<TaskTracing> result;
        Long total = 0L;
        for (LocalDateTime localDateTime : dateList) {
            TracingListVo vo = new TracingListVo();
            vo.setKey(localDateTime.format(formatterYear));
            vo.setShowName(localDateTime.format(formatterDay));
            Map<String,Long> map = new HashMap<>();
            result = jwdbList.stream().filter(e->e.getCreateTime().isBefore(localDateTime)
                    && e.getCreateTime().isAfter(localDateTime.minusDays(1))).collect(Collectors.toList());
            map.put("jwdb",!CollectionUtils.isEmpty(result) ? result.size() : 0L);
            total += !CollectionUtils.isEmpty(result) ? result.size() : 0L;
            result = zwdbList.stream().filter(e->e.getCreateTime().isBefore(localDateTime)
                    && e.getCreateTime().isAfter(localDateTime.minusDays(1))).collect(Collectors.toList());
            map.put("zwdb",!CollectionUtils.isEmpty(result) ? result.size() : 0L);
            total += !CollectionUtils.isEmpty(result) ? result.size() : 0L;
            vo.setCount(total);
            vo.setTracingMap(map);
            list.add(vo);
            total = 0L;
        }
        return list;
    }

    private List<TracingListVo> getCountItemsByHour(List<TracingListVo> list, List<LocalDateTime> dateList, List<TaskTracing> jwdbList,
                                                    List<TaskTracing> zwdbList) {
        jwdbList = CollectionUtils.isEmpty(jwdbList) ? new ArrayList<>() : jwdbList;
        DateTimeFormatter formatterHour = DateTimeFormatter.ofPattern("HH:mm");
        DateTimeFormatter formatterDayHour = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
        List<TaskTracing> result;
        Long total = 0L;
        for (LocalDateTime localDateTime : dateList) {
            TracingListVo vo = new TracingListVo();
            vo.setKey(localDateTime.format(formatterDayHour));
            vo.setShowName(localDateTime.format(formatterHour));
            Map<String,Long> map = new HashMap<>();
            //设置警务督办数量
            result = jwdbList.stream().filter(e->e.getCreateTime().isBefore(localDateTime)
                    && e.getCreateTime().isAfter(localDateTime.minusHours(1))).collect(Collectors.toList());
            map.put("jwdb",!CollectionUtils.isEmpty(result) ? result.size() : 0L);
            total += !CollectionUtils.isEmpty(result) ? result.size() : 0L;
            //设置政务督办数量
            result = zwdbList.stream().filter(e->e.getCreateTime().isBefore(localDateTime)
                    && e.getCreateTime().isAfter(localDateTime.minusHours(1))).collect(Collectors.toList());
            map.put("zwdb",!CollectionUtils.isEmpty(result) ? result.size() : 0L);
            total += !CollectionUtils.isEmpty(result) ? result.size() : 0L;
            vo.setCount(total);
            vo.setTracingMap(map);
            list.add(vo);
            total = 0L;
        }
        return list;
    }

    /**
     * 获取部门情况（发布挂账、办结挂账）
     *
     * @param dto dto
     * @param areaCodePrefix areaCodePrefix
     * @param deptTypes 部门类型
     * @return jieguo
     */
    private List<TracingListVo> getDeptInfo(TracingStatisticsDto dto,String areaCodePrefix,
                                                   List<Integer> deptTypes){
        Map<String,String> showNameMap;
        List<DeptDto> depts = permissionService.getDeptByTypes(deptTypes);
        depts = CollectionUtils.isEmpty(depts) ? new ArrayList<>() : depts.stream()
                .filter(e->e.getCode().startsWith(areaCodePrefix)).collect(Collectors.toList());
        showNameMap = CollectionUtils.isEmpty(depts) ? new HashMap<>() : depts.stream().collect(Collectors.toMap(
                e->String.valueOf(e.getId()),DeptDto::getShortName
        ));
        Boolean isDept = true;
        //获取发布list
        List<String> publishStatus = Arrays.asList(TaskStatusEnum.DOING.getCode(),TaskStatusEnum.DONE.getCode(),TaskStatusEnum.CLOSED.getCode(),
                        TaskStatusEnum.APPROVING.getCode(),TaskStatusEnum.REJECTED.getCode()).stream()
                .map(e->String.valueOf(e)).collect(Collectors.toList());
        List<TracingListVo> publishList = taskTracingStatisticsMapper.getDeptStatisticCount(dto,areaCodePrefix,publishStatus,
                -1,deptTypes);
        Map<String, Long> publishMap = CollectionUtils.isEmpty(publishList) ? new HashMap<>() : publishList.stream()
                .collect(Collectors.toMap(e -> e.getKey(), e -> e.getCount()));
        //获取办结list
        List<String> finishedStatus = Arrays.asList(TaskStatusEnum.DONE.getCode()).stream()
                .map(e->String.valueOf(e)).collect(Collectors.toList());
        List<TracingListVo> finishedList = taskTracingStatisticsMapper.getDeptStatisticCount(dto,areaCodePrefix,finishedStatus,
                TaskStatusEnum.DONE.getCode(),deptTypes);
        Map<String, Long> finishedMap = CollectionUtils.isEmpty(finishedList) ? new HashMap<>() : finishedList.stream()
                .collect(Collectors.toMap(e -> e.getKey(), e -> e.getCount()));
        //结果list
        List<TracingListVo> resultList = new ArrayList<>();
        //如果传入了deptId，则只展示一个派出所
        if (Objects.nonNull(dto.getDeptId())){
            String key = String.valueOf(dto.getDeptId());
            TracingListVo vo = new TracingListVo();
            vo.setKey(key);
            vo.setIsDept(isDept);
            vo.setShowName(showNameMap.get(key));
            Map<String,Long> map = new HashMap<>();
            map.put("publish",Objects.nonNull(publishMap.get(key)) ? publishMap.get(key) : 0);
            map.put("finish",Objects.nonNull(finishedMap.get(key)) ? finishedMap.get(key) : 0);
            vo.setTracingMap(map);
            resultList.add(vo);
            return resultList;
        }
        //若未传deptId，则展示当前区域下的派出所
        for (String key : showNameMap.keySet()) {
            TracingListVo vo = new TracingListVo();
            vo.setKey(key);
            vo.setIsDept(isDept);
            vo.setShowName(showNameMap.get(key));
            Map<String,Long> map = new HashMap<>();
            map.put("publish",Objects.nonNull(publishMap.get(key)) ? publishMap.get(key) : 0);
            map.put("finish",Objects.nonNull(finishedMap.get(key)) ? finishedMap.get(key) : 0);
            vo.setTracingMap(map);
            resultList.add(vo);
        }
        return resultList;
    }

}
