<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.task.tracing.mapper.TaskTracingProcessReplyMapper">
    <resultMap id="TaskTracingProcessReplyResultMap"
        type="com.trs.police.task.tracing.domain.entity.TaskTracingReply">
        <!--@mbg.generated-->
        <!--@Table t_task_tracing_reply-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_dept_id" jdbcType="BIGINT" property="createDeptId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="update_dept_id" jdbcType="BIGINT" property="updateDeptId"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="process_id" jdbcType="BIGINT" property="processId"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="reject" jdbcType="VARCHAR" property="reject"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="done" jdbcType="VARCHAR" property="done"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="cancel_done" jdbcType="VARCHAR" property="cancelDone"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="attachments" jdbcType="VARCHAR" property="attachments"
            typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="responsible_leader" jdbcType="VARCHAR" property="responsibleLeader"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="selectByTaskId" resultMap="TaskTracingProcessReplyResultMap">
        select *
        from t_task_tracing_reply r
        where r.task_id = #{taskId}
        order by create_time desc
    </select>
    <delete id="deleteByTaskId">
        delete
        from t_task_tracing_reply
        where task_id = #{taskId}
    </delete>
</mapper>