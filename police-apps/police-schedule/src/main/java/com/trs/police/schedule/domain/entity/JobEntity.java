package com.trs.police.schedule.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.constant.enums.DelayMessageTypeEnum;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.vo.message.DelayMessage;
import java.time.Duration;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@TableName(value = "t_schedule_job", autoResultMap = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long configId;
    /**
     * 任务所属模块
     */
    private OperateModule module;
    /**
     * 任务操作
     */
    private DelayMessageTypeEnum operation;
    /**
     * cron表达式
     */
    private Long relatedId;
    /**
     * job类全路径
     */
    private LocalDateTime timeLimit;

    /**
     * 生成DelayMessage
     *
     * @return message
     */
    public DelayMessage toMessage() {
        Duration duration = Duration.between(LocalDateTime.now(), timeLimit);
        DelayMessage delayMessage = new DelayMessage();
        delayMessage.setJobId(id);
        delayMessage.setOperation(operation);
        delayMessage.setService(module);
        delayMessage.setRelatedId(relatedId);
        delayMessage.setDelayLength((int) duration.toMillis());
        return delayMessage;
    }
}
