package com.trs.police.authorize.authorization.tymh;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/12/7 14:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TymhTokenDTO {
    private String id;
    private String loginName;
    private String userName;
    private String sex;
    private String idEntityCard;
    private String inDustRialId;
    private String orgCode;
    private String orgName;
    private String positionName;
    private String mobile;
    private String scope;
    private String userStatus;
    private LocalDateTime requestTime;
}
