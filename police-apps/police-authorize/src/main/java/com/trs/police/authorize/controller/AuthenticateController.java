package com.trs.police.authorize.controller;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.trs.police.authorize.authorization.AbstractYsAuthenticationToken;
import com.trs.police.authorize.domain.entity.User;
import com.trs.police.authorize.mapper.DeptMapper;
import com.trs.police.authorize.service.UserService;
import com.trs.police.common.core.utils.RemoteAddrUtil;
import com.trs.police.common.core.vo.log.LoginLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.naming.AuthenticationException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.Objects;

/**
 * 用户接口
 *
 * <AUTHOR>
 * @date 2022/09/15
 */
@RestController
@RequestMapping("/user")
@Slf4j
@Validated
public class AuthenticateController {

    @Resource
    private TokenStore tokenStore;

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private UserService userService;

    /**
     * 切换当前登录部门
     *
     * @param request Http请求
     * @param deptId  切换的部门id
     * @throws AuthenticationException 认证异常
     */
    @RequestMapping("/switch-dept")
    public void switchDept(HttpServletRequest request,
        @RequestParam("deptId") @NotBlank(message = "部门id为空！") String deptId)
        throws AuthenticationException {
        String accessToken = request.getHeader("Authorization").replaceAll("[Bb]earer ", "");
        if (!StringUtils.isNotBlank(accessToken)) {
            throw new AuthenticationException("token 不能为空！");
        }
        OAuth2AccessToken oAuth2AccessToken = tokenStore.readAccessToken(accessToken);
        if (Objects.isNull(oAuth2AccessToken)) {
            throw new AuthenticationException("token 已失效！");
        }
        OAuth2Authentication authentication = tokenStore.readAuthentication(oAuth2AccessToken);
        if (Objects.isNull(authentication)) {
            throw new AuthenticationException("token 已失效！");
        }
        AbstractYsAuthenticationToken userAuthentication = (AbstractYsAuthenticationToken) authentication.getUserAuthentication();
        Object principal = userAuthentication.getPrincipal();
        if (Objects.isNull(principal)) {
            throw new AuthenticationException("登录信息为空！");
        }

        User user = (User) principal;
        if (deptMapper.findAllByUserId(user.getId()).stream()
                .noneMatch(dept -> dept.getId().equals(Long.valueOf(deptId)))) {
            throw new AuthenticationException("部门id错误！");
        }
        log.info(String.format("----access_token %s 切换部门: %s", accessToken, deptId));
        userAuthentication.setDefaultUnitId(deptId);
        tokenStore.storeAccessToken(oAuth2AccessToken, authentication);
        LoginLogVO loginLogVO = new LoginLogVO();
        loginLogVO.setUserName(user.getUsername());
        loginLogVO.setType("切换部门");
        loginLogVO.setDetail("登录成功");
        loginLogVO.setIpAddress(RemoteAddrUtil.getRemoteAddress(request));
        userService.recordLoginLog(loginLogVO, authentication);
    }
}
