package com.trs.police.authorize.authorization.weixin;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.police.authorize.authorization.AbstractYsAuthenticationToken;
import com.trs.police.authorize.domain.entity.Dept;
import com.trs.police.authorize.domain.entity.User;
import com.trs.police.authorize.mapper.DeptMapper;
import com.trs.police.authorize.properties.ClientProperties;
import com.trs.police.common.core.entity.ResponseMessage;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.log.LoginLogVO;
import com.trs.police.common.openfeign.starter.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.UnapprovedClientAuthenticationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;

/**
 * 授权成功和失败后的处理器
 *
 * <AUTHOR>
 * @date 2019-03-01 14:34
 */
@Component
@Slf4j
public class WeixinLoginSuccessAndFailureHandler extends SimpleUrlAuthenticationSuccessHandler
        implements AuthenticationSuccessHandler, AuthenticationFailureHandler {

    private final RequestCache requestCache = new HttpSessionRequestCache();
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Resource
    private ClientDetailsService clientDetailsService;

    @Resource
    private ClientProperties clientProperties;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private DeptMapper deptMapper;


    @Lazy
    @Resource
    private AuthorizationServerTokenServices authorizationServerTokenServices;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) throws IOException {
        log.debug("登录失败", exception);
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        response.setContentType("application/json;charset=utf-8");
        response.getWriter().write(
                OBJECT_MAPPER.writeValueAsString(new ResponseMessage().message(exception.getMessage()).success(false)));
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws ServletException, IOException {
        TokenRequest tokenRequest;

        // 2. 通过 ClientDetailsService 获取 ClientDetails
        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientProperties.getClientId());

        // 3. 校验 ClientId和 ClientSecret的正确性
        if (clientDetails == null) {
            throw new UnapprovedClientAuthenticationException(
                    "clientId:" + clientProperties.getClientId() + "对应的信息不存在");
        } else if (!new BCryptPasswordEncoder().matches(clientProperties.getClientSecret(),
                clientDetails.getClientSecret())) {
            throw new UnapprovedClientAuthenticationException("clientSecret不正确");
        } else {
            // 4. 通过 TokenRequest构造器生成 TokenRequest
            tokenRequest = new TokenRequest(Collections.emptyMap(), clientProperties.getClientId(),
                    clientDetails.getScope(), "custom");
        }
        // 5. 通过 TokenRequest的 createOAuth2Request方法获取 OAuth2Request
        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);
        // 6. 通过 Authentication和 OAuth2Request构造出 OAuth2Authentication
        OAuth2Authentication auth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);

        // 7. 通过 AuthorizationServerTokenServices 生成 OAuth2AccessToken
        OAuth2AccessToken token = authorizationServerTokenServices.createAccessToken(auth2Authentication);
        // 记录登录日志
        recordLoginLog(auth2Authentication);
        String redirectUri = request.getParameter("redirect_uri");
        if (!StringUtils.isEmpty(redirectUri)) {
            response.sendRedirect(redirectUriWithToken(redirectUri, token));
            return;
        }
        // 8. 返回 Token
        log.info("登录成功");
        response.setContentType("application/json;charset=UTF-8");
        try (final PrintWriter writer = response.getWriter()) {
            String text = OBJECT_MAPPER.writeValueAsString(token);
            writer.write(text);
        } catch (Exception e) {
            log.warn("返回错误！", e);
            throw e;
        }
    }

    private void recordLoginLog(OAuth2Authentication auth2Authentication) {
        //生成日志
        LoginLogVO loginLogVO = createLoginLogVO(auth2Authentication);
        //记录日志
        try {
            operationLogService.createLoginLog(loginLogVO);
        } catch (Exception e) {
            log.error("日志创建失败" + e.getMessage());
        }
    }

    private LoginLogVO createLoginLogVO(OAuth2Authentication auth2Authentication) {
        //取出token
        AbstractYsAuthenticationToken token = getToken(auth2Authentication);
        if (Objects.isNull(token)) {
            return null;
        }
        User user = getUser(auth2Authentication);
        Dept dept = getDept(token);
        LoginLogVO loginLogVO = new LoginLogVO();
        loginLogVO.setCreateTime(LocalDateTime.now());
        loginLogVO.setDeptId(dept.getId());
        loginLogVO.setDeptName(dept.getName());
        loginLogVO.setDetail("登录成功");
        loginLogVO.setType(token.getLoginType());
        loginLogVO.setIdCard(user.getIdNumber());
        loginLogVO.setUserName(user.getUsername());
        loginLogVO.setTrueName(user.getRealName());
        loginLogVO.setIdCard(user.getIdNumber());
        JsonNode jsonNode = JsonUtil
                .parseJsonNode(JsonUtil.toJsonString(auth2Authentication.getUserAuthentication().getDetails()));
        if (Objects.nonNull(jsonNode)) {
            String ipAddress = jsonNode.get("remoteAddress").asText();
            loginLogVO.setIpAddress(ipAddress);
        }
        return loginLogVO;
    }

    private AbstractYsAuthenticationToken getToken(OAuth2Authentication auth2Authentication) {
        Authentication userAuthentication = auth2Authentication.getUserAuthentication();
        if (userAuthentication instanceof AbstractYsAuthenticationToken) {
            return (AbstractYsAuthenticationToken) userAuthentication;
        }
        return null;
    }

    private Dept getDept(AbstractYsAuthenticationToken token) {
        return deptMapper.selectById(Long.valueOf(token.getDefaultUnitId()));
    }

    private User getUser(OAuth2Authentication auth2Authentication) {
        Object principal = auth2Authentication.getPrincipal();
        //用户信息
        return (User) principal;
    }

    /**
     * 将重定向地址带上token
     *
     * @param redirectUri 重定向地址
     * @param token       token
     * @return 带token的重定向地址
     */
    private String redirectUriWithToken(String redirectUri, OAuth2AccessToken token) {
        if (redirectUri.contains("?")) {
            return String.format("%s&wxtoken=%s", redirectUri, token.getValue());
        }

        return String.format("%s?wxtoken=%s", redirectUri, token.getValue());
    }
}
