package com.trs.police.authorize.authorization.pwd;

import com.trs.police.authorize.domain.PwdLoginVO;
import com.trs.police.common.core.utils.JsonUtil;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.trs.police.common.core.utils.RemoteAddrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

/**
 * <AUTHOR>
 */
@Slf4j
public class PwdAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    /**
     * 构造方法
     */
    public PwdAuthenticationFilter() {
        super(new AntPathRequestMatcher("/login/pwd"));
    }

    @Override
    public Authentication attemptAuthentication(
        HttpServletRequest httpServletRequest,
        HttpServletResponse httpServletResponse)
        throws AuthenticationException {
        PwdLoginVO pwdLoginVO;
        try {
            String collect = httpServletRequest.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            pwdLoginVO = JsonUtil.parseObject(collect, PwdLoginVO.class);
        } catch (Exception e) {
            log.error("获取登陆信息失败", e);
            throw new InternalAuthenticationServiceException("获取登陆信息失败！");
        }
        if (Objects.isNull(pwdLoginVO)) {
            log.error("获取登陆信息失败,请求体为空");
            throw new InternalAuthenticationServiceException("获取登陆信息失败！");
        }
        String username = pwdLoginVO.getUsername();
        String password = pwdLoginVO.getPassword();
        String defaultUnitId = pwdLoginVO.getDefaultUnitId();

        if (StringUtils.isNoneBlank(username, password)) {

            PwdAuthenticationToken pwdAuthenticationToken = new PwdAuthenticationToken(username, password,
                defaultUnitId);
            String remoteAddress = RemoteAddrUtil.getRemoteAddress(httpServletRequest);
            log.debug("用户名：{}，密码：{}，默认单位：{}，登录地址：{}", username, password, defaultUnitId, remoteAddress);
            pwdAuthenticationToken.setRemoteAddress(remoteAddress);
            this.setDetails(httpServletRequest, pwdAuthenticationToken);

            return this.getAuthenticationManager().authenticate(pwdAuthenticationToken);
        } else {
            throw new UsernameNotFoundException("用户名和密码不能为空！");
        }
    }

    protected void setDetails(HttpServletRequest request, PwdAuthenticationToken authRequest) {
        authRequest.setDetails(this.authenticationDetailsSource.buildDetails(request));
    }
}
