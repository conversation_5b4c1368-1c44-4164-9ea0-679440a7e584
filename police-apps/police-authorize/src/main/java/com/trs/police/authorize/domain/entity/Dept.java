package com.trs.police.authorize.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.utils.BeanUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织机构信息表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "t_dept")
public class Dept extends AbstractBaseEntity {

    private static final long serialVersionUID = 5002999587408687379L;

    /**
     * 机构代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 机构名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 机构名称简称
     */
    @TableField(value = "short_name")
    private String shortName;

    /**
     * 上级部门id
     */
    @TableField(value = "pid")
    private Long pid;

    /**
     * 警种代码
     */
    @TableField(value = "police_kind")
    private Long policeKind;

    /**
     * 排序使用
     */
    @TableField(value = "show_order")
    private Integer showOrder;

    /**
     * 部门签章图片位置
     */
    @TableField(value = "signature")
    private Long signature;
    /**
     * 区域
     */
    @TableField(value = "district_code")
    private String districtCode;
    /**
     * 状态
     */
    @TableLogic
    @TableField(value = "deleted")
    private Short deleted;
    /**
     * 类型
     */
    @TableField(value = "type")
    private Long type;

    /**
     * 级别
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 部分路径信息
     */
    @TableField(value = "path")
    private String path;

    /**
     * 业务类型
     */
    @TableField(value = "child_type")
    private Long childType;

    /**
     * 构造dto
     *
     * @return dto
     */
    public DeptDto toDto() {
        DeptDto dto = new DeptDto();
        BeanUtil.copyPropertiesIgnoreNull(this, dto);
        return dto;
    }
}
