package com.trs.police.authorize.authorization.tymh;

import com.trs.police.authorize.authorization.AbstractYsAuthenticationToken;
import java.util.Collection;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.SpringSecurityCoreVersion;

/**
 * @author: luoxu
 * @date: 2019/2/1 15:57
 */
@Getter
@Setter
public class TymhAuthenticationToken extends AbstractYsAuthenticationToken {

    private static final long serialVersionUID = SpringSecurityCoreVersion.SERIAL_VERSION_UID;

    private final Object principal;
    private static final String LOGIN_TYPE = "tymh";

    /**
     * 构造方法
     *
     * @param username 用户名
     */
    public TymhAuthenticationToken(final String username) {
        super(null, LOGIN_TYPE);
        this.principal = username;
        setAuthenticated(false);
    }


    /**
     * 构造方法
     *
     * @param principal     凭证
     * @param authorities   权限
     * @param defaultUnitId 默认单位
     */
    public TymhAuthenticationToken(final Object principal, Collection<? extends GrantedAuthority> authorities,
        String defaultUnitId) {
        super(authorities, defaultUnitId, LOGIN_TYPE);
        this.principal = principal;
        super.setAuthenticated(true);
    }


    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }

        super.setAuthenticated(false);
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
    }
}
