package com.trs.police.authorize.config;

import com.trs.police.authorize.authorization.LogoutSuccessHandlerImpl;
import com.trs.police.authorize.authorization.app.AppAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.cas.CasAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.hnd.HndAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.base64.Base64AuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.jdzy.JdzyAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.pki.PkiAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.pwd.PwdAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.remote.YsAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.sms.SmsAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.tymh.TymhAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.weixin.WeixinAuthenticationSecurityConfig;
import com.trs.police.authorize.authorization.yq.YqAuthenticationSecurityConfig;
import com.trs.police.authorize.properties.WebSecurityProperties;
import com.trs.police.authorize.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/08/13
 */
@Order(2)
@EnableWebSecurity
@Slf4j
public class WebSecurityConfigure extends WebSecurityConfigurerAdapter {

    @Resource
    private WebSecurityProperties webSecurityProperties;

    @Resource
    private UserService userService;

    /**
     * 密码加密bean
     *
     * @return B Crypt密码加密器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userService).passwordEncoder(passwordEncoder());
    }

    /**
     * pki网关登录
     */
    @Resource
    private PkiAuthenticationSecurityConfig pkiAuthenticationSecurityConfig;

    /**
     * 用户名密码登录
     */
    @Resource
    private PwdAuthenticationSecurityConfig pwdAuthenticationSecurityConfig;

    /**
     * app登录 采用非对称加密身份证方式登录
     */
    @Resource
    private AppAuthenticationSecurityConfig appAuthenticationSecurityConfig;

    @Resource
    private CasAuthenticationSecurityConfig casAuthenticationSecurityConfig;

    /**
     * client密码登录
     */
    @Resource
    private YqAuthenticationSecurityConfig yqAuthenticationSecurityConfig;

    @Resource
    private TymhAuthenticationSecurityConfig tymhAuthenticationSecurityConfig;

    @Resource
    private JdzyAuthenticationSecurityConfig jdzyAuthenticationSecurityConfig;

    @Resource
    private ResourceServerSecurityConfigurerConfig resourceServerSecurityConfigurerConfig;

    @Resource
    private WeixinAuthenticationSecurityConfig weixinAuthenticationSecurityConfig;

    @Resource
    private HndAuthenticationSecurityConfig hndAuthenticationSecurityConfig;

    @Resource
    private YsAuthenticationSecurityConfig ysAuthenticationSecurityConfig;

    @Resource
    private Base64AuthenticationSecurityConfig base64AuthenticationSecurityConfig;

    @Resource
    private SmsAuthenticationSecurityConfig smsAuthenticationSecurityConfig;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        if(webSecurityProperties.isGxSkipSecurity()){
            http.authorizeRequests().
                    antMatchers("").permitAll();
        }
        http.authorizeRequests()
            // 不需授权即可访问的资源
            .antMatchers("/oauth/token", "/login/**","/user/login", "/error", "/oauth/revoke_token").permitAll()
            //拥有client资源的才可以访问client服务
            .antMatchers("/client/**").access("hasAuthority('client')")
            // 其他所有资源均需要登录后方可访问
            .anyRequest().authenticated()
            // 关闭 csrf 保护
            .and().csrf().disable().httpBasic().disable();
        http.apply(pkiAuthenticationSecurityConfig);
        http.apply(yqAuthenticationSecurityConfig);
        http.apply(pwdAuthenticationSecurityConfig);
        http.apply(appAuthenticationSecurityConfig);
        http.apply(casAuthenticationSecurityConfig);
        http.apply(tymhAuthenticationSecurityConfig);
        http.apply(jdzyAuthenticationSecurityConfig);
        //注册oauth2
//        http.apply(resourceServerSecurityConfigurerConfig.configure());
        //注册微信单点登录认证
        http.apply(weixinAuthenticationSecurityConfig);
        http.apply(hndAuthenticationSecurityConfig);
        http.apply(ysAuthenticationSecurityConfig);
        http.apply(base64AuthenticationSecurityConfig);
        http.apply(smsAuthenticationSecurityConfig);
        http.logout().logoutSuccessHandler(new LogoutSuccessHandlerImpl());
    }

    @Override
    public void configure(WebSecurity web) throws Exception {
        super.configure(web);
        web.debug(webSecurityProperties.isDebug());
    }

}
