package com.trs.police.authorize.authorization.weixin;

import com.alibaba.fastjson.JSON;
import com.trs.police.authorize.domain.entity.User;
import com.trs.police.authorize.service.DeptService;
import com.trs.police.authorize.service.UserService;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.GovWxUtils;
import com.trs.police.common.core.utils.OkHttpUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import java.util.Objects;

import static com.trs.police.common.core.constant.GovWxConstant.USER_INFO;

/**
 * 微信权限校验处理
 *
 * @author: wen.wen
 * @date: 2024/7/15 15:57
 */
@Data
@Slf4j
public class WeixinAuthenticationProvider implements AuthenticationProvider {

    private UserService userService;

    private DeptService deptService;

    private WeixinProperties weixinProperties;

    private GovWxUtils govWxUtils;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {

        WeixinAuthenticationToken token = (WeixinAuthenticationToken) authentication;
        //根据code获取该code对应的用户信息
        String idCard = getIdCardFromWeixin(token);
        User user = userService.loadUserByIdentity(idCard);
        if (Objects.isNull(user)) {
            log.error("idCard={}的用户在系统中不存在", idCard);
            throw new InternalAuthenticationServiceException("用户未注册");
        }
        Long defaultUnitId = userService.findDefaultUnitId(user.getId());

        WeixinAuthenticationToken authenticationToken = new WeixinAuthenticationToken(user, user.getAuthorities(), defaultUnitId.toString());

        authenticationToken.setDetails(token.getDetails());

        return authenticationToken;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return WeixinAuthenticationToken.class.isAssignableFrom(authentication);
    }

    /**
     * 从微信获取身份证号码
     *
     * @param token token
     * @return 身份证
     * @throws AuthenticationException 异常
     */
    private String getIdCardFromWeixin(WeixinAuthenticationToken token) throws AuthenticationException {
        try {
            String code = (String) token.getPrincipal();
            //获取微信的token
            String accessToken;
            if (token.getIsApp()) {
                accessToken = govWxUtils.getGovWxToken(weixinProperties.getAppCorpId(), weixinProperties.getAppCorpSecret(), weixinProperties.getUrl());
            } else {
                accessToken = govWxUtils.getGovWxToken(weixinProperties.getCorpId(), weixinProperties.getCorpSecret(), weixinProperties.getUrl());
            }
            String requestUrl = String.format("%s%s?access_token=%s&code=%s", weixinProperties.getUrl(), USER_INFO, accessToken, code);
            String result = OkHttpUtil.getInstance().getData(requestUrl);
            log.info("调用微信接口获取到的数据为:{}", result);
            return JSON.parseObject(result).getString("UserId");
//            JSONObject userInfo = JSON.parseObject(result);
//            String detailUrl = String.format("%s/cgi-bin/user/getuserdetail?access_token=%S", weixinProperties.getUrl(), wxToken);
//            Map<String, Object> params = new HashMap<>();
//            params.put("user_ticket", userInfo.getString("user_ticket"));
//            String result2 = OkHttpUtil.getInstance().postData(detailUrl, JSON.toJSONString(params));
//            return JSON.parseObject(result2).getString("UserId");
        } catch (Exception e) {
            throw new TRSException("获取微信用户信息发生异常", e);
        }
    }
}
