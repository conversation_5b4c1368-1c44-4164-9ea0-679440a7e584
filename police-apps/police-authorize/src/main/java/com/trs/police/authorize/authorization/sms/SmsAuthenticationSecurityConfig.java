package com.trs.police.authorize.authorization.sms;

import com.trs.police.authorize.authorization.LoginSuccessAndFailureHandler;
import com.trs.police.authorize.service.UserService;
import com.trs.police.common.redis.starter.service.RedisService;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 手机短信验证码登陆配置
 */
@Component
public class SmsAuthenticationSecurityConfig extends
    SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    @Resource
    private LoginSuccessAndFailureHandler successAndFailureHandler;

    @Resource
    private UserService userService;
    
    @Resource
    private RedisService redisService;

    @Resource
    private SmsLoginProperties smsLoginProperties;

    @Override
    public void configure(HttpSecurity httpSecurity) {
        SmsAuthenticationFilter filter = new SmsAuthenticationFilter();
        filter.setAuthenticationManager(httpSecurity.getSharedObject(AuthenticationManager.class));
        filter.setAuthenticationSuccessHandler(successAndFailureHandler);
        filter.setAuthenticationFailureHandler(successAndFailureHandler);

        SmsAuthenticationProvider smsAuthenticationProvider = new SmsAuthenticationProvider();
        smsAuthenticationProvider.setUserService(userService);
        smsAuthenticationProvider.setRedisService(redisService);
        smsAuthenticationProvider.setSmsLoginProperties(smsLoginProperties);

        httpSecurity.authenticationProvider(smsAuthenticationProvider)
            .addFilterAfter(filter, UsernamePasswordAuthenticationFilter.class);
    }
} 