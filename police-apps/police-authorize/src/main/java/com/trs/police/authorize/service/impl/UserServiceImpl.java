package com.trs.police.authorize.service.impl;


import com.alibaba.fastjson.JSON;
import com.trs.police.authorize.authorization.pwd.PwdAuthenticationToken;
import com.trs.police.authorize.domain.UserLockVO;
import com.trs.police.authorize.domain.entity.User;
import com.trs.police.authorize.mapper.UserMapper;
import com.trs.police.authorize.properties.LoginProperties;
import com.trs.police.authorize.service.UserService;
import com.trs.police.common.core.vo.log.LoginLogVO;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.OperationLogService;
import com.trs.police.common.redis.starter.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private OperationLogService operationLogService;

    private static final String LOCK_USER_PREFIX = "lockUser:";

    @Resource
    private LoginProperties loginProperties;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        //检查用户是否被限制登陆
        this.checkLockUser(username);

        User user = userMapper.findByName(username);

        if (Objects.nonNull(user)) {
            if (user.getStatus() != 1) {
                throw new UsernameNotFoundException(String.format("用户: [%s] 被禁用！", username));
            }
            return user;
        } else {
            throw new UsernameNotFoundException(String.format("用户或密码错误！", username));
        }

    }

    @Override
    public User loadUserByIdentity(@NotEmpty String identify) {
        return userMapper.findByIdentify(identify);
    }

    @Override
    public Long findDefaultUnitId(Long userId) {
        return userMapper.findDefaultUnitId(userId);
    }

    @Override
    @Cacheable(value = {"userInfo"}, key = "#userId")
    public User findUserById(Long userId) {
        return userMapper.selectById(userId);
    }

    @Override
    @Cacheable(value = {"roles"}, key = "#userId +'-'+#deptId")
    public Set<Long> getRoles(Long userId, Long deptId) {
        return userMapper.getRoles(userId, deptId);
    }

    @Override
    public void lockUser(String username, String loginType, Authentication authentication) {
        String key = LOCK_USER_PREFIX + username;
        long lockUserSeconds = loginProperties.getLockUserSeconds();
        int lockUserCount = loginProperties.getLockUserCount();

        UserLockVO userLock = (UserLockVO) redisService.get(key);
        if (Objects.isNull(userLock)) {
            LocalDateTime now = LocalDateTime.now();
            redisService.set(key, new UserLockVO(now), lockUserSeconds);
            LoginLogVO loginLogVO = new LoginLogVO();
            loginLogVO.setUserName(username);
            loginLogVO.setDetail("登录失败");
            loginLogVO.setType(loginType);
            recordLoginLog(loginLogVO, authentication);
            throw new InternalAuthenticationServiceException(
                String.format("用户或密码错误，你还有%s次机会！", lockUserCount - 1));
        } else {
            userLock.lock();
            if (userLock.getLoginFailCount() < lockUserCount) {
                //错误次数未超过设定值，使用第一次密码输入错误时间来计算key的过期时间
                LocalDateTime expire = userLock.getFirstLoginFailTime().plusSeconds(lockUserSeconds);
                long expireTime = Duration.between(expire, LocalDateTime.now()).toSeconds();
                redisService.set(key, userLock, Math.abs(expireTime));
                LoginLogVO loginLogVO = new LoginLogVO();
                loginLogVO.setUserName(username);
                loginLogVO.setDetail("登录失败");
                loginLogVO.setType(loginType);
                recordLoginLog(loginLogVO, authentication);
                throw new InternalAuthenticationServiceException(
                    String.format("用户或密码错误，你还有%s次机会！", lockUserCount - userLock.getLoginFailCount()));
            } else {
                //错误次数超过设定值，直接设定过期时间
                redisService.set(key, userLock, lockUserSeconds);
                LoginLogVO loginLogVO = new LoginLogVO();
                loginLogVO.setUserName(username);
                loginLogVO.setDetail("账号锁定");
                loginLogVO.setType(loginType);
                recordLoginLog(loginLogVO, authentication);
                throw new UsernameNotFoundException(
                    String.format("连续输入用户或密码错误超过三次,请在%s分钟后重试！", username,
                        lockUserSeconds / 60));
            }
        }
    }
    /**
     * 记录用户登录日志
     *
     * @param loginLogVO 登录日志信息
     * @param authentication 认证信息
     */
    public void recordLoginLog(LoginLogVO loginLogVO, Authentication authentication) {
        try {
            if (loginLogVO == null || loginLogVO.getUserName() == null || loginLogVO.getType() == null || loginLogVO.getDetail() == null) {
                log.error("记录登录日志的信息不完整 {}", JSON.toJSONString(loginLogVO));
                return;
            }
            // 记录登录失败日志
            if (authentication instanceof PwdAuthenticationToken) {
                PwdAuthenticationToken pwdAuthenticationToken = (PwdAuthenticationToken) authentication;
                if (Objects.nonNull(pwdAuthenticationToken.getRemoteAddress())) {
                    log.debug("登录日志记录ip地址: {}", pwdAuthenticationToken.getRemoteAddress());
                    loginLogVO.setIpAddress(pwdAuthenticationToken.getRemoteAddress());
                }
            }
            // 获取用户基本信息
            User userInfo = loadUserByIdentity(loginLogVO.getUserName());
            if (userInfo != null) {
                Long defaultUnitId = findDefaultUnitId(userInfo.getId());
                if (defaultUnitId != null) {
                    SimpleUserVO simpleUser = userMapper.findSimpleUser(userInfo.getId(), defaultUnitId);
                    loginLogVO.setDeptId(defaultUnitId);
                    loginLogVO.setDeptName(simpleUser.getDeptName());
                    //  部门id路径,不包含本级
                    loginLogVO.setDeptIdPath(simpleUser.getDeptIdPath());
                    loginLogVO.setIdCard(userInfo.getIdNumber());
                    loginLogVO.setTrueName(userInfo.getRealName());
                    loginLogVO.setCreateTime(LocalDateTime.now());
                    operationLogService.createLoginLog(loginLogVO);
                }
            }
        } catch (Exception e) {
            log.error("日志创建失败 {}", e.getMessage(), e);
        }
    }

    @Override
    public void unLockUser(String username) {
        String key = LOCK_USER_PREFIX + username;
        redisService.del(key);
    }

    @Override
    public void checkLockUser(String username) {
        String key = LOCK_USER_PREFIX + username;
        UserLockVO userLock = (UserLockVO) redisService.get(key);
        if (Objects.nonNull(userLock) && userLock.getLoginFailCount() >= loginProperties.getLockUserCount()) {
            Long expire = redisService.getExpire(key) / 60;
            throw new UsernameNotFoundException(
                String.format("用户: [%s] ，连续输入错误密码超过%s次,请在%s分钟后重试！", username,
                    loginProperties.getLockUserCount(), expire));
        }
    }

    @Override
    public User loadUserByMobile(String mobile) {
        List<User> users = userMapper.findByMobile(mobile);

        if (Objects.isNull(users) || users.isEmpty()) {
            throw new UsernameNotFoundException(String.format("手机号码[%s]对应的用户不存在！", mobile));
        } else if (users.size() > 1) {
            throw new UsernameNotFoundException(String.format("手机号码[%s]对应的用户不唯一！", mobile));
        }

        User user = users.get(0);
        //检查用户是否被限制登陆
        this.checkLockUser(user.getUsername());

        if (user.getStatus() != 1) {
            throw new UsernameNotFoundException(String.format("手机号码[%s]对应的用户被禁用！", mobile));
        }
        return user;
    }
}
