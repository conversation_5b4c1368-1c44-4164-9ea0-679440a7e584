<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.authorize.mapper.UserMapper">

    <resultMap id="simpleUserMap" type="com.trs.police.common.core.vo.permission.SimpleUserVO">
        <id column="id" property="userId"/>
        <result column="real_name" property="userName"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="dept_short_name" property="deptShortName"/>
        <result column="dept_code" property="deptCode"/>
        <result column="tel" property="tel"/>
        <result column="duty" property="duty"/>
        <result column="duty" property="deptIdPath"/>
    </resultMap>

    <select id="findSimpleUser" resultMap="simpleUserMap">
        SELECT u.id                        as id,
               u.real_name                 as real_name,
               d.id                        as dept_id,
               d.name                      as dept_name,
               d.short_name                as dept_short_name,
               d.code                      as dept_code,
               u.mobile                    as tel,
               u.duty                      as duty,
               d.path              as dept_id_path
        FROM t_user u,
             t_dept d
        where u.id = #{userId}
          and d.id = #{deptId}
    </select>

</mapper>