package com.trs.police.approval.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.approval.constant.enums.NodeStatusEnum;
import com.trs.police.approval.constant.enums.NodeType;
import com.trs.police.approval.domain.vo.ApprovalUserDeptVO;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 审批节点记录表(ApprovalNode)数据访问类
 *
 * <AUTHOR>
 * @since 2022-06-15 09:54:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_approval_node", autoResultMap = true)
public class NodeEntity extends AbstractBaseEntity implements Serializable {

    private static final long serialVersionUID = 629703817437040360L;

    /**
     * 名称
     */
    private String name;

    /**
     * 审批状态
     */
    private NodeStatusEnum status;

    /**
     * 流程主键
     */
    private Long processId;

    /**
     * 审批节点配置主键
     */
    private Long nodeConfigId;

    /**
     * 是否是相同部门
     */
    private Integer isSameOrg;

    /**
     * 节点类型 {@link NodeType}
     */
    private Integer nodeType;

    /**
     * 审批人
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ApprovalUserDeptVO[] approvers;
}