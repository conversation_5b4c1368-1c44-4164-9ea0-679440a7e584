package com.trs.police.approval.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.approval.constant.enums.ApprovalScopeEnum;
import com.trs.police.common.core.handler.typehandler.JsonToLongListHandler;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审批流程配置表(ApprovalProcessConfig)数据访问类
 *
 * <AUTHOR>
 * @since 2022-06-15 10:00:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_approval_process_config")
public class ProcessConfigEntity implements Serializable {

    private static final long serialVersionUID = 764523372620992873L;

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 流程名称
     */
    private String templateName;

    /**
     * 类型 dict: approval_type
     */
    private Long type;

    /**
     * 说明
     */
    private String remark;

    /**
     * 流程编号前缀
     */
    private String processNumberPrefix;

    /**
     * 是否停用
     */
    private Boolean isStopped;

    /**
     * 发起审批表单模版
     */
    private String startFormTemplate;

    /**
     * 使用范围(1: 功能业务, 2: 综合业务)
     */
    private ApprovalScopeEnum scope;

    /**
     * 允许发起的用户（综合业务）
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> allowUsers;

    /**
     * 允许发起的角色（综合业务）
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> allowRoles;

    /**
     * 允许发起的单位（综合业务）
     */
    @TableField(typeHandler = JsonToLongListHandler.class)
    private List<Long> allowUnits;

    /**
     * 是否删除
     */
    private Boolean isDeleted;
    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 是否每次操作都发送消息
     */
    private Integer isSendOperateMsg;

    /**
     * 是否支持同意并完结功能
     */
    private Boolean supportFinished;
}